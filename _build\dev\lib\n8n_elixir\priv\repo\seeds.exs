# Script for populating the database. You can run it as:
#
#     mix run priv/repo/seeds.exs
#
# Inside the script, you can read and write to any of your
# repositories directly:
#
#     N8nElixir.Repo.insert!(%N8nElixir.SomeSchema{})
#
# We recommend using the bang functions (`insert!`, `update!`
# and so on) as they will fail if something goes wrong.

alias N8nElixir.Repo
alias N8nElixir.Accounts.{User, Team}

# 创建默认团队（如果不存在）
team = case Repo.get_by(Team, name: "Default Team") do
  nil ->
    default_team = %Team{
      name: "Default Team",
      description: "Default team for initial setup",
      status: :active
    }
    Repo.insert!(default_team)
  existing_team ->
    existing_team
end

# 创建默认管理员用户（如果不存在）
case Repo.get_by(User, email: "<EMAIL>") do
  nil ->
    admin_user = %User{
      email: "<EMAIL>",
      first_name: "Ad<PERSON>",
      last_name: "User",
      role: :admin,
      status: :active,
      team_id: team.id
    }
    Repo.insert!(admin_user)
    IO.puts("Default admin user created: <EMAIL>")
  _existing_user ->
    IO.puts("Default admin user already exists: <EMAIL>")
end

IO.puts("Seeds completed successfully!")
IO.puts("Default admin user created: <EMAIL>")

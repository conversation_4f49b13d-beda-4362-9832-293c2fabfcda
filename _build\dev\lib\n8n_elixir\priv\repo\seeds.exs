# Script for populating the database. You can run it as:
#
#     mix run priv/repo/seeds.exs
#
# Inside the script, you can read and write to any of your
# repositories directly:
#
#     N8nElixir.Repo.insert!(%N8nElixir.SomeSchema{})
#
# We recommend using the bang functions (`insert!`, `update!`
# and so on) as they will fail if something goes wrong.

alias N8nElixir.Repo
alias N8nElixir.Accounts.{User, Team}

# 创建默认团队
default_team = %Team{
  name: "Default Team",
  description: "Default team for initial setup",
  status: :active
}

team = Repo.insert!(default_team)

# 创建默认管理员用户
admin_user = %User{
  email: "<EMAIL>",
  first_name: "Admin",
  last_name: "User",
  role: :admin,
  status: :active,
  team_id: team.id
}

Repo.insert!(admin_user)

IO.puts("Seeds completed successfully!")
IO.puts("Default admin user created: <EMAIL>")

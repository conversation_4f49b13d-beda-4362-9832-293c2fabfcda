<div class="workflow-editor">
  <!-- 顶部工具栏 -->
  <div class="toolbar">
    <div class="toolbar-left">
      <h1><%= @page_title %></h1>
    </div>

    <div class="toolbar-center">
      <div class="execution-controls">
        <button class="btn btn-primary" phx-click="save_workflow">
          <i class="fa fa-save"></i> Save
        </button>
      </div>
    </div>

    <div class="toolbar-right">
      <.link navigate="/workflows" class="btn btn-secondary">
        <i class="fa fa-arrow-left"></i> Back to Workflows
      </.link>
    </div>
  </div>

  <!-- 主编辑区域 -->
  <div class="editor-container">
    <div class="canvas-container">
      <div class="canvas">
        <div class="coming-soon">
          <h2>Workflow Editor</h2>
          <p>Visual workflow editor coming soon...</p>
          <%= if @workflow_id do %>
            <p>Editing workflow: <strong><%= @workflow_id %></strong></p>
          <% else %>
            <p>Creating new workflow</p>
          <% end %>
          <p>Mode: <strong><%= @mode %></strong></p>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
.workflow-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.toolbar {
  height: 60px;
  background: white;
  border-bottom: 1px solid #ddd;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.toolbar-left, .toolbar-center, .toolbar-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.toolbar-left h1 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.editor-container {
  flex: 1;
  display: flex;
  position: relative;
  overflow: hidden;
}

.canvas-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  background: 
    linear-gradient(90deg, #f0f0f0 1px, transparent 1px),
    linear-gradient(#f0f0f0 1px, transparent 1px);
  background-size: 20px 20px;
}

.canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.coming-soon {
  text-align: center;
  background: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
  max-width: 500px;
}

.coming-soon h2 {
  margin: 0 0 15px 0;
  color: #333;
}

.coming-soon p {
  margin: 10px 0;
  color: #666;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
  text-decoration: none;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
}
</style>
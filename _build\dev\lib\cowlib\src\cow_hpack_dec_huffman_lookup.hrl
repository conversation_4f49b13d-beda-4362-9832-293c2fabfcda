%% Copyright (c) <PERSON><PERSON><PERSON> <<EMAIL>>
%%
%% Permission to use, copy, modify, and/or distribute this software for any
%% purpose with or without fee is hereby granted, provided that the above
%% copyright notice and this permission notice appear in all copies.
%%
%% THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES
%% WITH REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF
%% MERCHANTABILITY AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR
%% ANY SPECIAL, DIRECT, INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES
%% WHATSOEVER RESULTING FROM LOSS OF USE, DATA OR PROFITS, WHETHER IN AN
%% ACTION OF CONTRACT, NEGLIGENCE OR OTHER TORTIOUS ACTION, ARISING OUT OF
%% OR IN CONNECTION WITH THE USE OR PERFORMANCE OF THIS SOFTWARE.

%% This lookup function was created by converting the
%% table from Nginx[1] into a form better suitable for
%% Erlang/OTP. This particular table takes a byte-sized
%% state and 4 bits to determine whether to emit a
%% character and what the next state is. It is most
%% appropriate for Erlang/OTP because we can benefit
%% from binary pattern matching optimizations by
%% matching the binary one byte at a time, calling
%% this lookup function twice. This and similar
%% algorithms are discussed here[2] and there[3].
%%
%% It is possible to write a lookup table taking
%% a full byte instead of just 4 bits, but this
%% would make this function take 65536 clauses instead
%% of the current 4096. This could be done later
%% as a further optimization but might not yield
%% significant improvements.
%% 
%% [1] https://hg.nginx.org/nginx/file/tip/src/http/v2/ngx_http_v2_huff_decode.c
%% [2] http://citeseerx.ist.psu.edu/viewdoc/download?doi=********.4248&rep=rep1&type=pdf
%% [3] https://commandlinefanatic.com/cgi-bin/showarticle.cgi?article=art007

dec_huffman_lookup(16#00, 16#0) -> {more, undefined, 16#04};
dec_huffman_lookup(16#00, 16#1) -> {more, undefined, 16#05};
dec_huffman_lookup(16#00, 16#2) -> {more, undefined, 16#07};
dec_huffman_lookup(16#00, 16#3) -> {more, undefined, 16#08};
dec_huffman_lookup(16#00, 16#4) -> {more, undefined, 16#0b};
dec_huffman_lookup(16#00, 16#5) -> {more, undefined, 16#0c};
dec_huffman_lookup(16#00, 16#6) -> {more, undefined, 16#10};
dec_huffman_lookup(16#00, 16#7) -> {more, undefined, 16#13};
dec_huffman_lookup(16#00, 16#8) -> {more, undefined, 16#19};
dec_huffman_lookup(16#00, 16#9) -> {more, undefined, 16#1c};
dec_huffman_lookup(16#00, 16#a) -> {more, undefined, 16#20};
dec_huffman_lookup(16#00, 16#b) -> {more, undefined, 16#23};
dec_huffman_lookup(16#00, 16#c) -> {more, undefined, 16#2a};
dec_huffman_lookup(16#00, 16#d) -> {more, undefined, 16#31};
dec_huffman_lookup(16#00, 16#e) -> {more, undefined, 16#39};
dec_huffman_lookup(16#00, 16#f) -> {ok, undefined, 16#40};
dec_huffman_lookup(16#01, 16#0) -> {ok, 16#30, 16#00};
dec_huffman_lookup(16#01, 16#1) -> {ok, 16#31, 16#00};
dec_huffman_lookup(16#01, 16#2) -> {ok, 16#32, 16#00};
dec_huffman_lookup(16#01, 16#3) -> {ok, 16#61, 16#00};
dec_huffman_lookup(16#01, 16#4) -> {ok, 16#63, 16#00};
dec_huffman_lookup(16#01, 16#5) -> {ok, 16#65, 16#00};
dec_huffman_lookup(16#01, 16#6) -> {ok, 16#69, 16#00};
dec_huffman_lookup(16#01, 16#7) -> {ok, 16#6f, 16#00};
dec_huffman_lookup(16#01, 16#8) -> {ok, 16#73, 16#00};
dec_huffman_lookup(16#01, 16#9) -> {ok, 16#74, 16#00};
dec_huffman_lookup(16#01, 16#a) -> {more, undefined, 16#0d};
dec_huffman_lookup(16#01, 16#b) -> {more, undefined, 16#0e};
dec_huffman_lookup(16#01, 16#c) -> {more, undefined, 16#11};
dec_huffman_lookup(16#01, 16#d) -> {more, undefined, 16#12};
dec_huffman_lookup(16#01, 16#e) -> {more, undefined, 16#14};
dec_huffman_lookup(16#01, 16#f) -> {more, undefined, 16#15};
dec_huffman_lookup(16#02, 16#0) -> {more, 16#30, 16#01};
dec_huffman_lookup(16#02, 16#1) -> {ok, 16#30, 16#16};
dec_huffman_lookup(16#02, 16#2) -> {more, 16#31, 16#01};
dec_huffman_lookup(16#02, 16#3) -> {ok, 16#31, 16#16};
dec_huffman_lookup(16#02, 16#4) -> {more, 16#32, 16#01};
dec_huffman_lookup(16#02, 16#5) -> {ok, 16#32, 16#16};
dec_huffman_lookup(16#02, 16#6) -> {more, 16#61, 16#01};
dec_huffman_lookup(16#02, 16#7) -> {ok, 16#61, 16#16};
dec_huffman_lookup(16#02, 16#8) -> {more, 16#63, 16#01};
dec_huffman_lookup(16#02, 16#9) -> {ok, 16#63, 16#16};
dec_huffman_lookup(16#02, 16#a) -> {more, 16#65, 16#01};
dec_huffman_lookup(16#02, 16#b) -> {ok, 16#65, 16#16};
dec_huffman_lookup(16#02, 16#c) -> {more, 16#69, 16#01};
dec_huffman_lookup(16#02, 16#d) -> {ok, 16#69, 16#16};
dec_huffman_lookup(16#02, 16#e) -> {more, 16#6f, 16#01};
dec_huffman_lookup(16#02, 16#f) -> {ok, 16#6f, 16#16};
dec_huffman_lookup(16#03, 16#0) -> {more, 16#30, 16#02};
dec_huffman_lookup(16#03, 16#1) -> {more, 16#30, 16#09};
dec_huffman_lookup(16#03, 16#2) -> {more, 16#30, 16#17};
dec_huffman_lookup(16#03, 16#3) -> {ok, 16#30, 16#28};
dec_huffman_lookup(16#03, 16#4) -> {more, 16#31, 16#02};
dec_huffman_lookup(16#03, 16#5) -> {more, 16#31, 16#09};
dec_huffman_lookup(16#03, 16#6) -> {more, 16#31, 16#17};
dec_huffman_lookup(16#03, 16#7) -> {ok, 16#31, 16#28};
dec_huffman_lookup(16#03, 16#8) -> {more, 16#32, 16#02};
dec_huffman_lookup(16#03, 16#9) -> {more, 16#32, 16#09};
dec_huffman_lookup(16#03, 16#a) -> {more, 16#32, 16#17};
dec_huffman_lookup(16#03, 16#b) -> {ok, 16#32, 16#28};
dec_huffman_lookup(16#03, 16#c) -> {more, 16#61, 16#02};
dec_huffman_lookup(16#03, 16#d) -> {more, 16#61, 16#09};
dec_huffman_lookup(16#03, 16#e) -> {more, 16#61, 16#17};
dec_huffman_lookup(16#03, 16#f) -> {ok, 16#61, 16#28};
dec_huffman_lookup(16#04, 16#0) -> {more, 16#30, 16#03};
dec_huffman_lookup(16#04, 16#1) -> {more, 16#30, 16#06};
dec_huffman_lookup(16#04, 16#2) -> {more, 16#30, 16#0a};
dec_huffman_lookup(16#04, 16#3) -> {more, 16#30, 16#0f};
dec_huffman_lookup(16#04, 16#4) -> {more, 16#30, 16#18};
dec_huffman_lookup(16#04, 16#5) -> {more, 16#30, 16#1f};
dec_huffman_lookup(16#04, 16#6) -> {more, 16#30, 16#29};
dec_huffman_lookup(16#04, 16#7) -> {ok, 16#30, 16#38};
dec_huffman_lookup(16#04, 16#8) -> {more, 16#31, 16#03};
dec_huffman_lookup(16#04, 16#9) -> {more, 16#31, 16#06};
dec_huffman_lookup(16#04, 16#a) -> {more, 16#31, 16#0a};
dec_huffman_lookup(16#04, 16#b) -> {more, 16#31, 16#0f};
dec_huffman_lookup(16#04, 16#c) -> {more, 16#31, 16#18};
dec_huffman_lookup(16#04, 16#d) -> {more, 16#31, 16#1f};
dec_huffman_lookup(16#04, 16#e) -> {more, 16#31, 16#29};
dec_huffman_lookup(16#04, 16#f) -> {ok, 16#31, 16#38};
dec_huffman_lookup(16#05, 16#0) -> {more, 16#32, 16#03};
dec_huffman_lookup(16#05, 16#1) -> {more, 16#32, 16#06};
dec_huffman_lookup(16#05, 16#2) -> {more, 16#32, 16#0a};
dec_huffman_lookup(16#05, 16#3) -> {more, 16#32, 16#0f};
dec_huffman_lookup(16#05, 16#4) -> {more, 16#32, 16#18};
dec_huffman_lookup(16#05, 16#5) -> {more, 16#32, 16#1f};
dec_huffman_lookup(16#05, 16#6) -> {more, 16#32, 16#29};
dec_huffman_lookup(16#05, 16#7) -> {ok, 16#32, 16#38};
dec_huffman_lookup(16#05, 16#8) -> {more, 16#61, 16#03};
dec_huffman_lookup(16#05, 16#9) -> {more, 16#61, 16#06};
dec_huffman_lookup(16#05, 16#a) -> {more, 16#61, 16#0a};
dec_huffman_lookup(16#05, 16#b) -> {more, 16#61, 16#0f};
dec_huffman_lookup(16#05, 16#c) -> {more, 16#61, 16#18};
dec_huffman_lookup(16#05, 16#d) -> {more, 16#61, 16#1f};
dec_huffman_lookup(16#05, 16#e) -> {more, 16#61, 16#29};
dec_huffman_lookup(16#05, 16#f) -> {ok, 16#61, 16#38};
dec_huffman_lookup(16#06, 16#0) -> {more, 16#63, 16#02};
dec_huffman_lookup(16#06, 16#1) -> {more, 16#63, 16#09};
dec_huffman_lookup(16#06, 16#2) -> {more, 16#63, 16#17};
dec_huffman_lookup(16#06, 16#3) -> {ok, 16#63, 16#28};
dec_huffman_lookup(16#06, 16#4) -> {more, 16#65, 16#02};
dec_huffman_lookup(16#06, 16#5) -> {more, 16#65, 16#09};
dec_huffman_lookup(16#06, 16#6) -> {more, 16#65, 16#17};
dec_huffman_lookup(16#06, 16#7) -> {ok, 16#65, 16#28};
dec_huffman_lookup(16#06, 16#8) -> {more, 16#69, 16#02};
dec_huffman_lookup(16#06, 16#9) -> {more, 16#69, 16#09};
dec_huffman_lookup(16#06, 16#a) -> {more, 16#69, 16#17};
dec_huffman_lookup(16#06, 16#b) -> {ok, 16#69, 16#28};
dec_huffman_lookup(16#06, 16#c) -> {more, 16#6f, 16#02};
dec_huffman_lookup(16#06, 16#d) -> {more, 16#6f, 16#09};
dec_huffman_lookup(16#06, 16#e) -> {more, 16#6f, 16#17};
dec_huffman_lookup(16#06, 16#f) -> {ok, 16#6f, 16#28};
dec_huffman_lookup(16#07, 16#0) -> {more, 16#63, 16#03};
dec_huffman_lookup(16#07, 16#1) -> {more, 16#63, 16#06};
dec_huffman_lookup(16#07, 16#2) -> {more, 16#63, 16#0a};
dec_huffman_lookup(16#07, 16#3) -> {more, 16#63, 16#0f};
dec_huffman_lookup(16#07, 16#4) -> {more, 16#63, 16#18};
dec_huffman_lookup(16#07, 16#5) -> {more, 16#63, 16#1f};
dec_huffman_lookup(16#07, 16#6) -> {more, 16#63, 16#29};
dec_huffman_lookup(16#07, 16#7) -> {ok, 16#63, 16#38};
dec_huffman_lookup(16#07, 16#8) -> {more, 16#65, 16#03};
dec_huffman_lookup(16#07, 16#9) -> {more, 16#65, 16#06};
dec_huffman_lookup(16#07, 16#a) -> {more, 16#65, 16#0a};
dec_huffman_lookup(16#07, 16#b) -> {more, 16#65, 16#0f};
dec_huffman_lookup(16#07, 16#c) -> {more, 16#65, 16#18};
dec_huffman_lookup(16#07, 16#d) -> {more, 16#65, 16#1f};
dec_huffman_lookup(16#07, 16#e) -> {more, 16#65, 16#29};
dec_huffman_lookup(16#07, 16#f) -> {ok, 16#65, 16#38};
dec_huffman_lookup(16#08, 16#0) -> {more, 16#69, 16#03};
dec_huffman_lookup(16#08, 16#1) -> {more, 16#69, 16#06};
dec_huffman_lookup(16#08, 16#2) -> {more, 16#69, 16#0a};
dec_huffman_lookup(16#08, 16#3) -> {more, 16#69, 16#0f};
dec_huffman_lookup(16#08, 16#4) -> {more, 16#69, 16#18};
dec_huffman_lookup(16#08, 16#5) -> {more, 16#69, 16#1f};
dec_huffman_lookup(16#08, 16#6) -> {more, 16#69, 16#29};
dec_huffman_lookup(16#08, 16#7) -> {ok, 16#69, 16#38};
dec_huffman_lookup(16#08, 16#8) -> {more, 16#6f, 16#03};
dec_huffman_lookup(16#08, 16#9) -> {more, 16#6f, 16#06};
dec_huffman_lookup(16#08, 16#a) -> {more, 16#6f, 16#0a};
dec_huffman_lookup(16#08, 16#b) -> {more, 16#6f, 16#0f};
dec_huffman_lookup(16#08, 16#c) -> {more, 16#6f, 16#18};
dec_huffman_lookup(16#08, 16#d) -> {more, 16#6f, 16#1f};
dec_huffman_lookup(16#08, 16#e) -> {more, 16#6f, 16#29};
dec_huffman_lookup(16#08, 16#f) -> {ok, 16#6f, 16#38};
dec_huffman_lookup(16#09, 16#0) -> {more, 16#73, 16#01};
dec_huffman_lookup(16#09, 16#1) -> {ok, 16#73, 16#16};
dec_huffman_lookup(16#09, 16#2) -> {more, 16#74, 16#01};
dec_huffman_lookup(16#09, 16#3) -> {ok, 16#74, 16#16};
dec_huffman_lookup(16#09, 16#4) -> {ok, 16#20, 16#00};
dec_huffman_lookup(16#09, 16#5) -> {ok, 16#25, 16#00};
dec_huffman_lookup(16#09, 16#6) -> {ok, 16#2d, 16#00};
dec_huffman_lookup(16#09, 16#7) -> {ok, 16#2e, 16#00};
dec_huffman_lookup(16#09, 16#8) -> {ok, 16#2f, 16#00};
dec_huffman_lookup(16#09, 16#9) -> {ok, 16#33, 16#00};
dec_huffman_lookup(16#09, 16#a) -> {ok, 16#34, 16#00};
dec_huffman_lookup(16#09, 16#b) -> {ok, 16#35, 16#00};
dec_huffman_lookup(16#09, 16#c) -> {ok, 16#36, 16#00};
dec_huffman_lookup(16#09, 16#d) -> {ok, 16#37, 16#00};
dec_huffman_lookup(16#09, 16#e) -> {ok, 16#38, 16#00};
dec_huffman_lookup(16#09, 16#f) -> {ok, 16#39, 16#00};
dec_huffman_lookup(16#0a, 16#0) -> {more, 16#73, 16#02};
dec_huffman_lookup(16#0a, 16#1) -> {more, 16#73, 16#09};
dec_huffman_lookup(16#0a, 16#2) -> {more, 16#73, 16#17};
dec_huffman_lookup(16#0a, 16#3) -> {ok, 16#73, 16#28};
dec_huffman_lookup(16#0a, 16#4) -> {more, 16#74, 16#02};
dec_huffman_lookup(16#0a, 16#5) -> {more, 16#74, 16#09};
dec_huffman_lookup(16#0a, 16#6) -> {more, 16#74, 16#17};
dec_huffman_lookup(16#0a, 16#7) -> {ok, 16#74, 16#28};
dec_huffman_lookup(16#0a, 16#8) -> {more, 16#20, 16#01};
dec_huffman_lookup(16#0a, 16#9) -> {ok, 16#20, 16#16};
dec_huffman_lookup(16#0a, 16#a) -> {more, 16#25, 16#01};
dec_huffman_lookup(16#0a, 16#b) -> {ok, 16#25, 16#16};
dec_huffman_lookup(16#0a, 16#c) -> {more, 16#2d, 16#01};
dec_huffman_lookup(16#0a, 16#d) -> {ok, 16#2d, 16#16};
dec_huffman_lookup(16#0a, 16#e) -> {more, 16#2e, 16#01};
dec_huffman_lookup(16#0a, 16#f) -> {ok, 16#2e, 16#16};
dec_huffman_lookup(16#0b, 16#0) -> {more, 16#73, 16#03};
dec_huffman_lookup(16#0b, 16#1) -> {more, 16#73, 16#06};
dec_huffman_lookup(16#0b, 16#2) -> {more, 16#73, 16#0a};
dec_huffman_lookup(16#0b, 16#3) -> {more, 16#73, 16#0f};
dec_huffman_lookup(16#0b, 16#4) -> {more, 16#73, 16#18};
dec_huffman_lookup(16#0b, 16#5) -> {more, 16#73, 16#1f};
dec_huffman_lookup(16#0b, 16#6) -> {more, 16#73, 16#29};
dec_huffman_lookup(16#0b, 16#7) -> {ok, 16#73, 16#38};
dec_huffman_lookup(16#0b, 16#8) -> {more, 16#74, 16#03};
dec_huffman_lookup(16#0b, 16#9) -> {more, 16#74, 16#06};
dec_huffman_lookup(16#0b, 16#a) -> {more, 16#74, 16#0a};
dec_huffman_lookup(16#0b, 16#b) -> {more, 16#74, 16#0f};
dec_huffman_lookup(16#0b, 16#c) -> {more, 16#74, 16#18};
dec_huffman_lookup(16#0b, 16#d) -> {more, 16#74, 16#1f};
dec_huffman_lookup(16#0b, 16#e) -> {more, 16#74, 16#29};
dec_huffman_lookup(16#0b, 16#f) -> {ok, 16#74, 16#38};
dec_huffman_lookup(16#0c, 16#0) -> {more, 16#20, 16#02};
dec_huffman_lookup(16#0c, 16#1) -> {more, 16#20, 16#09};
dec_huffman_lookup(16#0c, 16#2) -> {more, 16#20, 16#17};
dec_huffman_lookup(16#0c, 16#3) -> {ok, 16#20, 16#28};
dec_huffman_lookup(16#0c, 16#4) -> {more, 16#25, 16#02};
dec_huffman_lookup(16#0c, 16#5) -> {more, 16#25, 16#09};
dec_huffman_lookup(16#0c, 16#6) -> {more, 16#25, 16#17};
dec_huffman_lookup(16#0c, 16#7) -> {ok, 16#25, 16#28};
dec_huffman_lookup(16#0c, 16#8) -> {more, 16#2d, 16#02};
dec_huffman_lookup(16#0c, 16#9) -> {more, 16#2d, 16#09};
dec_huffman_lookup(16#0c, 16#a) -> {more, 16#2d, 16#17};
dec_huffman_lookup(16#0c, 16#b) -> {ok, 16#2d, 16#28};
dec_huffman_lookup(16#0c, 16#c) -> {more, 16#2e, 16#02};
dec_huffman_lookup(16#0c, 16#d) -> {more, 16#2e, 16#09};
dec_huffman_lookup(16#0c, 16#e) -> {more, 16#2e, 16#17};
dec_huffman_lookup(16#0c, 16#f) -> {ok, 16#2e, 16#28};
dec_huffman_lookup(16#0d, 16#0) -> {more, 16#20, 16#03};
dec_huffman_lookup(16#0d, 16#1) -> {more, 16#20, 16#06};
dec_huffman_lookup(16#0d, 16#2) -> {more, 16#20, 16#0a};
dec_huffman_lookup(16#0d, 16#3) -> {more, 16#20, 16#0f};
dec_huffman_lookup(16#0d, 16#4) -> {more, 16#20, 16#18};
dec_huffman_lookup(16#0d, 16#5) -> {more, 16#20, 16#1f};
dec_huffman_lookup(16#0d, 16#6) -> {more, 16#20, 16#29};
dec_huffman_lookup(16#0d, 16#7) -> {ok, 16#20, 16#38};
dec_huffman_lookup(16#0d, 16#8) -> {more, 16#25, 16#03};
dec_huffman_lookup(16#0d, 16#9) -> {more, 16#25, 16#06};
dec_huffman_lookup(16#0d, 16#a) -> {more, 16#25, 16#0a};
dec_huffman_lookup(16#0d, 16#b) -> {more, 16#25, 16#0f};
dec_huffman_lookup(16#0d, 16#c) -> {more, 16#25, 16#18};
dec_huffman_lookup(16#0d, 16#d) -> {more, 16#25, 16#1f};
dec_huffman_lookup(16#0d, 16#e) -> {more, 16#25, 16#29};
dec_huffman_lookup(16#0d, 16#f) -> {ok, 16#25, 16#38};
dec_huffman_lookup(16#0e, 16#0) -> {more, 16#2d, 16#03};
dec_huffman_lookup(16#0e, 16#1) -> {more, 16#2d, 16#06};
dec_huffman_lookup(16#0e, 16#2) -> {more, 16#2d, 16#0a};
dec_huffman_lookup(16#0e, 16#3) -> {more, 16#2d, 16#0f};
dec_huffman_lookup(16#0e, 16#4) -> {more, 16#2d, 16#18};
dec_huffman_lookup(16#0e, 16#5) -> {more, 16#2d, 16#1f};
dec_huffman_lookup(16#0e, 16#6) -> {more, 16#2d, 16#29};
dec_huffman_lookup(16#0e, 16#7) -> {ok, 16#2d, 16#38};
dec_huffman_lookup(16#0e, 16#8) -> {more, 16#2e, 16#03};
dec_huffman_lookup(16#0e, 16#9) -> {more, 16#2e, 16#06};
dec_huffman_lookup(16#0e, 16#a) -> {more, 16#2e, 16#0a};
dec_huffman_lookup(16#0e, 16#b) -> {more, 16#2e, 16#0f};
dec_huffman_lookup(16#0e, 16#c) -> {more, 16#2e, 16#18};
dec_huffman_lookup(16#0e, 16#d) -> {more, 16#2e, 16#1f};
dec_huffman_lookup(16#0e, 16#e) -> {more, 16#2e, 16#29};
dec_huffman_lookup(16#0e, 16#f) -> {ok, 16#2e, 16#38};
dec_huffman_lookup(16#0f, 16#0) -> {more, 16#2f, 16#01};
dec_huffman_lookup(16#0f, 16#1) -> {ok, 16#2f, 16#16};
dec_huffman_lookup(16#0f, 16#2) -> {more, 16#33, 16#01};
dec_huffman_lookup(16#0f, 16#3) -> {ok, 16#33, 16#16};
dec_huffman_lookup(16#0f, 16#4) -> {more, 16#34, 16#01};
dec_huffman_lookup(16#0f, 16#5) -> {ok, 16#34, 16#16};
dec_huffman_lookup(16#0f, 16#6) -> {more, 16#35, 16#01};
dec_huffman_lookup(16#0f, 16#7) -> {ok, 16#35, 16#16};
dec_huffman_lookup(16#0f, 16#8) -> {more, 16#36, 16#01};
dec_huffman_lookup(16#0f, 16#9) -> {ok, 16#36, 16#16};
dec_huffman_lookup(16#0f, 16#a) -> {more, 16#37, 16#01};
dec_huffman_lookup(16#0f, 16#b) -> {ok, 16#37, 16#16};
dec_huffman_lookup(16#0f, 16#c) -> {more, 16#38, 16#01};
dec_huffman_lookup(16#0f, 16#d) -> {ok, 16#38, 16#16};
dec_huffman_lookup(16#0f, 16#e) -> {more, 16#39, 16#01};
dec_huffman_lookup(16#0f, 16#f) -> {ok, 16#39, 16#16};
dec_huffman_lookup(16#10, 16#0) -> {more, 16#2f, 16#02};
dec_huffman_lookup(16#10, 16#1) -> {more, 16#2f, 16#09};
dec_huffman_lookup(16#10, 16#2) -> {more, 16#2f, 16#17};
dec_huffman_lookup(16#10, 16#3) -> {ok, 16#2f, 16#28};
dec_huffman_lookup(16#10, 16#4) -> {more, 16#33, 16#02};
dec_huffman_lookup(16#10, 16#5) -> {more, 16#33, 16#09};
dec_huffman_lookup(16#10, 16#6) -> {more, 16#33, 16#17};
dec_huffman_lookup(16#10, 16#7) -> {ok, 16#33, 16#28};
dec_huffman_lookup(16#10, 16#8) -> {more, 16#34, 16#02};
dec_huffman_lookup(16#10, 16#9) -> {more, 16#34, 16#09};
dec_huffman_lookup(16#10, 16#a) -> {more, 16#34, 16#17};
dec_huffman_lookup(16#10, 16#b) -> {ok, 16#34, 16#28};
dec_huffman_lookup(16#10, 16#c) -> {more, 16#35, 16#02};
dec_huffman_lookup(16#10, 16#d) -> {more, 16#35, 16#09};
dec_huffman_lookup(16#10, 16#e) -> {more, 16#35, 16#17};
dec_huffman_lookup(16#10, 16#f) -> {ok, 16#35, 16#28};
dec_huffman_lookup(16#11, 16#0) -> {more, 16#2f, 16#03};
dec_huffman_lookup(16#11, 16#1) -> {more, 16#2f, 16#06};
dec_huffman_lookup(16#11, 16#2) -> {more, 16#2f, 16#0a};
dec_huffman_lookup(16#11, 16#3) -> {more, 16#2f, 16#0f};
dec_huffman_lookup(16#11, 16#4) -> {more, 16#2f, 16#18};
dec_huffman_lookup(16#11, 16#5) -> {more, 16#2f, 16#1f};
dec_huffman_lookup(16#11, 16#6) -> {more, 16#2f, 16#29};
dec_huffman_lookup(16#11, 16#7) -> {ok, 16#2f, 16#38};
dec_huffman_lookup(16#11, 16#8) -> {more, 16#33, 16#03};
dec_huffman_lookup(16#11, 16#9) -> {more, 16#33, 16#06};
dec_huffman_lookup(16#11, 16#a) -> {more, 16#33, 16#0a};
dec_huffman_lookup(16#11, 16#b) -> {more, 16#33, 16#0f};
dec_huffman_lookup(16#11, 16#c) -> {more, 16#33, 16#18};
dec_huffman_lookup(16#11, 16#d) -> {more, 16#33, 16#1f};
dec_huffman_lookup(16#11, 16#e) -> {more, 16#33, 16#29};
dec_huffman_lookup(16#11, 16#f) -> {ok, 16#33, 16#38};
dec_huffman_lookup(16#12, 16#0) -> {more, 16#34, 16#03};
dec_huffman_lookup(16#12, 16#1) -> {more, 16#34, 16#06};
dec_huffman_lookup(16#12, 16#2) -> {more, 16#34, 16#0a};
dec_huffman_lookup(16#12, 16#3) -> {more, 16#34, 16#0f};
dec_huffman_lookup(16#12, 16#4) -> {more, 16#34, 16#18};
dec_huffman_lookup(16#12, 16#5) -> {more, 16#34, 16#1f};
dec_huffman_lookup(16#12, 16#6) -> {more, 16#34, 16#29};
dec_huffman_lookup(16#12, 16#7) -> {ok, 16#34, 16#38};
dec_huffman_lookup(16#12, 16#8) -> {more, 16#35, 16#03};
dec_huffman_lookup(16#12, 16#9) -> {more, 16#35, 16#06};
dec_huffman_lookup(16#12, 16#a) -> {more, 16#35, 16#0a};
dec_huffman_lookup(16#12, 16#b) -> {more, 16#35, 16#0f};
dec_huffman_lookup(16#12, 16#c) -> {more, 16#35, 16#18};
dec_huffman_lookup(16#12, 16#d) -> {more, 16#35, 16#1f};
dec_huffman_lookup(16#12, 16#e) -> {more, 16#35, 16#29};
dec_huffman_lookup(16#12, 16#f) -> {ok, 16#35, 16#38};
dec_huffman_lookup(16#13, 16#0) -> {more, 16#36, 16#02};
dec_huffman_lookup(16#13, 16#1) -> {more, 16#36, 16#09};
dec_huffman_lookup(16#13, 16#2) -> {more, 16#36, 16#17};
dec_huffman_lookup(16#13, 16#3) -> {ok, 16#36, 16#28};
dec_huffman_lookup(16#13, 16#4) -> {more, 16#37, 16#02};
dec_huffman_lookup(16#13, 16#5) -> {more, 16#37, 16#09};
dec_huffman_lookup(16#13, 16#6) -> {more, 16#37, 16#17};
dec_huffman_lookup(16#13, 16#7) -> {ok, 16#37, 16#28};
dec_huffman_lookup(16#13, 16#8) -> {more, 16#38, 16#02};
dec_huffman_lookup(16#13, 16#9) -> {more, 16#38, 16#09};
dec_huffman_lookup(16#13, 16#a) -> {more, 16#38, 16#17};
dec_huffman_lookup(16#13, 16#b) -> {ok, 16#38, 16#28};
dec_huffman_lookup(16#13, 16#c) -> {more, 16#39, 16#02};
dec_huffman_lookup(16#13, 16#d) -> {more, 16#39, 16#09};
dec_huffman_lookup(16#13, 16#e) -> {more, 16#39, 16#17};
dec_huffman_lookup(16#13, 16#f) -> {ok, 16#39, 16#28};
dec_huffman_lookup(16#14, 16#0) -> {more, 16#36, 16#03};
dec_huffman_lookup(16#14, 16#1) -> {more, 16#36, 16#06};
dec_huffman_lookup(16#14, 16#2) -> {more, 16#36, 16#0a};
dec_huffman_lookup(16#14, 16#3) -> {more, 16#36, 16#0f};
dec_huffman_lookup(16#14, 16#4) -> {more, 16#36, 16#18};
dec_huffman_lookup(16#14, 16#5) -> {more, 16#36, 16#1f};
dec_huffman_lookup(16#14, 16#6) -> {more, 16#36, 16#29};
dec_huffman_lookup(16#14, 16#7) -> {ok, 16#36, 16#38};
dec_huffman_lookup(16#14, 16#8) -> {more, 16#37, 16#03};
dec_huffman_lookup(16#14, 16#9) -> {more, 16#37, 16#06};
dec_huffman_lookup(16#14, 16#a) -> {more, 16#37, 16#0a};
dec_huffman_lookup(16#14, 16#b) -> {more, 16#37, 16#0f};
dec_huffman_lookup(16#14, 16#c) -> {more, 16#37, 16#18};
dec_huffman_lookup(16#14, 16#d) -> {more, 16#37, 16#1f};
dec_huffman_lookup(16#14, 16#e) -> {more, 16#37, 16#29};
dec_huffman_lookup(16#14, 16#f) -> {ok, 16#37, 16#38};
dec_huffman_lookup(16#15, 16#0) -> {more, 16#38, 16#03};
dec_huffman_lookup(16#15, 16#1) -> {more, 16#38, 16#06};
dec_huffman_lookup(16#15, 16#2) -> {more, 16#38, 16#0a};
dec_huffman_lookup(16#15, 16#3) -> {more, 16#38, 16#0f};
dec_huffman_lookup(16#15, 16#4) -> {more, 16#38, 16#18};
dec_huffman_lookup(16#15, 16#5) -> {more, 16#38, 16#1f};
dec_huffman_lookup(16#15, 16#6) -> {more, 16#38, 16#29};
dec_huffman_lookup(16#15, 16#7) -> {ok, 16#38, 16#38};
dec_huffman_lookup(16#15, 16#8) -> {more, 16#39, 16#03};
dec_huffman_lookup(16#15, 16#9) -> {more, 16#39, 16#06};
dec_huffman_lookup(16#15, 16#a) -> {more, 16#39, 16#0a};
dec_huffman_lookup(16#15, 16#b) -> {more, 16#39, 16#0f};
dec_huffman_lookup(16#15, 16#c) -> {more, 16#39, 16#18};
dec_huffman_lookup(16#15, 16#d) -> {more, 16#39, 16#1f};
dec_huffman_lookup(16#15, 16#e) -> {more, 16#39, 16#29};
dec_huffman_lookup(16#15, 16#f) -> {ok, 16#39, 16#38};
dec_huffman_lookup(16#16, 16#0) -> {more, undefined, 16#1a};
dec_huffman_lookup(16#16, 16#1) -> {more, undefined, 16#1b};
dec_huffman_lookup(16#16, 16#2) -> {more, undefined, 16#1d};
dec_huffman_lookup(16#16, 16#3) -> {more, undefined, 16#1e};
dec_huffman_lookup(16#16, 16#4) -> {more, undefined, 16#21};
dec_huffman_lookup(16#16, 16#5) -> {more, undefined, 16#22};
dec_huffman_lookup(16#16, 16#6) -> {more, undefined, 16#24};
dec_huffman_lookup(16#16, 16#7) -> {more, undefined, 16#25};
dec_huffman_lookup(16#16, 16#8) -> {more, undefined, 16#2b};
dec_huffman_lookup(16#16, 16#9) -> {more, undefined, 16#2e};
dec_huffman_lookup(16#16, 16#a) -> {more, undefined, 16#32};
dec_huffman_lookup(16#16, 16#b) -> {more, undefined, 16#35};
dec_huffman_lookup(16#16, 16#c) -> {more, undefined, 16#3a};
dec_huffman_lookup(16#16, 16#d) -> {more, undefined, 16#3d};
dec_huffman_lookup(16#16, 16#e) -> {more, undefined, 16#41};
dec_huffman_lookup(16#16, 16#f) -> {ok, undefined, 16#44};
dec_huffman_lookup(16#17, 16#0) -> {ok, 16#3d, 16#00};
dec_huffman_lookup(16#17, 16#1) -> {ok, 16#41, 16#00};
dec_huffman_lookup(16#17, 16#2) -> {ok, 16#5f, 16#00};
dec_huffman_lookup(16#17, 16#3) -> {ok, 16#62, 16#00};
dec_huffman_lookup(16#17, 16#4) -> {ok, 16#64, 16#00};
dec_huffman_lookup(16#17, 16#5) -> {ok, 16#66, 16#00};
dec_huffman_lookup(16#17, 16#6) -> {ok, 16#67, 16#00};
dec_huffman_lookup(16#17, 16#7) -> {ok, 16#68, 16#00};
dec_huffman_lookup(16#17, 16#8) -> {ok, 16#6c, 16#00};
dec_huffman_lookup(16#17, 16#9) -> {ok, 16#6d, 16#00};
dec_huffman_lookup(16#17, 16#a) -> {ok, 16#6e, 16#00};
dec_huffman_lookup(16#17, 16#b) -> {ok, 16#70, 16#00};
dec_huffman_lookup(16#17, 16#c) -> {ok, 16#72, 16#00};
dec_huffman_lookup(16#17, 16#d) -> {ok, 16#75, 16#00};
dec_huffman_lookup(16#17, 16#e) -> {more, undefined, 16#26};
dec_huffman_lookup(16#17, 16#f) -> {more, undefined, 16#27};
dec_huffman_lookup(16#18, 16#0) -> {more, 16#3d, 16#01};
dec_huffman_lookup(16#18, 16#1) -> {ok, 16#3d, 16#16};
dec_huffman_lookup(16#18, 16#2) -> {more, 16#41, 16#01};
dec_huffman_lookup(16#18, 16#3) -> {ok, 16#41, 16#16};
dec_huffman_lookup(16#18, 16#4) -> {more, 16#5f, 16#01};
dec_huffman_lookup(16#18, 16#5) -> {ok, 16#5f, 16#16};
dec_huffman_lookup(16#18, 16#6) -> {more, 16#62, 16#01};
dec_huffman_lookup(16#18, 16#7) -> {ok, 16#62, 16#16};
dec_huffman_lookup(16#18, 16#8) -> {more, 16#64, 16#01};
dec_huffman_lookup(16#18, 16#9) -> {ok, 16#64, 16#16};
dec_huffman_lookup(16#18, 16#a) -> {more, 16#66, 16#01};
dec_huffman_lookup(16#18, 16#b) -> {ok, 16#66, 16#16};
dec_huffman_lookup(16#18, 16#c) -> {more, 16#67, 16#01};
dec_huffman_lookup(16#18, 16#d) -> {ok, 16#67, 16#16};
dec_huffman_lookup(16#18, 16#e) -> {more, 16#68, 16#01};
dec_huffman_lookup(16#18, 16#f) -> {ok, 16#68, 16#16};
dec_huffman_lookup(16#19, 16#0) -> {more, 16#3d, 16#02};
dec_huffman_lookup(16#19, 16#1) -> {more, 16#3d, 16#09};
dec_huffman_lookup(16#19, 16#2) -> {more, 16#3d, 16#17};
dec_huffman_lookup(16#19, 16#3) -> {ok, 16#3d, 16#28};
dec_huffman_lookup(16#19, 16#4) -> {more, 16#41, 16#02};
dec_huffman_lookup(16#19, 16#5) -> {more, 16#41, 16#09};
dec_huffman_lookup(16#19, 16#6) -> {more, 16#41, 16#17};
dec_huffman_lookup(16#19, 16#7) -> {ok, 16#41, 16#28};
dec_huffman_lookup(16#19, 16#8) -> {more, 16#5f, 16#02};
dec_huffman_lookup(16#19, 16#9) -> {more, 16#5f, 16#09};
dec_huffman_lookup(16#19, 16#a) -> {more, 16#5f, 16#17};
dec_huffman_lookup(16#19, 16#b) -> {ok, 16#5f, 16#28};
dec_huffman_lookup(16#19, 16#c) -> {more, 16#62, 16#02};
dec_huffman_lookup(16#19, 16#d) -> {more, 16#62, 16#09};
dec_huffman_lookup(16#19, 16#e) -> {more, 16#62, 16#17};
dec_huffman_lookup(16#19, 16#f) -> {ok, 16#62, 16#28};
dec_huffman_lookup(16#1a, 16#0) -> {more, 16#3d, 16#03};
dec_huffman_lookup(16#1a, 16#1) -> {more, 16#3d, 16#06};
dec_huffman_lookup(16#1a, 16#2) -> {more, 16#3d, 16#0a};
dec_huffman_lookup(16#1a, 16#3) -> {more, 16#3d, 16#0f};
dec_huffman_lookup(16#1a, 16#4) -> {more, 16#3d, 16#18};
dec_huffman_lookup(16#1a, 16#5) -> {more, 16#3d, 16#1f};
dec_huffman_lookup(16#1a, 16#6) -> {more, 16#3d, 16#29};
dec_huffman_lookup(16#1a, 16#7) -> {ok, 16#3d, 16#38};
dec_huffman_lookup(16#1a, 16#8) -> {more, 16#41, 16#03};
dec_huffman_lookup(16#1a, 16#9) -> {more, 16#41, 16#06};
dec_huffman_lookup(16#1a, 16#a) -> {more, 16#41, 16#0a};
dec_huffman_lookup(16#1a, 16#b) -> {more, 16#41, 16#0f};
dec_huffman_lookup(16#1a, 16#c) -> {more, 16#41, 16#18};
dec_huffman_lookup(16#1a, 16#d) -> {more, 16#41, 16#1f};
dec_huffman_lookup(16#1a, 16#e) -> {more, 16#41, 16#29};
dec_huffman_lookup(16#1a, 16#f) -> {ok, 16#41, 16#38};
dec_huffman_lookup(16#1b, 16#0) -> {more, 16#5f, 16#03};
dec_huffman_lookup(16#1b, 16#1) -> {more, 16#5f, 16#06};
dec_huffman_lookup(16#1b, 16#2) -> {more, 16#5f, 16#0a};
dec_huffman_lookup(16#1b, 16#3) -> {more, 16#5f, 16#0f};
dec_huffman_lookup(16#1b, 16#4) -> {more, 16#5f, 16#18};
dec_huffman_lookup(16#1b, 16#5) -> {more, 16#5f, 16#1f};
dec_huffman_lookup(16#1b, 16#6) -> {more, 16#5f, 16#29};
dec_huffman_lookup(16#1b, 16#7) -> {ok, 16#5f, 16#38};
dec_huffman_lookup(16#1b, 16#8) -> {more, 16#62, 16#03};
dec_huffman_lookup(16#1b, 16#9) -> {more, 16#62, 16#06};
dec_huffman_lookup(16#1b, 16#a) -> {more, 16#62, 16#0a};
dec_huffman_lookup(16#1b, 16#b) -> {more, 16#62, 16#0f};
dec_huffman_lookup(16#1b, 16#c) -> {more, 16#62, 16#18};
dec_huffman_lookup(16#1b, 16#d) -> {more, 16#62, 16#1f};
dec_huffman_lookup(16#1b, 16#e) -> {more, 16#62, 16#29};
dec_huffman_lookup(16#1b, 16#f) -> {ok, 16#62, 16#38};
dec_huffman_lookup(16#1c, 16#0) -> {more, 16#64, 16#02};
dec_huffman_lookup(16#1c, 16#1) -> {more, 16#64, 16#09};
dec_huffman_lookup(16#1c, 16#2) -> {more, 16#64, 16#17};
dec_huffman_lookup(16#1c, 16#3) -> {ok, 16#64, 16#28};
dec_huffman_lookup(16#1c, 16#4) -> {more, 16#66, 16#02};
dec_huffman_lookup(16#1c, 16#5) -> {more, 16#66, 16#09};
dec_huffman_lookup(16#1c, 16#6) -> {more, 16#66, 16#17};
dec_huffman_lookup(16#1c, 16#7) -> {ok, 16#66, 16#28};
dec_huffman_lookup(16#1c, 16#8) -> {more, 16#67, 16#02};
dec_huffman_lookup(16#1c, 16#9) -> {more, 16#67, 16#09};
dec_huffman_lookup(16#1c, 16#a) -> {more, 16#67, 16#17};
dec_huffman_lookup(16#1c, 16#b) -> {ok, 16#67, 16#28};
dec_huffman_lookup(16#1c, 16#c) -> {more, 16#68, 16#02};
dec_huffman_lookup(16#1c, 16#d) -> {more, 16#68, 16#09};
dec_huffman_lookup(16#1c, 16#e) -> {more, 16#68, 16#17};
dec_huffman_lookup(16#1c, 16#f) -> {ok, 16#68, 16#28};
dec_huffman_lookup(16#1d, 16#0) -> {more, 16#64, 16#03};
dec_huffman_lookup(16#1d, 16#1) -> {more, 16#64, 16#06};
dec_huffman_lookup(16#1d, 16#2) -> {more, 16#64, 16#0a};
dec_huffman_lookup(16#1d, 16#3) -> {more, 16#64, 16#0f};
dec_huffman_lookup(16#1d, 16#4) -> {more, 16#64, 16#18};
dec_huffman_lookup(16#1d, 16#5) -> {more, 16#64, 16#1f};
dec_huffman_lookup(16#1d, 16#6) -> {more, 16#64, 16#29};
dec_huffman_lookup(16#1d, 16#7) -> {ok, 16#64, 16#38};
dec_huffman_lookup(16#1d, 16#8) -> {more, 16#66, 16#03};
dec_huffman_lookup(16#1d, 16#9) -> {more, 16#66, 16#06};
dec_huffman_lookup(16#1d, 16#a) -> {more, 16#66, 16#0a};
dec_huffman_lookup(16#1d, 16#b) -> {more, 16#66, 16#0f};
dec_huffman_lookup(16#1d, 16#c) -> {more, 16#66, 16#18};
dec_huffman_lookup(16#1d, 16#d) -> {more, 16#66, 16#1f};
dec_huffman_lookup(16#1d, 16#e) -> {more, 16#66, 16#29};
dec_huffman_lookup(16#1d, 16#f) -> {ok, 16#66, 16#38};
dec_huffman_lookup(16#1e, 16#0) -> {more, 16#67, 16#03};
dec_huffman_lookup(16#1e, 16#1) -> {more, 16#67, 16#06};
dec_huffman_lookup(16#1e, 16#2) -> {more, 16#67, 16#0a};
dec_huffman_lookup(16#1e, 16#3) -> {more, 16#67, 16#0f};
dec_huffman_lookup(16#1e, 16#4) -> {more, 16#67, 16#18};
dec_huffman_lookup(16#1e, 16#5) -> {more, 16#67, 16#1f};
dec_huffman_lookup(16#1e, 16#6) -> {more, 16#67, 16#29};
dec_huffman_lookup(16#1e, 16#7) -> {ok, 16#67, 16#38};
dec_huffman_lookup(16#1e, 16#8) -> {more, 16#68, 16#03};
dec_huffman_lookup(16#1e, 16#9) -> {more, 16#68, 16#06};
dec_huffman_lookup(16#1e, 16#a) -> {more, 16#68, 16#0a};
dec_huffman_lookup(16#1e, 16#b) -> {more, 16#68, 16#0f};
dec_huffman_lookup(16#1e, 16#c) -> {more, 16#68, 16#18};
dec_huffman_lookup(16#1e, 16#d) -> {more, 16#68, 16#1f};
dec_huffman_lookup(16#1e, 16#e) -> {more, 16#68, 16#29};
dec_huffman_lookup(16#1e, 16#f) -> {ok, 16#68, 16#38};
dec_huffman_lookup(16#1f, 16#0) -> {more, 16#6c, 16#01};
dec_huffman_lookup(16#1f, 16#1) -> {ok, 16#6c, 16#16};
dec_huffman_lookup(16#1f, 16#2) -> {more, 16#6d, 16#01};
dec_huffman_lookup(16#1f, 16#3) -> {ok, 16#6d, 16#16};
dec_huffman_lookup(16#1f, 16#4) -> {more, 16#6e, 16#01};
dec_huffman_lookup(16#1f, 16#5) -> {ok, 16#6e, 16#16};
dec_huffman_lookup(16#1f, 16#6) -> {more, 16#70, 16#01};
dec_huffman_lookup(16#1f, 16#7) -> {ok, 16#70, 16#16};
dec_huffman_lookup(16#1f, 16#8) -> {more, 16#72, 16#01};
dec_huffman_lookup(16#1f, 16#9) -> {ok, 16#72, 16#16};
dec_huffman_lookup(16#1f, 16#a) -> {more, 16#75, 16#01};
dec_huffman_lookup(16#1f, 16#b) -> {ok, 16#75, 16#16};
dec_huffman_lookup(16#1f, 16#c) -> {ok, 16#3a, 16#00};
dec_huffman_lookup(16#1f, 16#d) -> {ok, 16#42, 16#00};
dec_huffman_lookup(16#1f, 16#e) -> {ok, 16#43, 16#00};
dec_huffman_lookup(16#1f, 16#f) -> {ok, 16#44, 16#00};
dec_huffman_lookup(16#20, 16#0) -> {more, 16#6c, 16#02};
dec_huffman_lookup(16#20, 16#1) -> {more, 16#6c, 16#09};
dec_huffman_lookup(16#20, 16#2) -> {more, 16#6c, 16#17};
dec_huffman_lookup(16#20, 16#3) -> {ok, 16#6c, 16#28};
dec_huffman_lookup(16#20, 16#4) -> {more, 16#6d, 16#02};
dec_huffman_lookup(16#20, 16#5) -> {more, 16#6d, 16#09};
dec_huffman_lookup(16#20, 16#6) -> {more, 16#6d, 16#17};
dec_huffman_lookup(16#20, 16#7) -> {ok, 16#6d, 16#28};
dec_huffman_lookup(16#20, 16#8) -> {more, 16#6e, 16#02};
dec_huffman_lookup(16#20, 16#9) -> {more, 16#6e, 16#09};
dec_huffman_lookup(16#20, 16#a) -> {more, 16#6e, 16#17};
dec_huffman_lookup(16#20, 16#b) -> {ok, 16#6e, 16#28};
dec_huffman_lookup(16#20, 16#c) -> {more, 16#70, 16#02};
dec_huffman_lookup(16#20, 16#d) -> {more, 16#70, 16#09};
dec_huffman_lookup(16#20, 16#e) -> {more, 16#70, 16#17};
dec_huffman_lookup(16#20, 16#f) -> {ok, 16#70, 16#28};
dec_huffman_lookup(16#21, 16#0) -> {more, 16#6c, 16#03};
dec_huffman_lookup(16#21, 16#1) -> {more, 16#6c, 16#06};
dec_huffman_lookup(16#21, 16#2) -> {more, 16#6c, 16#0a};
dec_huffman_lookup(16#21, 16#3) -> {more, 16#6c, 16#0f};
dec_huffman_lookup(16#21, 16#4) -> {more, 16#6c, 16#18};
dec_huffman_lookup(16#21, 16#5) -> {more, 16#6c, 16#1f};
dec_huffman_lookup(16#21, 16#6) -> {more, 16#6c, 16#29};
dec_huffman_lookup(16#21, 16#7) -> {ok, 16#6c, 16#38};
dec_huffman_lookup(16#21, 16#8) -> {more, 16#6d, 16#03};
dec_huffman_lookup(16#21, 16#9) -> {more, 16#6d, 16#06};
dec_huffman_lookup(16#21, 16#a) -> {more, 16#6d, 16#0a};
dec_huffman_lookup(16#21, 16#b) -> {more, 16#6d, 16#0f};
dec_huffman_lookup(16#21, 16#c) -> {more, 16#6d, 16#18};
dec_huffman_lookup(16#21, 16#d) -> {more, 16#6d, 16#1f};
dec_huffman_lookup(16#21, 16#e) -> {more, 16#6d, 16#29};
dec_huffman_lookup(16#21, 16#f) -> {ok, 16#6d, 16#38};
dec_huffman_lookup(16#22, 16#0) -> {more, 16#6e, 16#03};
dec_huffman_lookup(16#22, 16#1) -> {more, 16#6e, 16#06};
dec_huffman_lookup(16#22, 16#2) -> {more, 16#6e, 16#0a};
dec_huffman_lookup(16#22, 16#3) -> {more, 16#6e, 16#0f};
dec_huffman_lookup(16#22, 16#4) -> {more, 16#6e, 16#18};
dec_huffman_lookup(16#22, 16#5) -> {more, 16#6e, 16#1f};
dec_huffman_lookup(16#22, 16#6) -> {more, 16#6e, 16#29};
dec_huffman_lookup(16#22, 16#7) -> {ok, 16#6e, 16#38};
dec_huffman_lookup(16#22, 16#8) -> {more, 16#70, 16#03};
dec_huffman_lookup(16#22, 16#9) -> {more, 16#70, 16#06};
dec_huffman_lookup(16#22, 16#a) -> {more, 16#70, 16#0a};
dec_huffman_lookup(16#22, 16#b) -> {more, 16#70, 16#0f};
dec_huffman_lookup(16#22, 16#c) -> {more, 16#70, 16#18};
dec_huffman_lookup(16#22, 16#d) -> {more, 16#70, 16#1f};
dec_huffman_lookup(16#22, 16#e) -> {more, 16#70, 16#29};
dec_huffman_lookup(16#22, 16#f) -> {ok, 16#70, 16#38};
dec_huffman_lookup(16#23, 16#0) -> {more, 16#72, 16#02};
dec_huffman_lookup(16#23, 16#1) -> {more, 16#72, 16#09};
dec_huffman_lookup(16#23, 16#2) -> {more, 16#72, 16#17};
dec_huffman_lookup(16#23, 16#3) -> {ok, 16#72, 16#28};
dec_huffman_lookup(16#23, 16#4) -> {more, 16#75, 16#02};
dec_huffman_lookup(16#23, 16#5) -> {more, 16#75, 16#09};
dec_huffman_lookup(16#23, 16#6) -> {more, 16#75, 16#17};
dec_huffman_lookup(16#23, 16#7) -> {ok, 16#75, 16#28};
dec_huffman_lookup(16#23, 16#8) -> {more, 16#3a, 16#01};
dec_huffman_lookup(16#23, 16#9) -> {ok, 16#3a, 16#16};
dec_huffman_lookup(16#23, 16#a) -> {more, 16#42, 16#01};
dec_huffman_lookup(16#23, 16#b) -> {ok, 16#42, 16#16};
dec_huffman_lookup(16#23, 16#c) -> {more, 16#43, 16#01};
dec_huffman_lookup(16#23, 16#d) -> {ok, 16#43, 16#16};
dec_huffman_lookup(16#23, 16#e) -> {more, 16#44, 16#01};
dec_huffman_lookup(16#23, 16#f) -> {ok, 16#44, 16#16};
dec_huffman_lookup(16#24, 16#0) -> {more, 16#72, 16#03};
dec_huffman_lookup(16#24, 16#1) -> {more, 16#72, 16#06};
dec_huffman_lookup(16#24, 16#2) -> {more, 16#72, 16#0a};
dec_huffman_lookup(16#24, 16#3) -> {more, 16#72, 16#0f};
dec_huffman_lookup(16#24, 16#4) -> {more, 16#72, 16#18};
dec_huffman_lookup(16#24, 16#5) -> {more, 16#72, 16#1f};
dec_huffman_lookup(16#24, 16#6) -> {more, 16#72, 16#29};
dec_huffman_lookup(16#24, 16#7) -> {ok, 16#72, 16#38};
dec_huffman_lookup(16#24, 16#8) -> {more, 16#75, 16#03};
dec_huffman_lookup(16#24, 16#9) -> {more, 16#75, 16#06};
dec_huffman_lookup(16#24, 16#a) -> {more, 16#75, 16#0a};
dec_huffman_lookup(16#24, 16#b) -> {more, 16#75, 16#0f};
dec_huffman_lookup(16#24, 16#c) -> {more, 16#75, 16#18};
dec_huffman_lookup(16#24, 16#d) -> {more, 16#75, 16#1f};
dec_huffman_lookup(16#24, 16#e) -> {more, 16#75, 16#29};
dec_huffman_lookup(16#24, 16#f) -> {ok, 16#75, 16#38};
dec_huffman_lookup(16#25, 16#0) -> {more, 16#3a, 16#02};
dec_huffman_lookup(16#25, 16#1) -> {more, 16#3a, 16#09};
dec_huffman_lookup(16#25, 16#2) -> {more, 16#3a, 16#17};
dec_huffman_lookup(16#25, 16#3) -> {ok, 16#3a, 16#28};
dec_huffman_lookup(16#25, 16#4) -> {more, 16#42, 16#02};
dec_huffman_lookup(16#25, 16#5) -> {more, 16#42, 16#09};
dec_huffman_lookup(16#25, 16#6) -> {more, 16#42, 16#17};
dec_huffman_lookup(16#25, 16#7) -> {ok, 16#42, 16#28};
dec_huffman_lookup(16#25, 16#8) -> {more, 16#43, 16#02};
dec_huffman_lookup(16#25, 16#9) -> {more, 16#43, 16#09};
dec_huffman_lookup(16#25, 16#a) -> {more, 16#43, 16#17};
dec_huffman_lookup(16#25, 16#b) -> {ok, 16#43, 16#28};
dec_huffman_lookup(16#25, 16#c) -> {more, 16#44, 16#02};
dec_huffman_lookup(16#25, 16#d) -> {more, 16#44, 16#09};
dec_huffman_lookup(16#25, 16#e) -> {more, 16#44, 16#17};
dec_huffman_lookup(16#25, 16#f) -> {ok, 16#44, 16#28};
dec_huffman_lookup(16#26, 16#0) -> {more, 16#3a, 16#03};
dec_huffman_lookup(16#26, 16#1) -> {more, 16#3a, 16#06};
dec_huffman_lookup(16#26, 16#2) -> {more, 16#3a, 16#0a};
dec_huffman_lookup(16#26, 16#3) -> {more, 16#3a, 16#0f};
dec_huffman_lookup(16#26, 16#4) -> {more, 16#3a, 16#18};
dec_huffman_lookup(16#26, 16#5) -> {more, 16#3a, 16#1f};
dec_huffman_lookup(16#26, 16#6) -> {more, 16#3a, 16#29};
dec_huffman_lookup(16#26, 16#7) -> {ok, 16#3a, 16#38};
dec_huffman_lookup(16#26, 16#8) -> {more, 16#42, 16#03};
dec_huffman_lookup(16#26, 16#9) -> {more, 16#42, 16#06};
dec_huffman_lookup(16#26, 16#a) -> {more, 16#42, 16#0a};
dec_huffman_lookup(16#26, 16#b) -> {more, 16#42, 16#0f};
dec_huffman_lookup(16#26, 16#c) -> {more, 16#42, 16#18};
dec_huffman_lookup(16#26, 16#d) -> {more, 16#42, 16#1f};
dec_huffman_lookup(16#26, 16#e) -> {more, 16#42, 16#29};
dec_huffman_lookup(16#26, 16#f) -> {ok, 16#42, 16#38};
dec_huffman_lookup(16#27, 16#0) -> {more, 16#43, 16#03};
dec_huffman_lookup(16#27, 16#1) -> {more, 16#43, 16#06};
dec_huffman_lookup(16#27, 16#2) -> {more, 16#43, 16#0a};
dec_huffman_lookup(16#27, 16#3) -> {more, 16#43, 16#0f};
dec_huffman_lookup(16#27, 16#4) -> {more, 16#43, 16#18};
dec_huffman_lookup(16#27, 16#5) -> {more, 16#43, 16#1f};
dec_huffman_lookup(16#27, 16#6) -> {more, 16#43, 16#29};
dec_huffman_lookup(16#27, 16#7) -> {ok, 16#43, 16#38};
dec_huffman_lookup(16#27, 16#8) -> {more, 16#44, 16#03};
dec_huffman_lookup(16#27, 16#9) -> {more, 16#44, 16#06};
dec_huffman_lookup(16#27, 16#a) -> {more, 16#44, 16#0a};
dec_huffman_lookup(16#27, 16#b) -> {more, 16#44, 16#0f};
dec_huffman_lookup(16#27, 16#c) -> {more, 16#44, 16#18};
dec_huffman_lookup(16#27, 16#d) -> {more, 16#44, 16#1f};
dec_huffman_lookup(16#27, 16#e) -> {more, 16#44, 16#29};
dec_huffman_lookup(16#27, 16#f) -> {ok, 16#44, 16#38};
dec_huffman_lookup(16#28, 16#0) -> {more, undefined, 16#2c};
dec_huffman_lookup(16#28, 16#1) -> {more, undefined, 16#2d};
dec_huffman_lookup(16#28, 16#2) -> {more, undefined, 16#2f};
dec_huffman_lookup(16#28, 16#3) -> {more, undefined, 16#30};
dec_huffman_lookup(16#28, 16#4) -> {more, undefined, 16#33};
dec_huffman_lookup(16#28, 16#5) -> {more, undefined, 16#34};
dec_huffman_lookup(16#28, 16#6) -> {more, undefined, 16#36};
dec_huffman_lookup(16#28, 16#7) -> {more, undefined, 16#37};
dec_huffman_lookup(16#28, 16#8) -> {more, undefined, 16#3b};
dec_huffman_lookup(16#28, 16#9) -> {more, undefined, 16#3c};
dec_huffman_lookup(16#28, 16#a) -> {more, undefined, 16#3e};
dec_huffman_lookup(16#28, 16#b) -> {more, undefined, 16#3f};
dec_huffman_lookup(16#28, 16#c) -> {more, undefined, 16#42};
dec_huffman_lookup(16#28, 16#d) -> {more, undefined, 16#43};
dec_huffman_lookup(16#28, 16#e) -> {more, undefined, 16#45};
dec_huffman_lookup(16#28, 16#f) -> {ok, undefined, 16#48};
dec_huffman_lookup(16#29, 16#0) -> {ok, 16#45, 16#00};
dec_huffman_lookup(16#29, 16#1) -> {ok, 16#46, 16#00};
dec_huffman_lookup(16#29, 16#2) -> {ok, 16#47, 16#00};
dec_huffman_lookup(16#29, 16#3) -> {ok, 16#48, 16#00};
dec_huffman_lookup(16#29, 16#4) -> {ok, 16#49, 16#00};
dec_huffman_lookup(16#29, 16#5) -> {ok, 16#4a, 16#00};
dec_huffman_lookup(16#29, 16#6) -> {ok, 16#4b, 16#00};
dec_huffman_lookup(16#29, 16#7) -> {ok, 16#4c, 16#00};
dec_huffman_lookup(16#29, 16#8) -> {ok, 16#4d, 16#00};
dec_huffman_lookup(16#29, 16#9) -> {ok, 16#4e, 16#00};
dec_huffman_lookup(16#29, 16#a) -> {ok, 16#4f, 16#00};
dec_huffman_lookup(16#29, 16#b) -> {ok, 16#50, 16#00};
dec_huffman_lookup(16#29, 16#c) -> {ok, 16#51, 16#00};
dec_huffman_lookup(16#29, 16#d) -> {ok, 16#52, 16#00};
dec_huffman_lookup(16#29, 16#e) -> {ok, 16#53, 16#00};
dec_huffman_lookup(16#29, 16#f) -> {ok, 16#54, 16#00};
dec_huffman_lookup(16#2a, 16#0) -> {more, 16#45, 16#01};
dec_huffman_lookup(16#2a, 16#1) -> {ok, 16#45, 16#16};
dec_huffman_lookup(16#2a, 16#2) -> {more, 16#46, 16#01};
dec_huffman_lookup(16#2a, 16#3) -> {ok, 16#46, 16#16};
dec_huffman_lookup(16#2a, 16#4) -> {more, 16#47, 16#01};
dec_huffman_lookup(16#2a, 16#5) -> {ok, 16#47, 16#16};
dec_huffman_lookup(16#2a, 16#6) -> {more, 16#48, 16#01};
dec_huffman_lookup(16#2a, 16#7) -> {ok, 16#48, 16#16};
dec_huffman_lookup(16#2a, 16#8) -> {more, 16#49, 16#01};
dec_huffman_lookup(16#2a, 16#9) -> {ok, 16#49, 16#16};
dec_huffman_lookup(16#2a, 16#a) -> {more, 16#4a, 16#01};
dec_huffman_lookup(16#2a, 16#b) -> {ok, 16#4a, 16#16};
dec_huffman_lookup(16#2a, 16#c) -> {more, 16#4b, 16#01};
dec_huffman_lookup(16#2a, 16#d) -> {ok, 16#4b, 16#16};
dec_huffman_lookup(16#2a, 16#e) -> {more, 16#4c, 16#01};
dec_huffman_lookup(16#2a, 16#f) -> {ok, 16#4c, 16#16};
dec_huffman_lookup(16#2b, 16#0) -> {more, 16#45, 16#02};
dec_huffman_lookup(16#2b, 16#1) -> {more, 16#45, 16#09};
dec_huffman_lookup(16#2b, 16#2) -> {more, 16#45, 16#17};
dec_huffman_lookup(16#2b, 16#3) -> {ok, 16#45, 16#28};
dec_huffman_lookup(16#2b, 16#4) -> {more, 16#46, 16#02};
dec_huffman_lookup(16#2b, 16#5) -> {more, 16#46, 16#09};
dec_huffman_lookup(16#2b, 16#6) -> {more, 16#46, 16#17};
dec_huffman_lookup(16#2b, 16#7) -> {ok, 16#46, 16#28};
dec_huffman_lookup(16#2b, 16#8) -> {more, 16#47, 16#02};
dec_huffman_lookup(16#2b, 16#9) -> {more, 16#47, 16#09};
dec_huffman_lookup(16#2b, 16#a) -> {more, 16#47, 16#17};
dec_huffman_lookup(16#2b, 16#b) -> {ok, 16#47, 16#28};
dec_huffman_lookup(16#2b, 16#c) -> {more, 16#48, 16#02};
dec_huffman_lookup(16#2b, 16#d) -> {more, 16#48, 16#09};
dec_huffman_lookup(16#2b, 16#e) -> {more, 16#48, 16#17};
dec_huffman_lookup(16#2b, 16#f) -> {ok, 16#48, 16#28};
dec_huffman_lookup(16#2c, 16#0) -> {more, 16#45, 16#03};
dec_huffman_lookup(16#2c, 16#1) -> {more, 16#45, 16#06};
dec_huffman_lookup(16#2c, 16#2) -> {more, 16#45, 16#0a};
dec_huffman_lookup(16#2c, 16#3) -> {more, 16#45, 16#0f};
dec_huffman_lookup(16#2c, 16#4) -> {more, 16#45, 16#18};
dec_huffman_lookup(16#2c, 16#5) -> {more, 16#45, 16#1f};
dec_huffman_lookup(16#2c, 16#6) -> {more, 16#45, 16#29};
dec_huffman_lookup(16#2c, 16#7) -> {ok, 16#45, 16#38};
dec_huffman_lookup(16#2c, 16#8) -> {more, 16#46, 16#03};
dec_huffman_lookup(16#2c, 16#9) -> {more, 16#46, 16#06};
dec_huffman_lookup(16#2c, 16#a) -> {more, 16#46, 16#0a};
dec_huffman_lookup(16#2c, 16#b) -> {more, 16#46, 16#0f};
dec_huffman_lookup(16#2c, 16#c) -> {more, 16#46, 16#18};
dec_huffman_lookup(16#2c, 16#d) -> {more, 16#46, 16#1f};
dec_huffman_lookup(16#2c, 16#e) -> {more, 16#46, 16#29};
dec_huffman_lookup(16#2c, 16#f) -> {ok, 16#46, 16#38};
dec_huffman_lookup(16#2d, 16#0) -> {more, 16#47, 16#03};
dec_huffman_lookup(16#2d, 16#1) -> {more, 16#47, 16#06};
dec_huffman_lookup(16#2d, 16#2) -> {more, 16#47, 16#0a};
dec_huffman_lookup(16#2d, 16#3) -> {more, 16#47, 16#0f};
dec_huffman_lookup(16#2d, 16#4) -> {more, 16#47, 16#18};
dec_huffman_lookup(16#2d, 16#5) -> {more, 16#47, 16#1f};
dec_huffman_lookup(16#2d, 16#6) -> {more, 16#47, 16#29};
dec_huffman_lookup(16#2d, 16#7) -> {ok, 16#47, 16#38};
dec_huffman_lookup(16#2d, 16#8) -> {more, 16#48, 16#03};
dec_huffman_lookup(16#2d, 16#9) -> {more, 16#48, 16#06};
dec_huffman_lookup(16#2d, 16#a) -> {more, 16#48, 16#0a};
dec_huffman_lookup(16#2d, 16#b) -> {more, 16#48, 16#0f};
dec_huffman_lookup(16#2d, 16#c) -> {more, 16#48, 16#18};
dec_huffman_lookup(16#2d, 16#d) -> {more, 16#48, 16#1f};
dec_huffman_lookup(16#2d, 16#e) -> {more, 16#48, 16#29};
dec_huffman_lookup(16#2d, 16#f) -> {ok, 16#48, 16#38};
dec_huffman_lookup(16#2e, 16#0) -> {more, 16#49, 16#02};
dec_huffman_lookup(16#2e, 16#1) -> {more, 16#49, 16#09};
dec_huffman_lookup(16#2e, 16#2) -> {more, 16#49, 16#17};
dec_huffman_lookup(16#2e, 16#3) -> {ok, 16#49, 16#28};
dec_huffman_lookup(16#2e, 16#4) -> {more, 16#4a, 16#02};
dec_huffman_lookup(16#2e, 16#5) -> {more, 16#4a, 16#09};
dec_huffman_lookup(16#2e, 16#6) -> {more, 16#4a, 16#17};
dec_huffman_lookup(16#2e, 16#7) -> {ok, 16#4a, 16#28};
dec_huffman_lookup(16#2e, 16#8) -> {more, 16#4b, 16#02};
dec_huffman_lookup(16#2e, 16#9) -> {more, 16#4b, 16#09};
dec_huffman_lookup(16#2e, 16#a) -> {more, 16#4b, 16#17};
dec_huffman_lookup(16#2e, 16#b) -> {ok, 16#4b, 16#28};
dec_huffman_lookup(16#2e, 16#c) -> {more, 16#4c, 16#02};
dec_huffman_lookup(16#2e, 16#d) -> {more, 16#4c, 16#09};
dec_huffman_lookup(16#2e, 16#e) -> {more, 16#4c, 16#17};
dec_huffman_lookup(16#2e, 16#f) -> {ok, 16#4c, 16#28};
dec_huffman_lookup(16#2f, 16#0) -> {more, 16#49, 16#03};
dec_huffman_lookup(16#2f, 16#1) -> {more, 16#49, 16#06};
dec_huffman_lookup(16#2f, 16#2) -> {more, 16#49, 16#0a};
dec_huffman_lookup(16#2f, 16#3) -> {more, 16#49, 16#0f};
dec_huffman_lookup(16#2f, 16#4) -> {more, 16#49, 16#18};
dec_huffman_lookup(16#2f, 16#5) -> {more, 16#49, 16#1f};
dec_huffman_lookup(16#2f, 16#6) -> {more, 16#49, 16#29};
dec_huffman_lookup(16#2f, 16#7) -> {ok, 16#49, 16#38};
dec_huffman_lookup(16#2f, 16#8) -> {more, 16#4a, 16#03};
dec_huffman_lookup(16#2f, 16#9) -> {more, 16#4a, 16#06};
dec_huffman_lookup(16#2f, 16#a) -> {more, 16#4a, 16#0a};
dec_huffman_lookup(16#2f, 16#b) -> {more, 16#4a, 16#0f};
dec_huffman_lookup(16#2f, 16#c) -> {more, 16#4a, 16#18};
dec_huffman_lookup(16#2f, 16#d) -> {more, 16#4a, 16#1f};
dec_huffman_lookup(16#2f, 16#e) -> {more, 16#4a, 16#29};
dec_huffman_lookup(16#2f, 16#f) -> {ok, 16#4a, 16#38};
dec_huffman_lookup(16#30, 16#0) -> {more, 16#4b, 16#03};
dec_huffman_lookup(16#30, 16#1) -> {more, 16#4b, 16#06};
dec_huffman_lookup(16#30, 16#2) -> {more, 16#4b, 16#0a};
dec_huffman_lookup(16#30, 16#3) -> {more, 16#4b, 16#0f};
dec_huffman_lookup(16#30, 16#4) -> {more, 16#4b, 16#18};
dec_huffman_lookup(16#30, 16#5) -> {more, 16#4b, 16#1f};
dec_huffman_lookup(16#30, 16#6) -> {more, 16#4b, 16#29};
dec_huffman_lookup(16#30, 16#7) -> {ok, 16#4b, 16#38};
dec_huffman_lookup(16#30, 16#8) -> {more, 16#4c, 16#03};
dec_huffman_lookup(16#30, 16#9) -> {more, 16#4c, 16#06};
dec_huffman_lookup(16#30, 16#a) -> {more, 16#4c, 16#0a};
dec_huffman_lookup(16#30, 16#b) -> {more, 16#4c, 16#0f};
dec_huffman_lookup(16#30, 16#c) -> {more, 16#4c, 16#18};
dec_huffman_lookup(16#30, 16#d) -> {more, 16#4c, 16#1f};
dec_huffman_lookup(16#30, 16#e) -> {more, 16#4c, 16#29};
dec_huffman_lookup(16#30, 16#f) -> {ok, 16#4c, 16#38};
dec_huffman_lookup(16#31, 16#0) -> {more, 16#4d, 16#01};
dec_huffman_lookup(16#31, 16#1) -> {ok, 16#4d, 16#16};
dec_huffman_lookup(16#31, 16#2) -> {more, 16#4e, 16#01};
dec_huffman_lookup(16#31, 16#3) -> {ok, 16#4e, 16#16};
dec_huffman_lookup(16#31, 16#4) -> {more, 16#4f, 16#01};
dec_huffman_lookup(16#31, 16#5) -> {ok, 16#4f, 16#16};
dec_huffman_lookup(16#31, 16#6) -> {more, 16#50, 16#01};
dec_huffman_lookup(16#31, 16#7) -> {ok, 16#50, 16#16};
dec_huffman_lookup(16#31, 16#8) -> {more, 16#51, 16#01};
dec_huffman_lookup(16#31, 16#9) -> {ok, 16#51, 16#16};
dec_huffman_lookup(16#31, 16#a) -> {more, 16#52, 16#01};
dec_huffman_lookup(16#31, 16#b) -> {ok, 16#52, 16#16};
dec_huffman_lookup(16#31, 16#c) -> {more, 16#53, 16#01};
dec_huffman_lookup(16#31, 16#d) -> {ok, 16#53, 16#16};
dec_huffman_lookup(16#31, 16#e) -> {more, 16#54, 16#01};
dec_huffman_lookup(16#31, 16#f) -> {ok, 16#54, 16#16};
dec_huffman_lookup(16#32, 16#0) -> {more, 16#4d, 16#02};
dec_huffman_lookup(16#32, 16#1) -> {more, 16#4d, 16#09};
dec_huffman_lookup(16#32, 16#2) -> {more, 16#4d, 16#17};
dec_huffman_lookup(16#32, 16#3) -> {ok, 16#4d, 16#28};
dec_huffman_lookup(16#32, 16#4) -> {more, 16#4e, 16#02};
dec_huffman_lookup(16#32, 16#5) -> {more, 16#4e, 16#09};
dec_huffman_lookup(16#32, 16#6) -> {more, 16#4e, 16#17};
dec_huffman_lookup(16#32, 16#7) -> {ok, 16#4e, 16#28};
dec_huffman_lookup(16#32, 16#8) -> {more, 16#4f, 16#02};
dec_huffman_lookup(16#32, 16#9) -> {more, 16#4f, 16#09};
dec_huffman_lookup(16#32, 16#a) -> {more, 16#4f, 16#17};
dec_huffman_lookup(16#32, 16#b) -> {ok, 16#4f, 16#28};
dec_huffman_lookup(16#32, 16#c) -> {more, 16#50, 16#02};
dec_huffman_lookup(16#32, 16#d) -> {more, 16#50, 16#09};
dec_huffman_lookup(16#32, 16#e) -> {more, 16#50, 16#17};
dec_huffman_lookup(16#32, 16#f) -> {ok, 16#50, 16#28};
dec_huffman_lookup(16#33, 16#0) -> {more, 16#4d, 16#03};
dec_huffman_lookup(16#33, 16#1) -> {more, 16#4d, 16#06};
dec_huffman_lookup(16#33, 16#2) -> {more, 16#4d, 16#0a};
dec_huffman_lookup(16#33, 16#3) -> {more, 16#4d, 16#0f};
dec_huffman_lookup(16#33, 16#4) -> {more, 16#4d, 16#18};
dec_huffman_lookup(16#33, 16#5) -> {more, 16#4d, 16#1f};
dec_huffman_lookup(16#33, 16#6) -> {more, 16#4d, 16#29};
dec_huffman_lookup(16#33, 16#7) -> {ok, 16#4d, 16#38};
dec_huffman_lookup(16#33, 16#8) -> {more, 16#4e, 16#03};
dec_huffman_lookup(16#33, 16#9) -> {more, 16#4e, 16#06};
dec_huffman_lookup(16#33, 16#a) -> {more, 16#4e, 16#0a};
dec_huffman_lookup(16#33, 16#b) -> {more, 16#4e, 16#0f};
dec_huffman_lookup(16#33, 16#c) -> {more, 16#4e, 16#18};
dec_huffman_lookup(16#33, 16#d) -> {more, 16#4e, 16#1f};
dec_huffman_lookup(16#33, 16#e) -> {more, 16#4e, 16#29};
dec_huffman_lookup(16#33, 16#f) -> {ok, 16#4e, 16#38};
dec_huffman_lookup(16#34, 16#0) -> {more, 16#4f, 16#03};
dec_huffman_lookup(16#34, 16#1) -> {more, 16#4f, 16#06};
dec_huffman_lookup(16#34, 16#2) -> {more, 16#4f, 16#0a};
dec_huffman_lookup(16#34, 16#3) -> {more, 16#4f, 16#0f};
dec_huffman_lookup(16#34, 16#4) -> {more, 16#4f, 16#18};
dec_huffman_lookup(16#34, 16#5) -> {more, 16#4f, 16#1f};
dec_huffman_lookup(16#34, 16#6) -> {more, 16#4f, 16#29};
dec_huffman_lookup(16#34, 16#7) -> {ok, 16#4f, 16#38};
dec_huffman_lookup(16#34, 16#8) -> {more, 16#50, 16#03};
dec_huffman_lookup(16#34, 16#9) -> {more, 16#50, 16#06};
dec_huffman_lookup(16#34, 16#a) -> {more, 16#50, 16#0a};
dec_huffman_lookup(16#34, 16#b) -> {more, 16#50, 16#0f};
dec_huffman_lookup(16#34, 16#c) -> {more, 16#50, 16#18};
dec_huffman_lookup(16#34, 16#d) -> {more, 16#50, 16#1f};
dec_huffman_lookup(16#34, 16#e) -> {more, 16#50, 16#29};
dec_huffman_lookup(16#34, 16#f) -> {ok, 16#50, 16#38};
dec_huffman_lookup(16#35, 16#0) -> {more, 16#51, 16#02};
dec_huffman_lookup(16#35, 16#1) -> {more, 16#51, 16#09};
dec_huffman_lookup(16#35, 16#2) -> {more, 16#51, 16#17};
dec_huffman_lookup(16#35, 16#3) -> {ok, 16#51, 16#28};
dec_huffman_lookup(16#35, 16#4) -> {more, 16#52, 16#02};
dec_huffman_lookup(16#35, 16#5) -> {more, 16#52, 16#09};
dec_huffman_lookup(16#35, 16#6) -> {more, 16#52, 16#17};
dec_huffman_lookup(16#35, 16#7) -> {ok, 16#52, 16#28};
dec_huffman_lookup(16#35, 16#8) -> {more, 16#53, 16#02};
dec_huffman_lookup(16#35, 16#9) -> {more, 16#53, 16#09};
dec_huffman_lookup(16#35, 16#a) -> {more, 16#53, 16#17};
dec_huffman_lookup(16#35, 16#b) -> {ok, 16#53, 16#28};
dec_huffman_lookup(16#35, 16#c) -> {more, 16#54, 16#02};
dec_huffman_lookup(16#35, 16#d) -> {more, 16#54, 16#09};
dec_huffman_lookup(16#35, 16#e) -> {more, 16#54, 16#17};
dec_huffman_lookup(16#35, 16#f) -> {ok, 16#54, 16#28};
dec_huffman_lookup(16#36, 16#0) -> {more, 16#51, 16#03};
dec_huffman_lookup(16#36, 16#1) -> {more, 16#51, 16#06};
dec_huffman_lookup(16#36, 16#2) -> {more, 16#51, 16#0a};
dec_huffman_lookup(16#36, 16#3) -> {more, 16#51, 16#0f};
dec_huffman_lookup(16#36, 16#4) -> {more, 16#51, 16#18};
dec_huffman_lookup(16#36, 16#5) -> {more, 16#51, 16#1f};
dec_huffman_lookup(16#36, 16#6) -> {more, 16#51, 16#29};
dec_huffman_lookup(16#36, 16#7) -> {ok, 16#51, 16#38};
dec_huffman_lookup(16#36, 16#8) -> {more, 16#52, 16#03};
dec_huffman_lookup(16#36, 16#9) -> {more, 16#52, 16#06};
dec_huffman_lookup(16#36, 16#a) -> {more, 16#52, 16#0a};
dec_huffman_lookup(16#36, 16#b) -> {more, 16#52, 16#0f};
dec_huffman_lookup(16#36, 16#c) -> {more, 16#52, 16#18};
dec_huffman_lookup(16#36, 16#d) -> {more, 16#52, 16#1f};
dec_huffman_lookup(16#36, 16#e) -> {more, 16#52, 16#29};
dec_huffman_lookup(16#36, 16#f) -> {ok, 16#52, 16#38};
dec_huffman_lookup(16#37, 16#0) -> {more, 16#53, 16#03};
dec_huffman_lookup(16#37, 16#1) -> {more, 16#53, 16#06};
dec_huffman_lookup(16#37, 16#2) -> {more, 16#53, 16#0a};
dec_huffman_lookup(16#37, 16#3) -> {more, 16#53, 16#0f};
dec_huffman_lookup(16#37, 16#4) -> {more, 16#53, 16#18};
dec_huffman_lookup(16#37, 16#5) -> {more, 16#53, 16#1f};
dec_huffman_lookup(16#37, 16#6) -> {more, 16#53, 16#29};
dec_huffman_lookup(16#37, 16#7) -> {ok, 16#53, 16#38};
dec_huffman_lookup(16#37, 16#8) -> {more, 16#54, 16#03};
dec_huffman_lookup(16#37, 16#9) -> {more, 16#54, 16#06};
dec_huffman_lookup(16#37, 16#a) -> {more, 16#54, 16#0a};
dec_huffman_lookup(16#37, 16#b) -> {more, 16#54, 16#0f};
dec_huffman_lookup(16#37, 16#c) -> {more, 16#54, 16#18};
dec_huffman_lookup(16#37, 16#d) -> {more, 16#54, 16#1f};
dec_huffman_lookup(16#37, 16#e) -> {more, 16#54, 16#29};
dec_huffman_lookup(16#37, 16#f) -> {ok, 16#54, 16#38};
dec_huffman_lookup(16#38, 16#0) -> {ok, 16#55, 16#00};
dec_huffman_lookup(16#38, 16#1) -> {ok, 16#56, 16#00};
dec_huffman_lookup(16#38, 16#2) -> {ok, 16#57, 16#00};
dec_huffman_lookup(16#38, 16#3) -> {ok, 16#59, 16#00};
dec_huffman_lookup(16#38, 16#4) -> {ok, 16#6a, 16#00};
dec_huffman_lookup(16#38, 16#5) -> {ok, 16#6b, 16#00};
dec_huffman_lookup(16#38, 16#6) -> {ok, 16#71, 16#00};
dec_huffman_lookup(16#38, 16#7) -> {ok, 16#76, 16#00};
dec_huffman_lookup(16#38, 16#8) -> {ok, 16#77, 16#00};
dec_huffman_lookup(16#38, 16#9) -> {ok, 16#78, 16#00};
dec_huffman_lookup(16#38, 16#a) -> {ok, 16#79, 16#00};
dec_huffman_lookup(16#38, 16#b) -> {ok, 16#7a, 16#00};
dec_huffman_lookup(16#38, 16#c) -> {more, undefined, 16#46};
dec_huffman_lookup(16#38, 16#d) -> {more, undefined, 16#47};
dec_huffman_lookup(16#38, 16#e) -> {more, undefined, 16#49};
dec_huffman_lookup(16#38, 16#f) -> {ok, undefined, 16#4a};
dec_huffman_lookup(16#39, 16#0) -> {more, 16#55, 16#01};
dec_huffman_lookup(16#39, 16#1) -> {ok, 16#55, 16#16};
dec_huffman_lookup(16#39, 16#2) -> {more, 16#56, 16#01};
dec_huffman_lookup(16#39, 16#3) -> {ok, 16#56, 16#16};
dec_huffman_lookup(16#39, 16#4) -> {more, 16#57, 16#01};
dec_huffman_lookup(16#39, 16#5) -> {ok, 16#57, 16#16};
dec_huffman_lookup(16#39, 16#6) -> {more, 16#59, 16#01};
dec_huffman_lookup(16#39, 16#7) -> {ok, 16#59, 16#16};
dec_huffman_lookup(16#39, 16#8) -> {more, 16#6a, 16#01};
dec_huffman_lookup(16#39, 16#9) -> {ok, 16#6a, 16#16};
dec_huffman_lookup(16#39, 16#a) -> {more, 16#6b, 16#01};
dec_huffman_lookup(16#39, 16#b) -> {ok, 16#6b, 16#16};
dec_huffman_lookup(16#39, 16#c) -> {more, 16#71, 16#01};
dec_huffman_lookup(16#39, 16#d) -> {ok, 16#71, 16#16};
dec_huffman_lookup(16#39, 16#e) -> {more, 16#76, 16#01};
dec_huffman_lookup(16#39, 16#f) -> {ok, 16#76, 16#16};
dec_huffman_lookup(16#3a, 16#0) -> {more, 16#55, 16#02};
dec_huffman_lookup(16#3a, 16#1) -> {more, 16#55, 16#09};
dec_huffman_lookup(16#3a, 16#2) -> {more, 16#55, 16#17};
dec_huffman_lookup(16#3a, 16#3) -> {ok, 16#55, 16#28};
dec_huffman_lookup(16#3a, 16#4) -> {more, 16#56, 16#02};
dec_huffman_lookup(16#3a, 16#5) -> {more, 16#56, 16#09};
dec_huffman_lookup(16#3a, 16#6) -> {more, 16#56, 16#17};
dec_huffman_lookup(16#3a, 16#7) -> {ok, 16#56, 16#28};
dec_huffman_lookup(16#3a, 16#8) -> {more, 16#57, 16#02};
dec_huffman_lookup(16#3a, 16#9) -> {more, 16#57, 16#09};
dec_huffman_lookup(16#3a, 16#a) -> {more, 16#57, 16#17};
dec_huffman_lookup(16#3a, 16#b) -> {ok, 16#57, 16#28};
dec_huffman_lookup(16#3a, 16#c) -> {more, 16#59, 16#02};
dec_huffman_lookup(16#3a, 16#d) -> {more, 16#59, 16#09};
dec_huffman_lookup(16#3a, 16#e) -> {more, 16#59, 16#17};
dec_huffman_lookup(16#3a, 16#f) -> {ok, 16#59, 16#28};
dec_huffman_lookup(16#3b, 16#0) -> {more, 16#55, 16#03};
dec_huffman_lookup(16#3b, 16#1) -> {more, 16#55, 16#06};
dec_huffman_lookup(16#3b, 16#2) -> {more, 16#55, 16#0a};
dec_huffman_lookup(16#3b, 16#3) -> {more, 16#55, 16#0f};
dec_huffman_lookup(16#3b, 16#4) -> {more, 16#55, 16#18};
dec_huffman_lookup(16#3b, 16#5) -> {more, 16#55, 16#1f};
dec_huffman_lookup(16#3b, 16#6) -> {more, 16#55, 16#29};
dec_huffman_lookup(16#3b, 16#7) -> {ok, 16#55, 16#38};
dec_huffman_lookup(16#3b, 16#8) -> {more, 16#56, 16#03};
dec_huffman_lookup(16#3b, 16#9) -> {more, 16#56, 16#06};
dec_huffman_lookup(16#3b, 16#a) -> {more, 16#56, 16#0a};
dec_huffman_lookup(16#3b, 16#b) -> {more, 16#56, 16#0f};
dec_huffman_lookup(16#3b, 16#c) -> {more, 16#56, 16#18};
dec_huffman_lookup(16#3b, 16#d) -> {more, 16#56, 16#1f};
dec_huffman_lookup(16#3b, 16#e) -> {more, 16#56, 16#29};
dec_huffman_lookup(16#3b, 16#f) -> {ok, 16#56, 16#38};
dec_huffman_lookup(16#3c, 16#0) -> {more, 16#57, 16#03};
dec_huffman_lookup(16#3c, 16#1) -> {more, 16#57, 16#06};
dec_huffman_lookup(16#3c, 16#2) -> {more, 16#57, 16#0a};
dec_huffman_lookup(16#3c, 16#3) -> {more, 16#57, 16#0f};
dec_huffman_lookup(16#3c, 16#4) -> {more, 16#57, 16#18};
dec_huffman_lookup(16#3c, 16#5) -> {more, 16#57, 16#1f};
dec_huffman_lookup(16#3c, 16#6) -> {more, 16#57, 16#29};
dec_huffman_lookup(16#3c, 16#7) -> {ok, 16#57, 16#38};
dec_huffman_lookup(16#3c, 16#8) -> {more, 16#59, 16#03};
dec_huffman_lookup(16#3c, 16#9) -> {more, 16#59, 16#06};
dec_huffman_lookup(16#3c, 16#a) -> {more, 16#59, 16#0a};
dec_huffman_lookup(16#3c, 16#b) -> {more, 16#59, 16#0f};
dec_huffman_lookup(16#3c, 16#c) -> {more, 16#59, 16#18};
dec_huffman_lookup(16#3c, 16#d) -> {more, 16#59, 16#1f};
dec_huffman_lookup(16#3c, 16#e) -> {more, 16#59, 16#29};
dec_huffman_lookup(16#3c, 16#f) -> {ok, 16#59, 16#38};
dec_huffman_lookup(16#3d, 16#0) -> {more, 16#6a, 16#02};
dec_huffman_lookup(16#3d, 16#1) -> {more, 16#6a, 16#09};
dec_huffman_lookup(16#3d, 16#2) -> {more, 16#6a, 16#17};
dec_huffman_lookup(16#3d, 16#3) -> {ok, 16#6a, 16#28};
dec_huffman_lookup(16#3d, 16#4) -> {more, 16#6b, 16#02};
dec_huffman_lookup(16#3d, 16#5) -> {more, 16#6b, 16#09};
dec_huffman_lookup(16#3d, 16#6) -> {more, 16#6b, 16#17};
dec_huffman_lookup(16#3d, 16#7) -> {ok, 16#6b, 16#28};
dec_huffman_lookup(16#3d, 16#8) -> {more, 16#71, 16#02};
dec_huffman_lookup(16#3d, 16#9) -> {more, 16#71, 16#09};
dec_huffman_lookup(16#3d, 16#a) -> {more, 16#71, 16#17};
dec_huffman_lookup(16#3d, 16#b) -> {ok, 16#71, 16#28};
dec_huffman_lookup(16#3d, 16#c) -> {more, 16#76, 16#02};
dec_huffman_lookup(16#3d, 16#d) -> {more, 16#76, 16#09};
dec_huffman_lookup(16#3d, 16#e) -> {more, 16#76, 16#17};
dec_huffman_lookup(16#3d, 16#f) -> {ok, 16#76, 16#28};
dec_huffman_lookup(16#3e, 16#0) -> {more, 16#6a, 16#03};
dec_huffman_lookup(16#3e, 16#1) -> {more, 16#6a, 16#06};
dec_huffman_lookup(16#3e, 16#2) -> {more, 16#6a, 16#0a};
dec_huffman_lookup(16#3e, 16#3) -> {more, 16#6a, 16#0f};
dec_huffman_lookup(16#3e, 16#4) -> {more, 16#6a, 16#18};
dec_huffman_lookup(16#3e, 16#5) -> {more, 16#6a, 16#1f};
dec_huffman_lookup(16#3e, 16#6) -> {more, 16#6a, 16#29};
dec_huffman_lookup(16#3e, 16#7) -> {ok, 16#6a, 16#38};
dec_huffman_lookup(16#3e, 16#8) -> {more, 16#6b, 16#03};
dec_huffman_lookup(16#3e, 16#9) -> {more, 16#6b, 16#06};
dec_huffman_lookup(16#3e, 16#a) -> {more, 16#6b, 16#0a};
dec_huffman_lookup(16#3e, 16#b) -> {more, 16#6b, 16#0f};
dec_huffman_lookup(16#3e, 16#c) -> {more, 16#6b, 16#18};
dec_huffman_lookup(16#3e, 16#d) -> {more, 16#6b, 16#1f};
dec_huffman_lookup(16#3e, 16#e) -> {more, 16#6b, 16#29};
dec_huffman_lookup(16#3e, 16#f) -> {ok, 16#6b, 16#38};
dec_huffman_lookup(16#3f, 16#0) -> {more, 16#71, 16#03};
dec_huffman_lookup(16#3f, 16#1) -> {more, 16#71, 16#06};
dec_huffman_lookup(16#3f, 16#2) -> {more, 16#71, 16#0a};
dec_huffman_lookup(16#3f, 16#3) -> {more, 16#71, 16#0f};
dec_huffman_lookup(16#3f, 16#4) -> {more, 16#71, 16#18};
dec_huffman_lookup(16#3f, 16#5) -> {more, 16#71, 16#1f};
dec_huffman_lookup(16#3f, 16#6) -> {more, 16#71, 16#29};
dec_huffman_lookup(16#3f, 16#7) -> {ok, 16#71, 16#38};
dec_huffman_lookup(16#3f, 16#8) -> {more, 16#76, 16#03};
dec_huffman_lookup(16#3f, 16#9) -> {more, 16#76, 16#06};
dec_huffman_lookup(16#3f, 16#a) -> {more, 16#76, 16#0a};
dec_huffman_lookup(16#3f, 16#b) -> {more, 16#76, 16#0f};
dec_huffman_lookup(16#3f, 16#c) -> {more, 16#76, 16#18};
dec_huffman_lookup(16#3f, 16#d) -> {more, 16#76, 16#1f};
dec_huffman_lookup(16#3f, 16#e) -> {more, 16#76, 16#29};
dec_huffman_lookup(16#3f, 16#f) -> {ok, 16#76, 16#38};
dec_huffman_lookup(16#40, 16#0) -> {more, 16#77, 16#01};
dec_huffman_lookup(16#40, 16#1) -> {ok, 16#77, 16#16};
dec_huffman_lookup(16#40, 16#2) -> {more, 16#78, 16#01};
dec_huffman_lookup(16#40, 16#3) -> {ok, 16#78, 16#16};
dec_huffman_lookup(16#40, 16#4) -> {more, 16#79, 16#01};
dec_huffman_lookup(16#40, 16#5) -> {ok, 16#79, 16#16};
dec_huffman_lookup(16#40, 16#6) -> {more, 16#7a, 16#01};
dec_huffman_lookup(16#40, 16#7) -> {ok, 16#7a, 16#16};
dec_huffman_lookup(16#40, 16#8) -> {ok, 16#26, 16#00};
dec_huffman_lookup(16#40, 16#9) -> {ok, 16#2a, 16#00};
dec_huffman_lookup(16#40, 16#a) -> {ok, 16#2c, 16#00};
dec_huffman_lookup(16#40, 16#b) -> {ok, 16#3b, 16#00};
dec_huffman_lookup(16#40, 16#c) -> {ok, 16#58, 16#00};
dec_huffman_lookup(16#40, 16#d) -> {ok, 16#5a, 16#00};
dec_huffman_lookup(16#40, 16#e) -> {more, undefined, 16#4b};
dec_huffman_lookup(16#40, 16#f) -> {ok, undefined, 16#4e};
dec_huffman_lookup(16#41, 16#0) -> {more, 16#77, 16#02};
dec_huffman_lookup(16#41, 16#1) -> {more, 16#77, 16#09};
dec_huffman_lookup(16#41, 16#2) -> {more, 16#77, 16#17};
dec_huffman_lookup(16#41, 16#3) -> {ok, 16#77, 16#28};
dec_huffman_lookup(16#41, 16#4) -> {more, 16#78, 16#02};
dec_huffman_lookup(16#41, 16#5) -> {more, 16#78, 16#09};
dec_huffman_lookup(16#41, 16#6) -> {more, 16#78, 16#17};
dec_huffman_lookup(16#41, 16#7) -> {ok, 16#78, 16#28};
dec_huffman_lookup(16#41, 16#8) -> {more, 16#79, 16#02};
dec_huffman_lookup(16#41, 16#9) -> {more, 16#79, 16#09};
dec_huffman_lookup(16#41, 16#a) -> {more, 16#79, 16#17};
dec_huffman_lookup(16#41, 16#b) -> {ok, 16#79, 16#28};
dec_huffman_lookup(16#41, 16#c) -> {more, 16#7a, 16#02};
dec_huffman_lookup(16#41, 16#d) -> {more, 16#7a, 16#09};
dec_huffman_lookup(16#41, 16#e) -> {more, 16#7a, 16#17};
dec_huffman_lookup(16#41, 16#f) -> {ok, 16#7a, 16#28};
dec_huffman_lookup(16#42, 16#0) -> {more, 16#77, 16#03};
dec_huffman_lookup(16#42, 16#1) -> {more, 16#77, 16#06};
dec_huffman_lookup(16#42, 16#2) -> {more, 16#77, 16#0a};
dec_huffman_lookup(16#42, 16#3) -> {more, 16#77, 16#0f};
dec_huffman_lookup(16#42, 16#4) -> {more, 16#77, 16#18};
dec_huffman_lookup(16#42, 16#5) -> {more, 16#77, 16#1f};
dec_huffman_lookup(16#42, 16#6) -> {more, 16#77, 16#29};
dec_huffman_lookup(16#42, 16#7) -> {ok, 16#77, 16#38};
dec_huffman_lookup(16#42, 16#8) -> {more, 16#78, 16#03};
dec_huffman_lookup(16#42, 16#9) -> {more, 16#78, 16#06};
dec_huffman_lookup(16#42, 16#a) -> {more, 16#78, 16#0a};
dec_huffman_lookup(16#42, 16#b) -> {more, 16#78, 16#0f};
dec_huffman_lookup(16#42, 16#c) -> {more, 16#78, 16#18};
dec_huffman_lookup(16#42, 16#d) -> {more, 16#78, 16#1f};
dec_huffman_lookup(16#42, 16#e) -> {more, 16#78, 16#29};
dec_huffman_lookup(16#42, 16#f) -> {ok, 16#78, 16#38};
dec_huffman_lookup(16#43, 16#0) -> {more, 16#79, 16#03};
dec_huffman_lookup(16#43, 16#1) -> {more, 16#79, 16#06};
dec_huffman_lookup(16#43, 16#2) -> {more, 16#79, 16#0a};
dec_huffman_lookup(16#43, 16#3) -> {more, 16#79, 16#0f};
dec_huffman_lookup(16#43, 16#4) -> {more, 16#79, 16#18};
dec_huffman_lookup(16#43, 16#5) -> {more, 16#79, 16#1f};
dec_huffman_lookup(16#43, 16#6) -> {more, 16#79, 16#29};
dec_huffman_lookup(16#43, 16#7) -> {ok, 16#79, 16#38};
dec_huffman_lookup(16#43, 16#8) -> {more, 16#7a, 16#03};
dec_huffman_lookup(16#43, 16#9) -> {more, 16#7a, 16#06};
dec_huffman_lookup(16#43, 16#a) -> {more, 16#7a, 16#0a};
dec_huffman_lookup(16#43, 16#b) -> {more, 16#7a, 16#0f};
dec_huffman_lookup(16#43, 16#c) -> {more, 16#7a, 16#18};
dec_huffman_lookup(16#43, 16#d) -> {more, 16#7a, 16#1f};
dec_huffman_lookup(16#43, 16#e) -> {more, 16#7a, 16#29};
dec_huffman_lookup(16#43, 16#f) -> {ok, 16#7a, 16#38};
dec_huffman_lookup(16#44, 16#0) -> {more, 16#26, 16#01};
dec_huffman_lookup(16#44, 16#1) -> {ok, 16#26, 16#16};
dec_huffman_lookup(16#44, 16#2) -> {more, 16#2a, 16#01};
dec_huffman_lookup(16#44, 16#3) -> {ok, 16#2a, 16#16};
dec_huffman_lookup(16#44, 16#4) -> {more, 16#2c, 16#01};
dec_huffman_lookup(16#44, 16#5) -> {ok, 16#2c, 16#16};
dec_huffman_lookup(16#44, 16#6) -> {more, 16#3b, 16#01};
dec_huffman_lookup(16#44, 16#7) -> {ok, 16#3b, 16#16};
dec_huffman_lookup(16#44, 16#8) -> {more, 16#58, 16#01};
dec_huffman_lookup(16#44, 16#9) -> {ok, 16#58, 16#16};
dec_huffman_lookup(16#44, 16#a) -> {more, 16#5a, 16#01};
dec_huffman_lookup(16#44, 16#b) -> {ok, 16#5a, 16#16};
dec_huffman_lookup(16#44, 16#c) -> {more, undefined, 16#4c};
dec_huffman_lookup(16#44, 16#d) -> {more, undefined, 16#4d};
dec_huffman_lookup(16#44, 16#e) -> {more, undefined, 16#4f};
dec_huffman_lookup(16#44, 16#f) -> {ok, undefined, 16#51};
dec_huffman_lookup(16#45, 16#0) -> {more, 16#26, 16#02};
dec_huffman_lookup(16#45, 16#1) -> {more, 16#26, 16#09};
dec_huffman_lookup(16#45, 16#2) -> {more, 16#26, 16#17};
dec_huffman_lookup(16#45, 16#3) -> {ok, 16#26, 16#28};
dec_huffman_lookup(16#45, 16#4) -> {more, 16#2a, 16#02};
dec_huffman_lookup(16#45, 16#5) -> {more, 16#2a, 16#09};
dec_huffman_lookup(16#45, 16#6) -> {more, 16#2a, 16#17};
dec_huffman_lookup(16#45, 16#7) -> {ok, 16#2a, 16#28};
dec_huffman_lookup(16#45, 16#8) -> {more, 16#2c, 16#02};
dec_huffman_lookup(16#45, 16#9) -> {more, 16#2c, 16#09};
dec_huffman_lookup(16#45, 16#a) -> {more, 16#2c, 16#17};
dec_huffman_lookup(16#45, 16#b) -> {ok, 16#2c, 16#28};
dec_huffman_lookup(16#45, 16#c) -> {more, 16#3b, 16#02};
dec_huffman_lookup(16#45, 16#d) -> {more, 16#3b, 16#09};
dec_huffman_lookup(16#45, 16#e) -> {more, 16#3b, 16#17};
dec_huffman_lookup(16#45, 16#f) -> {ok, 16#3b, 16#28};
dec_huffman_lookup(16#46, 16#0) -> {more, 16#26, 16#03};
dec_huffman_lookup(16#46, 16#1) -> {more, 16#26, 16#06};
dec_huffman_lookup(16#46, 16#2) -> {more, 16#26, 16#0a};
dec_huffman_lookup(16#46, 16#3) -> {more, 16#26, 16#0f};
dec_huffman_lookup(16#46, 16#4) -> {more, 16#26, 16#18};
dec_huffman_lookup(16#46, 16#5) -> {more, 16#26, 16#1f};
dec_huffman_lookup(16#46, 16#6) -> {more, 16#26, 16#29};
dec_huffman_lookup(16#46, 16#7) -> {ok, 16#26, 16#38};
dec_huffman_lookup(16#46, 16#8) -> {more, 16#2a, 16#03};
dec_huffman_lookup(16#46, 16#9) -> {more, 16#2a, 16#06};
dec_huffman_lookup(16#46, 16#a) -> {more, 16#2a, 16#0a};
dec_huffman_lookup(16#46, 16#b) -> {more, 16#2a, 16#0f};
dec_huffman_lookup(16#46, 16#c) -> {more, 16#2a, 16#18};
dec_huffman_lookup(16#46, 16#d) -> {more, 16#2a, 16#1f};
dec_huffman_lookup(16#46, 16#e) -> {more, 16#2a, 16#29};
dec_huffman_lookup(16#46, 16#f) -> {ok, 16#2a, 16#38};
dec_huffman_lookup(16#47, 16#0) -> {more, 16#2c, 16#03};
dec_huffman_lookup(16#47, 16#1) -> {more, 16#2c, 16#06};
dec_huffman_lookup(16#47, 16#2) -> {more, 16#2c, 16#0a};
dec_huffman_lookup(16#47, 16#3) -> {more, 16#2c, 16#0f};
dec_huffman_lookup(16#47, 16#4) -> {more, 16#2c, 16#18};
dec_huffman_lookup(16#47, 16#5) -> {more, 16#2c, 16#1f};
dec_huffman_lookup(16#47, 16#6) -> {more, 16#2c, 16#29};
dec_huffman_lookup(16#47, 16#7) -> {ok, 16#2c, 16#38};
dec_huffman_lookup(16#47, 16#8) -> {more, 16#3b, 16#03};
dec_huffman_lookup(16#47, 16#9) -> {more, 16#3b, 16#06};
dec_huffman_lookup(16#47, 16#a) -> {more, 16#3b, 16#0a};
dec_huffman_lookup(16#47, 16#b) -> {more, 16#3b, 16#0f};
dec_huffman_lookup(16#47, 16#c) -> {more, 16#3b, 16#18};
dec_huffman_lookup(16#47, 16#d) -> {more, 16#3b, 16#1f};
dec_huffman_lookup(16#47, 16#e) -> {more, 16#3b, 16#29};
dec_huffman_lookup(16#47, 16#f) -> {ok, 16#3b, 16#38};
dec_huffman_lookup(16#48, 16#0) -> {more, 16#58, 16#02};
dec_huffman_lookup(16#48, 16#1) -> {more, 16#58, 16#09};
dec_huffman_lookup(16#48, 16#2) -> {more, 16#58, 16#17};
dec_huffman_lookup(16#48, 16#3) -> {ok, 16#58, 16#28};
dec_huffman_lookup(16#48, 16#4) -> {more, 16#5a, 16#02};
dec_huffman_lookup(16#48, 16#5) -> {more, 16#5a, 16#09};
dec_huffman_lookup(16#48, 16#6) -> {more, 16#5a, 16#17};
dec_huffman_lookup(16#48, 16#7) -> {ok, 16#5a, 16#28};
dec_huffman_lookup(16#48, 16#8) -> {ok, 16#21, 16#00};
dec_huffman_lookup(16#48, 16#9) -> {ok, 16#22, 16#00};
dec_huffman_lookup(16#48, 16#a) -> {ok, 16#28, 16#00};
dec_huffman_lookup(16#48, 16#b) -> {ok, 16#29, 16#00};
dec_huffman_lookup(16#48, 16#c) -> {ok, 16#3f, 16#00};
dec_huffman_lookup(16#48, 16#d) -> {more, undefined, 16#50};
dec_huffman_lookup(16#48, 16#e) -> {more, undefined, 16#52};
dec_huffman_lookup(16#48, 16#f) -> {ok, undefined, 16#54};
dec_huffman_lookup(16#49, 16#0) -> {more, 16#58, 16#03};
dec_huffman_lookup(16#49, 16#1) -> {more, 16#58, 16#06};
dec_huffman_lookup(16#49, 16#2) -> {more, 16#58, 16#0a};
dec_huffman_lookup(16#49, 16#3) -> {more, 16#58, 16#0f};
dec_huffman_lookup(16#49, 16#4) -> {more, 16#58, 16#18};
dec_huffman_lookup(16#49, 16#5) -> {more, 16#58, 16#1f};
dec_huffman_lookup(16#49, 16#6) -> {more, 16#58, 16#29};
dec_huffman_lookup(16#49, 16#7) -> {ok, 16#58, 16#38};
dec_huffman_lookup(16#49, 16#8) -> {more, 16#5a, 16#03};
dec_huffman_lookup(16#49, 16#9) -> {more, 16#5a, 16#06};
dec_huffman_lookup(16#49, 16#a) -> {more, 16#5a, 16#0a};
dec_huffman_lookup(16#49, 16#b) -> {more, 16#5a, 16#0f};
dec_huffman_lookup(16#49, 16#c) -> {more, 16#5a, 16#18};
dec_huffman_lookup(16#49, 16#d) -> {more, 16#5a, 16#1f};
dec_huffman_lookup(16#49, 16#e) -> {more, 16#5a, 16#29};
dec_huffman_lookup(16#49, 16#f) -> {ok, 16#5a, 16#38};
dec_huffman_lookup(16#4a, 16#0) -> {more, 16#21, 16#01};
dec_huffman_lookup(16#4a, 16#1) -> {ok, 16#21, 16#16};
dec_huffman_lookup(16#4a, 16#2) -> {more, 16#22, 16#01};
dec_huffman_lookup(16#4a, 16#3) -> {ok, 16#22, 16#16};
dec_huffman_lookup(16#4a, 16#4) -> {more, 16#28, 16#01};
dec_huffman_lookup(16#4a, 16#5) -> {ok, 16#28, 16#16};
dec_huffman_lookup(16#4a, 16#6) -> {more, 16#29, 16#01};
dec_huffman_lookup(16#4a, 16#7) -> {ok, 16#29, 16#16};
dec_huffman_lookup(16#4a, 16#8) -> {more, 16#3f, 16#01};
dec_huffman_lookup(16#4a, 16#9) -> {ok, 16#3f, 16#16};
dec_huffman_lookup(16#4a, 16#a) -> {ok, 16#27, 16#00};
dec_huffman_lookup(16#4a, 16#b) -> {ok, 16#2b, 16#00};
dec_huffman_lookup(16#4a, 16#c) -> {ok, 16#7c, 16#00};
dec_huffman_lookup(16#4a, 16#d) -> {more, undefined, 16#53};
dec_huffman_lookup(16#4a, 16#e) -> {more, undefined, 16#55};
dec_huffman_lookup(16#4a, 16#f) -> {ok, undefined, 16#58};
dec_huffman_lookup(16#4b, 16#0) -> {more, 16#21, 16#02};
dec_huffman_lookup(16#4b, 16#1) -> {more, 16#21, 16#09};
dec_huffman_lookup(16#4b, 16#2) -> {more, 16#21, 16#17};
dec_huffman_lookup(16#4b, 16#3) -> {ok, 16#21, 16#28};
dec_huffman_lookup(16#4b, 16#4) -> {more, 16#22, 16#02};
dec_huffman_lookup(16#4b, 16#5) -> {more, 16#22, 16#09};
dec_huffman_lookup(16#4b, 16#6) -> {more, 16#22, 16#17};
dec_huffman_lookup(16#4b, 16#7) -> {ok, 16#22, 16#28};
dec_huffman_lookup(16#4b, 16#8) -> {more, 16#28, 16#02};
dec_huffman_lookup(16#4b, 16#9) -> {more, 16#28, 16#09};
dec_huffman_lookup(16#4b, 16#a) -> {more, 16#28, 16#17};
dec_huffman_lookup(16#4b, 16#b) -> {ok, 16#28, 16#28};
dec_huffman_lookup(16#4b, 16#c) -> {more, 16#29, 16#02};
dec_huffman_lookup(16#4b, 16#d) -> {more, 16#29, 16#09};
dec_huffman_lookup(16#4b, 16#e) -> {more, 16#29, 16#17};
dec_huffman_lookup(16#4b, 16#f) -> {ok, 16#29, 16#28};
dec_huffman_lookup(16#4c, 16#0) -> {more, 16#21, 16#03};
dec_huffman_lookup(16#4c, 16#1) -> {more, 16#21, 16#06};
dec_huffman_lookup(16#4c, 16#2) -> {more, 16#21, 16#0a};
dec_huffman_lookup(16#4c, 16#3) -> {more, 16#21, 16#0f};
dec_huffman_lookup(16#4c, 16#4) -> {more, 16#21, 16#18};
dec_huffman_lookup(16#4c, 16#5) -> {more, 16#21, 16#1f};
dec_huffman_lookup(16#4c, 16#6) -> {more, 16#21, 16#29};
dec_huffman_lookup(16#4c, 16#7) -> {ok, 16#21, 16#38};
dec_huffman_lookup(16#4c, 16#8) -> {more, 16#22, 16#03};
dec_huffman_lookup(16#4c, 16#9) -> {more, 16#22, 16#06};
dec_huffman_lookup(16#4c, 16#a) -> {more, 16#22, 16#0a};
dec_huffman_lookup(16#4c, 16#b) -> {more, 16#22, 16#0f};
dec_huffman_lookup(16#4c, 16#c) -> {more, 16#22, 16#18};
dec_huffman_lookup(16#4c, 16#d) -> {more, 16#22, 16#1f};
dec_huffman_lookup(16#4c, 16#e) -> {more, 16#22, 16#29};
dec_huffman_lookup(16#4c, 16#f) -> {ok, 16#22, 16#38};
dec_huffman_lookup(16#4d, 16#0) -> {more, 16#28, 16#03};
dec_huffman_lookup(16#4d, 16#1) -> {more, 16#28, 16#06};
dec_huffman_lookup(16#4d, 16#2) -> {more, 16#28, 16#0a};
dec_huffman_lookup(16#4d, 16#3) -> {more, 16#28, 16#0f};
dec_huffman_lookup(16#4d, 16#4) -> {more, 16#28, 16#18};
dec_huffman_lookup(16#4d, 16#5) -> {more, 16#28, 16#1f};
dec_huffman_lookup(16#4d, 16#6) -> {more, 16#28, 16#29};
dec_huffman_lookup(16#4d, 16#7) -> {ok, 16#28, 16#38};
dec_huffman_lookup(16#4d, 16#8) -> {more, 16#29, 16#03};
dec_huffman_lookup(16#4d, 16#9) -> {more, 16#29, 16#06};
dec_huffman_lookup(16#4d, 16#a) -> {more, 16#29, 16#0a};
dec_huffman_lookup(16#4d, 16#b) -> {more, 16#29, 16#0f};
dec_huffman_lookup(16#4d, 16#c) -> {more, 16#29, 16#18};
dec_huffman_lookup(16#4d, 16#d) -> {more, 16#29, 16#1f};
dec_huffman_lookup(16#4d, 16#e) -> {more, 16#29, 16#29};
dec_huffman_lookup(16#4d, 16#f) -> {ok, 16#29, 16#38};
dec_huffman_lookup(16#4e, 16#0) -> {more, 16#3f, 16#02};
dec_huffman_lookup(16#4e, 16#1) -> {more, 16#3f, 16#09};
dec_huffman_lookup(16#4e, 16#2) -> {more, 16#3f, 16#17};
dec_huffman_lookup(16#4e, 16#3) -> {ok, 16#3f, 16#28};
dec_huffman_lookup(16#4e, 16#4) -> {more, 16#27, 16#01};
dec_huffman_lookup(16#4e, 16#5) -> {ok, 16#27, 16#16};
dec_huffman_lookup(16#4e, 16#6) -> {more, 16#2b, 16#01};
dec_huffman_lookup(16#4e, 16#7) -> {ok, 16#2b, 16#16};
dec_huffman_lookup(16#4e, 16#8) -> {more, 16#7c, 16#01};
dec_huffman_lookup(16#4e, 16#9) -> {ok, 16#7c, 16#16};
dec_huffman_lookup(16#4e, 16#a) -> {ok, 16#23, 16#00};
dec_huffman_lookup(16#4e, 16#b) -> {ok, 16#3e, 16#00};
dec_huffman_lookup(16#4e, 16#c) -> {more, undefined, 16#56};
dec_huffman_lookup(16#4e, 16#d) -> {more, undefined, 16#57};
dec_huffman_lookup(16#4e, 16#e) -> {more, undefined, 16#59};
dec_huffman_lookup(16#4e, 16#f) -> {ok, undefined, 16#5a};
dec_huffman_lookup(16#4f, 16#0) -> {more, 16#3f, 16#03};
dec_huffman_lookup(16#4f, 16#1) -> {more, 16#3f, 16#06};
dec_huffman_lookup(16#4f, 16#2) -> {more, 16#3f, 16#0a};
dec_huffman_lookup(16#4f, 16#3) -> {more, 16#3f, 16#0f};
dec_huffman_lookup(16#4f, 16#4) -> {more, 16#3f, 16#18};
dec_huffman_lookup(16#4f, 16#5) -> {more, 16#3f, 16#1f};
dec_huffman_lookup(16#4f, 16#6) -> {more, 16#3f, 16#29};
dec_huffman_lookup(16#4f, 16#7) -> {ok, 16#3f, 16#38};
dec_huffman_lookup(16#4f, 16#8) -> {more, 16#27, 16#02};
dec_huffman_lookup(16#4f, 16#9) -> {more, 16#27, 16#09};
dec_huffman_lookup(16#4f, 16#a) -> {more, 16#27, 16#17};
dec_huffman_lookup(16#4f, 16#b) -> {ok, 16#27, 16#28};
dec_huffman_lookup(16#4f, 16#c) -> {more, 16#2b, 16#02};
dec_huffman_lookup(16#4f, 16#d) -> {more, 16#2b, 16#09};
dec_huffman_lookup(16#4f, 16#e) -> {more, 16#2b, 16#17};
dec_huffman_lookup(16#4f, 16#f) -> {ok, 16#2b, 16#28};
dec_huffman_lookup(16#50, 16#0) -> {more, 16#27, 16#03};
dec_huffman_lookup(16#50, 16#1) -> {more, 16#27, 16#06};
dec_huffman_lookup(16#50, 16#2) -> {more, 16#27, 16#0a};
dec_huffman_lookup(16#50, 16#3) -> {more, 16#27, 16#0f};
dec_huffman_lookup(16#50, 16#4) -> {more, 16#27, 16#18};
dec_huffman_lookup(16#50, 16#5) -> {more, 16#27, 16#1f};
dec_huffman_lookup(16#50, 16#6) -> {more, 16#27, 16#29};
dec_huffman_lookup(16#50, 16#7) -> {ok, 16#27, 16#38};
dec_huffman_lookup(16#50, 16#8) -> {more, 16#2b, 16#03};
dec_huffman_lookup(16#50, 16#9) -> {more, 16#2b, 16#06};
dec_huffman_lookup(16#50, 16#a) -> {more, 16#2b, 16#0a};
dec_huffman_lookup(16#50, 16#b) -> {more, 16#2b, 16#0f};
dec_huffman_lookup(16#50, 16#c) -> {more, 16#2b, 16#18};
dec_huffman_lookup(16#50, 16#d) -> {more, 16#2b, 16#1f};
dec_huffman_lookup(16#50, 16#e) -> {more, 16#2b, 16#29};
dec_huffman_lookup(16#50, 16#f) -> {ok, 16#2b, 16#38};
dec_huffman_lookup(16#51, 16#0) -> {more, 16#7c, 16#02};
dec_huffman_lookup(16#51, 16#1) -> {more, 16#7c, 16#09};
dec_huffman_lookup(16#51, 16#2) -> {more, 16#7c, 16#17};
dec_huffman_lookup(16#51, 16#3) -> {ok, 16#7c, 16#28};
dec_huffman_lookup(16#51, 16#4) -> {more, 16#23, 16#01};
dec_huffman_lookup(16#51, 16#5) -> {ok, 16#23, 16#16};
dec_huffman_lookup(16#51, 16#6) -> {more, 16#3e, 16#01};
dec_huffman_lookup(16#51, 16#7) -> {ok, 16#3e, 16#16};
dec_huffman_lookup(16#51, 16#8) -> {ok, 16#00, 16#00};
dec_huffman_lookup(16#51, 16#9) -> {ok, 16#24, 16#00};
dec_huffman_lookup(16#51, 16#a) -> {ok, 16#40, 16#00};
dec_huffman_lookup(16#51, 16#b) -> {ok, 16#5b, 16#00};
dec_huffman_lookup(16#51, 16#c) -> {ok, 16#5d, 16#00};
dec_huffman_lookup(16#51, 16#d) -> {ok, 16#7e, 16#00};
dec_huffman_lookup(16#51, 16#e) -> {more, undefined, 16#5b};
dec_huffman_lookup(16#51, 16#f) -> {ok, undefined, 16#5c};
dec_huffman_lookup(16#52, 16#0) -> {more, 16#7c, 16#03};
dec_huffman_lookup(16#52, 16#1) -> {more, 16#7c, 16#06};
dec_huffman_lookup(16#52, 16#2) -> {more, 16#7c, 16#0a};
dec_huffman_lookup(16#52, 16#3) -> {more, 16#7c, 16#0f};
dec_huffman_lookup(16#52, 16#4) -> {more, 16#7c, 16#18};
dec_huffman_lookup(16#52, 16#5) -> {more, 16#7c, 16#1f};
dec_huffman_lookup(16#52, 16#6) -> {more, 16#7c, 16#29};
dec_huffman_lookup(16#52, 16#7) -> {ok, 16#7c, 16#38};
dec_huffman_lookup(16#52, 16#8) -> {more, 16#23, 16#02};
dec_huffman_lookup(16#52, 16#9) -> {more, 16#23, 16#09};
dec_huffman_lookup(16#52, 16#a) -> {more, 16#23, 16#17};
dec_huffman_lookup(16#52, 16#b) -> {ok, 16#23, 16#28};
dec_huffman_lookup(16#52, 16#c) -> {more, 16#3e, 16#02};
dec_huffman_lookup(16#52, 16#d) -> {more, 16#3e, 16#09};
dec_huffman_lookup(16#52, 16#e) -> {more, 16#3e, 16#17};
dec_huffman_lookup(16#52, 16#f) -> {ok, 16#3e, 16#28};
dec_huffman_lookup(16#53, 16#0) -> {more, 16#23, 16#03};
dec_huffman_lookup(16#53, 16#1) -> {more, 16#23, 16#06};
dec_huffman_lookup(16#53, 16#2) -> {more, 16#23, 16#0a};
dec_huffman_lookup(16#53, 16#3) -> {more, 16#23, 16#0f};
dec_huffman_lookup(16#53, 16#4) -> {more, 16#23, 16#18};
dec_huffman_lookup(16#53, 16#5) -> {more, 16#23, 16#1f};
dec_huffman_lookup(16#53, 16#6) -> {more, 16#23, 16#29};
dec_huffman_lookup(16#53, 16#7) -> {ok, 16#23, 16#38};
dec_huffman_lookup(16#53, 16#8) -> {more, 16#3e, 16#03};
dec_huffman_lookup(16#53, 16#9) -> {more, 16#3e, 16#06};
dec_huffman_lookup(16#53, 16#a) -> {more, 16#3e, 16#0a};
dec_huffman_lookup(16#53, 16#b) -> {more, 16#3e, 16#0f};
dec_huffman_lookup(16#53, 16#c) -> {more, 16#3e, 16#18};
dec_huffman_lookup(16#53, 16#d) -> {more, 16#3e, 16#1f};
dec_huffman_lookup(16#53, 16#e) -> {more, 16#3e, 16#29};
dec_huffman_lookup(16#53, 16#f) -> {ok, 16#3e, 16#38};
dec_huffman_lookup(16#54, 16#0) -> {more, 16#00, 16#01};
dec_huffman_lookup(16#54, 16#1) -> {ok, 16#00, 16#16};
dec_huffman_lookup(16#54, 16#2) -> {more, 16#24, 16#01};
dec_huffman_lookup(16#54, 16#3) -> {ok, 16#24, 16#16};
dec_huffman_lookup(16#54, 16#4) -> {more, 16#40, 16#01};
dec_huffman_lookup(16#54, 16#5) -> {ok, 16#40, 16#16};
dec_huffman_lookup(16#54, 16#6) -> {more, 16#5b, 16#01};
dec_huffman_lookup(16#54, 16#7) -> {ok, 16#5b, 16#16};
dec_huffman_lookup(16#54, 16#8) -> {more, 16#5d, 16#01};
dec_huffman_lookup(16#54, 16#9) -> {ok, 16#5d, 16#16};
dec_huffman_lookup(16#54, 16#a) -> {more, 16#7e, 16#01};
dec_huffman_lookup(16#54, 16#b) -> {ok, 16#7e, 16#16};
dec_huffman_lookup(16#54, 16#c) -> {ok, 16#5e, 16#00};
dec_huffman_lookup(16#54, 16#d) -> {ok, 16#7d, 16#00};
dec_huffman_lookup(16#54, 16#e) -> {more, undefined, 16#5d};
dec_huffman_lookup(16#54, 16#f) -> {ok, undefined, 16#5e};
dec_huffman_lookup(16#55, 16#0) -> {more, 16#00, 16#02};
dec_huffman_lookup(16#55, 16#1) -> {more, 16#00, 16#09};
dec_huffman_lookup(16#55, 16#2) -> {more, 16#00, 16#17};
dec_huffman_lookup(16#55, 16#3) -> {ok, 16#00, 16#28};
dec_huffman_lookup(16#55, 16#4) -> {more, 16#24, 16#02};
dec_huffman_lookup(16#55, 16#5) -> {more, 16#24, 16#09};
dec_huffman_lookup(16#55, 16#6) -> {more, 16#24, 16#17};
dec_huffman_lookup(16#55, 16#7) -> {ok, 16#24, 16#28};
dec_huffman_lookup(16#55, 16#8) -> {more, 16#40, 16#02};
dec_huffman_lookup(16#55, 16#9) -> {more, 16#40, 16#09};
dec_huffman_lookup(16#55, 16#a) -> {more, 16#40, 16#17};
dec_huffman_lookup(16#55, 16#b) -> {ok, 16#40, 16#28};
dec_huffman_lookup(16#55, 16#c) -> {more, 16#5b, 16#02};
dec_huffman_lookup(16#55, 16#d) -> {more, 16#5b, 16#09};
dec_huffman_lookup(16#55, 16#e) -> {more, 16#5b, 16#17};
dec_huffman_lookup(16#55, 16#f) -> {ok, 16#5b, 16#28};
dec_huffman_lookup(16#56, 16#0) -> {more, 16#00, 16#03};
dec_huffman_lookup(16#56, 16#1) -> {more, 16#00, 16#06};
dec_huffman_lookup(16#56, 16#2) -> {more, 16#00, 16#0a};
dec_huffman_lookup(16#56, 16#3) -> {more, 16#00, 16#0f};
dec_huffman_lookup(16#56, 16#4) -> {more, 16#00, 16#18};
dec_huffman_lookup(16#56, 16#5) -> {more, 16#00, 16#1f};
dec_huffman_lookup(16#56, 16#6) -> {more, 16#00, 16#29};
dec_huffman_lookup(16#56, 16#7) -> {ok, 16#00, 16#38};
dec_huffman_lookup(16#56, 16#8) -> {more, 16#24, 16#03};
dec_huffman_lookup(16#56, 16#9) -> {more, 16#24, 16#06};
dec_huffman_lookup(16#56, 16#a) -> {more, 16#24, 16#0a};
dec_huffman_lookup(16#56, 16#b) -> {more, 16#24, 16#0f};
dec_huffman_lookup(16#56, 16#c) -> {more, 16#24, 16#18};
dec_huffman_lookup(16#56, 16#d) -> {more, 16#24, 16#1f};
dec_huffman_lookup(16#56, 16#e) -> {more, 16#24, 16#29};
dec_huffman_lookup(16#56, 16#f) -> {ok, 16#24, 16#38};
dec_huffman_lookup(16#57, 16#0) -> {more, 16#40, 16#03};
dec_huffman_lookup(16#57, 16#1) -> {more, 16#40, 16#06};
dec_huffman_lookup(16#57, 16#2) -> {more, 16#40, 16#0a};
dec_huffman_lookup(16#57, 16#3) -> {more, 16#40, 16#0f};
dec_huffman_lookup(16#57, 16#4) -> {more, 16#40, 16#18};
dec_huffman_lookup(16#57, 16#5) -> {more, 16#40, 16#1f};
dec_huffman_lookup(16#57, 16#6) -> {more, 16#40, 16#29};
dec_huffman_lookup(16#57, 16#7) -> {ok, 16#40, 16#38};
dec_huffman_lookup(16#57, 16#8) -> {more, 16#5b, 16#03};
dec_huffman_lookup(16#57, 16#9) -> {more, 16#5b, 16#06};
dec_huffman_lookup(16#57, 16#a) -> {more, 16#5b, 16#0a};
dec_huffman_lookup(16#57, 16#b) -> {more, 16#5b, 16#0f};
dec_huffman_lookup(16#57, 16#c) -> {more, 16#5b, 16#18};
dec_huffman_lookup(16#57, 16#d) -> {more, 16#5b, 16#1f};
dec_huffman_lookup(16#57, 16#e) -> {more, 16#5b, 16#29};
dec_huffman_lookup(16#57, 16#f) -> {ok, 16#5b, 16#38};
dec_huffman_lookup(16#58, 16#0) -> {more, 16#5d, 16#02};
dec_huffman_lookup(16#58, 16#1) -> {more, 16#5d, 16#09};
dec_huffman_lookup(16#58, 16#2) -> {more, 16#5d, 16#17};
dec_huffman_lookup(16#58, 16#3) -> {ok, 16#5d, 16#28};
dec_huffman_lookup(16#58, 16#4) -> {more, 16#7e, 16#02};
dec_huffman_lookup(16#58, 16#5) -> {more, 16#7e, 16#09};
dec_huffman_lookup(16#58, 16#6) -> {more, 16#7e, 16#17};
dec_huffman_lookup(16#58, 16#7) -> {ok, 16#7e, 16#28};
dec_huffman_lookup(16#58, 16#8) -> {more, 16#5e, 16#01};
dec_huffman_lookup(16#58, 16#9) -> {ok, 16#5e, 16#16};
dec_huffman_lookup(16#58, 16#a) -> {more, 16#7d, 16#01};
dec_huffman_lookup(16#58, 16#b) -> {ok, 16#7d, 16#16};
dec_huffman_lookup(16#58, 16#c) -> {ok, 16#3c, 16#00};
dec_huffman_lookup(16#58, 16#d) -> {ok, 16#60, 16#00};
dec_huffman_lookup(16#58, 16#e) -> {ok, 16#7b, 16#00};
dec_huffman_lookup(16#58, 16#f) -> {ok, undefined, 16#5f};
dec_huffman_lookup(16#59, 16#0) -> {more, 16#5d, 16#03};
dec_huffman_lookup(16#59, 16#1) -> {more, 16#5d, 16#06};
dec_huffman_lookup(16#59, 16#2) -> {more, 16#5d, 16#0a};
dec_huffman_lookup(16#59, 16#3) -> {more, 16#5d, 16#0f};
dec_huffman_lookup(16#59, 16#4) -> {more, 16#5d, 16#18};
dec_huffman_lookup(16#59, 16#5) -> {more, 16#5d, 16#1f};
dec_huffman_lookup(16#59, 16#6) -> {more, 16#5d, 16#29};
dec_huffman_lookup(16#59, 16#7) -> {ok, 16#5d, 16#38};
dec_huffman_lookup(16#59, 16#8) -> {more, 16#7e, 16#03};
dec_huffman_lookup(16#59, 16#9) -> {more, 16#7e, 16#06};
dec_huffman_lookup(16#59, 16#a) -> {more, 16#7e, 16#0a};
dec_huffman_lookup(16#59, 16#b) -> {more, 16#7e, 16#0f};
dec_huffman_lookup(16#59, 16#c) -> {more, 16#7e, 16#18};
dec_huffman_lookup(16#59, 16#d) -> {more, 16#7e, 16#1f};
dec_huffman_lookup(16#59, 16#e) -> {more, 16#7e, 16#29};
dec_huffman_lookup(16#59, 16#f) -> {ok, 16#7e, 16#38};
dec_huffman_lookup(16#5a, 16#0) -> {more, 16#5e, 16#02};
dec_huffman_lookup(16#5a, 16#1) -> {more, 16#5e, 16#09};
dec_huffman_lookup(16#5a, 16#2) -> {more, 16#5e, 16#17};
dec_huffman_lookup(16#5a, 16#3) -> {ok, 16#5e, 16#28};
dec_huffman_lookup(16#5a, 16#4) -> {more, 16#7d, 16#02};
dec_huffman_lookup(16#5a, 16#5) -> {more, 16#7d, 16#09};
dec_huffman_lookup(16#5a, 16#6) -> {more, 16#7d, 16#17};
dec_huffman_lookup(16#5a, 16#7) -> {ok, 16#7d, 16#28};
dec_huffman_lookup(16#5a, 16#8) -> {more, 16#3c, 16#01};
dec_huffman_lookup(16#5a, 16#9) -> {ok, 16#3c, 16#16};
dec_huffman_lookup(16#5a, 16#a) -> {more, 16#60, 16#01};
dec_huffman_lookup(16#5a, 16#b) -> {ok, 16#60, 16#16};
dec_huffman_lookup(16#5a, 16#c) -> {more, 16#7b, 16#01};
dec_huffman_lookup(16#5a, 16#d) -> {ok, 16#7b, 16#16};
dec_huffman_lookup(16#5a, 16#e) -> {more, undefined, 16#60};
dec_huffman_lookup(16#5a, 16#f) -> {ok, undefined, 16#6e};
dec_huffman_lookup(16#5b, 16#0) -> {more, 16#5e, 16#03};
dec_huffman_lookup(16#5b, 16#1) -> {more, 16#5e, 16#06};
dec_huffman_lookup(16#5b, 16#2) -> {more, 16#5e, 16#0a};
dec_huffman_lookup(16#5b, 16#3) -> {more, 16#5e, 16#0f};
dec_huffman_lookup(16#5b, 16#4) -> {more, 16#5e, 16#18};
dec_huffman_lookup(16#5b, 16#5) -> {more, 16#5e, 16#1f};
dec_huffman_lookup(16#5b, 16#6) -> {more, 16#5e, 16#29};
dec_huffman_lookup(16#5b, 16#7) -> {ok, 16#5e, 16#38};
dec_huffman_lookup(16#5b, 16#8) -> {more, 16#7d, 16#03};
dec_huffman_lookup(16#5b, 16#9) -> {more, 16#7d, 16#06};
dec_huffman_lookup(16#5b, 16#a) -> {more, 16#7d, 16#0a};
dec_huffman_lookup(16#5b, 16#b) -> {more, 16#7d, 16#0f};
dec_huffman_lookup(16#5b, 16#c) -> {more, 16#7d, 16#18};
dec_huffman_lookup(16#5b, 16#d) -> {more, 16#7d, 16#1f};
dec_huffman_lookup(16#5b, 16#e) -> {more, 16#7d, 16#29};
dec_huffman_lookup(16#5b, 16#f) -> {ok, 16#7d, 16#38};
dec_huffman_lookup(16#5c, 16#0) -> {more, 16#3c, 16#02};
dec_huffman_lookup(16#5c, 16#1) -> {more, 16#3c, 16#09};
dec_huffman_lookup(16#5c, 16#2) -> {more, 16#3c, 16#17};
dec_huffman_lookup(16#5c, 16#3) -> {ok, 16#3c, 16#28};
dec_huffman_lookup(16#5c, 16#4) -> {more, 16#60, 16#02};
dec_huffman_lookup(16#5c, 16#5) -> {more, 16#60, 16#09};
dec_huffman_lookup(16#5c, 16#6) -> {more, 16#60, 16#17};
dec_huffman_lookup(16#5c, 16#7) -> {ok, 16#60, 16#28};
dec_huffman_lookup(16#5c, 16#8) -> {more, 16#7b, 16#02};
dec_huffman_lookup(16#5c, 16#9) -> {more, 16#7b, 16#09};
dec_huffman_lookup(16#5c, 16#a) -> {more, 16#7b, 16#17};
dec_huffman_lookup(16#5c, 16#b) -> {ok, 16#7b, 16#28};
dec_huffman_lookup(16#5c, 16#c) -> {more, undefined, 16#61};
dec_huffman_lookup(16#5c, 16#d) -> {more, undefined, 16#65};
dec_huffman_lookup(16#5c, 16#e) -> {more, undefined, 16#6f};
dec_huffman_lookup(16#5c, 16#f) -> {ok, undefined, 16#85};
dec_huffman_lookup(16#5d, 16#0) -> {more, 16#3c, 16#03};
dec_huffman_lookup(16#5d, 16#1) -> {more, 16#3c, 16#06};
dec_huffman_lookup(16#5d, 16#2) -> {more, 16#3c, 16#0a};
dec_huffman_lookup(16#5d, 16#3) -> {more, 16#3c, 16#0f};
dec_huffman_lookup(16#5d, 16#4) -> {more, 16#3c, 16#18};
dec_huffman_lookup(16#5d, 16#5) -> {more, 16#3c, 16#1f};
dec_huffman_lookup(16#5d, 16#6) -> {more, 16#3c, 16#29};
dec_huffman_lookup(16#5d, 16#7) -> {ok, 16#3c, 16#38};
dec_huffman_lookup(16#5d, 16#8) -> {more, 16#60, 16#03};
dec_huffman_lookup(16#5d, 16#9) -> {more, 16#60, 16#06};
dec_huffman_lookup(16#5d, 16#a) -> {more, 16#60, 16#0a};
dec_huffman_lookup(16#5d, 16#b) -> {more, 16#60, 16#0f};
dec_huffman_lookup(16#5d, 16#c) -> {more, 16#60, 16#18};
dec_huffman_lookup(16#5d, 16#d) -> {more, 16#60, 16#1f};
dec_huffman_lookup(16#5d, 16#e) -> {more, 16#60, 16#29};
dec_huffman_lookup(16#5d, 16#f) -> {ok, 16#60, 16#38};
dec_huffman_lookup(16#5e, 16#0) -> {more, 16#7b, 16#03};
dec_huffman_lookup(16#5e, 16#1) -> {more, 16#7b, 16#06};
dec_huffman_lookup(16#5e, 16#2) -> {more, 16#7b, 16#0a};
dec_huffman_lookup(16#5e, 16#3) -> {more, 16#7b, 16#0f};
dec_huffman_lookup(16#5e, 16#4) -> {more, 16#7b, 16#18};
dec_huffman_lookup(16#5e, 16#5) -> {more, 16#7b, 16#1f};
dec_huffman_lookup(16#5e, 16#6) -> {more, 16#7b, 16#29};
dec_huffman_lookup(16#5e, 16#7) -> {ok, 16#7b, 16#38};
dec_huffman_lookup(16#5e, 16#8) -> {more, undefined, 16#62};
dec_huffman_lookup(16#5e, 16#9) -> {more, undefined, 16#63};
dec_huffman_lookup(16#5e, 16#a) -> {more, undefined, 16#66};
dec_huffman_lookup(16#5e, 16#b) -> {more, undefined, 16#69};
dec_huffman_lookup(16#5e, 16#c) -> {more, undefined, 16#70};
dec_huffman_lookup(16#5e, 16#d) -> {more, undefined, 16#77};
dec_huffman_lookup(16#5e, 16#e) -> {more, undefined, 16#86};
dec_huffman_lookup(16#5e, 16#f) -> {ok, undefined, 16#99};
dec_huffman_lookup(16#5f, 16#0) -> {ok, 16#5c, 16#00};
dec_huffman_lookup(16#5f, 16#1) -> {ok, 16#c3, 16#00};
dec_huffman_lookup(16#5f, 16#2) -> {ok, 16#d0, 16#00};
dec_huffman_lookup(16#5f, 16#3) -> {more, undefined, 16#64};
dec_huffman_lookup(16#5f, 16#4) -> {more, undefined, 16#67};
dec_huffman_lookup(16#5f, 16#5) -> {more, undefined, 16#68};
dec_huffman_lookup(16#5f, 16#6) -> {more, undefined, 16#6a};
dec_huffman_lookup(16#5f, 16#7) -> {more, undefined, 16#6b};
dec_huffman_lookup(16#5f, 16#8) -> {more, undefined, 16#71};
dec_huffman_lookup(16#5f, 16#9) -> {more, undefined, 16#74};
dec_huffman_lookup(16#5f, 16#a) -> {more, undefined, 16#78};
dec_huffman_lookup(16#5f, 16#b) -> {more, undefined, 16#7e};
dec_huffman_lookup(16#5f, 16#c) -> {more, undefined, 16#87};
dec_huffman_lookup(16#5f, 16#d) -> {more, undefined, 16#8e};
dec_huffman_lookup(16#5f, 16#e) -> {more, undefined, 16#9a};
dec_huffman_lookup(16#5f, 16#f) -> {ok, undefined, 16#a9};
dec_huffman_lookup(16#60, 16#0) -> {more, 16#5c, 16#01};
dec_huffman_lookup(16#60, 16#1) -> {ok, 16#5c, 16#16};
dec_huffman_lookup(16#60, 16#2) -> {more, 16#c3, 16#01};
dec_huffman_lookup(16#60, 16#3) -> {ok, 16#c3, 16#16};
dec_huffman_lookup(16#60, 16#4) -> {more, 16#d0, 16#01};
dec_huffman_lookup(16#60, 16#5) -> {ok, 16#d0, 16#16};
dec_huffman_lookup(16#60, 16#6) -> {ok, 16#80, 16#00};
dec_huffman_lookup(16#60, 16#7) -> {ok, 16#82, 16#00};
dec_huffman_lookup(16#60, 16#8) -> {ok, 16#83, 16#00};
dec_huffman_lookup(16#60, 16#9) -> {ok, 16#a2, 16#00};
dec_huffman_lookup(16#60, 16#a) -> {ok, 16#b8, 16#00};
dec_huffman_lookup(16#60, 16#b) -> {ok, 16#c2, 16#00};
dec_huffman_lookup(16#60, 16#c) -> {ok, 16#e0, 16#00};
dec_huffman_lookup(16#60, 16#d) -> {ok, 16#e2, 16#00};
dec_huffman_lookup(16#60, 16#e) -> {more, undefined, 16#6c};
dec_huffman_lookup(16#60, 16#f) -> {more, undefined, 16#6d};
dec_huffman_lookup(16#61, 16#0) -> {more, 16#5c, 16#02};
dec_huffman_lookup(16#61, 16#1) -> {more, 16#5c, 16#09};
dec_huffman_lookup(16#61, 16#2) -> {more, 16#5c, 16#17};
dec_huffman_lookup(16#61, 16#3) -> {ok, 16#5c, 16#28};
dec_huffman_lookup(16#61, 16#4) -> {more, 16#c3, 16#02};
dec_huffman_lookup(16#61, 16#5) -> {more, 16#c3, 16#09};
dec_huffman_lookup(16#61, 16#6) -> {more, 16#c3, 16#17};
dec_huffman_lookup(16#61, 16#7) -> {ok, 16#c3, 16#28};
dec_huffman_lookup(16#61, 16#8) -> {more, 16#d0, 16#02};
dec_huffman_lookup(16#61, 16#9) -> {more, 16#d0, 16#09};
dec_huffman_lookup(16#61, 16#a) -> {more, 16#d0, 16#17};
dec_huffman_lookup(16#61, 16#b) -> {ok, 16#d0, 16#28};
dec_huffman_lookup(16#61, 16#c) -> {more, 16#80, 16#01};
dec_huffman_lookup(16#61, 16#d) -> {ok, 16#80, 16#16};
dec_huffman_lookup(16#61, 16#e) -> {more, 16#82, 16#01};
dec_huffman_lookup(16#61, 16#f) -> {ok, 16#82, 16#16};
dec_huffman_lookup(16#62, 16#0) -> {more, 16#5c, 16#03};
dec_huffman_lookup(16#62, 16#1) -> {more, 16#5c, 16#06};
dec_huffman_lookup(16#62, 16#2) -> {more, 16#5c, 16#0a};
dec_huffman_lookup(16#62, 16#3) -> {more, 16#5c, 16#0f};
dec_huffman_lookup(16#62, 16#4) -> {more, 16#5c, 16#18};
dec_huffman_lookup(16#62, 16#5) -> {more, 16#5c, 16#1f};
dec_huffman_lookup(16#62, 16#6) -> {more, 16#5c, 16#29};
dec_huffman_lookup(16#62, 16#7) -> {ok, 16#5c, 16#38};
dec_huffman_lookup(16#62, 16#8) -> {more, 16#c3, 16#03};
dec_huffman_lookup(16#62, 16#9) -> {more, 16#c3, 16#06};
dec_huffman_lookup(16#62, 16#a) -> {more, 16#c3, 16#0a};
dec_huffman_lookup(16#62, 16#b) -> {more, 16#c3, 16#0f};
dec_huffman_lookup(16#62, 16#c) -> {more, 16#c3, 16#18};
dec_huffman_lookup(16#62, 16#d) -> {more, 16#c3, 16#1f};
dec_huffman_lookup(16#62, 16#e) -> {more, 16#c3, 16#29};
dec_huffman_lookup(16#62, 16#f) -> {ok, 16#c3, 16#38};
dec_huffman_lookup(16#63, 16#0) -> {more, 16#d0, 16#03};
dec_huffman_lookup(16#63, 16#1) -> {more, 16#d0, 16#06};
dec_huffman_lookup(16#63, 16#2) -> {more, 16#d0, 16#0a};
dec_huffman_lookup(16#63, 16#3) -> {more, 16#d0, 16#0f};
dec_huffman_lookup(16#63, 16#4) -> {more, 16#d0, 16#18};
dec_huffman_lookup(16#63, 16#5) -> {more, 16#d0, 16#1f};
dec_huffman_lookup(16#63, 16#6) -> {more, 16#d0, 16#29};
dec_huffman_lookup(16#63, 16#7) -> {ok, 16#d0, 16#38};
dec_huffman_lookup(16#63, 16#8) -> {more, 16#80, 16#02};
dec_huffman_lookup(16#63, 16#9) -> {more, 16#80, 16#09};
dec_huffman_lookup(16#63, 16#a) -> {more, 16#80, 16#17};
dec_huffman_lookup(16#63, 16#b) -> {ok, 16#80, 16#28};
dec_huffman_lookup(16#63, 16#c) -> {more, 16#82, 16#02};
dec_huffman_lookup(16#63, 16#d) -> {more, 16#82, 16#09};
dec_huffman_lookup(16#63, 16#e) -> {more, 16#82, 16#17};
dec_huffman_lookup(16#63, 16#f) -> {ok, 16#82, 16#28};
dec_huffman_lookup(16#64, 16#0) -> {more, 16#80, 16#03};
dec_huffman_lookup(16#64, 16#1) -> {more, 16#80, 16#06};
dec_huffman_lookup(16#64, 16#2) -> {more, 16#80, 16#0a};
dec_huffman_lookup(16#64, 16#3) -> {more, 16#80, 16#0f};
dec_huffman_lookup(16#64, 16#4) -> {more, 16#80, 16#18};
dec_huffman_lookup(16#64, 16#5) -> {more, 16#80, 16#1f};
dec_huffman_lookup(16#64, 16#6) -> {more, 16#80, 16#29};
dec_huffman_lookup(16#64, 16#7) -> {ok, 16#80, 16#38};
dec_huffman_lookup(16#64, 16#8) -> {more, 16#82, 16#03};
dec_huffman_lookup(16#64, 16#9) -> {more, 16#82, 16#06};
dec_huffman_lookup(16#64, 16#a) -> {more, 16#82, 16#0a};
dec_huffman_lookup(16#64, 16#b) -> {more, 16#82, 16#0f};
dec_huffman_lookup(16#64, 16#c) -> {more, 16#82, 16#18};
dec_huffman_lookup(16#64, 16#d) -> {more, 16#82, 16#1f};
dec_huffman_lookup(16#64, 16#e) -> {more, 16#82, 16#29};
dec_huffman_lookup(16#64, 16#f) -> {ok, 16#82, 16#38};
dec_huffman_lookup(16#65, 16#0) -> {more, 16#83, 16#01};
dec_huffman_lookup(16#65, 16#1) -> {ok, 16#83, 16#16};
dec_huffman_lookup(16#65, 16#2) -> {more, 16#a2, 16#01};
dec_huffman_lookup(16#65, 16#3) -> {ok, 16#a2, 16#16};
dec_huffman_lookup(16#65, 16#4) -> {more, 16#b8, 16#01};
dec_huffman_lookup(16#65, 16#5) -> {ok, 16#b8, 16#16};
dec_huffman_lookup(16#65, 16#6) -> {more, 16#c2, 16#01};
dec_huffman_lookup(16#65, 16#7) -> {ok, 16#c2, 16#16};
dec_huffman_lookup(16#65, 16#8) -> {more, 16#e0, 16#01};
dec_huffman_lookup(16#65, 16#9) -> {ok, 16#e0, 16#16};
dec_huffman_lookup(16#65, 16#a) -> {more, 16#e2, 16#01};
dec_huffman_lookup(16#65, 16#b) -> {ok, 16#e2, 16#16};
dec_huffman_lookup(16#65, 16#c) -> {ok, 16#99, 16#00};
dec_huffman_lookup(16#65, 16#d) -> {ok, 16#a1, 16#00};
dec_huffman_lookup(16#65, 16#e) -> {ok, 16#a7, 16#00};
dec_huffman_lookup(16#65, 16#f) -> {ok, 16#ac, 16#00};
dec_huffman_lookup(16#66, 16#0) -> {more, 16#83, 16#02};
dec_huffman_lookup(16#66, 16#1) -> {more, 16#83, 16#09};
dec_huffman_lookup(16#66, 16#2) -> {more, 16#83, 16#17};
dec_huffman_lookup(16#66, 16#3) -> {ok, 16#83, 16#28};
dec_huffman_lookup(16#66, 16#4) -> {more, 16#a2, 16#02};
dec_huffman_lookup(16#66, 16#5) -> {more, 16#a2, 16#09};
dec_huffman_lookup(16#66, 16#6) -> {more, 16#a2, 16#17};
dec_huffman_lookup(16#66, 16#7) -> {ok, 16#a2, 16#28};
dec_huffman_lookup(16#66, 16#8) -> {more, 16#b8, 16#02};
dec_huffman_lookup(16#66, 16#9) -> {more, 16#b8, 16#09};
dec_huffman_lookup(16#66, 16#a) -> {more, 16#b8, 16#17};
dec_huffman_lookup(16#66, 16#b) -> {ok, 16#b8, 16#28};
dec_huffman_lookup(16#66, 16#c) -> {more, 16#c2, 16#02};
dec_huffman_lookup(16#66, 16#d) -> {more, 16#c2, 16#09};
dec_huffman_lookup(16#66, 16#e) -> {more, 16#c2, 16#17};
dec_huffman_lookup(16#66, 16#f) -> {ok, 16#c2, 16#28};
dec_huffman_lookup(16#67, 16#0) -> {more, 16#83, 16#03};
dec_huffman_lookup(16#67, 16#1) -> {more, 16#83, 16#06};
dec_huffman_lookup(16#67, 16#2) -> {more, 16#83, 16#0a};
dec_huffman_lookup(16#67, 16#3) -> {more, 16#83, 16#0f};
dec_huffman_lookup(16#67, 16#4) -> {more, 16#83, 16#18};
dec_huffman_lookup(16#67, 16#5) -> {more, 16#83, 16#1f};
dec_huffman_lookup(16#67, 16#6) -> {more, 16#83, 16#29};
dec_huffman_lookup(16#67, 16#7) -> {ok, 16#83, 16#38};
dec_huffman_lookup(16#67, 16#8) -> {more, 16#a2, 16#03};
dec_huffman_lookup(16#67, 16#9) -> {more, 16#a2, 16#06};
dec_huffman_lookup(16#67, 16#a) -> {more, 16#a2, 16#0a};
dec_huffman_lookup(16#67, 16#b) -> {more, 16#a2, 16#0f};
dec_huffman_lookup(16#67, 16#c) -> {more, 16#a2, 16#18};
dec_huffman_lookup(16#67, 16#d) -> {more, 16#a2, 16#1f};
dec_huffman_lookup(16#67, 16#e) -> {more, 16#a2, 16#29};
dec_huffman_lookup(16#67, 16#f) -> {ok, 16#a2, 16#38};
dec_huffman_lookup(16#68, 16#0) -> {more, 16#b8, 16#03};
dec_huffman_lookup(16#68, 16#1) -> {more, 16#b8, 16#06};
dec_huffman_lookup(16#68, 16#2) -> {more, 16#b8, 16#0a};
dec_huffman_lookup(16#68, 16#3) -> {more, 16#b8, 16#0f};
dec_huffman_lookup(16#68, 16#4) -> {more, 16#b8, 16#18};
dec_huffman_lookup(16#68, 16#5) -> {more, 16#b8, 16#1f};
dec_huffman_lookup(16#68, 16#6) -> {more, 16#b8, 16#29};
dec_huffman_lookup(16#68, 16#7) -> {ok, 16#b8, 16#38};
dec_huffman_lookup(16#68, 16#8) -> {more, 16#c2, 16#03};
dec_huffman_lookup(16#68, 16#9) -> {more, 16#c2, 16#06};
dec_huffman_lookup(16#68, 16#a) -> {more, 16#c2, 16#0a};
dec_huffman_lookup(16#68, 16#b) -> {more, 16#c2, 16#0f};
dec_huffman_lookup(16#68, 16#c) -> {more, 16#c2, 16#18};
dec_huffman_lookup(16#68, 16#d) -> {more, 16#c2, 16#1f};
dec_huffman_lookup(16#68, 16#e) -> {more, 16#c2, 16#29};
dec_huffman_lookup(16#68, 16#f) -> {ok, 16#c2, 16#38};
dec_huffman_lookup(16#69, 16#0) -> {more, 16#e0, 16#02};
dec_huffman_lookup(16#69, 16#1) -> {more, 16#e0, 16#09};
dec_huffman_lookup(16#69, 16#2) -> {more, 16#e0, 16#17};
dec_huffman_lookup(16#69, 16#3) -> {ok, 16#e0, 16#28};
dec_huffman_lookup(16#69, 16#4) -> {more, 16#e2, 16#02};
dec_huffman_lookup(16#69, 16#5) -> {more, 16#e2, 16#09};
dec_huffman_lookup(16#69, 16#6) -> {more, 16#e2, 16#17};
dec_huffman_lookup(16#69, 16#7) -> {ok, 16#e2, 16#28};
dec_huffman_lookup(16#69, 16#8) -> {more, 16#99, 16#01};
dec_huffman_lookup(16#69, 16#9) -> {ok, 16#99, 16#16};
dec_huffman_lookup(16#69, 16#a) -> {more, 16#a1, 16#01};
dec_huffman_lookup(16#69, 16#b) -> {ok, 16#a1, 16#16};
dec_huffman_lookup(16#69, 16#c) -> {more, 16#a7, 16#01};
dec_huffman_lookup(16#69, 16#d) -> {ok, 16#a7, 16#16};
dec_huffman_lookup(16#69, 16#e) -> {more, 16#ac, 16#01};
dec_huffman_lookup(16#69, 16#f) -> {ok, 16#ac, 16#16};
dec_huffman_lookup(16#6a, 16#0) -> {more, 16#e0, 16#03};
dec_huffman_lookup(16#6a, 16#1) -> {more, 16#e0, 16#06};
dec_huffman_lookup(16#6a, 16#2) -> {more, 16#e0, 16#0a};
dec_huffman_lookup(16#6a, 16#3) -> {more, 16#e0, 16#0f};
dec_huffman_lookup(16#6a, 16#4) -> {more, 16#e0, 16#18};
dec_huffman_lookup(16#6a, 16#5) -> {more, 16#e0, 16#1f};
dec_huffman_lookup(16#6a, 16#6) -> {more, 16#e0, 16#29};
dec_huffman_lookup(16#6a, 16#7) -> {ok, 16#e0, 16#38};
dec_huffman_lookup(16#6a, 16#8) -> {more, 16#e2, 16#03};
dec_huffman_lookup(16#6a, 16#9) -> {more, 16#e2, 16#06};
dec_huffman_lookup(16#6a, 16#a) -> {more, 16#e2, 16#0a};
dec_huffman_lookup(16#6a, 16#b) -> {more, 16#e2, 16#0f};
dec_huffman_lookup(16#6a, 16#c) -> {more, 16#e2, 16#18};
dec_huffman_lookup(16#6a, 16#d) -> {more, 16#e2, 16#1f};
dec_huffman_lookup(16#6a, 16#e) -> {more, 16#e2, 16#29};
dec_huffman_lookup(16#6a, 16#f) -> {ok, 16#e2, 16#38};
dec_huffman_lookup(16#6b, 16#0) -> {more, 16#99, 16#02};
dec_huffman_lookup(16#6b, 16#1) -> {more, 16#99, 16#09};
dec_huffman_lookup(16#6b, 16#2) -> {more, 16#99, 16#17};
dec_huffman_lookup(16#6b, 16#3) -> {ok, 16#99, 16#28};
dec_huffman_lookup(16#6b, 16#4) -> {more, 16#a1, 16#02};
dec_huffman_lookup(16#6b, 16#5) -> {more, 16#a1, 16#09};
dec_huffman_lookup(16#6b, 16#6) -> {more, 16#a1, 16#17};
dec_huffman_lookup(16#6b, 16#7) -> {ok, 16#a1, 16#28};
dec_huffman_lookup(16#6b, 16#8) -> {more, 16#a7, 16#02};
dec_huffman_lookup(16#6b, 16#9) -> {more, 16#a7, 16#09};
dec_huffman_lookup(16#6b, 16#a) -> {more, 16#a7, 16#17};
dec_huffman_lookup(16#6b, 16#b) -> {ok, 16#a7, 16#28};
dec_huffman_lookup(16#6b, 16#c) -> {more, 16#ac, 16#02};
dec_huffman_lookup(16#6b, 16#d) -> {more, 16#ac, 16#09};
dec_huffman_lookup(16#6b, 16#e) -> {more, 16#ac, 16#17};
dec_huffman_lookup(16#6b, 16#f) -> {ok, 16#ac, 16#28};
dec_huffman_lookup(16#6c, 16#0) -> {more, 16#99, 16#03};
dec_huffman_lookup(16#6c, 16#1) -> {more, 16#99, 16#06};
dec_huffman_lookup(16#6c, 16#2) -> {more, 16#99, 16#0a};
dec_huffman_lookup(16#6c, 16#3) -> {more, 16#99, 16#0f};
dec_huffman_lookup(16#6c, 16#4) -> {more, 16#99, 16#18};
dec_huffman_lookup(16#6c, 16#5) -> {more, 16#99, 16#1f};
dec_huffman_lookup(16#6c, 16#6) -> {more, 16#99, 16#29};
dec_huffman_lookup(16#6c, 16#7) -> {ok, 16#99, 16#38};
dec_huffman_lookup(16#6c, 16#8) -> {more, 16#a1, 16#03};
dec_huffman_lookup(16#6c, 16#9) -> {more, 16#a1, 16#06};
dec_huffman_lookup(16#6c, 16#a) -> {more, 16#a1, 16#0a};
dec_huffman_lookup(16#6c, 16#b) -> {more, 16#a1, 16#0f};
dec_huffman_lookup(16#6c, 16#c) -> {more, 16#a1, 16#18};
dec_huffman_lookup(16#6c, 16#d) -> {more, 16#a1, 16#1f};
dec_huffman_lookup(16#6c, 16#e) -> {more, 16#a1, 16#29};
dec_huffman_lookup(16#6c, 16#f) -> {ok, 16#a1, 16#38};
dec_huffman_lookup(16#6d, 16#0) -> {more, 16#a7, 16#03};
dec_huffman_lookup(16#6d, 16#1) -> {more, 16#a7, 16#06};
dec_huffman_lookup(16#6d, 16#2) -> {more, 16#a7, 16#0a};
dec_huffman_lookup(16#6d, 16#3) -> {more, 16#a7, 16#0f};
dec_huffman_lookup(16#6d, 16#4) -> {more, 16#a7, 16#18};
dec_huffman_lookup(16#6d, 16#5) -> {more, 16#a7, 16#1f};
dec_huffman_lookup(16#6d, 16#6) -> {more, 16#a7, 16#29};
dec_huffman_lookup(16#6d, 16#7) -> {ok, 16#a7, 16#38};
dec_huffman_lookup(16#6d, 16#8) -> {more, 16#ac, 16#03};
dec_huffman_lookup(16#6d, 16#9) -> {more, 16#ac, 16#06};
dec_huffman_lookup(16#6d, 16#a) -> {more, 16#ac, 16#0a};
dec_huffman_lookup(16#6d, 16#b) -> {more, 16#ac, 16#0f};
dec_huffman_lookup(16#6d, 16#c) -> {more, 16#ac, 16#18};
dec_huffman_lookup(16#6d, 16#d) -> {more, 16#ac, 16#1f};
dec_huffman_lookup(16#6d, 16#e) -> {more, 16#ac, 16#29};
dec_huffman_lookup(16#6d, 16#f) -> {ok, 16#ac, 16#38};
dec_huffman_lookup(16#6e, 16#0) -> {more, undefined, 16#72};
dec_huffman_lookup(16#6e, 16#1) -> {more, undefined, 16#73};
dec_huffman_lookup(16#6e, 16#2) -> {more, undefined, 16#75};
dec_huffman_lookup(16#6e, 16#3) -> {more, undefined, 16#76};
dec_huffman_lookup(16#6e, 16#4) -> {more, undefined, 16#79};
dec_huffman_lookup(16#6e, 16#5) -> {more, undefined, 16#7b};
dec_huffman_lookup(16#6e, 16#6) -> {more, undefined, 16#7f};
dec_huffman_lookup(16#6e, 16#7) -> {more, undefined, 16#82};
dec_huffman_lookup(16#6e, 16#8) -> {more, undefined, 16#88};
dec_huffman_lookup(16#6e, 16#9) -> {more, undefined, 16#8b};
dec_huffman_lookup(16#6e, 16#a) -> {more, undefined, 16#8f};
dec_huffman_lookup(16#6e, 16#b) -> {more, undefined, 16#92};
dec_huffman_lookup(16#6e, 16#c) -> {more, undefined, 16#9b};
dec_huffman_lookup(16#6e, 16#d) -> {more, undefined, 16#a2};
dec_huffman_lookup(16#6e, 16#e) -> {more, undefined, 16#aa};
dec_huffman_lookup(16#6e, 16#f) -> {ok, undefined, 16#b4};
dec_huffman_lookup(16#6f, 16#0) -> {ok, 16#b0, 16#00};
dec_huffman_lookup(16#6f, 16#1) -> {ok, 16#b1, 16#00};
dec_huffman_lookup(16#6f, 16#2) -> {ok, 16#b3, 16#00};
dec_huffman_lookup(16#6f, 16#3) -> {ok, 16#d1, 16#00};
dec_huffman_lookup(16#6f, 16#4) -> {ok, 16#d8, 16#00};
dec_huffman_lookup(16#6f, 16#5) -> {ok, 16#d9, 16#00};
dec_huffman_lookup(16#6f, 16#6) -> {ok, 16#e3, 16#00};
dec_huffman_lookup(16#6f, 16#7) -> {ok, 16#e5, 16#00};
dec_huffman_lookup(16#6f, 16#8) -> {ok, 16#e6, 16#00};
dec_huffman_lookup(16#6f, 16#9) -> {more, undefined, 16#7a};
dec_huffman_lookup(16#6f, 16#a) -> {more, undefined, 16#7c};
dec_huffman_lookup(16#6f, 16#b) -> {more, undefined, 16#7d};
dec_huffman_lookup(16#6f, 16#c) -> {more, undefined, 16#80};
dec_huffman_lookup(16#6f, 16#d) -> {more, undefined, 16#81};
dec_huffman_lookup(16#6f, 16#e) -> {more, undefined, 16#83};
dec_huffman_lookup(16#6f, 16#f) -> {more, undefined, 16#84};
dec_huffman_lookup(16#70, 16#0) -> {more, 16#b0, 16#01};
dec_huffman_lookup(16#70, 16#1) -> {ok, 16#b0, 16#16};
dec_huffman_lookup(16#70, 16#2) -> {more, 16#b1, 16#01};
dec_huffman_lookup(16#70, 16#3) -> {ok, 16#b1, 16#16};
dec_huffman_lookup(16#70, 16#4) -> {more, 16#b3, 16#01};
dec_huffman_lookup(16#70, 16#5) -> {ok, 16#b3, 16#16};
dec_huffman_lookup(16#70, 16#6) -> {more, 16#d1, 16#01};
dec_huffman_lookup(16#70, 16#7) -> {ok, 16#d1, 16#16};
dec_huffman_lookup(16#70, 16#8) -> {more, 16#d8, 16#01};
dec_huffman_lookup(16#70, 16#9) -> {ok, 16#d8, 16#16};
dec_huffman_lookup(16#70, 16#a) -> {more, 16#d9, 16#01};
dec_huffman_lookup(16#70, 16#b) -> {ok, 16#d9, 16#16};
dec_huffman_lookup(16#70, 16#c) -> {more, 16#e3, 16#01};
dec_huffman_lookup(16#70, 16#d) -> {ok, 16#e3, 16#16};
dec_huffman_lookup(16#70, 16#e) -> {more, 16#e5, 16#01};
dec_huffman_lookup(16#70, 16#f) -> {ok, 16#e5, 16#16};
dec_huffman_lookup(16#71, 16#0) -> {more, 16#b0, 16#02};
dec_huffman_lookup(16#71, 16#1) -> {more, 16#b0, 16#09};
dec_huffman_lookup(16#71, 16#2) -> {more, 16#b0, 16#17};
dec_huffman_lookup(16#71, 16#3) -> {ok, 16#b0, 16#28};
dec_huffman_lookup(16#71, 16#4) -> {more, 16#b1, 16#02};
dec_huffman_lookup(16#71, 16#5) -> {more, 16#b1, 16#09};
dec_huffman_lookup(16#71, 16#6) -> {more, 16#b1, 16#17};
dec_huffman_lookup(16#71, 16#7) -> {ok, 16#b1, 16#28};
dec_huffman_lookup(16#71, 16#8) -> {more, 16#b3, 16#02};
dec_huffman_lookup(16#71, 16#9) -> {more, 16#b3, 16#09};
dec_huffman_lookup(16#71, 16#a) -> {more, 16#b3, 16#17};
dec_huffman_lookup(16#71, 16#b) -> {ok, 16#b3, 16#28};
dec_huffman_lookup(16#71, 16#c) -> {more, 16#d1, 16#02};
dec_huffman_lookup(16#71, 16#d) -> {more, 16#d1, 16#09};
dec_huffman_lookup(16#71, 16#e) -> {more, 16#d1, 16#17};
dec_huffman_lookup(16#71, 16#f) -> {ok, 16#d1, 16#28};
dec_huffman_lookup(16#72, 16#0) -> {more, 16#b0, 16#03};
dec_huffman_lookup(16#72, 16#1) -> {more, 16#b0, 16#06};
dec_huffman_lookup(16#72, 16#2) -> {more, 16#b0, 16#0a};
dec_huffman_lookup(16#72, 16#3) -> {more, 16#b0, 16#0f};
dec_huffman_lookup(16#72, 16#4) -> {more, 16#b0, 16#18};
dec_huffman_lookup(16#72, 16#5) -> {more, 16#b0, 16#1f};
dec_huffman_lookup(16#72, 16#6) -> {more, 16#b0, 16#29};
dec_huffman_lookup(16#72, 16#7) -> {ok, 16#b0, 16#38};
dec_huffman_lookup(16#72, 16#8) -> {more, 16#b1, 16#03};
dec_huffman_lookup(16#72, 16#9) -> {more, 16#b1, 16#06};
dec_huffman_lookup(16#72, 16#a) -> {more, 16#b1, 16#0a};
dec_huffman_lookup(16#72, 16#b) -> {more, 16#b1, 16#0f};
dec_huffman_lookup(16#72, 16#c) -> {more, 16#b1, 16#18};
dec_huffman_lookup(16#72, 16#d) -> {more, 16#b1, 16#1f};
dec_huffman_lookup(16#72, 16#e) -> {more, 16#b1, 16#29};
dec_huffman_lookup(16#72, 16#f) -> {ok, 16#b1, 16#38};
dec_huffman_lookup(16#73, 16#0) -> {more, 16#b3, 16#03};
dec_huffman_lookup(16#73, 16#1) -> {more, 16#b3, 16#06};
dec_huffman_lookup(16#73, 16#2) -> {more, 16#b3, 16#0a};
dec_huffman_lookup(16#73, 16#3) -> {more, 16#b3, 16#0f};
dec_huffman_lookup(16#73, 16#4) -> {more, 16#b3, 16#18};
dec_huffman_lookup(16#73, 16#5) -> {more, 16#b3, 16#1f};
dec_huffman_lookup(16#73, 16#6) -> {more, 16#b3, 16#29};
dec_huffman_lookup(16#73, 16#7) -> {ok, 16#b3, 16#38};
dec_huffman_lookup(16#73, 16#8) -> {more, 16#d1, 16#03};
dec_huffman_lookup(16#73, 16#9) -> {more, 16#d1, 16#06};
dec_huffman_lookup(16#73, 16#a) -> {more, 16#d1, 16#0a};
dec_huffman_lookup(16#73, 16#b) -> {more, 16#d1, 16#0f};
dec_huffman_lookup(16#73, 16#c) -> {more, 16#d1, 16#18};
dec_huffman_lookup(16#73, 16#d) -> {more, 16#d1, 16#1f};
dec_huffman_lookup(16#73, 16#e) -> {more, 16#d1, 16#29};
dec_huffman_lookup(16#73, 16#f) -> {ok, 16#d1, 16#38};
dec_huffman_lookup(16#74, 16#0) -> {more, 16#d8, 16#02};
dec_huffman_lookup(16#74, 16#1) -> {more, 16#d8, 16#09};
dec_huffman_lookup(16#74, 16#2) -> {more, 16#d8, 16#17};
dec_huffman_lookup(16#74, 16#3) -> {ok, 16#d8, 16#28};
dec_huffman_lookup(16#74, 16#4) -> {more, 16#d9, 16#02};
dec_huffman_lookup(16#74, 16#5) -> {more, 16#d9, 16#09};
dec_huffman_lookup(16#74, 16#6) -> {more, 16#d9, 16#17};
dec_huffman_lookup(16#74, 16#7) -> {ok, 16#d9, 16#28};
dec_huffman_lookup(16#74, 16#8) -> {more, 16#e3, 16#02};
dec_huffman_lookup(16#74, 16#9) -> {more, 16#e3, 16#09};
dec_huffman_lookup(16#74, 16#a) -> {more, 16#e3, 16#17};
dec_huffman_lookup(16#74, 16#b) -> {ok, 16#e3, 16#28};
dec_huffman_lookup(16#74, 16#c) -> {more, 16#e5, 16#02};
dec_huffman_lookup(16#74, 16#d) -> {more, 16#e5, 16#09};
dec_huffman_lookup(16#74, 16#e) -> {more, 16#e5, 16#17};
dec_huffman_lookup(16#74, 16#f) -> {ok, 16#e5, 16#28};
dec_huffman_lookup(16#75, 16#0) -> {more, 16#d8, 16#03};
dec_huffman_lookup(16#75, 16#1) -> {more, 16#d8, 16#06};
dec_huffman_lookup(16#75, 16#2) -> {more, 16#d8, 16#0a};
dec_huffman_lookup(16#75, 16#3) -> {more, 16#d8, 16#0f};
dec_huffman_lookup(16#75, 16#4) -> {more, 16#d8, 16#18};
dec_huffman_lookup(16#75, 16#5) -> {more, 16#d8, 16#1f};
dec_huffman_lookup(16#75, 16#6) -> {more, 16#d8, 16#29};
dec_huffman_lookup(16#75, 16#7) -> {ok, 16#d8, 16#38};
dec_huffman_lookup(16#75, 16#8) -> {more, 16#d9, 16#03};
dec_huffman_lookup(16#75, 16#9) -> {more, 16#d9, 16#06};
dec_huffman_lookup(16#75, 16#a) -> {more, 16#d9, 16#0a};
dec_huffman_lookup(16#75, 16#b) -> {more, 16#d9, 16#0f};
dec_huffman_lookup(16#75, 16#c) -> {more, 16#d9, 16#18};
dec_huffman_lookup(16#75, 16#d) -> {more, 16#d9, 16#1f};
dec_huffman_lookup(16#75, 16#e) -> {more, 16#d9, 16#29};
dec_huffman_lookup(16#75, 16#f) -> {ok, 16#d9, 16#38};
dec_huffman_lookup(16#76, 16#0) -> {more, 16#e3, 16#03};
dec_huffman_lookup(16#76, 16#1) -> {more, 16#e3, 16#06};
dec_huffman_lookup(16#76, 16#2) -> {more, 16#e3, 16#0a};
dec_huffman_lookup(16#76, 16#3) -> {more, 16#e3, 16#0f};
dec_huffman_lookup(16#76, 16#4) -> {more, 16#e3, 16#18};
dec_huffman_lookup(16#76, 16#5) -> {more, 16#e3, 16#1f};
dec_huffman_lookup(16#76, 16#6) -> {more, 16#e3, 16#29};
dec_huffman_lookup(16#76, 16#7) -> {ok, 16#e3, 16#38};
dec_huffman_lookup(16#76, 16#8) -> {more, 16#e5, 16#03};
dec_huffman_lookup(16#76, 16#9) -> {more, 16#e5, 16#06};
dec_huffman_lookup(16#76, 16#a) -> {more, 16#e5, 16#0a};
dec_huffman_lookup(16#76, 16#b) -> {more, 16#e5, 16#0f};
dec_huffman_lookup(16#76, 16#c) -> {more, 16#e5, 16#18};
dec_huffman_lookup(16#76, 16#d) -> {more, 16#e5, 16#1f};
dec_huffman_lookup(16#76, 16#e) -> {more, 16#e5, 16#29};
dec_huffman_lookup(16#76, 16#f) -> {ok, 16#e5, 16#38};
dec_huffman_lookup(16#77, 16#0) -> {more, 16#e6, 16#01};
dec_huffman_lookup(16#77, 16#1) -> {ok, 16#e6, 16#16};
dec_huffman_lookup(16#77, 16#2) -> {ok, 16#81, 16#00};
dec_huffman_lookup(16#77, 16#3) -> {ok, 16#84, 16#00};
dec_huffman_lookup(16#77, 16#4) -> {ok, 16#85, 16#00};
dec_huffman_lookup(16#77, 16#5) -> {ok, 16#86, 16#00};
dec_huffman_lookup(16#77, 16#6) -> {ok, 16#88, 16#00};
dec_huffman_lookup(16#77, 16#7) -> {ok, 16#92, 16#00};
dec_huffman_lookup(16#77, 16#8) -> {ok, 16#9a, 16#00};
dec_huffman_lookup(16#77, 16#9) -> {ok, 16#9c, 16#00};
dec_huffman_lookup(16#77, 16#a) -> {ok, 16#a0, 16#00};
dec_huffman_lookup(16#77, 16#b) -> {ok, 16#a3, 16#00};
dec_huffman_lookup(16#77, 16#c) -> {ok, 16#a4, 16#00};
dec_huffman_lookup(16#77, 16#d) -> {ok, 16#a9, 16#00};
dec_huffman_lookup(16#77, 16#e) -> {ok, 16#aa, 16#00};
dec_huffman_lookup(16#77, 16#f) -> {ok, 16#ad, 16#00};
dec_huffman_lookup(16#78, 16#0) -> {more, 16#e6, 16#02};
dec_huffman_lookup(16#78, 16#1) -> {more, 16#e6, 16#09};
dec_huffman_lookup(16#78, 16#2) -> {more, 16#e6, 16#17};
dec_huffman_lookup(16#78, 16#3) -> {ok, 16#e6, 16#28};
dec_huffman_lookup(16#78, 16#4) -> {more, 16#81, 16#01};
dec_huffman_lookup(16#78, 16#5) -> {ok, 16#81, 16#16};
dec_huffman_lookup(16#78, 16#6) -> {more, 16#84, 16#01};
dec_huffman_lookup(16#78, 16#7) -> {ok, 16#84, 16#16};
dec_huffman_lookup(16#78, 16#8) -> {more, 16#85, 16#01};
dec_huffman_lookup(16#78, 16#9) -> {ok, 16#85, 16#16};
dec_huffman_lookup(16#78, 16#a) -> {more, 16#86, 16#01};
dec_huffman_lookup(16#78, 16#b) -> {ok, 16#86, 16#16};
dec_huffman_lookup(16#78, 16#c) -> {more, 16#88, 16#01};
dec_huffman_lookup(16#78, 16#d) -> {ok, 16#88, 16#16};
dec_huffman_lookup(16#78, 16#e) -> {more, 16#92, 16#01};
dec_huffman_lookup(16#78, 16#f) -> {ok, 16#92, 16#16};
dec_huffman_lookup(16#79, 16#0) -> {more, 16#e6, 16#03};
dec_huffman_lookup(16#79, 16#1) -> {more, 16#e6, 16#06};
dec_huffman_lookup(16#79, 16#2) -> {more, 16#e6, 16#0a};
dec_huffman_lookup(16#79, 16#3) -> {more, 16#e6, 16#0f};
dec_huffman_lookup(16#79, 16#4) -> {more, 16#e6, 16#18};
dec_huffman_lookup(16#79, 16#5) -> {more, 16#e6, 16#1f};
dec_huffman_lookup(16#79, 16#6) -> {more, 16#e6, 16#29};
dec_huffman_lookup(16#79, 16#7) -> {ok, 16#e6, 16#38};
dec_huffman_lookup(16#79, 16#8) -> {more, 16#81, 16#02};
dec_huffman_lookup(16#79, 16#9) -> {more, 16#81, 16#09};
dec_huffman_lookup(16#79, 16#a) -> {more, 16#81, 16#17};
dec_huffman_lookup(16#79, 16#b) -> {ok, 16#81, 16#28};
dec_huffman_lookup(16#79, 16#c) -> {more, 16#84, 16#02};
dec_huffman_lookup(16#79, 16#d) -> {more, 16#84, 16#09};
dec_huffman_lookup(16#79, 16#e) -> {more, 16#84, 16#17};
dec_huffman_lookup(16#79, 16#f) -> {ok, 16#84, 16#28};
dec_huffman_lookup(16#7a, 16#0) -> {more, 16#81, 16#03};
dec_huffman_lookup(16#7a, 16#1) -> {more, 16#81, 16#06};
dec_huffman_lookup(16#7a, 16#2) -> {more, 16#81, 16#0a};
dec_huffman_lookup(16#7a, 16#3) -> {more, 16#81, 16#0f};
dec_huffman_lookup(16#7a, 16#4) -> {more, 16#81, 16#18};
dec_huffman_lookup(16#7a, 16#5) -> {more, 16#81, 16#1f};
dec_huffman_lookup(16#7a, 16#6) -> {more, 16#81, 16#29};
dec_huffman_lookup(16#7a, 16#7) -> {ok, 16#81, 16#38};
dec_huffman_lookup(16#7a, 16#8) -> {more, 16#84, 16#03};
dec_huffman_lookup(16#7a, 16#9) -> {more, 16#84, 16#06};
dec_huffman_lookup(16#7a, 16#a) -> {more, 16#84, 16#0a};
dec_huffman_lookup(16#7a, 16#b) -> {more, 16#84, 16#0f};
dec_huffman_lookup(16#7a, 16#c) -> {more, 16#84, 16#18};
dec_huffman_lookup(16#7a, 16#d) -> {more, 16#84, 16#1f};
dec_huffman_lookup(16#7a, 16#e) -> {more, 16#84, 16#29};
dec_huffman_lookup(16#7a, 16#f) -> {ok, 16#84, 16#38};
dec_huffman_lookup(16#7b, 16#0) -> {more, 16#85, 16#02};
dec_huffman_lookup(16#7b, 16#1) -> {more, 16#85, 16#09};
dec_huffman_lookup(16#7b, 16#2) -> {more, 16#85, 16#17};
dec_huffman_lookup(16#7b, 16#3) -> {ok, 16#85, 16#28};
dec_huffman_lookup(16#7b, 16#4) -> {more, 16#86, 16#02};
dec_huffman_lookup(16#7b, 16#5) -> {more, 16#86, 16#09};
dec_huffman_lookup(16#7b, 16#6) -> {more, 16#86, 16#17};
dec_huffman_lookup(16#7b, 16#7) -> {ok, 16#86, 16#28};
dec_huffman_lookup(16#7b, 16#8) -> {more, 16#88, 16#02};
dec_huffman_lookup(16#7b, 16#9) -> {more, 16#88, 16#09};
dec_huffman_lookup(16#7b, 16#a) -> {more, 16#88, 16#17};
dec_huffman_lookup(16#7b, 16#b) -> {ok, 16#88, 16#28};
dec_huffman_lookup(16#7b, 16#c) -> {more, 16#92, 16#02};
dec_huffman_lookup(16#7b, 16#d) -> {more, 16#92, 16#09};
dec_huffman_lookup(16#7b, 16#e) -> {more, 16#92, 16#17};
dec_huffman_lookup(16#7b, 16#f) -> {ok, 16#92, 16#28};
dec_huffman_lookup(16#7c, 16#0) -> {more, 16#85, 16#03};
dec_huffman_lookup(16#7c, 16#1) -> {more, 16#85, 16#06};
dec_huffman_lookup(16#7c, 16#2) -> {more, 16#85, 16#0a};
dec_huffman_lookup(16#7c, 16#3) -> {more, 16#85, 16#0f};
dec_huffman_lookup(16#7c, 16#4) -> {more, 16#85, 16#18};
dec_huffman_lookup(16#7c, 16#5) -> {more, 16#85, 16#1f};
dec_huffman_lookup(16#7c, 16#6) -> {more, 16#85, 16#29};
dec_huffman_lookup(16#7c, 16#7) -> {ok, 16#85, 16#38};
dec_huffman_lookup(16#7c, 16#8) -> {more, 16#86, 16#03};
dec_huffman_lookup(16#7c, 16#9) -> {more, 16#86, 16#06};
dec_huffman_lookup(16#7c, 16#a) -> {more, 16#86, 16#0a};
dec_huffman_lookup(16#7c, 16#b) -> {more, 16#86, 16#0f};
dec_huffman_lookup(16#7c, 16#c) -> {more, 16#86, 16#18};
dec_huffman_lookup(16#7c, 16#d) -> {more, 16#86, 16#1f};
dec_huffman_lookup(16#7c, 16#e) -> {more, 16#86, 16#29};
dec_huffman_lookup(16#7c, 16#f) -> {ok, 16#86, 16#38};
dec_huffman_lookup(16#7d, 16#0) -> {more, 16#88, 16#03};
dec_huffman_lookup(16#7d, 16#1) -> {more, 16#88, 16#06};
dec_huffman_lookup(16#7d, 16#2) -> {more, 16#88, 16#0a};
dec_huffman_lookup(16#7d, 16#3) -> {more, 16#88, 16#0f};
dec_huffman_lookup(16#7d, 16#4) -> {more, 16#88, 16#18};
dec_huffman_lookup(16#7d, 16#5) -> {more, 16#88, 16#1f};
dec_huffman_lookup(16#7d, 16#6) -> {more, 16#88, 16#29};
dec_huffman_lookup(16#7d, 16#7) -> {ok, 16#88, 16#38};
dec_huffman_lookup(16#7d, 16#8) -> {more, 16#92, 16#03};
dec_huffman_lookup(16#7d, 16#9) -> {more, 16#92, 16#06};
dec_huffman_lookup(16#7d, 16#a) -> {more, 16#92, 16#0a};
dec_huffman_lookup(16#7d, 16#b) -> {more, 16#92, 16#0f};
dec_huffman_lookup(16#7d, 16#c) -> {more, 16#92, 16#18};
dec_huffman_lookup(16#7d, 16#d) -> {more, 16#92, 16#1f};
dec_huffman_lookup(16#7d, 16#e) -> {more, 16#92, 16#29};
dec_huffman_lookup(16#7d, 16#f) -> {ok, 16#92, 16#38};
dec_huffman_lookup(16#7e, 16#0) -> {more, 16#9a, 16#01};
dec_huffman_lookup(16#7e, 16#1) -> {ok, 16#9a, 16#16};
dec_huffman_lookup(16#7e, 16#2) -> {more, 16#9c, 16#01};
dec_huffman_lookup(16#7e, 16#3) -> {ok, 16#9c, 16#16};
dec_huffman_lookup(16#7e, 16#4) -> {more, 16#a0, 16#01};
dec_huffman_lookup(16#7e, 16#5) -> {ok, 16#a0, 16#16};
dec_huffman_lookup(16#7e, 16#6) -> {more, 16#a3, 16#01};
dec_huffman_lookup(16#7e, 16#7) -> {ok, 16#a3, 16#16};
dec_huffman_lookup(16#7e, 16#8) -> {more, 16#a4, 16#01};
dec_huffman_lookup(16#7e, 16#9) -> {ok, 16#a4, 16#16};
dec_huffman_lookup(16#7e, 16#a) -> {more, 16#a9, 16#01};
dec_huffman_lookup(16#7e, 16#b) -> {ok, 16#a9, 16#16};
dec_huffman_lookup(16#7e, 16#c) -> {more, 16#aa, 16#01};
dec_huffman_lookup(16#7e, 16#d) -> {ok, 16#aa, 16#16};
dec_huffman_lookup(16#7e, 16#e) -> {more, 16#ad, 16#01};
dec_huffman_lookup(16#7e, 16#f) -> {ok, 16#ad, 16#16};
dec_huffman_lookup(16#7f, 16#0) -> {more, 16#9a, 16#02};
dec_huffman_lookup(16#7f, 16#1) -> {more, 16#9a, 16#09};
dec_huffman_lookup(16#7f, 16#2) -> {more, 16#9a, 16#17};
dec_huffman_lookup(16#7f, 16#3) -> {ok, 16#9a, 16#28};
dec_huffman_lookup(16#7f, 16#4) -> {more, 16#9c, 16#02};
dec_huffman_lookup(16#7f, 16#5) -> {more, 16#9c, 16#09};
dec_huffman_lookup(16#7f, 16#6) -> {more, 16#9c, 16#17};
dec_huffman_lookup(16#7f, 16#7) -> {ok, 16#9c, 16#28};
dec_huffman_lookup(16#7f, 16#8) -> {more, 16#a0, 16#02};
dec_huffman_lookup(16#7f, 16#9) -> {more, 16#a0, 16#09};
dec_huffman_lookup(16#7f, 16#a) -> {more, 16#a0, 16#17};
dec_huffman_lookup(16#7f, 16#b) -> {ok, 16#a0, 16#28};
dec_huffman_lookup(16#7f, 16#c) -> {more, 16#a3, 16#02};
dec_huffman_lookup(16#7f, 16#d) -> {more, 16#a3, 16#09};
dec_huffman_lookup(16#7f, 16#e) -> {more, 16#a3, 16#17};
dec_huffman_lookup(16#7f, 16#f) -> {ok, 16#a3, 16#28};
dec_huffman_lookup(16#80, 16#0) -> {more, 16#9a, 16#03};
dec_huffman_lookup(16#80, 16#1) -> {more, 16#9a, 16#06};
dec_huffman_lookup(16#80, 16#2) -> {more, 16#9a, 16#0a};
dec_huffman_lookup(16#80, 16#3) -> {more, 16#9a, 16#0f};
dec_huffman_lookup(16#80, 16#4) -> {more, 16#9a, 16#18};
dec_huffman_lookup(16#80, 16#5) -> {more, 16#9a, 16#1f};
dec_huffman_lookup(16#80, 16#6) -> {more, 16#9a, 16#29};
dec_huffman_lookup(16#80, 16#7) -> {ok, 16#9a, 16#38};
dec_huffman_lookup(16#80, 16#8) -> {more, 16#9c, 16#03};
dec_huffman_lookup(16#80, 16#9) -> {more, 16#9c, 16#06};
dec_huffman_lookup(16#80, 16#a) -> {more, 16#9c, 16#0a};
dec_huffman_lookup(16#80, 16#b) -> {more, 16#9c, 16#0f};
dec_huffman_lookup(16#80, 16#c) -> {more, 16#9c, 16#18};
dec_huffman_lookup(16#80, 16#d) -> {more, 16#9c, 16#1f};
dec_huffman_lookup(16#80, 16#e) -> {more, 16#9c, 16#29};
dec_huffman_lookup(16#80, 16#f) -> {ok, 16#9c, 16#38};
dec_huffman_lookup(16#81, 16#0) -> {more, 16#a0, 16#03};
dec_huffman_lookup(16#81, 16#1) -> {more, 16#a0, 16#06};
dec_huffman_lookup(16#81, 16#2) -> {more, 16#a0, 16#0a};
dec_huffman_lookup(16#81, 16#3) -> {more, 16#a0, 16#0f};
dec_huffman_lookup(16#81, 16#4) -> {more, 16#a0, 16#18};
dec_huffman_lookup(16#81, 16#5) -> {more, 16#a0, 16#1f};
dec_huffman_lookup(16#81, 16#6) -> {more, 16#a0, 16#29};
dec_huffman_lookup(16#81, 16#7) -> {ok, 16#a0, 16#38};
dec_huffman_lookup(16#81, 16#8) -> {more, 16#a3, 16#03};
dec_huffman_lookup(16#81, 16#9) -> {more, 16#a3, 16#06};
dec_huffman_lookup(16#81, 16#a) -> {more, 16#a3, 16#0a};
dec_huffman_lookup(16#81, 16#b) -> {more, 16#a3, 16#0f};
dec_huffman_lookup(16#81, 16#c) -> {more, 16#a3, 16#18};
dec_huffman_lookup(16#81, 16#d) -> {more, 16#a3, 16#1f};
dec_huffman_lookup(16#81, 16#e) -> {more, 16#a3, 16#29};
dec_huffman_lookup(16#81, 16#f) -> {ok, 16#a3, 16#38};
dec_huffman_lookup(16#82, 16#0) -> {more, 16#a4, 16#02};
dec_huffman_lookup(16#82, 16#1) -> {more, 16#a4, 16#09};
dec_huffman_lookup(16#82, 16#2) -> {more, 16#a4, 16#17};
dec_huffman_lookup(16#82, 16#3) -> {ok, 16#a4, 16#28};
dec_huffman_lookup(16#82, 16#4) -> {more, 16#a9, 16#02};
dec_huffman_lookup(16#82, 16#5) -> {more, 16#a9, 16#09};
dec_huffman_lookup(16#82, 16#6) -> {more, 16#a9, 16#17};
dec_huffman_lookup(16#82, 16#7) -> {ok, 16#a9, 16#28};
dec_huffman_lookup(16#82, 16#8) -> {more, 16#aa, 16#02};
dec_huffman_lookup(16#82, 16#9) -> {more, 16#aa, 16#09};
dec_huffman_lookup(16#82, 16#a) -> {more, 16#aa, 16#17};
dec_huffman_lookup(16#82, 16#b) -> {ok, 16#aa, 16#28};
dec_huffman_lookup(16#82, 16#c) -> {more, 16#ad, 16#02};
dec_huffman_lookup(16#82, 16#d) -> {more, 16#ad, 16#09};
dec_huffman_lookup(16#82, 16#e) -> {more, 16#ad, 16#17};
dec_huffman_lookup(16#82, 16#f) -> {ok, 16#ad, 16#28};
dec_huffman_lookup(16#83, 16#0) -> {more, 16#a4, 16#03};
dec_huffman_lookup(16#83, 16#1) -> {more, 16#a4, 16#06};
dec_huffman_lookup(16#83, 16#2) -> {more, 16#a4, 16#0a};
dec_huffman_lookup(16#83, 16#3) -> {more, 16#a4, 16#0f};
dec_huffman_lookup(16#83, 16#4) -> {more, 16#a4, 16#18};
dec_huffman_lookup(16#83, 16#5) -> {more, 16#a4, 16#1f};
dec_huffman_lookup(16#83, 16#6) -> {more, 16#a4, 16#29};
dec_huffman_lookup(16#83, 16#7) -> {ok, 16#a4, 16#38};
dec_huffman_lookup(16#83, 16#8) -> {more, 16#a9, 16#03};
dec_huffman_lookup(16#83, 16#9) -> {more, 16#a9, 16#06};
dec_huffman_lookup(16#83, 16#a) -> {more, 16#a9, 16#0a};
dec_huffman_lookup(16#83, 16#b) -> {more, 16#a9, 16#0f};
dec_huffman_lookup(16#83, 16#c) -> {more, 16#a9, 16#18};
dec_huffman_lookup(16#83, 16#d) -> {more, 16#a9, 16#1f};
dec_huffman_lookup(16#83, 16#e) -> {more, 16#a9, 16#29};
dec_huffman_lookup(16#83, 16#f) -> {ok, 16#a9, 16#38};
dec_huffman_lookup(16#84, 16#0) -> {more, 16#aa, 16#03};
dec_huffman_lookup(16#84, 16#1) -> {more, 16#aa, 16#06};
dec_huffman_lookup(16#84, 16#2) -> {more, 16#aa, 16#0a};
dec_huffman_lookup(16#84, 16#3) -> {more, 16#aa, 16#0f};
dec_huffman_lookup(16#84, 16#4) -> {more, 16#aa, 16#18};
dec_huffman_lookup(16#84, 16#5) -> {more, 16#aa, 16#1f};
dec_huffman_lookup(16#84, 16#6) -> {more, 16#aa, 16#29};
dec_huffman_lookup(16#84, 16#7) -> {ok, 16#aa, 16#38};
dec_huffman_lookup(16#84, 16#8) -> {more, 16#ad, 16#03};
dec_huffman_lookup(16#84, 16#9) -> {more, 16#ad, 16#06};
dec_huffman_lookup(16#84, 16#a) -> {more, 16#ad, 16#0a};
dec_huffman_lookup(16#84, 16#b) -> {more, 16#ad, 16#0f};
dec_huffman_lookup(16#84, 16#c) -> {more, 16#ad, 16#18};
dec_huffman_lookup(16#84, 16#d) -> {more, 16#ad, 16#1f};
dec_huffman_lookup(16#84, 16#e) -> {more, 16#ad, 16#29};
dec_huffman_lookup(16#84, 16#f) -> {ok, 16#ad, 16#38};
dec_huffman_lookup(16#85, 16#0) -> {more, undefined, 16#89};
dec_huffman_lookup(16#85, 16#1) -> {more, undefined, 16#8a};
dec_huffman_lookup(16#85, 16#2) -> {more, undefined, 16#8c};
dec_huffman_lookup(16#85, 16#3) -> {more, undefined, 16#8d};
dec_huffman_lookup(16#85, 16#4) -> {more, undefined, 16#90};
dec_huffman_lookup(16#85, 16#5) -> {more, undefined, 16#91};
dec_huffman_lookup(16#85, 16#6) -> {more, undefined, 16#93};
dec_huffman_lookup(16#85, 16#7) -> {more, undefined, 16#96};
dec_huffman_lookup(16#85, 16#8) -> {more, undefined, 16#9c};
dec_huffman_lookup(16#85, 16#9) -> {more, undefined, 16#9f};
dec_huffman_lookup(16#85, 16#a) -> {more, undefined, 16#a3};
dec_huffman_lookup(16#85, 16#b) -> {more, undefined, 16#a6};
dec_huffman_lookup(16#85, 16#c) -> {more, undefined, 16#ab};
dec_huffman_lookup(16#85, 16#d) -> {more, undefined, 16#ae};
dec_huffman_lookup(16#85, 16#e) -> {more, undefined, 16#b5};
dec_huffman_lookup(16#85, 16#f) -> {ok, undefined, 16#be};
dec_huffman_lookup(16#86, 16#0) -> {ok, 16#b2, 16#00};
dec_huffman_lookup(16#86, 16#1) -> {ok, 16#b5, 16#00};
dec_huffman_lookup(16#86, 16#2) -> {ok, 16#b9, 16#00};
dec_huffman_lookup(16#86, 16#3) -> {ok, 16#ba, 16#00};
dec_huffman_lookup(16#86, 16#4) -> {ok, 16#bb, 16#00};
dec_huffman_lookup(16#86, 16#5) -> {ok, 16#bd, 16#00};
dec_huffman_lookup(16#86, 16#6) -> {ok, 16#be, 16#00};
dec_huffman_lookup(16#86, 16#7) -> {ok, 16#c4, 16#00};
dec_huffman_lookup(16#86, 16#8) -> {ok, 16#c6, 16#00};
dec_huffman_lookup(16#86, 16#9) -> {ok, 16#e4, 16#00};
dec_huffman_lookup(16#86, 16#a) -> {ok, 16#e8, 16#00};
dec_huffman_lookup(16#86, 16#b) -> {ok, 16#e9, 16#00};
dec_huffman_lookup(16#86, 16#c) -> {more, undefined, 16#94};
dec_huffman_lookup(16#86, 16#d) -> {more, undefined, 16#95};
dec_huffman_lookup(16#86, 16#e) -> {more, undefined, 16#97};
dec_huffman_lookup(16#86, 16#f) -> {more, undefined, 16#98};
dec_huffman_lookup(16#87, 16#0) -> {more, 16#b2, 16#01};
dec_huffman_lookup(16#87, 16#1) -> {ok, 16#b2, 16#16};
dec_huffman_lookup(16#87, 16#2) -> {more, 16#b5, 16#01};
dec_huffman_lookup(16#87, 16#3) -> {ok, 16#b5, 16#16};
dec_huffman_lookup(16#87, 16#4) -> {more, 16#b9, 16#01};
dec_huffman_lookup(16#87, 16#5) -> {ok, 16#b9, 16#16};
dec_huffman_lookup(16#87, 16#6) -> {more, 16#ba, 16#01};
dec_huffman_lookup(16#87, 16#7) -> {ok, 16#ba, 16#16};
dec_huffman_lookup(16#87, 16#8) -> {more, 16#bb, 16#01};
dec_huffman_lookup(16#87, 16#9) -> {ok, 16#bb, 16#16};
dec_huffman_lookup(16#87, 16#a) -> {more, 16#bd, 16#01};
dec_huffman_lookup(16#87, 16#b) -> {ok, 16#bd, 16#16};
dec_huffman_lookup(16#87, 16#c) -> {more, 16#be, 16#01};
dec_huffman_lookup(16#87, 16#d) -> {ok, 16#be, 16#16};
dec_huffman_lookup(16#87, 16#e) -> {more, 16#c4, 16#01};
dec_huffman_lookup(16#87, 16#f) -> {ok, 16#c4, 16#16};
dec_huffman_lookup(16#88, 16#0) -> {more, 16#b2, 16#02};
dec_huffman_lookup(16#88, 16#1) -> {more, 16#b2, 16#09};
dec_huffman_lookup(16#88, 16#2) -> {more, 16#b2, 16#17};
dec_huffman_lookup(16#88, 16#3) -> {ok, 16#b2, 16#28};
dec_huffman_lookup(16#88, 16#4) -> {more, 16#b5, 16#02};
dec_huffman_lookup(16#88, 16#5) -> {more, 16#b5, 16#09};
dec_huffman_lookup(16#88, 16#6) -> {more, 16#b5, 16#17};
dec_huffman_lookup(16#88, 16#7) -> {ok, 16#b5, 16#28};
dec_huffman_lookup(16#88, 16#8) -> {more, 16#b9, 16#02};
dec_huffman_lookup(16#88, 16#9) -> {more, 16#b9, 16#09};
dec_huffman_lookup(16#88, 16#a) -> {more, 16#b9, 16#17};
dec_huffman_lookup(16#88, 16#b) -> {ok, 16#b9, 16#28};
dec_huffman_lookup(16#88, 16#c) -> {more, 16#ba, 16#02};
dec_huffman_lookup(16#88, 16#d) -> {more, 16#ba, 16#09};
dec_huffman_lookup(16#88, 16#e) -> {more, 16#ba, 16#17};
dec_huffman_lookup(16#88, 16#f) -> {ok, 16#ba, 16#28};
dec_huffman_lookup(16#89, 16#0) -> {more, 16#b2, 16#03};
dec_huffman_lookup(16#89, 16#1) -> {more, 16#b2, 16#06};
dec_huffman_lookup(16#89, 16#2) -> {more, 16#b2, 16#0a};
dec_huffman_lookup(16#89, 16#3) -> {more, 16#b2, 16#0f};
dec_huffman_lookup(16#89, 16#4) -> {more, 16#b2, 16#18};
dec_huffman_lookup(16#89, 16#5) -> {more, 16#b2, 16#1f};
dec_huffman_lookup(16#89, 16#6) -> {more, 16#b2, 16#29};
dec_huffman_lookup(16#89, 16#7) -> {ok, 16#b2, 16#38};
dec_huffman_lookup(16#89, 16#8) -> {more, 16#b5, 16#03};
dec_huffman_lookup(16#89, 16#9) -> {more, 16#b5, 16#06};
dec_huffman_lookup(16#89, 16#a) -> {more, 16#b5, 16#0a};
dec_huffman_lookup(16#89, 16#b) -> {more, 16#b5, 16#0f};
dec_huffman_lookup(16#89, 16#c) -> {more, 16#b5, 16#18};
dec_huffman_lookup(16#89, 16#d) -> {more, 16#b5, 16#1f};
dec_huffman_lookup(16#89, 16#e) -> {more, 16#b5, 16#29};
dec_huffman_lookup(16#89, 16#f) -> {ok, 16#b5, 16#38};
dec_huffman_lookup(16#8a, 16#0) -> {more, 16#b9, 16#03};
dec_huffman_lookup(16#8a, 16#1) -> {more, 16#b9, 16#06};
dec_huffman_lookup(16#8a, 16#2) -> {more, 16#b9, 16#0a};
dec_huffman_lookup(16#8a, 16#3) -> {more, 16#b9, 16#0f};
dec_huffman_lookup(16#8a, 16#4) -> {more, 16#b9, 16#18};
dec_huffman_lookup(16#8a, 16#5) -> {more, 16#b9, 16#1f};
dec_huffman_lookup(16#8a, 16#6) -> {more, 16#b9, 16#29};
dec_huffman_lookup(16#8a, 16#7) -> {ok, 16#b9, 16#38};
dec_huffman_lookup(16#8a, 16#8) -> {more, 16#ba, 16#03};
dec_huffman_lookup(16#8a, 16#9) -> {more, 16#ba, 16#06};
dec_huffman_lookup(16#8a, 16#a) -> {more, 16#ba, 16#0a};
dec_huffman_lookup(16#8a, 16#b) -> {more, 16#ba, 16#0f};
dec_huffman_lookup(16#8a, 16#c) -> {more, 16#ba, 16#18};
dec_huffman_lookup(16#8a, 16#d) -> {more, 16#ba, 16#1f};
dec_huffman_lookup(16#8a, 16#e) -> {more, 16#ba, 16#29};
dec_huffman_lookup(16#8a, 16#f) -> {ok, 16#ba, 16#38};
dec_huffman_lookup(16#8b, 16#0) -> {more, 16#bb, 16#02};
dec_huffman_lookup(16#8b, 16#1) -> {more, 16#bb, 16#09};
dec_huffman_lookup(16#8b, 16#2) -> {more, 16#bb, 16#17};
dec_huffman_lookup(16#8b, 16#3) -> {ok, 16#bb, 16#28};
dec_huffman_lookup(16#8b, 16#4) -> {more, 16#bd, 16#02};
dec_huffman_lookup(16#8b, 16#5) -> {more, 16#bd, 16#09};
dec_huffman_lookup(16#8b, 16#6) -> {more, 16#bd, 16#17};
dec_huffman_lookup(16#8b, 16#7) -> {ok, 16#bd, 16#28};
dec_huffman_lookup(16#8b, 16#8) -> {more, 16#be, 16#02};
dec_huffman_lookup(16#8b, 16#9) -> {more, 16#be, 16#09};
dec_huffman_lookup(16#8b, 16#a) -> {more, 16#be, 16#17};
dec_huffman_lookup(16#8b, 16#b) -> {ok, 16#be, 16#28};
dec_huffman_lookup(16#8b, 16#c) -> {more, 16#c4, 16#02};
dec_huffman_lookup(16#8b, 16#d) -> {more, 16#c4, 16#09};
dec_huffman_lookup(16#8b, 16#e) -> {more, 16#c4, 16#17};
dec_huffman_lookup(16#8b, 16#f) -> {ok, 16#c4, 16#28};
dec_huffman_lookup(16#8c, 16#0) -> {more, 16#bb, 16#03};
dec_huffman_lookup(16#8c, 16#1) -> {more, 16#bb, 16#06};
dec_huffman_lookup(16#8c, 16#2) -> {more, 16#bb, 16#0a};
dec_huffman_lookup(16#8c, 16#3) -> {more, 16#bb, 16#0f};
dec_huffman_lookup(16#8c, 16#4) -> {more, 16#bb, 16#18};
dec_huffman_lookup(16#8c, 16#5) -> {more, 16#bb, 16#1f};
dec_huffman_lookup(16#8c, 16#6) -> {more, 16#bb, 16#29};
dec_huffman_lookup(16#8c, 16#7) -> {ok, 16#bb, 16#38};
dec_huffman_lookup(16#8c, 16#8) -> {more, 16#bd, 16#03};
dec_huffman_lookup(16#8c, 16#9) -> {more, 16#bd, 16#06};
dec_huffman_lookup(16#8c, 16#a) -> {more, 16#bd, 16#0a};
dec_huffman_lookup(16#8c, 16#b) -> {more, 16#bd, 16#0f};
dec_huffman_lookup(16#8c, 16#c) -> {more, 16#bd, 16#18};
dec_huffman_lookup(16#8c, 16#d) -> {more, 16#bd, 16#1f};
dec_huffman_lookup(16#8c, 16#e) -> {more, 16#bd, 16#29};
dec_huffman_lookup(16#8c, 16#f) -> {ok, 16#bd, 16#38};
dec_huffman_lookup(16#8d, 16#0) -> {more, 16#be, 16#03};
dec_huffman_lookup(16#8d, 16#1) -> {more, 16#be, 16#06};
dec_huffman_lookup(16#8d, 16#2) -> {more, 16#be, 16#0a};
dec_huffman_lookup(16#8d, 16#3) -> {more, 16#be, 16#0f};
dec_huffman_lookup(16#8d, 16#4) -> {more, 16#be, 16#18};
dec_huffman_lookup(16#8d, 16#5) -> {more, 16#be, 16#1f};
dec_huffman_lookup(16#8d, 16#6) -> {more, 16#be, 16#29};
dec_huffman_lookup(16#8d, 16#7) -> {ok, 16#be, 16#38};
dec_huffman_lookup(16#8d, 16#8) -> {more, 16#c4, 16#03};
dec_huffman_lookup(16#8d, 16#9) -> {more, 16#c4, 16#06};
dec_huffman_lookup(16#8d, 16#a) -> {more, 16#c4, 16#0a};
dec_huffman_lookup(16#8d, 16#b) -> {more, 16#c4, 16#0f};
dec_huffman_lookup(16#8d, 16#c) -> {more, 16#c4, 16#18};
dec_huffman_lookup(16#8d, 16#d) -> {more, 16#c4, 16#1f};
dec_huffman_lookup(16#8d, 16#e) -> {more, 16#c4, 16#29};
dec_huffman_lookup(16#8d, 16#f) -> {ok, 16#c4, 16#38};
dec_huffman_lookup(16#8e, 16#0) -> {more, 16#c6, 16#01};
dec_huffman_lookup(16#8e, 16#1) -> {ok, 16#c6, 16#16};
dec_huffman_lookup(16#8e, 16#2) -> {more, 16#e4, 16#01};
dec_huffman_lookup(16#8e, 16#3) -> {ok, 16#e4, 16#16};
dec_huffman_lookup(16#8e, 16#4) -> {more, 16#e8, 16#01};
dec_huffman_lookup(16#8e, 16#5) -> {ok, 16#e8, 16#16};
dec_huffman_lookup(16#8e, 16#6) -> {more, 16#e9, 16#01};
dec_huffman_lookup(16#8e, 16#7) -> {ok, 16#e9, 16#16};
dec_huffman_lookup(16#8e, 16#8) -> {ok, 16#01, 16#00};
dec_huffman_lookup(16#8e, 16#9) -> {ok, 16#87, 16#00};
dec_huffman_lookup(16#8e, 16#a) -> {ok, 16#89, 16#00};
dec_huffman_lookup(16#8e, 16#b) -> {ok, 16#8a, 16#00};
dec_huffman_lookup(16#8e, 16#c) -> {ok, 16#8b, 16#00};
dec_huffman_lookup(16#8e, 16#d) -> {ok, 16#8c, 16#00};
dec_huffman_lookup(16#8e, 16#e) -> {ok, 16#8d, 16#00};
dec_huffman_lookup(16#8e, 16#f) -> {ok, 16#8f, 16#00};
dec_huffman_lookup(16#8f, 16#0) -> {more, 16#c6, 16#02};
dec_huffman_lookup(16#8f, 16#1) -> {more, 16#c6, 16#09};
dec_huffman_lookup(16#8f, 16#2) -> {more, 16#c6, 16#17};
dec_huffman_lookup(16#8f, 16#3) -> {ok, 16#c6, 16#28};
dec_huffman_lookup(16#8f, 16#4) -> {more, 16#e4, 16#02};
dec_huffman_lookup(16#8f, 16#5) -> {more, 16#e4, 16#09};
dec_huffman_lookup(16#8f, 16#6) -> {more, 16#e4, 16#17};
dec_huffman_lookup(16#8f, 16#7) -> {ok, 16#e4, 16#28};
dec_huffman_lookup(16#8f, 16#8) -> {more, 16#e8, 16#02};
dec_huffman_lookup(16#8f, 16#9) -> {more, 16#e8, 16#09};
dec_huffman_lookup(16#8f, 16#a) -> {more, 16#e8, 16#17};
dec_huffman_lookup(16#8f, 16#b) -> {ok, 16#e8, 16#28};
dec_huffman_lookup(16#8f, 16#c) -> {more, 16#e9, 16#02};
dec_huffman_lookup(16#8f, 16#d) -> {more, 16#e9, 16#09};
dec_huffman_lookup(16#8f, 16#e) -> {more, 16#e9, 16#17};
dec_huffman_lookup(16#8f, 16#f) -> {ok, 16#e9, 16#28};
dec_huffman_lookup(16#90, 16#0) -> {more, 16#c6, 16#03};
dec_huffman_lookup(16#90, 16#1) -> {more, 16#c6, 16#06};
dec_huffman_lookup(16#90, 16#2) -> {more, 16#c6, 16#0a};
dec_huffman_lookup(16#90, 16#3) -> {more, 16#c6, 16#0f};
dec_huffman_lookup(16#90, 16#4) -> {more, 16#c6, 16#18};
dec_huffman_lookup(16#90, 16#5) -> {more, 16#c6, 16#1f};
dec_huffman_lookup(16#90, 16#6) -> {more, 16#c6, 16#29};
dec_huffman_lookup(16#90, 16#7) -> {ok, 16#c6, 16#38};
dec_huffman_lookup(16#90, 16#8) -> {more, 16#e4, 16#03};
dec_huffman_lookup(16#90, 16#9) -> {more, 16#e4, 16#06};
dec_huffman_lookup(16#90, 16#a) -> {more, 16#e4, 16#0a};
dec_huffman_lookup(16#90, 16#b) -> {more, 16#e4, 16#0f};
dec_huffman_lookup(16#90, 16#c) -> {more, 16#e4, 16#18};
dec_huffman_lookup(16#90, 16#d) -> {more, 16#e4, 16#1f};
dec_huffman_lookup(16#90, 16#e) -> {more, 16#e4, 16#29};
dec_huffman_lookup(16#90, 16#f) -> {ok, 16#e4, 16#38};
dec_huffman_lookup(16#91, 16#0) -> {more, 16#e8, 16#03};
dec_huffman_lookup(16#91, 16#1) -> {more, 16#e8, 16#06};
dec_huffman_lookup(16#91, 16#2) -> {more, 16#e8, 16#0a};
dec_huffman_lookup(16#91, 16#3) -> {more, 16#e8, 16#0f};
dec_huffman_lookup(16#91, 16#4) -> {more, 16#e8, 16#18};
dec_huffman_lookup(16#91, 16#5) -> {more, 16#e8, 16#1f};
dec_huffman_lookup(16#91, 16#6) -> {more, 16#e8, 16#29};
dec_huffman_lookup(16#91, 16#7) -> {ok, 16#e8, 16#38};
dec_huffman_lookup(16#91, 16#8) -> {more, 16#e9, 16#03};
dec_huffman_lookup(16#91, 16#9) -> {more, 16#e9, 16#06};
dec_huffman_lookup(16#91, 16#a) -> {more, 16#e9, 16#0a};
dec_huffman_lookup(16#91, 16#b) -> {more, 16#e9, 16#0f};
dec_huffman_lookup(16#91, 16#c) -> {more, 16#e9, 16#18};
dec_huffman_lookup(16#91, 16#d) -> {more, 16#e9, 16#1f};
dec_huffman_lookup(16#91, 16#e) -> {more, 16#e9, 16#29};
dec_huffman_lookup(16#91, 16#f) -> {ok, 16#e9, 16#38};
dec_huffman_lookup(16#92, 16#0) -> {more, 16#01, 16#01};
dec_huffman_lookup(16#92, 16#1) -> {ok, 16#01, 16#16};
dec_huffman_lookup(16#92, 16#2) -> {more, 16#87, 16#01};
dec_huffman_lookup(16#92, 16#3) -> {ok, 16#87, 16#16};
dec_huffman_lookup(16#92, 16#4) -> {more, 16#89, 16#01};
dec_huffman_lookup(16#92, 16#5) -> {ok, 16#89, 16#16};
dec_huffman_lookup(16#92, 16#6) -> {more, 16#8a, 16#01};
dec_huffman_lookup(16#92, 16#7) -> {ok, 16#8a, 16#16};
dec_huffman_lookup(16#92, 16#8) -> {more, 16#8b, 16#01};
dec_huffman_lookup(16#92, 16#9) -> {ok, 16#8b, 16#16};
dec_huffman_lookup(16#92, 16#a) -> {more, 16#8c, 16#01};
dec_huffman_lookup(16#92, 16#b) -> {ok, 16#8c, 16#16};
dec_huffman_lookup(16#92, 16#c) -> {more, 16#8d, 16#01};
dec_huffman_lookup(16#92, 16#d) -> {ok, 16#8d, 16#16};
dec_huffman_lookup(16#92, 16#e) -> {more, 16#8f, 16#01};
dec_huffman_lookup(16#92, 16#f) -> {ok, 16#8f, 16#16};
dec_huffman_lookup(16#93, 16#0) -> {more, 16#01, 16#02};
dec_huffman_lookup(16#93, 16#1) -> {more, 16#01, 16#09};
dec_huffman_lookup(16#93, 16#2) -> {more, 16#01, 16#17};
dec_huffman_lookup(16#93, 16#3) -> {ok, 16#01, 16#28};
dec_huffman_lookup(16#93, 16#4) -> {more, 16#87, 16#02};
dec_huffman_lookup(16#93, 16#5) -> {more, 16#87, 16#09};
dec_huffman_lookup(16#93, 16#6) -> {more, 16#87, 16#17};
dec_huffman_lookup(16#93, 16#7) -> {ok, 16#87, 16#28};
dec_huffman_lookup(16#93, 16#8) -> {more, 16#89, 16#02};
dec_huffman_lookup(16#93, 16#9) -> {more, 16#89, 16#09};
dec_huffman_lookup(16#93, 16#a) -> {more, 16#89, 16#17};
dec_huffman_lookup(16#93, 16#b) -> {ok, 16#89, 16#28};
dec_huffman_lookup(16#93, 16#c) -> {more, 16#8a, 16#02};
dec_huffman_lookup(16#93, 16#d) -> {more, 16#8a, 16#09};
dec_huffman_lookup(16#93, 16#e) -> {more, 16#8a, 16#17};
dec_huffman_lookup(16#93, 16#f) -> {ok, 16#8a, 16#28};
dec_huffman_lookup(16#94, 16#0) -> {more, 16#01, 16#03};
dec_huffman_lookup(16#94, 16#1) -> {more, 16#01, 16#06};
dec_huffman_lookup(16#94, 16#2) -> {more, 16#01, 16#0a};
dec_huffman_lookup(16#94, 16#3) -> {more, 16#01, 16#0f};
dec_huffman_lookup(16#94, 16#4) -> {more, 16#01, 16#18};
dec_huffman_lookup(16#94, 16#5) -> {more, 16#01, 16#1f};
dec_huffman_lookup(16#94, 16#6) -> {more, 16#01, 16#29};
dec_huffman_lookup(16#94, 16#7) -> {ok, 16#01, 16#38};
dec_huffman_lookup(16#94, 16#8) -> {more, 16#87, 16#03};
dec_huffman_lookup(16#94, 16#9) -> {more, 16#87, 16#06};
dec_huffman_lookup(16#94, 16#a) -> {more, 16#87, 16#0a};
dec_huffman_lookup(16#94, 16#b) -> {more, 16#87, 16#0f};
dec_huffman_lookup(16#94, 16#c) -> {more, 16#87, 16#18};
dec_huffman_lookup(16#94, 16#d) -> {more, 16#87, 16#1f};
dec_huffman_lookup(16#94, 16#e) -> {more, 16#87, 16#29};
dec_huffman_lookup(16#94, 16#f) -> {ok, 16#87, 16#38};
dec_huffman_lookup(16#95, 16#0) -> {more, 16#89, 16#03};
dec_huffman_lookup(16#95, 16#1) -> {more, 16#89, 16#06};
dec_huffman_lookup(16#95, 16#2) -> {more, 16#89, 16#0a};
dec_huffman_lookup(16#95, 16#3) -> {more, 16#89, 16#0f};
dec_huffman_lookup(16#95, 16#4) -> {more, 16#89, 16#18};
dec_huffman_lookup(16#95, 16#5) -> {more, 16#89, 16#1f};
dec_huffman_lookup(16#95, 16#6) -> {more, 16#89, 16#29};
dec_huffman_lookup(16#95, 16#7) -> {ok, 16#89, 16#38};
dec_huffman_lookup(16#95, 16#8) -> {more, 16#8a, 16#03};
dec_huffman_lookup(16#95, 16#9) -> {more, 16#8a, 16#06};
dec_huffman_lookup(16#95, 16#a) -> {more, 16#8a, 16#0a};
dec_huffman_lookup(16#95, 16#b) -> {more, 16#8a, 16#0f};
dec_huffman_lookup(16#95, 16#c) -> {more, 16#8a, 16#18};
dec_huffman_lookup(16#95, 16#d) -> {more, 16#8a, 16#1f};
dec_huffman_lookup(16#95, 16#e) -> {more, 16#8a, 16#29};
dec_huffman_lookup(16#95, 16#f) -> {ok, 16#8a, 16#38};
dec_huffman_lookup(16#96, 16#0) -> {more, 16#8b, 16#02};
dec_huffman_lookup(16#96, 16#1) -> {more, 16#8b, 16#09};
dec_huffman_lookup(16#96, 16#2) -> {more, 16#8b, 16#17};
dec_huffman_lookup(16#96, 16#3) -> {ok, 16#8b, 16#28};
dec_huffman_lookup(16#96, 16#4) -> {more, 16#8c, 16#02};
dec_huffman_lookup(16#96, 16#5) -> {more, 16#8c, 16#09};
dec_huffman_lookup(16#96, 16#6) -> {more, 16#8c, 16#17};
dec_huffman_lookup(16#96, 16#7) -> {ok, 16#8c, 16#28};
dec_huffman_lookup(16#96, 16#8) -> {more, 16#8d, 16#02};
dec_huffman_lookup(16#96, 16#9) -> {more, 16#8d, 16#09};
dec_huffman_lookup(16#96, 16#a) -> {more, 16#8d, 16#17};
dec_huffman_lookup(16#96, 16#b) -> {ok, 16#8d, 16#28};
dec_huffman_lookup(16#96, 16#c) -> {more, 16#8f, 16#02};
dec_huffman_lookup(16#96, 16#d) -> {more, 16#8f, 16#09};
dec_huffman_lookup(16#96, 16#e) -> {more, 16#8f, 16#17};
dec_huffman_lookup(16#96, 16#f) -> {ok, 16#8f, 16#28};
dec_huffman_lookup(16#97, 16#0) -> {more, 16#8b, 16#03};
dec_huffman_lookup(16#97, 16#1) -> {more, 16#8b, 16#06};
dec_huffman_lookup(16#97, 16#2) -> {more, 16#8b, 16#0a};
dec_huffman_lookup(16#97, 16#3) -> {more, 16#8b, 16#0f};
dec_huffman_lookup(16#97, 16#4) -> {more, 16#8b, 16#18};
dec_huffman_lookup(16#97, 16#5) -> {more, 16#8b, 16#1f};
dec_huffman_lookup(16#97, 16#6) -> {more, 16#8b, 16#29};
dec_huffman_lookup(16#97, 16#7) -> {ok, 16#8b, 16#38};
dec_huffman_lookup(16#97, 16#8) -> {more, 16#8c, 16#03};
dec_huffman_lookup(16#97, 16#9) -> {more, 16#8c, 16#06};
dec_huffman_lookup(16#97, 16#a) -> {more, 16#8c, 16#0a};
dec_huffman_lookup(16#97, 16#b) -> {more, 16#8c, 16#0f};
dec_huffman_lookup(16#97, 16#c) -> {more, 16#8c, 16#18};
dec_huffman_lookup(16#97, 16#d) -> {more, 16#8c, 16#1f};
dec_huffman_lookup(16#97, 16#e) -> {more, 16#8c, 16#29};
dec_huffman_lookup(16#97, 16#f) -> {ok, 16#8c, 16#38};
dec_huffman_lookup(16#98, 16#0) -> {more, 16#8d, 16#03};
dec_huffman_lookup(16#98, 16#1) -> {more, 16#8d, 16#06};
dec_huffman_lookup(16#98, 16#2) -> {more, 16#8d, 16#0a};
dec_huffman_lookup(16#98, 16#3) -> {more, 16#8d, 16#0f};
dec_huffman_lookup(16#98, 16#4) -> {more, 16#8d, 16#18};
dec_huffman_lookup(16#98, 16#5) -> {more, 16#8d, 16#1f};
dec_huffman_lookup(16#98, 16#6) -> {more, 16#8d, 16#29};
dec_huffman_lookup(16#98, 16#7) -> {ok, 16#8d, 16#38};
dec_huffman_lookup(16#98, 16#8) -> {more, 16#8f, 16#03};
dec_huffman_lookup(16#98, 16#9) -> {more, 16#8f, 16#06};
dec_huffman_lookup(16#98, 16#a) -> {more, 16#8f, 16#0a};
dec_huffman_lookup(16#98, 16#b) -> {more, 16#8f, 16#0f};
dec_huffman_lookup(16#98, 16#c) -> {more, 16#8f, 16#18};
dec_huffman_lookup(16#98, 16#d) -> {more, 16#8f, 16#1f};
dec_huffman_lookup(16#98, 16#e) -> {more, 16#8f, 16#29};
dec_huffman_lookup(16#98, 16#f) -> {ok, 16#8f, 16#38};
dec_huffman_lookup(16#99, 16#0) -> {more, undefined, 16#9d};
dec_huffman_lookup(16#99, 16#1) -> {more, undefined, 16#9e};
dec_huffman_lookup(16#99, 16#2) -> {more, undefined, 16#a0};
dec_huffman_lookup(16#99, 16#3) -> {more, undefined, 16#a1};
dec_huffman_lookup(16#99, 16#4) -> {more, undefined, 16#a4};
dec_huffman_lookup(16#99, 16#5) -> {more, undefined, 16#a5};
dec_huffman_lookup(16#99, 16#6) -> {more, undefined, 16#a7};
dec_huffman_lookup(16#99, 16#7) -> {more, undefined, 16#a8};
dec_huffman_lookup(16#99, 16#8) -> {more, undefined, 16#ac};
dec_huffman_lookup(16#99, 16#9) -> {more, undefined, 16#ad};
dec_huffman_lookup(16#99, 16#a) -> {more, undefined, 16#af};
dec_huffman_lookup(16#99, 16#b) -> {more, undefined, 16#b1};
dec_huffman_lookup(16#99, 16#c) -> {more, undefined, 16#b6};
dec_huffman_lookup(16#99, 16#d) -> {more, undefined, 16#b9};
dec_huffman_lookup(16#99, 16#e) -> {more, undefined, 16#bf};
dec_huffman_lookup(16#99, 16#f) -> {ok, undefined, 16#cf};
dec_huffman_lookup(16#9a, 16#0) -> {ok, 16#93, 16#00};
dec_huffman_lookup(16#9a, 16#1) -> {ok, 16#95, 16#00};
dec_huffman_lookup(16#9a, 16#2) -> {ok, 16#96, 16#00};
dec_huffman_lookup(16#9a, 16#3) -> {ok, 16#97, 16#00};
dec_huffman_lookup(16#9a, 16#4) -> {ok, 16#98, 16#00};
dec_huffman_lookup(16#9a, 16#5) -> {ok, 16#9b, 16#00};
dec_huffman_lookup(16#9a, 16#6) -> {ok, 16#9d, 16#00};
dec_huffman_lookup(16#9a, 16#7) -> {ok, 16#9e, 16#00};
dec_huffman_lookup(16#9a, 16#8) -> {ok, 16#a5, 16#00};
dec_huffman_lookup(16#9a, 16#9) -> {ok, 16#a6, 16#00};
dec_huffman_lookup(16#9a, 16#a) -> {ok, 16#a8, 16#00};
dec_huffman_lookup(16#9a, 16#b) -> {ok, 16#ae, 16#00};
dec_huffman_lookup(16#9a, 16#c) -> {ok, 16#af, 16#00};
dec_huffman_lookup(16#9a, 16#d) -> {ok, 16#b4, 16#00};
dec_huffman_lookup(16#9a, 16#e) -> {ok, 16#b6, 16#00};
dec_huffman_lookup(16#9a, 16#f) -> {ok, 16#b7, 16#00};
dec_huffman_lookup(16#9b, 16#0) -> {more, 16#93, 16#01};
dec_huffman_lookup(16#9b, 16#1) -> {ok, 16#93, 16#16};
dec_huffman_lookup(16#9b, 16#2) -> {more, 16#95, 16#01};
dec_huffman_lookup(16#9b, 16#3) -> {ok, 16#95, 16#16};
dec_huffman_lookup(16#9b, 16#4) -> {more, 16#96, 16#01};
dec_huffman_lookup(16#9b, 16#5) -> {ok, 16#96, 16#16};
dec_huffman_lookup(16#9b, 16#6) -> {more, 16#97, 16#01};
dec_huffman_lookup(16#9b, 16#7) -> {ok, 16#97, 16#16};
dec_huffman_lookup(16#9b, 16#8) -> {more, 16#98, 16#01};
dec_huffman_lookup(16#9b, 16#9) -> {ok, 16#98, 16#16};
dec_huffman_lookup(16#9b, 16#a) -> {more, 16#9b, 16#01};
dec_huffman_lookup(16#9b, 16#b) -> {ok, 16#9b, 16#16};
dec_huffman_lookup(16#9b, 16#c) -> {more, 16#9d, 16#01};
dec_huffman_lookup(16#9b, 16#d) -> {ok, 16#9d, 16#16};
dec_huffman_lookup(16#9b, 16#e) -> {more, 16#9e, 16#01};
dec_huffman_lookup(16#9b, 16#f) -> {ok, 16#9e, 16#16};
dec_huffman_lookup(16#9c, 16#0) -> {more, 16#93, 16#02};
dec_huffman_lookup(16#9c, 16#1) -> {more, 16#93, 16#09};
dec_huffman_lookup(16#9c, 16#2) -> {more, 16#93, 16#17};
dec_huffman_lookup(16#9c, 16#3) -> {ok, 16#93, 16#28};
dec_huffman_lookup(16#9c, 16#4) -> {more, 16#95, 16#02};
dec_huffman_lookup(16#9c, 16#5) -> {more, 16#95, 16#09};
dec_huffman_lookup(16#9c, 16#6) -> {more, 16#95, 16#17};
dec_huffman_lookup(16#9c, 16#7) -> {ok, 16#95, 16#28};
dec_huffman_lookup(16#9c, 16#8) -> {more, 16#96, 16#02};
dec_huffman_lookup(16#9c, 16#9) -> {more, 16#96, 16#09};
dec_huffman_lookup(16#9c, 16#a) -> {more, 16#96, 16#17};
dec_huffman_lookup(16#9c, 16#b) -> {ok, 16#96, 16#28};
dec_huffman_lookup(16#9c, 16#c) -> {more, 16#97, 16#02};
dec_huffman_lookup(16#9c, 16#d) -> {more, 16#97, 16#09};
dec_huffman_lookup(16#9c, 16#e) -> {more, 16#97, 16#17};
dec_huffman_lookup(16#9c, 16#f) -> {ok, 16#97, 16#28};
dec_huffman_lookup(16#9d, 16#0) -> {more, 16#93, 16#03};
dec_huffman_lookup(16#9d, 16#1) -> {more, 16#93, 16#06};
dec_huffman_lookup(16#9d, 16#2) -> {more, 16#93, 16#0a};
dec_huffman_lookup(16#9d, 16#3) -> {more, 16#93, 16#0f};
dec_huffman_lookup(16#9d, 16#4) -> {more, 16#93, 16#18};
dec_huffman_lookup(16#9d, 16#5) -> {more, 16#93, 16#1f};
dec_huffman_lookup(16#9d, 16#6) -> {more, 16#93, 16#29};
dec_huffman_lookup(16#9d, 16#7) -> {ok, 16#93, 16#38};
dec_huffman_lookup(16#9d, 16#8) -> {more, 16#95, 16#03};
dec_huffman_lookup(16#9d, 16#9) -> {more, 16#95, 16#06};
dec_huffman_lookup(16#9d, 16#a) -> {more, 16#95, 16#0a};
dec_huffman_lookup(16#9d, 16#b) -> {more, 16#95, 16#0f};
dec_huffman_lookup(16#9d, 16#c) -> {more, 16#95, 16#18};
dec_huffman_lookup(16#9d, 16#d) -> {more, 16#95, 16#1f};
dec_huffman_lookup(16#9d, 16#e) -> {more, 16#95, 16#29};
dec_huffman_lookup(16#9d, 16#f) -> {ok, 16#95, 16#38};
dec_huffman_lookup(16#9e, 16#0) -> {more, 16#96, 16#03};
dec_huffman_lookup(16#9e, 16#1) -> {more, 16#96, 16#06};
dec_huffman_lookup(16#9e, 16#2) -> {more, 16#96, 16#0a};
dec_huffman_lookup(16#9e, 16#3) -> {more, 16#96, 16#0f};
dec_huffman_lookup(16#9e, 16#4) -> {more, 16#96, 16#18};
dec_huffman_lookup(16#9e, 16#5) -> {more, 16#96, 16#1f};
dec_huffman_lookup(16#9e, 16#6) -> {more, 16#96, 16#29};
dec_huffman_lookup(16#9e, 16#7) -> {ok, 16#96, 16#38};
dec_huffman_lookup(16#9e, 16#8) -> {more, 16#97, 16#03};
dec_huffman_lookup(16#9e, 16#9) -> {more, 16#97, 16#06};
dec_huffman_lookup(16#9e, 16#a) -> {more, 16#97, 16#0a};
dec_huffman_lookup(16#9e, 16#b) -> {more, 16#97, 16#0f};
dec_huffman_lookup(16#9e, 16#c) -> {more, 16#97, 16#18};
dec_huffman_lookup(16#9e, 16#d) -> {more, 16#97, 16#1f};
dec_huffman_lookup(16#9e, 16#e) -> {more, 16#97, 16#29};
dec_huffman_lookup(16#9e, 16#f) -> {ok, 16#97, 16#38};
dec_huffman_lookup(16#9f, 16#0) -> {more, 16#98, 16#02};
dec_huffman_lookup(16#9f, 16#1) -> {more, 16#98, 16#09};
dec_huffman_lookup(16#9f, 16#2) -> {more, 16#98, 16#17};
dec_huffman_lookup(16#9f, 16#3) -> {ok, 16#98, 16#28};
dec_huffman_lookup(16#9f, 16#4) -> {more, 16#9b, 16#02};
dec_huffman_lookup(16#9f, 16#5) -> {more, 16#9b, 16#09};
dec_huffman_lookup(16#9f, 16#6) -> {more, 16#9b, 16#17};
dec_huffman_lookup(16#9f, 16#7) -> {ok, 16#9b, 16#28};
dec_huffman_lookup(16#9f, 16#8) -> {more, 16#9d, 16#02};
dec_huffman_lookup(16#9f, 16#9) -> {more, 16#9d, 16#09};
dec_huffman_lookup(16#9f, 16#a) -> {more, 16#9d, 16#17};
dec_huffman_lookup(16#9f, 16#b) -> {ok, 16#9d, 16#28};
dec_huffman_lookup(16#9f, 16#c) -> {more, 16#9e, 16#02};
dec_huffman_lookup(16#9f, 16#d) -> {more, 16#9e, 16#09};
dec_huffman_lookup(16#9f, 16#e) -> {more, 16#9e, 16#17};
dec_huffman_lookup(16#9f, 16#f) -> {ok, 16#9e, 16#28};
dec_huffman_lookup(16#a0, 16#0) -> {more, 16#98, 16#03};
dec_huffman_lookup(16#a0, 16#1) -> {more, 16#98, 16#06};
dec_huffman_lookup(16#a0, 16#2) -> {more, 16#98, 16#0a};
dec_huffman_lookup(16#a0, 16#3) -> {more, 16#98, 16#0f};
dec_huffman_lookup(16#a0, 16#4) -> {more, 16#98, 16#18};
dec_huffman_lookup(16#a0, 16#5) -> {more, 16#98, 16#1f};
dec_huffman_lookup(16#a0, 16#6) -> {more, 16#98, 16#29};
dec_huffman_lookup(16#a0, 16#7) -> {ok, 16#98, 16#38};
dec_huffman_lookup(16#a0, 16#8) -> {more, 16#9b, 16#03};
dec_huffman_lookup(16#a0, 16#9) -> {more, 16#9b, 16#06};
dec_huffman_lookup(16#a0, 16#a) -> {more, 16#9b, 16#0a};
dec_huffman_lookup(16#a0, 16#b) -> {more, 16#9b, 16#0f};
dec_huffman_lookup(16#a0, 16#c) -> {more, 16#9b, 16#18};
dec_huffman_lookup(16#a0, 16#d) -> {more, 16#9b, 16#1f};
dec_huffman_lookup(16#a0, 16#e) -> {more, 16#9b, 16#29};
dec_huffman_lookup(16#a0, 16#f) -> {ok, 16#9b, 16#38};
dec_huffman_lookup(16#a1, 16#0) -> {more, 16#9d, 16#03};
dec_huffman_lookup(16#a1, 16#1) -> {more, 16#9d, 16#06};
dec_huffman_lookup(16#a1, 16#2) -> {more, 16#9d, 16#0a};
dec_huffman_lookup(16#a1, 16#3) -> {more, 16#9d, 16#0f};
dec_huffman_lookup(16#a1, 16#4) -> {more, 16#9d, 16#18};
dec_huffman_lookup(16#a1, 16#5) -> {more, 16#9d, 16#1f};
dec_huffman_lookup(16#a1, 16#6) -> {more, 16#9d, 16#29};
dec_huffman_lookup(16#a1, 16#7) -> {ok, 16#9d, 16#38};
dec_huffman_lookup(16#a1, 16#8) -> {more, 16#9e, 16#03};
dec_huffman_lookup(16#a1, 16#9) -> {more, 16#9e, 16#06};
dec_huffman_lookup(16#a1, 16#a) -> {more, 16#9e, 16#0a};
dec_huffman_lookup(16#a1, 16#b) -> {more, 16#9e, 16#0f};
dec_huffman_lookup(16#a1, 16#c) -> {more, 16#9e, 16#18};
dec_huffman_lookup(16#a1, 16#d) -> {more, 16#9e, 16#1f};
dec_huffman_lookup(16#a1, 16#e) -> {more, 16#9e, 16#29};
dec_huffman_lookup(16#a1, 16#f) -> {ok, 16#9e, 16#38};
dec_huffman_lookup(16#a2, 16#0) -> {more, 16#a5, 16#01};
dec_huffman_lookup(16#a2, 16#1) -> {ok, 16#a5, 16#16};
dec_huffman_lookup(16#a2, 16#2) -> {more, 16#a6, 16#01};
dec_huffman_lookup(16#a2, 16#3) -> {ok, 16#a6, 16#16};
dec_huffman_lookup(16#a2, 16#4) -> {more, 16#a8, 16#01};
dec_huffman_lookup(16#a2, 16#5) -> {ok, 16#a8, 16#16};
dec_huffman_lookup(16#a2, 16#6) -> {more, 16#ae, 16#01};
dec_huffman_lookup(16#a2, 16#7) -> {ok, 16#ae, 16#16};
dec_huffman_lookup(16#a2, 16#8) -> {more, 16#af, 16#01};
dec_huffman_lookup(16#a2, 16#9) -> {ok, 16#af, 16#16};
dec_huffman_lookup(16#a2, 16#a) -> {more, 16#b4, 16#01};
dec_huffman_lookup(16#a2, 16#b) -> {ok, 16#b4, 16#16};
dec_huffman_lookup(16#a2, 16#c) -> {more, 16#b6, 16#01};
dec_huffman_lookup(16#a2, 16#d) -> {ok, 16#b6, 16#16};
dec_huffman_lookup(16#a2, 16#e) -> {more, 16#b7, 16#01};
dec_huffman_lookup(16#a2, 16#f) -> {ok, 16#b7, 16#16};
dec_huffman_lookup(16#a3, 16#0) -> {more, 16#a5, 16#02};
dec_huffman_lookup(16#a3, 16#1) -> {more, 16#a5, 16#09};
dec_huffman_lookup(16#a3, 16#2) -> {more, 16#a5, 16#17};
dec_huffman_lookup(16#a3, 16#3) -> {ok, 16#a5, 16#28};
dec_huffman_lookup(16#a3, 16#4) -> {more, 16#a6, 16#02};
dec_huffman_lookup(16#a3, 16#5) -> {more, 16#a6, 16#09};
dec_huffman_lookup(16#a3, 16#6) -> {more, 16#a6, 16#17};
dec_huffman_lookup(16#a3, 16#7) -> {ok, 16#a6, 16#28};
dec_huffman_lookup(16#a3, 16#8) -> {more, 16#a8, 16#02};
dec_huffman_lookup(16#a3, 16#9) -> {more, 16#a8, 16#09};
dec_huffman_lookup(16#a3, 16#a) -> {more, 16#a8, 16#17};
dec_huffman_lookup(16#a3, 16#b) -> {ok, 16#a8, 16#28};
dec_huffman_lookup(16#a3, 16#c) -> {more, 16#ae, 16#02};
dec_huffman_lookup(16#a3, 16#d) -> {more, 16#ae, 16#09};
dec_huffman_lookup(16#a3, 16#e) -> {more, 16#ae, 16#17};
dec_huffman_lookup(16#a3, 16#f) -> {ok, 16#ae, 16#28};
dec_huffman_lookup(16#a4, 16#0) -> {more, 16#a5, 16#03};
dec_huffman_lookup(16#a4, 16#1) -> {more, 16#a5, 16#06};
dec_huffman_lookup(16#a4, 16#2) -> {more, 16#a5, 16#0a};
dec_huffman_lookup(16#a4, 16#3) -> {more, 16#a5, 16#0f};
dec_huffman_lookup(16#a4, 16#4) -> {more, 16#a5, 16#18};
dec_huffman_lookup(16#a4, 16#5) -> {more, 16#a5, 16#1f};
dec_huffman_lookup(16#a4, 16#6) -> {more, 16#a5, 16#29};
dec_huffman_lookup(16#a4, 16#7) -> {ok, 16#a5, 16#38};
dec_huffman_lookup(16#a4, 16#8) -> {more, 16#a6, 16#03};
dec_huffman_lookup(16#a4, 16#9) -> {more, 16#a6, 16#06};
dec_huffman_lookup(16#a4, 16#a) -> {more, 16#a6, 16#0a};
dec_huffman_lookup(16#a4, 16#b) -> {more, 16#a6, 16#0f};
dec_huffman_lookup(16#a4, 16#c) -> {more, 16#a6, 16#18};
dec_huffman_lookup(16#a4, 16#d) -> {more, 16#a6, 16#1f};
dec_huffman_lookup(16#a4, 16#e) -> {more, 16#a6, 16#29};
dec_huffman_lookup(16#a4, 16#f) -> {ok, 16#a6, 16#38};
dec_huffman_lookup(16#a5, 16#0) -> {more, 16#a8, 16#03};
dec_huffman_lookup(16#a5, 16#1) -> {more, 16#a8, 16#06};
dec_huffman_lookup(16#a5, 16#2) -> {more, 16#a8, 16#0a};
dec_huffman_lookup(16#a5, 16#3) -> {more, 16#a8, 16#0f};
dec_huffman_lookup(16#a5, 16#4) -> {more, 16#a8, 16#18};
dec_huffman_lookup(16#a5, 16#5) -> {more, 16#a8, 16#1f};
dec_huffman_lookup(16#a5, 16#6) -> {more, 16#a8, 16#29};
dec_huffman_lookup(16#a5, 16#7) -> {ok, 16#a8, 16#38};
dec_huffman_lookup(16#a5, 16#8) -> {more, 16#ae, 16#03};
dec_huffman_lookup(16#a5, 16#9) -> {more, 16#ae, 16#06};
dec_huffman_lookup(16#a5, 16#a) -> {more, 16#ae, 16#0a};
dec_huffman_lookup(16#a5, 16#b) -> {more, 16#ae, 16#0f};
dec_huffman_lookup(16#a5, 16#c) -> {more, 16#ae, 16#18};
dec_huffman_lookup(16#a5, 16#d) -> {more, 16#ae, 16#1f};
dec_huffman_lookup(16#a5, 16#e) -> {more, 16#ae, 16#29};
dec_huffman_lookup(16#a5, 16#f) -> {ok, 16#ae, 16#38};
dec_huffman_lookup(16#a6, 16#0) -> {more, 16#af, 16#02};
dec_huffman_lookup(16#a6, 16#1) -> {more, 16#af, 16#09};
dec_huffman_lookup(16#a6, 16#2) -> {more, 16#af, 16#17};
dec_huffman_lookup(16#a6, 16#3) -> {ok, 16#af, 16#28};
dec_huffman_lookup(16#a6, 16#4) -> {more, 16#b4, 16#02};
dec_huffman_lookup(16#a6, 16#5) -> {more, 16#b4, 16#09};
dec_huffman_lookup(16#a6, 16#6) -> {more, 16#b4, 16#17};
dec_huffman_lookup(16#a6, 16#7) -> {ok, 16#b4, 16#28};
dec_huffman_lookup(16#a6, 16#8) -> {more, 16#b6, 16#02};
dec_huffman_lookup(16#a6, 16#9) -> {more, 16#b6, 16#09};
dec_huffman_lookup(16#a6, 16#a) -> {more, 16#b6, 16#17};
dec_huffman_lookup(16#a6, 16#b) -> {ok, 16#b6, 16#28};
dec_huffman_lookup(16#a6, 16#c) -> {more, 16#b7, 16#02};
dec_huffman_lookup(16#a6, 16#d) -> {more, 16#b7, 16#09};
dec_huffman_lookup(16#a6, 16#e) -> {more, 16#b7, 16#17};
dec_huffman_lookup(16#a6, 16#f) -> {ok, 16#b7, 16#28};
dec_huffman_lookup(16#a7, 16#0) -> {more, 16#af, 16#03};
dec_huffman_lookup(16#a7, 16#1) -> {more, 16#af, 16#06};
dec_huffman_lookup(16#a7, 16#2) -> {more, 16#af, 16#0a};
dec_huffman_lookup(16#a7, 16#3) -> {more, 16#af, 16#0f};
dec_huffman_lookup(16#a7, 16#4) -> {more, 16#af, 16#18};
dec_huffman_lookup(16#a7, 16#5) -> {more, 16#af, 16#1f};
dec_huffman_lookup(16#a7, 16#6) -> {more, 16#af, 16#29};
dec_huffman_lookup(16#a7, 16#7) -> {ok, 16#af, 16#38};
dec_huffman_lookup(16#a7, 16#8) -> {more, 16#b4, 16#03};
dec_huffman_lookup(16#a7, 16#9) -> {more, 16#b4, 16#06};
dec_huffman_lookup(16#a7, 16#a) -> {more, 16#b4, 16#0a};
dec_huffman_lookup(16#a7, 16#b) -> {more, 16#b4, 16#0f};
dec_huffman_lookup(16#a7, 16#c) -> {more, 16#b4, 16#18};
dec_huffman_lookup(16#a7, 16#d) -> {more, 16#b4, 16#1f};
dec_huffman_lookup(16#a7, 16#e) -> {more, 16#b4, 16#29};
dec_huffman_lookup(16#a7, 16#f) -> {ok, 16#b4, 16#38};
dec_huffman_lookup(16#a8, 16#0) -> {more, 16#b6, 16#03};
dec_huffman_lookup(16#a8, 16#1) -> {more, 16#b6, 16#06};
dec_huffman_lookup(16#a8, 16#2) -> {more, 16#b6, 16#0a};
dec_huffman_lookup(16#a8, 16#3) -> {more, 16#b6, 16#0f};
dec_huffman_lookup(16#a8, 16#4) -> {more, 16#b6, 16#18};
dec_huffman_lookup(16#a8, 16#5) -> {more, 16#b6, 16#1f};
dec_huffman_lookup(16#a8, 16#6) -> {more, 16#b6, 16#29};
dec_huffman_lookup(16#a8, 16#7) -> {ok, 16#b6, 16#38};
dec_huffman_lookup(16#a8, 16#8) -> {more, 16#b7, 16#03};
dec_huffman_lookup(16#a8, 16#9) -> {more, 16#b7, 16#06};
dec_huffman_lookup(16#a8, 16#a) -> {more, 16#b7, 16#0a};
dec_huffman_lookup(16#a8, 16#b) -> {more, 16#b7, 16#0f};
dec_huffman_lookup(16#a8, 16#c) -> {more, 16#b7, 16#18};
dec_huffman_lookup(16#a8, 16#d) -> {more, 16#b7, 16#1f};
dec_huffman_lookup(16#a8, 16#e) -> {more, 16#b7, 16#29};
dec_huffman_lookup(16#a8, 16#f) -> {ok, 16#b7, 16#38};
dec_huffman_lookup(16#a9, 16#0) -> {ok, 16#bc, 16#00};
dec_huffman_lookup(16#a9, 16#1) -> {ok, 16#bf, 16#00};
dec_huffman_lookup(16#a9, 16#2) -> {ok, 16#c5, 16#00};
dec_huffman_lookup(16#a9, 16#3) -> {ok, 16#e7, 16#00};
dec_huffman_lookup(16#a9, 16#4) -> {ok, 16#ef, 16#00};
dec_huffman_lookup(16#a9, 16#5) -> {more, undefined, 16#b0};
dec_huffman_lookup(16#a9, 16#6) -> {more, undefined, 16#b2};
dec_huffman_lookup(16#a9, 16#7) -> {more, undefined, 16#b3};
dec_huffman_lookup(16#a9, 16#8) -> {more, undefined, 16#b7};
dec_huffman_lookup(16#a9, 16#9) -> {more, undefined, 16#b8};
dec_huffman_lookup(16#a9, 16#a) -> {more, undefined, 16#ba};
dec_huffman_lookup(16#a9, 16#b) -> {more, undefined, 16#bb};
dec_huffman_lookup(16#a9, 16#c) -> {more, undefined, 16#c0};
dec_huffman_lookup(16#a9, 16#d) -> {more, undefined, 16#c7};
dec_huffman_lookup(16#a9, 16#e) -> {more, undefined, 16#d0};
dec_huffman_lookup(16#a9, 16#f) -> {ok, undefined, 16#df};
dec_huffman_lookup(16#aa, 16#0) -> {more, 16#bc, 16#01};
dec_huffman_lookup(16#aa, 16#1) -> {ok, 16#bc, 16#16};
dec_huffman_lookup(16#aa, 16#2) -> {more, 16#bf, 16#01};
dec_huffman_lookup(16#aa, 16#3) -> {ok, 16#bf, 16#16};
dec_huffman_lookup(16#aa, 16#4) -> {more, 16#c5, 16#01};
dec_huffman_lookup(16#aa, 16#5) -> {ok, 16#c5, 16#16};
dec_huffman_lookup(16#aa, 16#6) -> {more, 16#e7, 16#01};
dec_huffman_lookup(16#aa, 16#7) -> {ok, 16#e7, 16#16};
dec_huffman_lookup(16#aa, 16#8) -> {more, 16#ef, 16#01};
dec_huffman_lookup(16#aa, 16#9) -> {ok, 16#ef, 16#16};
dec_huffman_lookup(16#aa, 16#a) -> {ok, 16#09, 16#00};
dec_huffman_lookup(16#aa, 16#b) -> {ok, 16#8e, 16#00};
dec_huffman_lookup(16#aa, 16#c) -> {ok, 16#90, 16#00};
dec_huffman_lookup(16#aa, 16#d) -> {ok, 16#91, 16#00};
dec_huffman_lookup(16#aa, 16#e) -> {ok, 16#94, 16#00};
dec_huffman_lookup(16#aa, 16#f) -> {ok, 16#9f, 16#00};
dec_huffman_lookup(16#ab, 16#0) -> {more, 16#bc, 16#02};
dec_huffman_lookup(16#ab, 16#1) -> {more, 16#bc, 16#09};
dec_huffman_lookup(16#ab, 16#2) -> {more, 16#bc, 16#17};
dec_huffman_lookup(16#ab, 16#3) -> {ok, 16#bc, 16#28};
dec_huffman_lookup(16#ab, 16#4) -> {more, 16#bf, 16#02};
dec_huffman_lookup(16#ab, 16#5) -> {more, 16#bf, 16#09};
dec_huffman_lookup(16#ab, 16#6) -> {more, 16#bf, 16#17};
dec_huffman_lookup(16#ab, 16#7) -> {ok, 16#bf, 16#28};
dec_huffman_lookup(16#ab, 16#8) -> {more, 16#c5, 16#02};
dec_huffman_lookup(16#ab, 16#9) -> {more, 16#c5, 16#09};
dec_huffman_lookup(16#ab, 16#a) -> {more, 16#c5, 16#17};
dec_huffman_lookup(16#ab, 16#b) -> {ok, 16#c5, 16#28};
dec_huffman_lookup(16#ab, 16#c) -> {more, 16#e7, 16#02};
dec_huffman_lookup(16#ab, 16#d) -> {more, 16#e7, 16#09};
dec_huffman_lookup(16#ab, 16#e) -> {more, 16#e7, 16#17};
dec_huffman_lookup(16#ab, 16#f) -> {ok, 16#e7, 16#28};
dec_huffman_lookup(16#ac, 16#0) -> {more, 16#bc, 16#03};
dec_huffman_lookup(16#ac, 16#1) -> {more, 16#bc, 16#06};
dec_huffman_lookup(16#ac, 16#2) -> {more, 16#bc, 16#0a};
dec_huffman_lookup(16#ac, 16#3) -> {more, 16#bc, 16#0f};
dec_huffman_lookup(16#ac, 16#4) -> {more, 16#bc, 16#18};
dec_huffman_lookup(16#ac, 16#5) -> {more, 16#bc, 16#1f};
dec_huffman_lookup(16#ac, 16#6) -> {more, 16#bc, 16#29};
dec_huffman_lookup(16#ac, 16#7) -> {ok, 16#bc, 16#38};
dec_huffman_lookup(16#ac, 16#8) -> {more, 16#bf, 16#03};
dec_huffman_lookup(16#ac, 16#9) -> {more, 16#bf, 16#06};
dec_huffman_lookup(16#ac, 16#a) -> {more, 16#bf, 16#0a};
dec_huffman_lookup(16#ac, 16#b) -> {more, 16#bf, 16#0f};
dec_huffman_lookup(16#ac, 16#c) -> {more, 16#bf, 16#18};
dec_huffman_lookup(16#ac, 16#d) -> {more, 16#bf, 16#1f};
dec_huffman_lookup(16#ac, 16#e) -> {more, 16#bf, 16#29};
dec_huffman_lookup(16#ac, 16#f) -> {ok, 16#bf, 16#38};
dec_huffman_lookup(16#ad, 16#0) -> {more, 16#c5, 16#03};
dec_huffman_lookup(16#ad, 16#1) -> {more, 16#c5, 16#06};
dec_huffman_lookup(16#ad, 16#2) -> {more, 16#c5, 16#0a};
dec_huffman_lookup(16#ad, 16#3) -> {more, 16#c5, 16#0f};
dec_huffman_lookup(16#ad, 16#4) -> {more, 16#c5, 16#18};
dec_huffman_lookup(16#ad, 16#5) -> {more, 16#c5, 16#1f};
dec_huffman_lookup(16#ad, 16#6) -> {more, 16#c5, 16#29};
dec_huffman_lookup(16#ad, 16#7) -> {ok, 16#c5, 16#38};
dec_huffman_lookup(16#ad, 16#8) -> {more, 16#e7, 16#03};
dec_huffman_lookup(16#ad, 16#9) -> {more, 16#e7, 16#06};
dec_huffman_lookup(16#ad, 16#a) -> {more, 16#e7, 16#0a};
dec_huffman_lookup(16#ad, 16#b) -> {more, 16#e7, 16#0f};
dec_huffman_lookup(16#ad, 16#c) -> {more, 16#e7, 16#18};
dec_huffman_lookup(16#ad, 16#d) -> {more, 16#e7, 16#1f};
dec_huffman_lookup(16#ad, 16#e) -> {more, 16#e7, 16#29};
dec_huffman_lookup(16#ad, 16#f) -> {ok, 16#e7, 16#38};
dec_huffman_lookup(16#ae, 16#0) -> {more, 16#ef, 16#02};
dec_huffman_lookup(16#ae, 16#1) -> {more, 16#ef, 16#09};
dec_huffman_lookup(16#ae, 16#2) -> {more, 16#ef, 16#17};
dec_huffman_lookup(16#ae, 16#3) -> {ok, 16#ef, 16#28};
dec_huffman_lookup(16#ae, 16#4) -> {more, 16#09, 16#01};
dec_huffman_lookup(16#ae, 16#5) -> {ok, 16#09, 16#16};
dec_huffman_lookup(16#ae, 16#6) -> {more, 16#8e, 16#01};
dec_huffman_lookup(16#ae, 16#7) -> {ok, 16#8e, 16#16};
dec_huffman_lookup(16#ae, 16#8) -> {more, 16#90, 16#01};
dec_huffman_lookup(16#ae, 16#9) -> {ok, 16#90, 16#16};
dec_huffman_lookup(16#ae, 16#a) -> {more, 16#91, 16#01};
dec_huffman_lookup(16#ae, 16#b) -> {ok, 16#91, 16#16};
dec_huffman_lookup(16#ae, 16#c) -> {more, 16#94, 16#01};
dec_huffman_lookup(16#ae, 16#d) -> {ok, 16#94, 16#16};
dec_huffman_lookup(16#ae, 16#e) -> {more, 16#9f, 16#01};
dec_huffman_lookup(16#ae, 16#f) -> {ok, 16#9f, 16#16};
dec_huffman_lookup(16#af, 16#0) -> {more, 16#ef, 16#03};
dec_huffman_lookup(16#af, 16#1) -> {more, 16#ef, 16#06};
dec_huffman_lookup(16#af, 16#2) -> {more, 16#ef, 16#0a};
dec_huffman_lookup(16#af, 16#3) -> {more, 16#ef, 16#0f};
dec_huffman_lookup(16#af, 16#4) -> {more, 16#ef, 16#18};
dec_huffman_lookup(16#af, 16#5) -> {more, 16#ef, 16#1f};
dec_huffman_lookup(16#af, 16#6) -> {more, 16#ef, 16#29};
dec_huffman_lookup(16#af, 16#7) -> {ok, 16#ef, 16#38};
dec_huffman_lookup(16#af, 16#8) -> {more, 16#09, 16#02};
dec_huffman_lookup(16#af, 16#9) -> {more, 16#09, 16#09};
dec_huffman_lookup(16#af, 16#a) -> {more, 16#09, 16#17};
dec_huffman_lookup(16#af, 16#b) -> {ok, 16#09, 16#28};
dec_huffman_lookup(16#af, 16#c) -> {more, 16#8e, 16#02};
dec_huffman_lookup(16#af, 16#d) -> {more, 16#8e, 16#09};
dec_huffman_lookup(16#af, 16#e) -> {more, 16#8e, 16#17};
dec_huffman_lookup(16#af, 16#f) -> {ok, 16#8e, 16#28};
dec_huffman_lookup(16#b0, 16#0) -> {more, 16#09, 16#03};
dec_huffman_lookup(16#b0, 16#1) -> {more, 16#09, 16#06};
dec_huffman_lookup(16#b0, 16#2) -> {more, 16#09, 16#0a};
dec_huffman_lookup(16#b0, 16#3) -> {more, 16#09, 16#0f};
dec_huffman_lookup(16#b0, 16#4) -> {more, 16#09, 16#18};
dec_huffman_lookup(16#b0, 16#5) -> {more, 16#09, 16#1f};
dec_huffman_lookup(16#b0, 16#6) -> {more, 16#09, 16#29};
dec_huffman_lookup(16#b0, 16#7) -> {ok, 16#09, 16#38};
dec_huffman_lookup(16#b0, 16#8) -> {more, 16#8e, 16#03};
dec_huffman_lookup(16#b0, 16#9) -> {more, 16#8e, 16#06};
dec_huffman_lookup(16#b0, 16#a) -> {more, 16#8e, 16#0a};
dec_huffman_lookup(16#b0, 16#b) -> {more, 16#8e, 16#0f};
dec_huffman_lookup(16#b0, 16#c) -> {more, 16#8e, 16#18};
dec_huffman_lookup(16#b0, 16#d) -> {more, 16#8e, 16#1f};
dec_huffman_lookup(16#b0, 16#e) -> {more, 16#8e, 16#29};
dec_huffman_lookup(16#b0, 16#f) -> {ok, 16#8e, 16#38};
dec_huffman_lookup(16#b1, 16#0) -> {more, 16#90, 16#02};
dec_huffman_lookup(16#b1, 16#1) -> {more, 16#90, 16#09};
dec_huffman_lookup(16#b1, 16#2) -> {more, 16#90, 16#17};
dec_huffman_lookup(16#b1, 16#3) -> {ok, 16#90, 16#28};
dec_huffman_lookup(16#b1, 16#4) -> {more, 16#91, 16#02};
dec_huffman_lookup(16#b1, 16#5) -> {more, 16#91, 16#09};
dec_huffman_lookup(16#b1, 16#6) -> {more, 16#91, 16#17};
dec_huffman_lookup(16#b1, 16#7) -> {ok, 16#91, 16#28};
dec_huffman_lookup(16#b1, 16#8) -> {more, 16#94, 16#02};
dec_huffman_lookup(16#b1, 16#9) -> {more, 16#94, 16#09};
dec_huffman_lookup(16#b1, 16#a) -> {more, 16#94, 16#17};
dec_huffman_lookup(16#b1, 16#b) -> {ok, 16#94, 16#28};
dec_huffman_lookup(16#b1, 16#c) -> {more, 16#9f, 16#02};
dec_huffman_lookup(16#b1, 16#d) -> {more, 16#9f, 16#09};
dec_huffman_lookup(16#b1, 16#e) -> {more, 16#9f, 16#17};
dec_huffman_lookup(16#b1, 16#f) -> {ok, 16#9f, 16#28};
dec_huffman_lookup(16#b2, 16#0) -> {more, 16#90, 16#03};
dec_huffman_lookup(16#b2, 16#1) -> {more, 16#90, 16#06};
dec_huffman_lookup(16#b2, 16#2) -> {more, 16#90, 16#0a};
dec_huffman_lookup(16#b2, 16#3) -> {more, 16#90, 16#0f};
dec_huffman_lookup(16#b2, 16#4) -> {more, 16#90, 16#18};
dec_huffman_lookup(16#b2, 16#5) -> {more, 16#90, 16#1f};
dec_huffman_lookup(16#b2, 16#6) -> {more, 16#90, 16#29};
dec_huffman_lookup(16#b2, 16#7) -> {ok, 16#90, 16#38};
dec_huffman_lookup(16#b2, 16#8) -> {more, 16#91, 16#03};
dec_huffman_lookup(16#b2, 16#9) -> {more, 16#91, 16#06};
dec_huffman_lookup(16#b2, 16#a) -> {more, 16#91, 16#0a};
dec_huffman_lookup(16#b2, 16#b) -> {more, 16#91, 16#0f};
dec_huffman_lookup(16#b2, 16#c) -> {more, 16#91, 16#18};
dec_huffman_lookup(16#b2, 16#d) -> {more, 16#91, 16#1f};
dec_huffman_lookup(16#b2, 16#e) -> {more, 16#91, 16#29};
dec_huffman_lookup(16#b2, 16#f) -> {ok, 16#91, 16#38};
dec_huffman_lookup(16#b3, 16#0) -> {more, 16#94, 16#03};
dec_huffman_lookup(16#b3, 16#1) -> {more, 16#94, 16#06};
dec_huffman_lookup(16#b3, 16#2) -> {more, 16#94, 16#0a};
dec_huffman_lookup(16#b3, 16#3) -> {more, 16#94, 16#0f};
dec_huffman_lookup(16#b3, 16#4) -> {more, 16#94, 16#18};
dec_huffman_lookup(16#b3, 16#5) -> {more, 16#94, 16#1f};
dec_huffman_lookup(16#b3, 16#6) -> {more, 16#94, 16#29};
dec_huffman_lookup(16#b3, 16#7) -> {ok, 16#94, 16#38};
dec_huffman_lookup(16#b3, 16#8) -> {more, 16#9f, 16#03};
dec_huffman_lookup(16#b3, 16#9) -> {more, 16#9f, 16#06};
dec_huffman_lookup(16#b3, 16#a) -> {more, 16#9f, 16#0a};
dec_huffman_lookup(16#b3, 16#b) -> {more, 16#9f, 16#0f};
dec_huffman_lookup(16#b3, 16#c) -> {more, 16#9f, 16#18};
dec_huffman_lookup(16#b3, 16#d) -> {more, 16#9f, 16#1f};
dec_huffman_lookup(16#b3, 16#e) -> {more, 16#9f, 16#29};
dec_huffman_lookup(16#b3, 16#f) -> {ok, 16#9f, 16#38};
dec_huffman_lookup(16#b4, 16#0) -> {ok, 16#ab, 16#00};
dec_huffman_lookup(16#b4, 16#1) -> {ok, 16#ce, 16#00};
dec_huffman_lookup(16#b4, 16#2) -> {ok, 16#d7, 16#00};
dec_huffman_lookup(16#b4, 16#3) -> {ok, 16#e1, 16#00};
dec_huffman_lookup(16#b4, 16#4) -> {ok, 16#ec, 16#00};
dec_huffman_lookup(16#b4, 16#5) -> {ok, 16#ed, 16#00};
dec_huffman_lookup(16#b4, 16#6) -> {more, undefined, 16#bc};
dec_huffman_lookup(16#b4, 16#7) -> {more, undefined, 16#bd};
dec_huffman_lookup(16#b4, 16#8) -> {more, undefined, 16#c1};
dec_huffman_lookup(16#b4, 16#9) -> {more, undefined, 16#c4};
dec_huffman_lookup(16#b4, 16#a) -> {more, undefined, 16#c8};
dec_huffman_lookup(16#b4, 16#b) -> {more, undefined, 16#cb};
dec_huffman_lookup(16#b4, 16#c) -> {more, undefined, 16#d1};
dec_huffman_lookup(16#b4, 16#d) -> {more, undefined, 16#d8};
dec_huffman_lookup(16#b4, 16#e) -> {more, undefined, 16#e0};
dec_huffman_lookup(16#b4, 16#f) -> {ok, undefined, 16#ee};
dec_huffman_lookup(16#b5, 16#0) -> {more, 16#ab, 16#01};
dec_huffman_lookup(16#b5, 16#1) -> {ok, 16#ab, 16#16};
dec_huffman_lookup(16#b5, 16#2) -> {more, 16#ce, 16#01};
dec_huffman_lookup(16#b5, 16#3) -> {ok, 16#ce, 16#16};
dec_huffman_lookup(16#b5, 16#4) -> {more, 16#d7, 16#01};
dec_huffman_lookup(16#b5, 16#5) -> {ok, 16#d7, 16#16};
dec_huffman_lookup(16#b5, 16#6) -> {more, 16#e1, 16#01};
dec_huffman_lookup(16#b5, 16#7) -> {ok, 16#e1, 16#16};
dec_huffman_lookup(16#b5, 16#8) -> {more, 16#ec, 16#01};
dec_huffman_lookup(16#b5, 16#9) -> {ok, 16#ec, 16#16};
dec_huffman_lookup(16#b5, 16#a) -> {more, 16#ed, 16#01};
dec_huffman_lookup(16#b5, 16#b) -> {ok, 16#ed, 16#16};
dec_huffman_lookup(16#b5, 16#c) -> {ok, 16#c7, 16#00};
dec_huffman_lookup(16#b5, 16#d) -> {ok, 16#cf, 16#00};
dec_huffman_lookup(16#b5, 16#e) -> {ok, 16#ea, 16#00};
dec_huffman_lookup(16#b5, 16#f) -> {ok, 16#eb, 16#00};
dec_huffman_lookup(16#b6, 16#0) -> {more, 16#ab, 16#02};
dec_huffman_lookup(16#b6, 16#1) -> {more, 16#ab, 16#09};
dec_huffman_lookup(16#b6, 16#2) -> {more, 16#ab, 16#17};
dec_huffman_lookup(16#b6, 16#3) -> {ok, 16#ab, 16#28};
dec_huffman_lookup(16#b6, 16#4) -> {more, 16#ce, 16#02};
dec_huffman_lookup(16#b6, 16#5) -> {more, 16#ce, 16#09};
dec_huffman_lookup(16#b6, 16#6) -> {more, 16#ce, 16#17};
dec_huffman_lookup(16#b6, 16#7) -> {ok, 16#ce, 16#28};
dec_huffman_lookup(16#b6, 16#8) -> {more, 16#d7, 16#02};
dec_huffman_lookup(16#b6, 16#9) -> {more, 16#d7, 16#09};
dec_huffman_lookup(16#b6, 16#a) -> {more, 16#d7, 16#17};
dec_huffman_lookup(16#b6, 16#b) -> {ok, 16#d7, 16#28};
dec_huffman_lookup(16#b6, 16#c) -> {more, 16#e1, 16#02};
dec_huffman_lookup(16#b6, 16#d) -> {more, 16#e1, 16#09};
dec_huffman_lookup(16#b6, 16#e) -> {more, 16#e1, 16#17};
dec_huffman_lookup(16#b6, 16#f) -> {ok, 16#e1, 16#28};
dec_huffman_lookup(16#b7, 16#0) -> {more, 16#ab, 16#03};
dec_huffman_lookup(16#b7, 16#1) -> {more, 16#ab, 16#06};
dec_huffman_lookup(16#b7, 16#2) -> {more, 16#ab, 16#0a};
dec_huffman_lookup(16#b7, 16#3) -> {more, 16#ab, 16#0f};
dec_huffman_lookup(16#b7, 16#4) -> {more, 16#ab, 16#18};
dec_huffman_lookup(16#b7, 16#5) -> {more, 16#ab, 16#1f};
dec_huffman_lookup(16#b7, 16#6) -> {more, 16#ab, 16#29};
dec_huffman_lookup(16#b7, 16#7) -> {ok, 16#ab, 16#38};
dec_huffman_lookup(16#b7, 16#8) -> {more, 16#ce, 16#03};
dec_huffman_lookup(16#b7, 16#9) -> {more, 16#ce, 16#06};
dec_huffman_lookup(16#b7, 16#a) -> {more, 16#ce, 16#0a};
dec_huffman_lookup(16#b7, 16#b) -> {more, 16#ce, 16#0f};
dec_huffman_lookup(16#b7, 16#c) -> {more, 16#ce, 16#18};
dec_huffman_lookup(16#b7, 16#d) -> {more, 16#ce, 16#1f};
dec_huffman_lookup(16#b7, 16#e) -> {more, 16#ce, 16#29};
dec_huffman_lookup(16#b7, 16#f) -> {ok, 16#ce, 16#38};
dec_huffman_lookup(16#b8, 16#0) -> {more, 16#d7, 16#03};
dec_huffman_lookup(16#b8, 16#1) -> {more, 16#d7, 16#06};
dec_huffman_lookup(16#b8, 16#2) -> {more, 16#d7, 16#0a};
dec_huffman_lookup(16#b8, 16#3) -> {more, 16#d7, 16#0f};
dec_huffman_lookup(16#b8, 16#4) -> {more, 16#d7, 16#18};
dec_huffman_lookup(16#b8, 16#5) -> {more, 16#d7, 16#1f};
dec_huffman_lookup(16#b8, 16#6) -> {more, 16#d7, 16#29};
dec_huffman_lookup(16#b8, 16#7) -> {ok, 16#d7, 16#38};
dec_huffman_lookup(16#b8, 16#8) -> {more, 16#e1, 16#03};
dec_huffman_lookup(16#b8, 16#9) -> {more, 16#e1, 16#06};
dec_huffman_lookup(16#b8, 16#a) -> {more, 16#e1, 16#0a};
dec_huffman_lookup(16#b8, 16#b) -> {more, 16#e1, 16#0f};
dec_huffman_lookup(16#b8, 16#c) -> {more, 16#e1, 16#18};
dec_huffman_lookup(16#b8, 16#d) -> {more, 16#e1, 16#1f};
dec_huffman_lookup(16#b8, 16#e) -> {more, 16#e1, 16#29};
dec_huffman_lookup(16#b8, 16#f) -> {ok, 16#e1, 16#38};
dec_huffman_lookup(16#b9, 16#0) -> {more, 16#ec, 16#02};
dec_huffman_lookup(16#b9, 16#1) -> {more, 16#ec, 16#09};
dec_huffman_lookup(16#b9, 16#2) -> {more, 16#ec, 16#17};
dec_huffman_lookup(16#b9, 16#3) -> {ok, 16#ec, 16#28};
dec_huffman_lookup(16#b9, 16#4) -> {more, 16#ed, 16#02};
dec_huffman_lookup(16#b9, 16#5) -> {more, 16#ed, 16#09};
dec_huffman_lookup(16#b9, 16#6) -> {more, 16#ed, 16#17};
dec_huffman_lookup(16#b9, 16#7) -> {ok, 16#ed, 16#28};
dec_huffman_lookup(16#b9, 16#8) -> {more, 16#c7, 16#01};
dec_huffman_lookup(16#b9, 16#9) -> {ok, 16#c7, 16#16};
dec_huffman_lookup(16#b9, 16#a) -> {more, 16#cf, 16#01};
dec_huffman_lookup(16#b9, 16#b) -> {ok, 16#cf, 16#16};
dec_huffman_lookup(16#b9, 16#c) -> {more, 16#ea, 16#01};
dec_huffman_lookup(16#b9, 16#d) -> {ok, 16#ea, 16#16};
dec_huffman_lookup(16#b9, 16#e) -> {more, 16#eb, 16#01};
dec_huffman_lookup(16#b9, 16#f) -> {ok, 16#eb, 16#16};
dec_huffman_lookup(16#ba, 16#0) -> {more, 16#ec, 16#03};
dec_huffman_lookup(16#ba, 16#1) -> {more, 16#ec, 16#06};
dec_huffman_lookup(16#ba, 16#2) -> {more, 16#ec, 16#0a};
dec_huffman_lookup(16#ba, 16#3) -> {more, 16#ec, 16#0f};
dec_huffman_lookup(16#ba, 16#4) -> {more, 16#ec, 16#18};
dec_huffman_lookup(16#ba, 16#5) -> {more, 16#ec, 16#1f};
dec_huffman_lookup(16#ba, 16#6) -> {more, 16#ec, 16#29};
dec_huffman_lookup(16#ba, 16#7) -> {ok, 16#ec, 16#38};
dec_huffman_lookup(16#ba, 16#8) -> {more, 16#ed, 16#03};
dec_huffman_lookup(16#ba, 16#9) -> {more, 16#ed, 16#06};
dec_huffman_lookup(16#ba, 16#a) -> {more, 16#ed, 16#0a};
dec_huffman_lookup(16#ba, 16#b) -> {more, 16#ed, 16#0f};
dec_huffman_lookup(16#ba, 16#c) -> {more, 16#ed, 16#18};
dec_huffman_lookup(16#ba, 16#d) -> {more, 16#ed, 16#1f};
dec_huffman_lookup(16#ba, 16#e) -> {more, 16#ed, 16#29};
dec_huffman_lookup(16#ba, 16#f) -> {ok, 16#ed, 16#38};
dec_huffman_lookup(16#bb, 16#0) -> {more, 16#c7, 16#02};
dec_huffman_lookup(16#bb, 16#1) -> {more, 16#c7, 16#09};
dec_huffman_lookup(16#bb, 16#2) -> {more, 16#c7, 16#17};
dec_huffman_lookup(16#bb, 16#3) -> {ok, 16#c7, 16#28};
dec_huffman_lookup(16#bb, 16#4) -> {more, 16#cf, 16#02};
dec_huffman_lookup(16#bb, 16#5) -> {more, 16#cf, 16#09};
dec_huffman_lookup(16#bb, 16#6) -> {more, 16#cf, 16#17};
dec_huffman_lookup(16#bb, 16#7) -> {ok, 16#cf, 16#28};
dec_huffman_lookup(16#bb, 16#8) -> {more, 16#ea, 16#02};
dec_huffman_lookup(16#bb, 16#9) -> {more, 16#ea, 16#09};
dec_huffman_lookup(16#bb, 16#a) -> {more, 16#ea, 16#17};
dec_huffman_lookup(16#bb, 16#b) -> {ok, 16#ea, 16#28};
dec_huffman_lookup(16#bb, 16#c) -> {more, 16#eb, 16#02};
dec_huffman_lookup(16#bb, 16#d) -> {more, 16#eb, 16#09};
dec_huffman_lookup(16#bb, 16#e) -> {more, 16#eb, 16#17};
dec_huffman_lookup(16#bb, 16#f) -> {ok, 16#eb, 16#28};
dec_huffman_lookup(16#bc, 16#0) -> {more, 16#c7, 16#03};
dec_huffman_lookup(16#bc, 16#1) -> {more, 16#c7, 16#06};
dec_huffman_lookup(16#bc, 16#2) -> {more, 16#c7, 16#0a};
dec_huffman_lookup(16#bc, 16#3) -> {more, 16#c7, 16#0f};
dec_huffman_lookup(16#bc, 16#4) -> {more, 16#c7, 16#18};
dec_huffman_lookup(16#bc, 16#5) -> {more, 16#c7, 16#1f};
dec_huffman_lookup(16#bc, 16#6) -> {more, 16#c7, 16#29};
dec_huffman_lookup(16#bc, 16#7) -> {ok, 16#c7, 16#38};
dec_huffman_lookup(16#bc, 16#8) -> {more, 16#cf, 16#03};
dec_huffman_lookup(16#bc, 16#9) -> {more, 16#cf, 16#06};
dec_huffman_lookup(16#bc, 16#a) -> {more, 16#cf, 16#0a};
dec_huffman_lookup(16#bc, 16#b) -> {more, 16#cf, 16#0f};
dec_huffman_lookup(16#bc, 16#c) -> {more, 16#cf, 16#18};
dec_huffman_lookup(16#bc, 16#d) -> {more, 16#cf, 16#1f};
dec_huffman_lookup(16#bc, 16#e) -> {more, 16#cf, 16#29};
dec_huffman_lookup(16#bc, 16#f) -> {ok, 16#cf, 16#38};
dec_huffman_lookup(16#bd, 16#0) -> {more, 16#ea, 16#03};
dec_huffman_lookup(16#bd, 16#1) -> {more, 16#ea, 16#06};
dec_huffman_lookup(16#bd, 16#2) -> {more, 16#ea, 16#0a};
dec_huffman_lookup(16#bd, 16#3) -> {more, 16#ea, 16#0f};
dec_huffman_lookup(16#bd, 16#4) -> {more, 16#ea, 16#18};
dec_huffman_lookup(16#bd, 16#5) -> {more, 16#ea, 16#1f};
dec_huffman_lookup(16#bd, 16#6) -> {more, 16#ea, 16#29};
dec_huffman_lookup(16#bd, 16#7) -> {ok, 16#ea, 16#38};
dec_huffman_lookup(16#bd, 16#8) -> {more, 16#eb, 16#03};
dec_huffman_lookup(16#bd, 16#9) -> {more, 16#eb, 16#06};
dec_huffman_lookup(16#bd, 16#a) -> {more, 16#eb, 16#0a};
dec_huffman_lookup(16#bd, 16#b) -> {more, 16#eb, 16#0f};
dec_huffman_lookup(16#bd, 16#c) -> {more, 16#eb, 16#18};
dec_huffman_lookup(16#bd, 16#d) -> {more, 16#eb, 16#1f};
dec_huffman_lookup(16#bd, 16#e) -> {more, 16#eb, 16#29};
dec_huffman_lookup(16#bd, 16#f) -> {ok, 16#eb, 16#38};
dec_huffman_lookup(16#be, 16#0) -> {more, undefined, 16#c2};
dec_huffman_lookup(16#be, 16#1) -> {more, undefined, 16#c3};
dec_huffman_lookup(16#be, 16#2) -> {more, undefined, 16#c5};
dec_huffman_lookup(16#be, 16#3) -> {more, undefined, 16#c6};
dec_huffman_lookup(16#be, 16#4) -> {more, undefined, 16#c9};
dec_huffman_lookup(16#be, 16#5) -> {more, undefined, 16#ca};
dec_huffman_lookup(16#be, 16#6) -> {more, undefined, 16#cc};
dec_huffman_lookup(16#be, 16#7) -> {more, undefined, 16#cd};
dec_huffman_lookup(16#be, 16#8) -> {more, undefined, 16#d2};
dec_huffman_lookup(16#be, 16#9) -> {more, undefined, 16#d5};
dec_huffman_lookup(16#be, 16#a) -> {more, undefined, 16#d9};
dec_huffman_lookup(16#be, 16#b) -> {more, undefined, 16#dc};
dec_huffman_lookup(16#be, 16#c) -> {more, undefined, 16#e1};
dec_huffman_lookup(16#be, 16#d) -> {more, undefined, 16#e7};
dec_huffman_lookup(16#be, 16#e) -> {more, undefined, 16#ef};
dec_huffman_lookup(16#be, 16#f) -> {ok, undefined, 16#f6};
dec_huffman_lookup(16#bf, 16#0) -> {ok, 16#c0, 16#00};
dec_huffman_lookup(16#bf, 16#1) -> {ok, 16#c1, 16#00};
dec_huffman_lookup(16#bf, 16#2) -> {ok, 16#c8, 16#00};
dec_huffman_lookup(16#bf, 16#3) -> {ok, 16#c9, 16#00};
dec_huffman_lookup(16#bf, 16#4) -> {ok, 16#ca, 16#00};
dec_huffman_lookup(16#bf, 16#5) -> {ok, 16#cd, 16#00};
dec_huffman_lookup(16#bf, 16#6) -> {ok, 16#d2, 16#00};
dec_huffman_lookup(16#bf, 16#7) -> {ok, 16#d5, 16#00};
dec_huffman_lookup(16#bf, 16#8) -> {ok, 16#da, 16#00};
dec_huffman_lookup(16#bf, 16#9) -> {ok, 16#db, 16#00};
dec_huffman_lookup(16#bf, 16#a) -> {ok, 16#ee, 16#00};
dec_huffman_lookup(16#bf, 16#b) -> {ok, 16#f0, 16#00};
dec_huffman_lookup(16#bf, 16#c) -> {ok, 16#f2, 16#00};
dec_huffman_lookup(16#bf, 16#d) -> {ok, 16#f3, 16#00};
dec_huffman_lookup(16#bf, 16#e) -> {ok, 16#ff, 16#00};
dec_huffman_lookup(16#bf, 16#f) -> {more, undefined, 16#ce};
dec_huffman_lookup(16#c0, 16#0) -> {more, 16#c0, 16#01};
dec_huffman_lookup(16#c0, 16#1) -> {ok, 16#c0, 16#16};
dec_huffman_lookup(16#c0, 16#2) -> {more, 16#c1, 16#01};
dec_huffman_lookup(16#c0, 16#3) -> {ok, 16#c1, 16#16};
dec_huffman_lookup(16#c0, 16#4) -> {more, 16#c8, 16#01};
dec_huffman_lookup(16#c0, 16#5) -> {ok, 16#c8, 16#16};
dec_huffman_lookup(16#c0, 16#6) -> {more, 16#c9, 16#01};
dec_huffman_lookup(16#c0, 16#7) -> {ok, 16#c9, 16#16};
dec_huffman_lookup(16#c0, 16#8) -> {more, 16#ca, 16#01};
dec_huffman_lookup(16#c0, 16#9) -> {ok, 16#ca, 16#16};
dec_huffman_lookup(16#c0, 16#a) -> {more, 16#cd, 16#01};
dec_huffman_lookup(16#c0, 16#b) -> {ok, 16#cd, 16#16};
dec_huffman_lookup(16#c0, 16#c) -> {more, 16#d2, 16#01};
dec_huffman_lookup(16#c0, 16#d) -> {ok, 16#d2, 16#16};
dec_huffman_lookup(16#c0, 16#e) -> {more, 16#d5, 16#01};
dec_huffman_lookup(16#c0, 16#f) -> {ok, 16#d5, 16#16};
dec_huffman_lookup(16#c1, 16#0) -> {more, 16#c0, 16#02};
dec_huffman_lookup(16#c1, 16#1) -> {more, 16#c0, 16#09};
dec_huffman_lookup(16#c1, 16#2) -> {more, 16#c0, 16#17};
dec_huffman_lookup(16#c1, 16#3) -> {ok, 16#c0, 16#28};
dec_huffman_lookup(16#c1, 16#4) -> {more, 16#c1, 16#02};
dec_huffman_lookup(16#c1, 16#5) -> {more, 16#c1, 16#09};
dec_huffman_lookup(16#c1, 16#6) -> {more, 16#c1, 16#17};
dec_huffman_lookup(16#c1, 16#7) -> {ok, 16#c1, 16#28};
dec_huffman_lookup(16#c1, 16#8) -> {more, 16#c8, 16#02};
dec_huffman_lookup(16#c1, 16#9) -> {more, 16#c8, 16#09};
dec_huffman_lookup(16#c1, 16#a) -> {more, 16#c8, 16#17};
dec_huffman_lookup(16#c1, 16#b) -> {ok, 16#c8, 16#28};
dec_huffman_lookup(16#c1, 16#c) -> {more, 16#c9, 16#02};
dec_huffman_lookup(16#c1, 16#d) -> {more, 16#c9, 16#09};
dec_huffman_lookup(16#c1, 16#e) -> {more, 16#c9, 16#17};
dec_huffman_lookup(16#c1, 16#f) -> {ok, 16#c9, 16#28};
dec_huffman_lookup(16#c2, 16#0) -> {more, 16#c0, 16#03};
dec_huffman_lookup(16#c2, 16#1) -> {more, 16#c0, 16#06};
dec_huffman_lookup(16#c2, 16#2) -> {more, 16#c0, 16#0a};
dec_huffman_lookup(16#c2, 16#3) -> {more, 16#c0, 16#0f};
dec_huffman_lookup(16#c2, 16#4) -> {more, 16#c0, 16#18};
dec_huffman_lookup(16#c2, 16#5) -> {more, 16#c0, 16#1f};
dec_huffman_lookup(16#c2, 16#6) -> {more, 16#c0, 16#29};
dec_huffman_lookup(16#c2, 16#7) -> {ok, 16#c0, 16#38};
dec_huffman_lookup(16#c2, 16#8) -> {more, 16#c1, 16#03};
dec_huffman_lookup(16#c2, 16#9) -> {more, 16#c1, 16#06};
dec_huffman_lookup(16#c2, 16#a) -> {more, 16#c1, 16#0a};
dec_huffman_lookup(16#c2, 16#b) -> {more, 16#c1, 16#0f};
dec_huffman_lookup(16#c2, 16#c) -> {more, 16#c1, 16#18};
dec_huffman_lookup(16#c2, 16#d) -> {more, 16#c1, 16#1f};
dec_huffman_lookup(16#c2, 16#e) -> {more, 16#c1, 16#29};
dec_huffman_lookup(16#c2, 16#f) -> {ok, 16#c1, 16#38};
dec_huffman_lookup(16#c3, 16#0) -> {more, 16#c8, 16#03};
dec_huffman_lookup(16#c3, 16#1) -> {more, 16#c8, 16#06};
dec_huffman_lookup(16#c3, 16#2) -> {more, 16#c8, 16#0a};
dec_huffman_lookup(16#c3, 16#3) -> {more, 16#c8, 16#0f};
dec_huffman_lookup(16#c3, 16#4) -> {more, 16#c8, 16#18};
dec_huffman_lookup(16#c3, 16#5) -> {more, 16#c8, 16#1f};
dec_huffman_lookup(16#c3, 16#6) -> {more, 16#c8, 16#29};
dec_huffman_lookup(16#c3, 16#7) -> {ok, 16#c8, 16#38};
dec_huffman_lookup(16#c3, 16#8) -> {more, 16#c9, 16#03};
dec_huffman_lookup(16#c3, 16#9) -> {more, 16#c9, 16#06};
dec_huffman_lookup(16#c3, 16#a) -> {more, 16#c9, 16#0a};
dec_huffman_lookup(16#c3, 16#b) -> {more, 16#c9, 16#0f};
dec_huffman_lookup(16#c3, 16#c) -> {more, 16#c9, 16#18};
dec_huffman_lookup(16#c3, 16#d) -> {more, 16#c9, 16#1f};
dec_huffman_lookup(16#c3, 16#e) -> {more, 16#c9, 16#29};
dec_huffman_lookup(16#c3, 16#f) -> {ok, 16#c9, 16#38};
dec_huffman_lookup(16#c4, 16#0) -> {more, 16#ca, 16#02};
dec_huffman_lookup(16#c4, 16#1) -> {more, 16#ca, 16#09};
dec_huffman_lookup(16#c4, 16#2) -> {more, 16#ca, 16#17};
dec_huffman_lookup(16#c4, 16#3) -> {ok, 16#ca, 16#28};
dec_huffman_lookup(16#c4, 16#4) -> {more, 16#cd, 16#02};
dec_huffman_lookup(16#c4, 16#5) -> {more, 16#cd, 16#09};
dec_huffman_lookup(16#c4, 16#6) -> {more, 16#cd, 16#17};
dec_huffman_lookup(16#c4, 16#7) -> {ok, 16#cd, 16#28};
dec_huffman_lookup(16#c4, 16#8) -> {more, 16#d2, 16#02};
dec_huffman_lookup(16#c4, 16#9) -> {more, 16#d2, 16#09};
dec_huffman_lookup(16#c4, 16#a) -> {more, 16#d2, 16#17};
dec_huffman_lookup(16#c4, 16#b) -> {ok, 16#d2, 16#28};
dec_huffman_lookup(16#c4, 16#c) -> {more, 16#d5, 16#02};
dec_huffman_lookup(16#c4, 16#d) -> {more, 16#d5, 16#09};
dec_huffman_lookup(16#c4, 16#e) -> {more, 16#d5, 16#17};
dec_huffman_lookup(16#c4, 16#f) -> {ok, 16#d5, 16#28};
dec_huffman_lookup(16#c5, 16#0) -> {more, 16#ca, 16#03};
dec_huffman_lookup(16#c5, 16#1) -> {more, 16#ca, 16#06};
dec_huffman_lookup(16#c5, 16#2) -> {more, 16#ca, 16#0a};
dec_huffman_lookup(16#c5, 16#3) -> {more, 16#ca, 16#0f};
dec_huffman_lookup(16#c5, 16#4) -> {more, 16#ca, 16#18};
dec_huffman_lookup(16#c5, 16#5) -> {more, 16#ca, 16#1f};
dec_huffman_lookup(16#c5, 16#6) -> {more, 16#ca, 16#29};
dec_huffman_lookup(16#c5, 16#7) -> {ok, 16#ca, 16#38};
dec_huffman_lookup(16#c5, 16#8) -> {more, 16#cd, 16#03};
dec_huffman_lookup(16#c5, 16#9) -> {more, 16#cd, 16#06};
dec_huffman_lookup(16#c5, 16#a) -> {more, 16#cd, 16#0a};
dec_huffman_lookup(16#c5, 16#b) -> {more, 16#cd, 16#0f};
dec_huffman_lookup(16#c5, 16#c) -> {more, 16#cd, 16#18};
dec_huffman_lookup(16#c5, 16#d) -> {more, 16#cd, 16#1f};
dec_huffman_lookup(16#c5, 16#e) -> {more, 16#cd, 16#29};
dec_huffman_lookup(16#c5, 16#f) -> {ok, 16#cd, 16#38};
dec_huffman_lookup(16#c6, 16#0) -> {more, 16#d2, 16#03};
dec_huffman_lookup(16#c6, 16#1) -> {more, 16#d2, 16#06};
dec_huffman_lookup(16#c6, 16#2) -> {more, 16#d2, 16#0a};
dec_huffman_lookup(16#c6, 16#3) -> {more, 16#d2, 16#0f};
dec_huffman_lookup(16#c6, 16#4) -> {more, 16#d2, 16#18};
dec_huffman_lookup(16#c6, 16#5) -> {more, 16#d2, 16#1f};
dec_huffman_lookup(16#c6, 16#6) -> {more, 16#d2, 16#29};
dec_huffman_lookup(16#c6, 16#7) -> {ok, 16#d2, 16#38};
dec_huffman_lookup(16#c6, 16#8) -> {more, 16#d5, 16#03};
dec_huffman_lookup(16#c6, 16#9) -> {more, 16#d5, 16#06};
dec_huffman_lookup(16#c6, 16#a) -> {more, 16#d5, 16#0a};
dec_huffman_lookup(16#c6, 16#b) -> {more, 16#d5, 16#0f};
dec_huffman_lookup(16#c6, 16#c) -> {more, 16#d5, 16#18};
dec_huffman_lookup(16#c6, 16#d) -> {more, 16#d5, 16#1f};
dec_huffman_lookup(16#c6, 16#e) -> {more, 16#d5, 16#29};
dec_huffman_lookup(16#c6, 16#f) -> {ok, 16#d5, 16#38};
dec_huffman_lookup(16#c7, 16#0) -> {more, 16#da, 16#01};
dec_huffman_lookup(16#c7, 16#1) -> {ok, 16#da, 16#16};
dec_huffman_lookup(16#c7, 16#2) -> {more, 16#db, 16#01};
dec_huffman_lookup(16#c7, 16#3) -> {ok, 16#db, 16#16};
dec_huffman_lookup(16#c7, 16#4) -> {more, 16#ee, 16#01};
dec_huffman_lookup(16#c7, 16#5) -> {ok, 16#ee, 16#16};
dec_huffman_lookup(16#c7, 16#6) -> {more, 16#f0, 16#01};
dec_huffman_lookup(16#c7, 16#7) -> {ok, 16#f0, 16#16};
dec_huffman_lookup(16#c7, 16#8) -> {more, 16#f2, 16#01};
dec_huffman_lookup(16#c7, 16#9) -> {ok, 16#f2, 16#16};
dec_huffman_lookup(16#c7, 16#a) -> {more, 16#f3, 16#01};
dec_huffman_lookup(16#c7, 16#b) -> {ok, 16#f3, 16#16};
dec_huffman_lookup(16#c7, 16#c) -> {more, 16#ff, 16#01};
dec_huffman_lookup(16#c7, 16#d) -> {ok, 16#ff, 16#16};
dec_huffman_lookup(16#c7, 16#e) -> {ok, 16#cb, 16#00};
dec_huffman_lookup(16#c7, 16#f) -> {ok, 16#cc, 16#00};
dec_huffman_lookup(16#c8, 16#0) -> {more, 16#da, 16#02};
dec_huffman_lookup(16#c8, 16#1) -> {more, 16#da, 16#09};
dec_huffman_lookup(16#c8, 16#2) -> {more, 16#da, 16#17};
dec_huffman_lookup(16#c8, 16#3) -> {ok, 16#da, 16#28};
dec_huffman_lookup(16#c8, 16#4) -> {more, 16#db, 16#02};
dec_huffman_lookup(16#c8, 16#5) -> {more, 16#db, 16#09};
dec_huffman_lookup(16#c8, 16#6) -> {more, 16#db, 16#17};
dec_huffman_lookup(16#c8, 16#7) -> {ok, 16#db, 16#28};
dec_huffman_lookup(16#c8, 16#8) -> {more, 16#ee, 16#02};
dec_huffman_lookup(16#c8, 16#9) -> {more, 16#ee, 16#09};
dec_huffman_lookup(16#c8, 16#a) -> {more, 16#ee, 16#17};
dec_huffman_lookup(16#c8, 16#b) -> {ok, 16#ee, 16#28};
dec_huffman_lookup(16#c8, 16#c) -> {more, 16#f0, 16#02};
dec_huffman_lookup(16#c8, 16#d) -> {more, 16#f0, 16#09};
dec_huffman_lookup(16#c8, 16#e) -> {more, 16#f0, 16#17};
dec_huffman_lookup(16#c8, 16#f) -> {ok, 16#f0, 16#28};
dec_huffman_lookup(16#c9, 16#0) -> {more, 16#da, 16#03};
dec_huffman_lookup(16#c9, 16#1) -> {more, 16#da, 16#06};
dec_huffman_lookup(16#c9, 16#2) -> {more, 16#da, 16#0a};
dec_huffman_lookup(16#c9, 16#3) -> {more, 16#da, 16#0f};
dec_huffman_lookup(16#c9, 16#4) -> {more, 16#da, 16#18};
dec_huffman_lookup(16#c9, 16#5) -> {more, 16#da, 16#1f};
dec_huffman_lookup(16#c9, 16#6) -> {more, 16#da, 16#29};
dec_huffman_lookup(16#c9, 16#7) -> {ok, 16#da, 16#38};
dec_huffman_lookup(16#c9, 16#8) -> {more, 16#db, 16#03};
dec_huffman_lookup(16#c9, 16#9) -> {more, 16#db, 16#06};
dec_huffman_lookup(16#c9, 16#a) -> {more, 16#db, 16#0a};
dec_huffman_lookup(16#c9, 16#b) -> {more, 16#db, 16#0f};
dec_huffman_lookup(16#c9, 16#c) -> {more, 16#db, 16#18};
dec_huffman_lookup(16#c9, 16#d) -> {more, 16#db, 16#1f};
dec_huffman_lookup(16#c9, 16#e) -> {more, 16#db, 16#29};
dec_huffman_lookup(16#c9, 16#f) -> {ok, 16#db, 16#38};
dec_huffman_lookup(16#ca, 16#0) -> {more, 16#ee, 16#03};
dec_huffman_lookup(16#ca, 16#1) -> {more, 16#ee, 16#06};
dec_huffman_lookup(16#ca, 16#2) -> {more, 16#ee, 16#0a};
dec_huffman_lookup(16#ca, 16#3) -> {more, 16#ee, 16#0f};
dec_huffman_lookup(16#ca, 16#4) -> {more, 16#ee, 16#18};
dec_huffman_lookup(16#ca, 16#5) -> {more, 16#ee, 16#1f};
dec_huffman_lookup(16#ca, 16#6) -> {more, 16#ee, 16#29};
dec_huffman_lookup(16#ca, 16#7) -> {ok, 16#ee, 16#38};
dec_huffman_lookup(16#ca, 16#8) -> {more, 16#f0, 16#03};
dec_huffman_lookup(16#ca, 16#9) -> {more, 16#f0, 16#06};
dec_huffman_lookup(16#ca, 16#a) -> {more, 16#f0, 16#0a};
dec_huffman_lookup(16#ca, 16#b) -> {more, 16#f0, 16#0f};
dec_huffman_lookup(16#ca, 16#c) -> {more, 16#f0, 16#18};
dec_huffman_lookup(16#ca, 16#d) -> {more, 16#f0, 16#1f};
dec_huffman_lookup(16#ca, 16#e) -> {more, 16#f0, 16#29};
dec_huffman_lookup(16#ca, 16#f) -> {ok, 16#f0, 16#38};
dec_huffman_lookup(16#cb, 16#0) -> {more, 16#f2, 16#02};
dec_huffman_lookup(16#cb, 16#1) -> {more, 16#f2, 16#09};
dec_huffman_lookup(16#cb, 16#2) -> {more, 16#f2, 16#17};
dec_huffman_lookup(16#cb, 16#3) -> {ok, 16#f2, 16#28};
dec_huffman_lookup(16#cb, 16#4) -> {more, 16#f3, 16#02};
dec_huffman_lookup(16#cb, 16#5) -> {more, 16#f3, 16#09};
dec_huffman_lookup(16#cb, 16#6) -> {more, 16#f3, 16#17};
dec_huffman_lookup(16#cb, 16#7) -> {ok, 16#f3, 16#28};
dec_huffman_lookup(16#cb, 16#8) -> {more, 16#ff, 16#02};
dec_huffman_lookup(16#cb, 16#9) -> {more, 16#ff, 16#09};
dec_huffman_lookup(16#cb, 16#a) -> {more, 16#ff, 16#17};
dec_huffman_lookup(16#cb, 16#b) -> {ok, 16#ff, 16#28};
dec_huffman_lookup(16#cb, 16#c) -> {more, 16#cb, 16#01};
dec_huffman_lookup(16#cb, 16#d) -> {ok, 16#cb, 16#16};
dec_huffman_lookup(16#cb, 16#e) -> {more, 16#cc, 16#01};
dec_huffman_lookup(16#cb, 16#f) -> {ok, 16#cc, 16#16};
dec_huffman_lookup(16#cc, 16#0) -> {more, 16#f2, 16#03};
dec_huffman_lookup(16#cc, 16#1) -> {more, 16#f2, 16#06};
dec_huffman_lookup(16#cc, 16#2) -> {more, 16#f2, 16#0a};
dec_huffman_lookup(16#cc, 16#3) -> {more, 16#f2, 16#0f};
dec_huffman_lookup(16#cc, 16#4) -> {more, 16#f2, 16#18};
dec_huffman_lookup(16#cc, 16#5) -> {more, 16#f2, 16#1f};
dec_huffman_lookup(16#cc, 16#6) -> {more, 16#f2, 16#29};
dec_huffman_lookup(16#cc, 16#7) -> {ok, 16#f2, 16#38};
dec_huffman_lookup(16#cc, 16#8) -> {more, 16#f3, 16#03};
dec_huffman_lookup(16#cc, 16#9) -> {more, 16#f3, 16#06};
dec_huffman_lookup(16#cc, 16#a) -> {more, 16#f3, 16#0a};
dec_huffman_lookup(16#cc, 16#b) -> {more, 16#f3, 16#0f};
dec_huffman_lookup(16#cc, 16#c) -> {more, 16#f3, 16#18};
dec_huffman_lookup(16#cc, 16#d) -> {more, 16#f3, 16#1f};
dec_huffman_lookup(16#cc, 16#e) -> {more, 16#f3, 16#29};
dec_huffman_lookup(16#cc, 16#f) -> {ok, 16#f3, 16#38};
dec_huffman_lookup(16#cd, 16#0) -> {more, 16#ff, 16#03};
dec_huffman_lookup(16#cd, 16#1) -> {more, 16#ff, 16#06};
dec_huffman_lookup(16#cd, 16#2) -> {more, 16#ff, 16#0a};
dec_huffman_lookup(16#cd, 16#3) -> {more, 16#ff, 16#0f};
dec_huffman_lookup(16#cd, 16#4) -> {more, 16#ff, 16#18};
dec_huffman_lookup(16#cd, 16#5) -> {more, 16#ff, 16#1f};
dec_huffman_lookup(16#cd, 16#6) -> {more, 16#ff, 16#29};
dec_huffman_lookup(16#cd, 16#7) -> {ok, 16#ff, 16#38};
dec_huffman_lookup(16#cd, 16#8) -> {more, 16#cb, 16#02};
dec_huffman_lookup(16#cd, 16#9) -> {more, 16#cb, 16#09};
dec_huffman_lookup(16#cd, 16#a) -> {more, 16#cb, 16#17};
dec_huffman_lookup(16#cd, 16#b) -> {ok, 16#cb, 16#28};
dec_huffman_lookup(16#cd, 16#c) -> {more, 16#cc, 16#02};
dec_huffman_lookup(16#cd, 16#d) -> {more, 16#cc, 16#09};
dec_huffman_lookup(16#cd, 16#e) -> {more, 16#cc, 16#17};
dec_huffman_lookup(16#cd, 16#f) -> {ok, 16#cc, 16#28};
dec_huffman_lookup(16#ce, 16#0) -> {more, 16#cb, 16#03};
dec_huffman_lookup(16#ce, 16#1) -> {more, 16#cb, 16#06};
dec_huffman_lookup(16#ce, 16#2) -> {more, 16#cb, 16#0a};
dec_huffman_lookup(16#ce, 16#3) -> {more, 16#cb, 16#0f};
dec_huffman_lookup(16#ce, 16#4) -> {more, 16#cb, 16#18};
dec_huffman_lookup(16#ce, 16#5) -> {more, 16#cb, 16#1f};
dec_huffman_lookup(16#ce, 16#6) -> {more, 16#cb, 16#29};
dec_huffman_lookup(16#ce, 16#7) -> {ok, 16#cb, 16#38};
dec_huffman_lookup(16#ce, 16#8) -> {more, 16#cc, 16#03};
dec_huffman_lookup(16#ce, 16#9) -> {more, 16#cc, 16#06};
dec_huffman_lookup(16#ce, 16#a) -> {more, 16#cc, 16#0a};
dec_huffman_lookup(16#ce, 16#b) -> {more, 16#cc, 16#0f};
dec_huffman_lookup(16#ce, 16#c) -> {more, 16#cc, 16#18};
dec_huffman_lookup(16#ce, 16#d) -> {more, 16#cc, 16#1f};
dec_huffman_lookup(16#ce, 16#e) -> {more, 16#cc, 16#29};
dec_huffman_lookup(16#ce, 16#f) -> {ok, 16#cc, 16#38};
dec_huffman_lookup(16#cf, 16#0) -> {more, undefined, 16#d3};
dec_huffman_lookup(16#cf, 16#1) -> {more, undefined, 16#d4};
dec_huffman_lookup(16#cf, 16#2) -> {more, undefined, 16#d6};
dec_huffman_lookup(16#cf, 16#3) -> {more, undefined, 16#d7};
dec_huffman_lookup(16#cf, 16#4) -> {more, undefined, 16#da};
dec_huffman_lookup(16#cf, 16#5) -> {more, undefined, 16#db};
dec_huffman_lookup(16#cf, 16#6) -> {more, undefined, 16#dd};
dec_huffman_lookup(16#cf, 16#7) -> {more, undefined, 16#de};
dec_huffman_lookup(16#cf, 16#8) -> {more, undefined, 16#e2};
dec_huffman_lookup(16#cf, 16#9) -> {more, undefined, 16#e4};
dec_huffman_lookup(16#cf, 16#a) -> {more, undefined, 16#e8};
dec_huffman_lookup(16#cf, 16#b) -> {more, undefined, 16#eb};
dec_huffman_lookup(16#cf, 16#c) -> {more, undefined, 16#f0};
dec_huffman_lookup(16#cf, 16#d) -> {more, undefined, 16#f3};
dec_huffman_lookup(16#cf, 16#e) -> {more, undefined, 16#f7};
dec_huffman_lookup(16#cf, 16#f) -> {ok, undefined, 16#fa};
dec_huffman_lookup(16#d0, 16#0) -> {ok, 16#d3, 16#00};
dec_huffman_lookup(16#d0, 16#1) -> {ok, 16#d4, 16#00};
dec_huffman_lookup(16#d0, 16#2) -> {ok, 16#d6, 16#00};
dec_huffman_lookup(16#d0, 16#3) -> {ok, 16#dd, 16#00};
dec_huffman_lookup(16#d0, 16#4) -> {ok, 16#de, 16#00};
dec_huffman_lookup(16#d0, 16#5) -> {ok, 16#df, 16#00};
dec_huffman_lookup(16#d0, 16#6) -> {ok, 16#f1, 16#00};
dec_huffman_lookup(16#d0, 16#7) -> {ok, 16#f4, 16#00};
dec_huffman_lookup(16#d0, 16#8) -> {ok, 16#f5, 16#00};
dec_huffman_lookup(16#d0, 16#9) -> {ok, 16#f6, 16#00};
dec_huffman_lookup(16#d0, 16#a) -> {ok, 16#f7, 16#00};
dec_huffman_lookup(16#d0, 16#b) -> {ok, 16#f8, 16#00};
dec_huffman_lookup(16#d0, 16#c) -> {ok, 16#fa, 16#00};
dec_huffman_lookup(16#d0, 16#d) -> {ok, 16#fb, 16#00};
dec_huffman_lookup(16#d0, 16#e) -> {ok, 16#fc, 16#00};
dec_huffman_lookup(16#d0, 16#f) -> {ok, 16#fd, 16#00};
dec_huffman_lookup(16#d1, 16#0) -> {more, 16#d3, 16#01};
dec_huffman_lookup(16#d1, 16#1) -> {ok, 16#d3, 16#16};
dec_huffman_lookup(16#d1, 16#2) -> {more, 16#d4, 16#01};
dec_huffman_lookup(16#d1, 16#3) -> {ok, 16#d4, 16#16};
dec_huffman_lookup(16#d1, 16#4) -> {more, 16#d6, 16#01};
dec_huffman_lookup(16#d1, 16#5) -> {ok, 16#d6, 16#16};
dec_huffman_lookup(16#d1, 16#6) -> {more, 16#dd, 16#01};
dec_huffman_lookup(16#d1, 16#7) -> {ok, 16#dd, 16#16};
dec_huffman_lookup(16#d1, 16#8) -> {more, 16#de, 16#01};
dec_huffman_lookup(16#d1, 16#9) -> {ok, 16#de, 16#16};
dec_huffman_lookup(16#d1, 16#a) -> {more, 16#df, 16#01};
dec_huffman_lookup(16#d1, 16#b) -> {ok, 16#df, 16#16};
dec_huffman_lookup(16#d1, 16#c) -> {more, 16#f1, 16#01};
dec_huffman_lookup(16#d1, 16#d) -> {ok, 16#f1, 16#16};
dec_huffman_lookup(16#d1, 16#e) -> {more, 16#f4, 16#01};
dec_huffman_lookup(16#d1, 16#f) -> {ok, 16#f4, 16#16};
dec_huffman_lookup(16#d2, 16#0) -> {more, 16#d3, 16#02};
dec_huffman_lookup(16#d2, 16#1) -> {more, 16#d3, 16#09};
dec_huffman_lookup(16#d2, 16#2) -> {more, 16#d3, 16#17};
dec_huffman_lookup(16#d2, 16#3) -> {ok, 16#d3, 16#28};
dec_huffman_lookup(16#d2, 16#4) -> {more, 16#d4, 16#02};
dec_huffman_lookup(16#d2, 16#5) -> {more, 16#d4, 16#09};
dec_huffman_lookup(16#d2, 16#6) -> {more, 16#d4, 16#17};
dec_huffman_lookup(16#d2, 16#7) -> {ok, 16#d4, 16#28};
dec_huffman_lookup(16#d2, 16#8) -> {more, 16#d6, 16#02};
dec_huffman_lookup(16#d2, 16#9) -> {more, 16#d6, 16#09};
dec_huffman_lookup(16#d2, 16#a) -> {more, 16#d6, 16#17};
dec_huffman_lookup(16#d2, 16#b) -> {ok, 16#d6, 16#28};
dec_huffman_lookup(16#d2, 16#c) -> {more, 16#dd, 16#02};
dec_huffman_lookup(16#d2, 16#d) -> {more, 16#dd, 16#09};
dec_huffman_lookup(16#d2, 16#e) -> {more, 16#dd, 16#17};
dec_huffman_lookup(16#d2, 16#f) -> {ok, 16#dd, 16#28};
dec_huffman_lookup(16#d3, 16#0) -> {more, 16#d3, 16#03};
dec_huffman_lookup(16#d3, 16#1) -> {more, 16#d3, 16#06};
dec_huffman_lookup(16#d3, 16#2) -> {more, 16#d3, 16#0a};
dec_huffman_lookup(16#d3, 16#3) -> {more, 16#d3, 16#0f};
dec_huffman_lookup(16#d3, 16#4) -> {more, 16#d3, 16#18};
dec_huffman_lookup(16#d3, 16#5) -> {more, 16#d3, 16#1f};
dec_huffman_lookup(16#d3, 16#6) -> {more, 16#d3, 16#29};
dec_huffman_lookup(16#d3, 16#7) -> {ok, 16#d3, 16#38};
dec_huffman_lookup(16#d3, 16#8) -> {more, 16#d4, 16#03};
dec_huffman_lookup(16#d3, 16#9) -> {more, 16#d4, 16#06};
dec_huffman_lookup(16#d3, 16#a) -> {more, 16#d4, 16#0a};
dec_huffman_lookup(16#d3, 16#b) -> {more, 16#d4, 16#0f};
dec_huffman_lookup(16#d3, 16#c) -> {more, 16#d4, 16#18};
dec_huffman_lookup(16#d3, 16#d) -> {more, 16#d4, 16#1f};
dec_huffman_lookup(16#d3, 16#e) -> {more, 16#d4, 16#29};
dec_huffman_lookup(16#d3, 16#f) -> {ok, 16#d4, 16#38};
dec_huffman_lookup(16#d4, 16#0) -> {more, 16#d6, 16#03};
dec_huffman_lookup(16#d4, 16#1) -> {more, 16#d6, 16#06};
dec_huffman_lookup(16#d4, 16#2) -> {more, 16#d6, 16#0a};
dec_huffman_lookup(16#d4, 16#3) -> {more, 16#d6, 16#0f};
dec_huffman_lookup(16#d4, 16#4) -> {more, 16#d6, 16#18};
dec_huffman_lookup(16#d4, 16#5) -> {more, 16#d6, 16#1f};
dec_huffman_lookup(16#d4, 16#6) -> {more, 16#d6, 16#29};
dec_huffman_lookup(16#d4, 16#7) -> {ok, 16#d6, 16#38};
dec_huffman_lookup(16#d4, 16#8) -> {more, 16#dd, 16#03};
dec_huffman_lookup(16#d4, 16#9) -> {more, 16#dd, 16#06};
dec_huffman_lookup(16#d4, 16#a) -> {more, 16#dd, 16#0a};
dec_huffman_lookup(16#d4, 16#b) -> {more, 16#dd, 16#0f};
dec_huffman_lookup(16#d4, 16#c) -> {more, 16#dd, 16#18};
dec_huffman_lookup(16#d4, 16#d) -> {more, 16#dd, 16#1f};
dec_huffman_lookup(16#d4, 16#e) -> {more, 16#dd, 16#29};
dec_huffman_lookup(16#d4, 16#f) -> {ok, 16#dd, 16#38};
dec_huffman_lookup(16#d5, 16#0) -> {more, 16#de, 16#02};
dec_huffman_lookup(16#d5, 16#1) -> {more, 16#de, 16#09};
dec_huffman_lookup(16#d5, 16#2) -> {more, 16#de, 16#17};
dec_huffman_lookup(16#d5, 16#3) -> {ok, 16#de, 16#28};
dec_huffman_lookup(16#d5, 16#4) -> {more, 16#df, 16#02};
dec_huffman_lookup(16#d5, 16#5) -> {more, 16#df, 16#09};
dec_huffman_lookup(16#d5, 16#6) -> {more, 16#df, 16#17};
dec_huffman_lookup(16#d5, 16#7) -> {ok, 16#df, 16#28};
dec_huffman_lookup(16#d5, 16#8) -> {more, 16#f1, 16#02};
dec_huffman_lookup(16#d5, 16#9) -> {more, 16#f1, 16#09};
dec_huffman_lookup(16#d5, 16#a) -> {more, 16#f1, 16#17};
dec_huffman_lookup(16#d5, 16#b) -> {ok, 16#f1, 16#28};
dec_huffman_lookup(16#d5, 16#c) -> {more, 16#f4, 16#02};
dec_huffman_lookup(16#d5, 16#d) -> {more, 16#f4, 16#09};
dec_huffman_lookup(16#d5, 16#e) -> {more, 16#f4, 16#17};
dec_huffman_lookup(16#d5, 16#f) -> {ok, 16#f4, 16#28};
dec_huffman_lookup(16#d6, 16#0) -> {more, 16#de, 16#03};
dec_huffman_lookup(16#d6, 16#1) -> {more, 16#de, 16#06};
dec_huffman_lookup(16#d6, 16#2) -> {more, 16#de, 16#0a};
dec_huffman_lookup(16#d6, 16#3) -> {more, 16#de, 16#0f};
dec_huffman_lookup(16#d6, 16#4) -> {more, 16#de, 16#18};
dec_huffman_lookup(16#d6, 16#5) -> {more, 16#de, 16#1f};
dec_huffman_lookup(16#d6, 16#6) -> {more, 16#de, 16#29};
dec_huffman_lookup(16#d6, 16#7) -> {ok, 16#de, 16#38};
dec_huffman_lookup(16#d6, 16#8) -> {more, 16#df, 16#03};
dec_huffman_lookup(16#d6, 16#9) -> {more, 16#df, 16#06};
dec_huffman_lookup(16#d6, 16#a) -> {more, 16#df, 16#0a};
dec_huffman_lookup(16#d6, 16#b) -> {more, 16#df, 16#0f};
dec_huffman_lookup(16#d6, 16#c) -> {more, 16#df, 16#18};
dec_huffman_lookup(16#d6, 16#d) -> {more, 16#df, 16#1f};
dec_huffman_lookup(16#d6, 16#e) -> {more, 16#df, 16#29};
dec_huffman_lookup(16#d6, 16#f) -> {ok, 16#df, 16#38};
dec_huffman_lookup(16#d7, 16#0) -> {more, 16#f1, 16#03};
dec_huffman_lookup(16#d7, 16#1) -> {more, 16#f1, 16#06};
dec_huffman_lookup(16#d7, 16#2) -> {more, 16#f1, 16#0a};
dec_huffman_lookup(16#d7, 16#3) -> {more, 16#f1, 16#0f};
dec_huffman_lookup(16#d7, 16#4) -> {more, 16#f1, 16#18};
dec_huffman_lookup(16#d7, 16#5) -> {more, 16#f1, 16#1f};
dec_huffman_lookup(16#d7, 16#6) -> {more, 16#f1, 16#29};
dec_huffman_lookup(16#d7, 16#7) -> {ok, 16#f1, 16#38};
dec_huffman_lookup(16#d7, 16#8) -> {more, 16#f4, 16#03};
dec_huffman_lookup(16#d7, 16#9) -> {more, 16#f4, 16#06};
dec_huffman_lookup(16#d7, 16#a) -> {more, 16#f4, 16#0a};
dec_huffman_lookup(16#d7, 16#b) -> {more, 16#f4, 16#0f};
dec_huffman_lookup(16#d7, 16#c) -> {more, 16#f4, 16#18};
dec_huffman_lookup(16#d7, 16#d) -> {more, 16#f4, 16#1f};
dec_huffman_lookup(16#d7, 16#e) -> {more, 16#f4, 16#29};
dec_huffman_lookup(16#d7, 16#f) -> {ok, 16#f4, 16#38};
dec_huffman_lookup(16#d8, 16#0) -> {more, 16#f5, 16#01};
dec_huffman_lookup(16#d8, 16#1) -> {ok, 16#f5, 16#16};
dec_huffman_lookup(16#d8, 16#2) -> {more, 16#f6, 16#01};
dec_huffman_lookup(16#d8, 16#3) -> {ok, 16#f6, 16#16};
dec_huffman_lookup(16#d8, 16#4) -> {more, 16#f7, 16#01};
dec_huffman_lookup(16#d8, 16#5) -> {ok, 16#f7, 16#16};
dec_huffman_lookup(16#d8, 16#6) -> {more, 16#f8, 16#01};
dec_huffman_lookup(16#d8, 16#7) -> {ok, 16#f8, 16#16};
dec_huffman_lookup(16#d8, 16#8) -> {more, 16#fa, 16#01};
dec_huffman_lookup(16#d8, 16#9) -> {ok, 16#fa, 16#16};
dec_huffman_lookup(16#d8, 16#a) -> {more, 16#fb, 16#01};
dec_huffman_lookup(16#d8, 16#b) -> {ok, 16#fb, 16#16};
dec_huffman_lookup(16#d8, 16#c) -> {more, 16#fc, 16#01};
dec_huffman_lookup(16#d8, 16#d) -> {ok, 16#fc, 16#16};
dec_huffman_lookup(16#d8, 16#e) -> {more, 16#fd, 16#01};
dec_huffman_lookup(16#d8, 16#f) -> {ok, 16#fd, 16#16};
dec_huffman_lookup(16#d9, 16#0) -> {more, 16#f5, 16#02};
dec_huffman_lookup(16#d9, 16#1) -> {more, 16#f5, 16#09};
dec_huffman_lookup(16#d9, 16#2) -> {more, 16#f5, 16#17};
dec_huffman_lookup(16#d9, 16#3) -> {ok, 16#f5, 16#28};
dec_huffman_lookup(16#d9, 16#4) -> {more, 16#f6, 16#02};
dec_huffman_lookup(16#d9, 16#5) -> {more, 16#f6, 16#09};
dec_huffman_lookup(16#d9, 16#6) -> {more, 16#f6, 16#17};
dec_huffman_lookup(16#d9, 16#7) -> {ok, 16#f6, 16#28};
dec_huffman_lookup(16#d9, 16#8) -> {more, 16#f7, 16#02};
dec_huffman_lookup(16#d9, 16#9) -> {more, 16#f7, 16#09};
dec_huffman_lookup(16#d9, 16#a) -> {more, 16#f7, 16#17};
dec_huffman_lookup(16#d9, 16#b) -> {ok, 16#f7, 16#28};
dec_huffman_lookup(16#d9, 16#c) -> {more, 16#f8, 16#02};
dec_huffman_lookup(16#d9, 16#d) -> {more, 16#f8, 16#09};
dec_huffman_lookup(16#d9, 16#e) -> {more, 16#f8, 16#17};
dec_huffman_lookup(16#d9, 16#f) -> {ok, 16#f8, 16#28};
dec_huffman_lookup(16#da, 16#0) -> {more, 16#f5, 16#03};
dec_huffman_lookup(16#da, 16#1) -> {more, 16#f5, 16#06};
dec_huffman_lookup(16#da, 16#2) -> {more, 16#f5, 16#0a};
dec_huffman_lookup(16#da, 16#3) -> {more, 16#f5, 16#0f};
dec_huffman_lookup(16#da, 16#4) -> {more, 16#f5, 16#18};
dec_huffman_lookup(16#da, 16#5) -> {more, 16#f5, 16#1f};
dec_huffman_lookup(16#da, 16#6) -> {more, 16#f5, 16#29};
dec_huffman_lookup(16#da, 16#7) -> {ok, 16#f5, 16#38};
dec_huffman_lookup(16#da, 16#8) -> {more, 16#f6, 16#03};
dec_huffman_lookup(16#da, 16#9) -> {more, 16#f6, 16#06};
dec_huffman_lookup(16#da, 16#a) -> {more, 16#f6, 16#0a};
dec_huffman_lookup(16#da, 16#b) -> {more, 16#f6, 16#0f};
dec_huffman_lookup(16#da, 16#c) -> {more, 16#f6, 16#18};
dec_huffman_lookup(16#da, 16#d) -> {more, 16#f6, 16#1f};
dec_huffman_lookup(16#da, 16#e) -> {more, 16#f6, 16#29};
dec_huffman_lookup(16#da, 16#f) -> {ok, 16#f6, 16#38};
dec_huffman_lookup(16#db, 16#0) -> {more, 16#f7, 16#03};
dec_huffman_lookup(16#db, 16#1) -> {more, 16#f7, 16#06};
dec_huffman_lookup(16#db, 16#2) -> {more, 16#f7, 16#0a};
dec_huffman_lookup(16#db, 16#3) -> {more, 16#f7, 16#0f};
dec_huffman_lookup(16#db, 16#4) -> {more, 16#f7, 16#18};
dec_huffman_lookup(16#db, 16#5) -> {more, 16#f7, 16#1f};
dec_huffman_lookup(16#db, 16#6) -> {more, 16#f7, 16#29};
dec_huffman_lookup(16#db, 16#7) -> {ok, 16#f7, 16#38};
dec_huffman_lookup(16#db, 16#8) -> {more, 16#f8, 16#03};
dec_huffman_lookup(16#db, 16#9) -> {more, 16#f8, 16#06};
dec_huffman_lookup(16#db, 16#a) -> {more, 16#f8, 16#0a};
dec_huffman_lookup(16#db, 16#b) -> {more, 16#f8, 16#0f};
dec_huffman_lookup(16#db, 16#c) -> {more, 16#f8, 16#18};
dec_huffman_lookup(16#db, 16#d) -> {more, 16#f8, 16#1f};
dec_huffman_lookup(16#db, 16#e) -> {more, 16#f8, 16#29};
dec_huffman_lookup(16#db, 16#f) -> {ok, 16#f8, 16#38};
dec_huffman_lookup(16#dc, 16#0) -> {more, 16#fa, 16#02};
dec_huffman_lookup(16#dc, 16#1) -> {more, 16#fa, 16#09};
dec_huffman_lookup(16#dc, 16#2) -> {more, 16#fa, 16#17};
dec_huffman_lookup(16#dc, 16#3) -> {ok, 16#fa, 16#28};
dec_huffman_lookup(16#dc, 16#4) -> {more, 16#fb, 16#02};
dec_huffman_lookup(16#dc, 16#5) -> {more, 16#fb, 16#09};
dec_huffman_lookup(16#dc, 16#6) -> {more, 16#fb, 16#17};
dec_huffman_lookup(16#dc, 16#7) -> {ok, 16#fb, 16#28};
dec_huffman_lookup(16#dc, 16#8) -> {more, 16#fc, 16#02};
dec_huffman_lookup(16#dc, 16#9) -> {more, 16#fc, 16#09};
dec_huffman_lookup(16#dc, 16#a) -> {more, 16#fc, 16#17};
dec_huffman_lookup(16#dc, 16#b) -> {ok, 16#fc, 16#28};
dec_huffman_lookup(16#dc, 16#c) -> {more, 16#fd, 16#02};
dec_huffman_lookup(16#dc, 16#d) -> {more, 16#fd, 16#09};
dec_huffman_lookup(16#dc, 16#e) -> {more, 16#fd, 16#17};
dec_huffman_lookup(16#dc, 16#f) -> {ok, 16#fd, 16#28};
dec_huffman_lookup(16#dd, 16#0) -> {more, 16#fa, 16#03};
dec_huffman_lookup(16#dd, 16#1) -> {more, 16#fa, 16#06};
dec_huffman_lookup(16#dd, 16#2) -> {more, 16#fa, 16#0a};
dec_huffman_lookup(16#dd, 16#3) -> {more, 16#fa, 16#0f};
dec_huffman_lookup(16#dd, 16#4) -> {more, 16#fa, 16#18};
dec_huffman_lookup(16#dd, 16#5) -> {more, 16#fa, 16#1f};
dec_huffman_lookup(16#dd, 16#6) -> {more, 16#fa, 16#29};
dec_huffman_lookup(16#dd, 16#7) -> {ok, 16#fa, 16#38};
dec_huffman_lookup(16#dd, 16#8) -> {more, 16#fb, 16#03};
dec_huffman_lookup(16#dd, 16#9) -> {more, 16#fb, 16#06};
dec_huffman_lookup(16#dd, 16#a) -> {more, 16#fb, 16#0a};
dec_huffman_lookup(16#dd, 16#b) -> {more, 16#fb, 16#0f};
dec_huffman_lookup(16#dd, 16#c) -> {more, 16#fb, 16#18};
dec_huffman_lookup(16#dd, 16#d) -> {more, 16#fb, 16#1f};
dec_huffman_lookup(16#dd, 16#e) -> {more, 16#fb, 16#29};
dec_huffman_lookup(16#dd, 16#f) -> {ok, 16#fb, 16#38};
dec_huffman_lookup(16#de, 16#0) -> {more, 16#fc, 16#03};
dec_huffman_lookup(16#de, 16#1) -> {more, 16#fc, 16#06};
dec_huffman_lookup(16#de, 16#2) -> {more, 16#fc, 16#0a};
dec_huffman_lookup(16#de, 16#3) -> {more, 16#fc, 16#0f};
dec_huffman_lookup(16#de, 16#4) -> {more, 16#fc, 16#18};
dec_huffman_lookup(16#de, 16#5) -> {more, 16#fc, 16#1f};
dec_huffman_lookup(16#de, 16#6) -> {more, 16#fc, 16#29};
dec_huffman_lookup(16#de, 16#7) -> {ok, 16#fc, 16#38};
dec_huffman_lookup(16#de, 16#8) -> {more, 16#fd, 16#03};
dec_huffman_lookup(16#de, 16#9) -> {more, 16#fd, 16#06};
dec_huffman_lookup(16#de, 16#a) -> {more, 16#fd, 16#0a};
dec_huffman_lookup(16#de, 16#b) -> {more, 16#fd, 16#0f};
dec_huffman_lookup(16#de, 16#c) -> {more, 16#fd, 16#18};
dec_huffman_lookup(16#de, 16#d) -> {more, 16#fd, 16#1f};
dec_huffman_lookup(16#de, 16#e) -> {more, 16#fd, 16#29};
dec_huffman_lookup(16#de, 16#f) -> {ok, 16#fd, 16#38};
dec_huffman_lookup(16#df, 16#0) -> {ok, 16#fe, 16#00};
dec_huffman_lookup(16#df, 16#1) -> {more, undefined, 16#e3};
dec_huffman_lookup(16#df, 16#2) -> {more, undefined, 16#e5};
dec_huffman_lookup(16#df, 16#3) -> {more, undefined, 16#e6};
dec_huffman_lookup(16#df, 16#4) -> {more, undefined, 16#e9};
dec_huffman_lookup(16#df, 16#5) -> {more, undefined, 16#ea};
dec_huffman_lookup(16#df, 16#6) -> {more, undefined, 16#ec};
dec_huffman_lookup(16#df, 16#7) -> {more, undefined, 16#ed};
dec_huffman_lookup(16#df, 16#8) -> {more, undefined, 16#f1};
dec_huffman_lookup(16#df, 16#9) -> {more, undefined, 16#f2};
dec_huffman_lookup(16#df, 16#a) -> {more, undefined, 16#f4};
dec_huffman_lookup(16#df, 16#b) -> {more, undefined, 16#f5};
dec_huffman_lookup(16#df, 16#c) -> {more, undefined, 16#f8};
dec_huffman_lookup(16#df, 16#d) -> {more, undefined, 16#f9};
dec_huffman_lookup(16#df, 16#e) -> {more, undefined, 16#fb};
dec_huffman_lookup(16#df, 16#f) -> {ok, undefined, 16#fc};
dec_huffman_lookup(16#e0, 16#0) -> {more, 16#fe, 16#01};
dec_huffman_lookup(16#e0, 16#1) -> {ok, 16#fe, 16#16};
dec_huffman_lookup(16#e0, 16#2) -> {ok, 16#02, 16#00};
dec_huffman_lookup(16#e0, 16#3) -> {ok, 16#03, 16#00};
dec_huffman_lookup(16#e0, 16#4) -> {ok, 16#04, 16#00};
dec_huffman_lookup(16#e0, 16#5) -> {ok, 16#05, 16#00};
dec_huffman_lookup(16#e0, 16#6) -> {ok, 16#06, 16#00};
dec_huffman_lookup(16#e0, 16#7) -> {ok, 16#07, 16#00};
dec_huffman_lookup(16#e0, 16#8) -> {ok, 16#08, 16#00};
dec_huffman_lookup(16#e0, 16#9) -> {ok, 16#0b, 16#00};
dec_huffman_lookup(16#e0, 16#a) -> {ok, 16#0c, 16#00};
dec_huffman_lookup(16#e0, 16#b) -> {ok, 16#0e, 16#00};
dec_huffman_lookup(16#e0, 16#c) -> {ok, 16#0f, 16#00};
dec_huffman_lookup(16#e0, 16#d) -> {ok, 16#10, 16#00};
dec_huffman_lookup(16#e0, 16#e) -> {ok, 16#11, 16#00};
dec_huffman_lookup(16#e0, 16#f) -> {ok, 16#12, 16#00};
dec_huffman_lookup(16#e1, 16#0) -> {more, 16#fe, 16#02};
dec_huffman_lookup(16#e1, 16#1) -> {more, 16#fe, 16#09};
dec_huffman_lookup(16#e1, 16#2) -> {more, 16#fe, 16#17};
dec_huffman_lookup(16#e1, 16#3) -> {ok, 16#fe, 16#28};
dec_huffman_lookup(16#e1, 16#4) -> {more, 16#02, 16#01};
dec_huffman_lookup(16#e1, 16#5) -> {ok, 16#02, 16#16};
dec_huffman_lookup(16#e1, 16#6) -> {more, 16#03, 16#01};
dec_huffman_lookup(16#e1, 16#7) -> {ok, 16#03, 16#16};
dec_huffman_lookup(16#e1, 16#8) -> {more, 16#04, 16#01};
dec_huffman_lookup(16#e1, 16#9) -> {ok, 16#04, 16#16};
dec_huffman_lookup(16#e1, 16#a) -> {more, 16#05, 16#01};
dec_huffman_lookup(16#e1, 16#b) -> {ok, 16#05, 16#16};
dec_huffman_lookup(16#e1, 16#c) -> {more, 16#06, 16#01};
dec_huffman_lookup(16#e1, 16#d) -> {ok, 16#06, 16#16};
dec_huffman_lookup(16#e1, 16#e) -> {more, 16#07, 16#01};
dec_huffman_lookup(16#e1, 16#f) -> {ok, 16#07, 16#16};
dec_huffman_lookup(16#e2, 16#0) -> {more, 16#fe, 16#03};
dec_huffman_lookup(16#e2, 16#1) -> {more, 16#fe, 16#06};
dec_huffman_lookup(16#e2, 16#2) -> {more, 16#fe, 16#0a};
dec_huffman_lookup(16#e2, 16#3) -> {more, 16#fe, 16#0f};
dec_huffman_lookup(16#e2, 16#4) -> {more, 16#fe, 16#18};
dec_huffman_lookup(16#e2, 16#5) -> {more, 16#fe, 16#1f};
dec_huffman_lookup(16#e2, 16#6) -> {more, 16#fe, 16#29};
dec_huffman_lookup(16#e2, 16#7) -> {ok, 16#fe, 16#38};
dec_huffman_lookup(16#e2, 16#8) -> {more, 16#02, 16#02};
dec_huffman_lookup(16#e2, 16#9) -> {more, 16#02, 16#09};
dec_huffman_lookup(16#e2, 16#a) -> {more, 16#02, 16#17};
dec_huffman_lookup(16#e2, 16#b) -> {ok, 16#02, 16#28};
dec_huffman_lookup(16#e2, 16#c) -> {more, 16#03, 16#02};
dec_huffman_lookup(16#e2, 16#d) -> {more, 16#03, 16#09};
dec_huffman_lookup(16#e2, 16#e) -> {more, 16#03, 16#17};
dec_huffman_lookup(16#e2, 16#f) -> {ok, 16#03, 16#28};
dec_huffman_lookup(16#e3, 16#0) -> {more, 16#02, 16#03};
dec_huffman_lookup(16#e3, 16#1) -> {more, 16#02, 16#06};
dec_huffman_lookup(16#e3, 16#2) -> {more, 16#02, 16#0a};
dec_huffman_lookup(16#e3, 16#3) -> {more, 16#02, 16#0f};
dec_huffman_lookup(16#e3, 16#4) -> {more, 16#02, 16#18};
dec_huffman_lookup(16#e3, 16#5) -> {more, 16#02, 16#1f};
dec_huffman_lookup(16#e3, 16#6) -> {more, 16#02, 16#29};
dec_huffman_lookup(16#e3, 16#7) -> {ok, 16#02, 16#38};
dec_huffman_lookup(16#e3, 16#8) -> {more, 16#03, 16#03};
dec_huffman_lookup(16#e3, 16#9) -> {more, 16#03, 16#06};
dec_huffman_lookup(16#e3, 16#a) -> {more, 16#03, 16#0a};
dec_huffman_lookup(16#e3, 16#b) -> {more, 16#03, 16#0f};
dec_huffman_lookup(16#e3, 16#c) -> {more, 16#03, 16#18};
dec_huffman_lookup(16#e3, 16#d) -> {more, 16#03, 16#1f};
dec_huffman_lookup(16#e3, 16#e) -> {more, 16#03, 16#29};
dec_huffman_lookup(16#e3, 16#f) -> {ok, 16#03, 16#38};
dec_huffman_lookup(16#e4, 16#0) -> {more, 16#04, 16#02};
dec_huffman_lookup(16#e4, 16#1) -> {more, 16#04, 16#09};
dec_huffman_lookup(16#e4, 16#2) -> {more, 16#04, 16#17};
dec_huffman_lookup(16#e4, 16#3) -> {ok, 16#04, 16#28};
dec_huffman_lookup(16#e4, 16#4) -> {more, 16#05, 16#02};
dec_huffman_lookup(16#e4, 16#5) -> {more, 16#05, 16#09};
dec_huffman_lookup(16#e4, 16#6) -> {more, 16#05, 16#17};
dec_huffman_lookup(16#e4, 16#7) -> {ok, 16#05, 16#28};
dec_huffman_lookup(16#e4, 16#8) -> {more, 16#06, 16#02};
dec_huffman_lookup(16#e4, 16#9) -> {more, 16#06, 16#09};
dec_huffman_lookup(16#e4, 16#a) -> {more, 16#06, 16#17};
dec_huffman_lookup(16#e4, 16#b) -> {ok, 16#06, 16#28};
dec_huffman_lookup(16#e4, 16#c) -> {more, 16#07, 16#02};
dec_huffman_lookup(16#e4, 16#d) -> {more, 16#07, 16#09};
dec_huffman_lookup(16#e4, 16#e) -> {more, 16#07, 16#17};
dec_huffman_lookup(16#e4, 16#f) -> {ok, 16#07, 16#28};
dec_huffman_lookup(16#e5, 16#0) -> {more, 16#04, 16#03};
dec_huffman_lookup(16#e5, 16#1) -> {more, 16#04, 16#06};
dec_huffman_lookup(16#e5, 16#2) -> {more, 16#04, 16#0a};
dec_huffman_lookup(16#e5, 16#3) -> {more, 16#04, 16#0f};
dec_huffman_lookup(16#e5, 16#4) -> {more, 16#04, 16#18};
dec_huffman_lookup(16#e5, 16#5) -> {more, 16#04, 16#1f};
dec_huffman_lookup(16#e5, 16#6) -> {more, 16#04, 16#29};
dec_huffman_lookup(16#e5, 16#7) -> {ok, 16#04, 16#38};
dec_huffman_lookup(16#e5, 16#8) -> {more, 16#05, 16#03};
dec_huffman_lookup(16#e5, 16#9) -> {more, 16#05, 16#06};
dec_huffman_lookup(16#e5, 16#a) -> {more, 16#05, 16#0a};
dec_huffman_lookup(16#e5, 16#b) -> {more, 16#05, 16#0f};
dec_huffman_lookup(16#e5, 16#c) -> {more, 16#05, 16#18};
dec_huffman_lookup(16#e5, 16#d) -> {more, 16#05, 16#1f};
dec_huffman_lookup(16#e5, 16#e) -> {more, 16#05, 16#29};
dec_huffman_lookup(16#e5, 16#f) -> {ok, 16#05, 16#38};
dec_huffman_lookup(16#e6, 16#0) -> {more, 16#06, 16#03};
dec_huffman_lookup(16#e6, 16#1) -> {more, 16#06, 16#06};
dec_huffman_lookup(16#e6, 16#2) -> {more, 16#06, 16#0a};
dec_huffman_lookup(16#e6, 16#3) -> {more, 16#06, 16#0f};
dec_huffman_lookup(16#e6, 16#4) -> {more, 16#06, 16#18};
dec_huffman_lookup(16#e6, 16#5) -> {more, 16#06, 16#1f};
dec_huffman_lookup(16#e6, 16#6) -> {more, 16#06, 16#29};
dec_huffman_lookup(16#e6, 16#7) -> {ok, 16#06, 16#38};
dec_huffman_lookup(16#e6, 16#8) -> {more, 16#07, 16#03};
dec_huffman_lookup(16#e6, 16#9) -> {more, 16#07, 16#06};
dec_huffman_lookup(16#e6, 16#a) -> {more, 16#07, 16#0a};
dec_huffman_lookup(16#e6, 16#b) -> {more, 16#07, 16#0f};
dec_huffman_lookup(16#e6, 16#c) -> {more, 16#07, 16#18};
dec_huffman_lookup(16#e6, 16#d) -> {more, 16#07, 16#1f};
dec_huffman_lookup(16#e6, 16#e) -> {more, 16#07, 16#29};
dec_huffman_lookup(16#e6, 16#f) -> {ok, 16#07, 16#38};
dec_huffman_lookup(16#e7, 16#0) -> {more, 16#08, 16#01};
dec_huffman_lookup(16#e7, 16#1) -> {ok, 16#08, 16#16};
dec_huffman_lookup(16#e7, 16#2) -> {more, 16#0b, 16#01};
dec_huffman_lookup(16#e7, 16#3) -> {ok, 16#0b, 16#16};
dec_huffman_lookup(16#e7, 16#4) -> {more, 16#0c, 16#01};
dec_huffman_lookup(16#e7, 16#5) -> {ok, 16#0c, 16#16};
dec_huffman_lookup(16#e7, 16#6) -> {more, 16#0e, 16#01};
dec_huffman_lookup(16#e7, 16#7) -> {ok, 16#0e, 16#16};
dec_huffman_lookup(16#e7, 16#8) -> {more, 16#0f, 16#01};
dec_huffman_lookup(16#e7, 16#9) -> {ok, 16#0f, 16#16};
dec_huffman_lookup(16#e7, 16#a) -> {more, 16#10, 16#01};
dec_huffman_lookup(16#e7, 16#b) -> {ok, 16#10, 16#16};
dec_huffman_lookup(16#e7, 16#c) -> {more, 16#11, 16#01};
dec_huffman_lookup(16#e7, 16#d) -> {ok, 16#11, 16#16};
dec_huffman_lookup(16#e7, 16#e) -> {more, 16#12, 16#01};
dec_huffman_lookup(16#e7, 16#f) -> {ok, 16#12, 16#16};
dec_huffman_lookup(16#e8, 16#0) -> {more, 16#08, 16#02};
dec_huffman_lookup(16#e8, 16#1) -> {more, 16#08, 16#09};
dec_huffman_lookup(16#e8, 16#2) -> {more, 16#08, 16#17};
dec_huffman_lookup(16#e8, 16#3) -> {ok, 16#08, 16#28};
dec_huffman_lookup(16#e8, 16#4) -> {more, 16#0b, 16#02};
dec_huffman_lookup(16#e8, 16#5) -> {more, 16#0b, 16#09};
dec_huffman_lookup(16#e8, 16#6) -> {more, 16#0b, 16#17};
dec_huffman_lookup(16#e8, 16#7) -> {ok, 16#0b, 16#28};
dec_huffman_lookup(16#e8, 16#8) -> {more, 16#0c, 16#02};
dec_huffman_lookup(16#e8, 16#9) -> {more, 16#0c, 16#09};
dec_huffman_lookup(16#e8, 16#a) -> {more, 16#0c, 16#17};
dec_huffman_lookup(16#e8, 16#b) -> {ok, 16#0c, 16#28};
dec_huffman_lookup(16#e8, 16#c) -> {more, 16#0e, 16#02};
dec_huffman_lookup(16#e8, 16#d) -> {more, 16#0e, 16#09};
dec_huffman_lookup(16#e8, 16#e) -> {more, 16#0e, 16#17};
dec_huffman_lookup(16#e8, 16#f) -> {ok, 16#0e, 16#28};
dec_huffman_lookup(16#e9, 16#0) -> {more, 16#08, 16#03};
dec_huffman_lookup(16#e9, 16#1) -> {more, 16#08, 16#06};
dec_huffman_lookup(16#e9, 16#2) -> {more, 16#08, 16#0a};
dec_huffman_lookup(16#e9, 16#3) -> {more, 16#08, 16#0f};
dec_huffman_lookup(16#e9, 16#4) -> {more, 16#08, 16#18};
dec_huffman_lookup(16#e9, 16#5) -> {more, 16#08, 16#1f};
dec_huffman_lookup(16#e9, 16#6) -> {more, 16#08, 16#29};
dec_huffman_lookup(16#e9, 16#7) -> {ok, 16#08, 16#38};
dec_huffman_lookup(16#e9, 16#8) -> {more, 16#0b, 16#03};
dec_huffman_lookup(16#e9, 16#9) -> {more, 16#0b, 16#06};
dec_huffman_lookup(16#e9, 16#a) -> {more, 16#0b, 16#0a};
dec_huffman_lookup(16#e9, 16#b) -> {more, 16#0b, 16#0f};
dec_huffman_lookup(16#e9, 16#c) -> {more, 16#0b, 16#18};
dec_huffman_lookup(16#e9, 16#d) -> {more, 16#0b, 16#1f};
dec_huffman_lookup(16#e9, 16#e) -> {more, 16#0b, 16#29};
dec_huffman_lookup(16#e9, 16#f) -> {ok, 16#0b, 16#38};
dec_huffman_lookup(16#ea, 16#0) -> {more, 16#0c, 16#03};
dec_huffman_lookup(16#ea, 16#1) -> {more, 16#0c, 16#06};
dec_huffman_lookup(16#ea, 16#2) -> {more, 16#0c, 16#0a};
dec_huffman_lookup(16#ea, 16#3) -> {more, 16#0c, 16#0f};
dec_huffman_lookup(16#ea, 16#4) -> {more, 16#0c, 16#18};
dec_huffman_lookup(16#ea, 16#5) -> {more, 16#0c, 16#1f};
dec_huffman_lookup(16#ea, 16#6) -> {more, 16#0c, 16#29};
dec_huffman_lookup(16#ea, 16#7) -> {ok, 16#0c, 16#38};
dec_huffman_lookup(16#ea, 16#8) -> {more, 16#0e, 16#03};
dec_huffman_lookup(16#ea, 16#9) -> {more, 16#0e, 16#06};
dec_huffman_lookup(16#ea, 16#a) -> {more, 16#0e, 16#0a};
dec_huffman_lookup(16#ea, 16#b) -> {more, 16#0e, 16#0f};
dec_huffman_lookup(16#ea, 16#c) -> {more, 16#0e, 16#18};
dec_huffman_lookup(16#ea, 16#d) -> {more, 16#0e, 16#1f};
dec_huffman_lookup(16#ea, 16#e) -> {more, 16#0e, 16#29};
dec_huffman_lookup(16#ea, 16#f) -> {ok, 16#0e, 16#38};
dec_huffman_lookup(16#eb, 16#0) -> {more, 16#0f, 16#02};
dec_huffman_lookup(16#eb, 16#1) -> {more, 16#0f, 16#09};
dec_huffman_lookup(16#eb, 16#2) -> {more, 16#0f, 16#17};
dec_huffman_lookup(16#eb, 16#3) -> {ok, 16#0f, 16#28};
dec_huffman_lookup(16#eb, 16#4) -> {more, 16#10, 16#02};
dec_huffman_lookup(16#eb, 16#5) -> {more, 16#10, 16#09};
dec_huffman_lookup(16#eb, 16#6) -> {more, 16#10, 16#17};
dec_huffman_lookup(16#eb, 16#7) -> {ok, 16#10, 16#28};
dec_huffman_lookup(16#eb, 16#8) -> {more, 16#11, 16#02};
dec_huffman_lookup(16#eb, 16#9) -> {more, 16#11, 16#09};
dec_huffman_lookup(16#eb, 16#a) -> {more, 16#11, 16#17};
dec_huffman_lookup(16#eb, 16#b) -> {ok, 16#11, 16#28};
dec_huffman_lookup(16#eb, 16#c) -> {more, 16#12, 16#02};
dec_huffman_lookup(16#eb, 16#d) -> {more, 16#12, 16#09};
dec_huffman_lookup(16#eb, 16#e) -> {more, 16#12, 16#17};
dec_huffman_lookup(16#eb, 16#f) -> {ok, 16#12, 16#28};
dec_huffman_lookup(16#ec, 16#0) -> {more, 16#0f, 16#03};
dec_huffman_lookup(16#ec, 16#1) -> {more, 16#0f, 16#06};
dec_huffman_lookup(16#ec, 16#2) -> {more, 16#0f, 16#0a};
dec_huffman_lookup(16#ec, 16#3) -> {more, 16#0f, 16#0f};
dec_huffman_lookup(16#ec, 16#4) -> {more, 16#0f, 16#18};
dec_huffman_lookup(16#ec, 16#5) -> {more, 16#0f, 16#1f};
dec_huffman_lookup(16#ec, 16#6) -> {more, 16#0f, 16#29};
dec_huffman_lookup(16#ec, 16#7) -> {ok, 16#0f, 16#38};
dec_huffman_lookup(16#ec, 16#8) -> {more, 16#10, 16#03};
dec_huffman_lookup(16#ec, 16#9) -> {more, 16#10, 16#06};
dec_huffman_lookup(16#ec, 16#a) -> {more, 16#10, 16#0a};
dec_huffman_lookup(16#ec, 16#b) -> {more, 16#10, 16#0f};
dec_huffman_lookup(16#ec, 16#c) -> {more, 16#10, 16#18};
dec_huffman_lookup(16#ec, 16#d) -> {more, 16#10, 16#1f};
dec_huffman_lookup(16#ec, 16#e) -> {more, 16#10, 16#29};
dec_huffman_lookup(16#ec, 16#f) -> {ok, 16#10, 16#38};
dec_huffman_lookup(16#ed, 16#0) -> {more, 16#11, 16#03};
dec_huffman_lookup(16#ed, 16#1) -> {more, 16#11, 16#06};
dec_huffman_lookup(16#ed, 16#2) -> {more, 16#11, 16#0a};
dec_huffman_lookup(16#ed, 16#3) -> {more, 16#11, 16#0f};
dec_huffman_lookup(16#ed, 16#4) -> {more, 16#11, 16#18};
dec_huffman_lookup(16#ed, 16#5) -> {more, 16#11, 16#1f};
dec_huffman_lookup(16#ed, 16#6) -> {more, 16#11, 16#29};
dec_huffman_lookup(16#ed, 16#7) -> {ok, 16#11, 16#38};
dec_huffman_lookup(16#ed, 16#8) -> {more, 16#12, 16#03};
dec_huffman_lookup(16#ed, 16#9) -> {more, 16#12, 16#06};
dec_huffman_lookup(16#ed, 16#a) -> {more, 16#12, 16#0a};
dec_huffman_lookup(16#ed, 16#b) -> {more, 16#12, 16#0f};
dec_huffman_lookup(16#ed, 16#c) -> {more, 16#12, 16#18};
dec_huffman_lookup(16#ed, 16#d) -> {more, 16#12, 16#1f};
dec_huffman_lookup(16#ed, 16#e) -> {more, 16#12, 16#29};
dec_huffman_lookup(16#ed, 16#f) -> {ok, 16#12, 16#38};
dec_huffman_lookup(16#ee, 16#0) -> {ok, 16#13, 16#00};
dec_huffman_lookup(16#ee, 16#1) -> {ok, 16#14, 16#00};
dec_huffman_lookup(16#ee, 16#2) -> {ok, 16#15, 16#00};
dec_huffman_lookup(16#ee, 16#3) -> {ok, 16#17, 16#00};
dec_huffman_lookup(16#ee, 16#4) -> {ok, 16#18, 16#00};
dec_huffman_lookup(16#ee, 16#5) -> {ok, 16#19, 16#00};
dec_huffman_lookup(16#ee, 16#6) -> {ok, 16#1a, 16#00};
dec_huffman_lookup(16#ee, 16#7) -> {ok, 16#1b, 16#00};
dec_huffman_lookup(16#ee, 16#8) -> {ok, 16#1c, 16#00};
dec_huffman_lookup(16#ee, 16#9) -> {ok, 16#1d, 16#00};
dec_huffman_lookup(16#ee, 16#a) -> {ok, 16#1e, 16#00};
dec_huffman_lookup(16#ee, 16#b) -> {ok, 16#1f, 16#00};
dec_huffman_lookup(16#ee, 16#c) -> {ok, 16#7f, 16#00};
dec_huffman_lookup(16#ee, 16#d) -> {ok, 16#dc, 16#00};
dec_huffman_lookup(16#ee, 16#e) -> {ok, 16#f9, 16#00};
dec_huffman_lookup(16#ee, 16#f) -> {ok, undefined, 16#fd};
dec_huffman_lookup(16#ef, 16#0) -> {more, 16#13, 16#01};
dec_huffman_lookup(16#ef, 16#1) -> {ok, 16#13, 16#16};
dec_huffman_lookup(16#ef, 16#2) -> {more, 16#14, 16#01};
dec_huffman_lookup(16#ef, 16#3) -> {ok, 16#14, 16#16};
dec_huffman_lookup(16#ef, 16#4) -> {more, 16#15, 16#01};
dec_huffman_lookup(16#ef, 16#5) -> {ok, 16#15, 16#16};
dec_huffman_lookup(16#ef, 16#6) -> {more, 16#17, 16#01};
dec_huffman_lookup(16#ef, 16#7) -> {ok, 16#17, 16#16};
dec_huffman_lookup(16#ef, 16#8) -> {more, 16#18, 16#01};
dec_huffman_lookup(16#ef, 16#9) -> {ok, 16#18, 16#16};
dec_huffman_lookup(16#ef, 16#a) -> {more, 16#19, 16#01};
dec_huffman_lookup(16#ef, 16#b) -> {ok, 16#19, 16#16};
dec_huffman_lookup(16#ef, 16#c) -> {more, 16#1a, 16#01};
dec_huffman_lookup(16#ef, 16#d) -> {ok, 16#1a, 16#16};
dec_huffman_lookup(16#ef, 16#e) -> {more, 16#1b, 16#01};
dec_huffman_lookup(16#ef, 16#f) -> {ok, 16#1b, 16#16};
dec_huffman_lookup(16#f0, 16#0) -> {more, 16#13, 16#02};
dec_huffman_lookup(16#f0, 16#1) -> {more, 16#13, 16#09};
dec_huffman_lookup(16#f0, 16#2) -> {more, 16#13, 16#17};
dec_huffman_lookup(16#f0, 16#3) -> {ok, 16#13, 16#28};
dec_huffman_lookup(16#f0, 16#4) -> {more, 16#14, 16#02};
dec_huffman_lookup(16#f0, 16#5) -> {more, 16#14, 16#09};
dec_huffman_lookup(16#f0, 16#6) -> {more, 16#14, 16#17};
dec_huffman_lookup(16#f0, 16#7) -> {ok, 16#14, 16#28};
dec_huffman_lookup(16#f0, 16#8) -> {more, 16#15, 16#02};
dec_huffman_lookup(16#f0, 16#9) -> {more, 16#15, 16#09};
dec_huffman_lookup(16#f0, 16#a) -> {more, 16#15, 16#17};
dec_huffman_lookup(16#f0, 16#b) -> {ok, 16#15, 16#28};
dec_huffman_lookup(16#f0, 16#c) -> {more, 16#17, 16#02};
dec_huffman_lookup(16#f0, 16#d) -> {more, 16#17, 16#09};
dec_huffman_lookup(16#f0, 16#e) -> {more, 16#17, 16#17};
dec_huffman_lookup(16#f0, 16#f) -> {ok, 16#17, 16#28};
dec_huffman_lookup(16#f1, 16#0) -> {more, 16#13, 16#03};
dec_huffman_lookup(16#f1, 16#1) -> {more, 16#13, 16#06};
dec_huffman_lookup(16#f1, 16#2) -> {more, 16#13, 16#0a};
dec_huffman_lookup(16#f1, 16#3) -> {more, 16#13, 16#0f};
dec_huffman_lookup(16#f1, 16#4) -> {more, 16#13, 16#18};
dec_huffman_lookup(16#f1, 16#5) -> {more, 16#13, 16#1f};
dec_huffman_lookup(16#f1, 16#6) -> {more, 16#13, 16#29};
dec_huffman_lookup(16#f1, 16#7) -> {ok, 16#13, 16#38};
dec_huffman_lookup(16#f1, 16#8) -> {more, 16#14, 16#03};
dec_huffman_lookup(16#f1, 16#9) -> {more, 16#14, 16#06};
dec_huffman_lookup(16#f1, 16#a) -> {more, 16#14, 16#0a};
dec_huffman_lookup(16#f1, 16#b) -> {more, 16#14, 16#0f};
dec_huffman_lookup(16#f1, 16#c) -> {more, 16#14, 16#18};
dec_huffman_lookup(16#f1, 16#d) -> {more, 16#14, 16#1f};
dec_huffman_lookup(16#f1, 16#e) -> {more, 16#14, 16#29};
dec_huffman_lookup(16#f1, 16#f) -> {ok, 16#14, 16#38};
dec_huffman_lookup(16#f2, 16#0) -> {more, 16#15, 16#03};
dec_huffman_lookup(16#f2, 16#1) -> {more, 16#15, 16#06};
dec_huffman_lookup(16#f2, 16#2) -> {more, 16#15, 16#0a};
dec_huffman_lookup(16#f2, 16#3) -> {more, 16#15, 16#0f};
dec_huffman_lookup(16#f2, 16#4) -> {more, 16#15, 16#18};
dec_huffman_lookup(16#f2, 16#5) -> {more, 16#15, 16#1f};
dec_huffman_lookup(16#f2, 16#6) -> {more, 16#15, 16#29};
dec_huffman_lookup(16#f2, 16#7) -> {ok, 16#15, 16#38};
dec_huffman_lookup(16#f2, 16#8) -> {more, 16#17, 16#03};
dec_huffman_lookup(16#f2, 16#9) -> {more, 16#17, 16#06};
dec_huffman_lookup(16#f2, 16#a) -> {more, 16#17, 16#0a};
dec_huffman_lookup(16#f2, 16#b) -> {more, 16#17, 16#0f};
dec_huffman_lookup(16#f2, 16#c) -> {more, 16#17, 16#18};
dec_huffman_lookup(16#f2, 16#d) -> {more, 16#17, 16#1f};
dec_huffman_lookup(16#f2, 16#e) -> {more, 16#17, 16#29};
dec_huffman_lookup(16#f2, 16#f) -> {ok, 16#17, 16#38};
dec_huffman_lookup(16#f3, 16#0) -> {more, 16#18, 16#02};
dec_huffman_lookup(16#f3, 16#1) -> {more, 16#18, 16#09};
dec_huffman_lookup(16#f3, 16#2) -> {more, 16#18, 16#17};
dec_huffman_lookup(16#f3, 16#3) -> {ok, 16#18, 16#28};
dec_huffman_lookup(16#f3, 16#4) -> {more, 16#19, 16#02};
dec_huffman_lookup(16#f3, 16#5) -> {more, 16#19, 16#09};
dec_huffman_lookup(16#f3, 16#6) -> {more, 16#19, 16#17};
dec_huffman_lookup(16#f3, 16#7) -> {ok, 16#19, 16#28};
dec_huffman_lookup(16#f3, 16#8) -> {more, 16#1a, 16#02};
dec_huffman_lookup(16#f3, 16#9) -> {more, 16#1a, 16#09};
dec_huffman_lookup(16#f3, 16#a) -> {more, 16#1a, 16#17};
dec_huffman_lookup(16#f3, 16#b) -> {ok, 16#1a, 16#28};
dec_huffman_lookup(16#f3, 16#c) -> {more, 16#1b, 16#02};
dec_huffman_lookup(16#f3, 16#d) -> {more, 16#1b, 16#09};
dec_huffman_lookup(16#f3, 16#e) -> {more, 16#1b, 16#17};
dec_huffman_lookup(16#f3, 16#f) -> {ok, 16#1b, 16#28};
dec_huffman_lookup(16#f4, 16#0) -> {more, 16#18, 16#03};
dec_huffman_lookup(16#f4, 16#1) -> {more, 16#18, 16#06};
dec_huffman_lookup(16#f4, 16#2) -> {more, 16#18, 16#0a};
dec_huffman_lookup(16#f4, 16#3) -> {more, 16#18, 16#0f};
dec_huffman_lookup(16#f4, 16#4) -> {more, 16#18, 16#18};
dec_huffman_lookup(16#f4, 16#5) -> {more, 16#18, 16#1f};
dec_huffman_lookup(16#f4, 16#6) -> {more, 16#18, 16#29};
dec_huffman_lookup(16#f4, 16#7) -> {ok, 16#18, 16#38};
dec_huffman_lookup(16#f4, 16#8) -> {more, 16#19, 16#03};
dec_huffman_lookup(16#f4, 16#9) -> {more, 16#19, 16#06};
dec_huffman_lookup(16#f4, 16#a) -> {more, 16#19, 16#0a};
dec_huffman_lookup(16#f4, 16#b) -> {more, 16#19, 16#0f};
dec_huffman_lookup(16#f4, 16#c) -> {more, 16#19, 16#18};
dec_huffman_lookup(16#f4, 16#d) -> {more, 16#19, 16#1f};
dec_huffman_lookup(16#f4, 16#e) -> {more, 16#19, 16#29};
dec_huffman_lookup(16#f4, 16#f) -> {ok, 16#19, 16#38};
dec_huffman_lookup(16#f5, 16#0) -> {more, 16#1a, 16#03};
dec_huffman_lookup(16#f5, 16#1) -> {more, 16#1a, 16#06};
dec_huffman_lookup(16#f5, 16#2) -> {more, 16#1a, 16#0a};
dec_huffman_lookup(16#f5, 16#3) -> {more, 16#1a, 16#0f};
dec_huffman_lookup(16#f5, 16#4) -> {more, 16#1a, 16#18};
dec_huffman_lookup(16#f5, 16#5) -> {more, 16#1a, 16#1f};
dec_huffman_lookup(16#f5, 16#6) -> {more, 16#1a, 16#29};
dec_huffman_lookup(16#f5, 16#7) -> {ok, 16#1a, 16#38};
dec_huffman_lookup(16#f5, 16#8) -> {more, 16#1b, 16#03};
dec_huffman_lookup(16#f5, 16#9) -> {more, 16#1b, 16#06};
dec_huffman_lookup(16#f5, 16#a) -> {more, 16#1b, 16#0a};
dec_huffman_lookup(16#f5, 16#b) -> {more, 16#1b, 16#0f};
dec_huffman_lookup(16#f5, 16#c) -> {more, 16#1b, 16#18};
dec_huffman_lookup(16#f5, 16#d) -> {more, 16#1b, 16#1f};
dec_huffman_lookup(16#f5, 16#e) -> {more, 16#1b, 16#29};
dec_huffman_lookup(16#f5, 16#f) -> {ok, 16#1b, 16#38};
dec_huffman_lookup(16#f6, 16#0) -> {more, 16#1c, 16#01};
dec_huffman_lookup(16#f6, 16#1) -> {ok, 16#1c, 16#16};
dec_huffman_lookup(16#f6, 16#2) -> {more, 16#1d, 16#01};
dec_huffman_lookup(16#f6, 16#3) -> {ok, 16#1d, 16#16};
dec_huffman_lookup(16#f6, 16#4) -> {more, 16#1e, 16#01};
dec_huffman_lookup(16#f6, 16#5) -> {ok, 16#1e, 16#16};
dec_huffman_lookup(16#f6, 16#6) -> {more, 16#1f, 16#01};
dec_huffman_lookup(16#f6, 16#7) -> {ok, 16#1f, 16#16};
dec_huffman_lookup(16#f6, 16#8) -> {more, 16#7f, 16#01};
dec_huffman_lookup(16#f6, 16#9) -> {ok, 16#7f, 16#16};
dec_huffman_lookup(16#f6, 16#a) -> {more, 16#dc, 16#01};
dec_huffman_lookup(16#f6, 16#b) -> {ok, 16#dc, 16#16};
dec_huffman_lookup(16#f6, 16#c) -> {more, 16#f9, 16#01};
dec_huffman_lookup(16#f6, 16#d) -> {ok, 16#f9, 16#16};
dec_huffman_lookup(16#f6, 16#e) -> {more, undefined, 16#fe};
dec_huffman_lookup(16#f6, 16#f) -> {ok, undefined, 16#ff};
dec_huffman_lookup(16#f7, 16#0) -> {more, 16#1c, 16#02};
dec_huffman_lookup(16#f7, 16#1) -> {more, 16#1c, 16#09};
dec_huffman_lookup(16#f7, 16#2) -> {more, 16#1c, 16#17};
dec_huffman_lookup(16#f7, 16#3) -> {ok, 16#1c, 16#28};
dec_huffman_lookup(16#f7, 16#4) -> {more, 16#1d, 16#02};
dec_huffman_lookup(16#f7, 16#5) -> {more, 16#1d, 16#09};
dec_huffman_lookup(16#f7, 16#6) -> {more, 16#1d, 16#17};
dec_huffman_lookup(16#f7, 16#7) -> {ok, 16#1d, 16#28};
dec_huffman_lookup(16#f7, 16#8) -> {more, 16#1e, 16#02};
dec_huffman_lookup(16#f7, 16#9) -> {more, 16#1e, 16#09};
dec_huffman_lookup(16#f7, 16#a) -> {more, 16#1e, 16#17};
dec_huffman_lookup(16#f7, 16#b) -> {ok, 16#1e, 16#28};
dec_huffman_lookup(16#f7, 16#c) -> {more, 16#1f, 16#02};
dec_huffman_lookup(16#f7, 16#d) -> {more, 16#1f, 16#09};
dec_huffman_lookup(16#f7, 16#e) -> {more, 16#1f, 16#17};
dec_huffman_lookup(16#f7, 16#f) -> {ok, 16#1f, 16#28};
dec_huffman_lookup(16#f8, 16#0) -> {more, 16#1c, 16#03};
dec_huffman_lookup(16#f8, 16#1) -> {more, 16#1c, 16#06};
dec_huffman_lookup(16#f8, 16#2) -> {more, 16#1c, 16#0a};
dec_huffman_lookup(16#f8, 16#3) -> {more, 16#1c, 16#0f};
dec_huffman_lookup(16#f8, 16#4) -> {more, 16#1c, 16#18};
dec_huffman_lookup(16#f8, 16#5) -> {more, 16#1c, 16#1f};
dec_huffman_lookup(16#f8, 16#6) -> {more, 16#1c, 16#29};
dec_huffman_lookup(16#f8, 16#7) -> {ok, 16#1c, 16#38};
dec_huffman_lookup(16#f8, 16#8) -> {more, 16#1d, 16#03};
dec_huffman_lookup(16#f8, 16#9) -> {more, 16#1d, 16#06};
dec_huffman_lookup(16#f8, 16#a) -> {more, 16#1d, 16#0a};
dec_huffman_lookup(16#f8, 16#b) -> {more, 16#1d, 16#0f};
dec_huffman_lookup(16#f8, 16#c) -> {more, 16#1d, 16#18};
dec_huffman_lookup(16#f8, 16#d) -> {more, 16#1d, 16#1f};
dec_huffman_lookup(16#f8, 16#e) -> {more, 16#1d, 16#29};
dec_huffman_lookup(16#f8, 16#f) -> {ok, 16#1d, 16#38};
dec_huffman_lookup(16#f9, 16#0) -> {more, 16#1e, 16#03};
dec_huffman_lookup(16#f9, 16#1) -> {more, 16#1e, 16#06};
dec_huffman_lookup(16#f9, 16#2) -> {more, 16#1e, 16#0a};
dec_huffman_lookup(16#f9, 16#3) -> {more, 16#1e, 16#0f};
dec_huffman_lookup(16#f9, 16#4) -> {more, 16#1e, 16#18};
dec_huffman_lookup(16#f9, 16#5) -> {more, 16#1e, 16#1f};
dec_huffman_lookup(16#f9, 16#6) -> {more, 16#1e, 16#29};
dec_huffman_lookup(16#f9, 16#7) -> {ok, 16#1e, 16#38};
dec_huffman_lookup(16#f9, 16#8) -> {more, 16#1f, 16#03};
dec_huffman_lookup(16#f9, 16#9) -> {more, 16#1f, 16#06};
dec_huffman_lookup(16#f9, 16#a) -> {more, 16#1f, 16#0a};
dec_huffman_lookup(16#f9, 16#b) -> {more, 16#1f, 16#0f};
dec_huffman_lookup(16#f9, 16#c) -> {more, 16#1f, 16#18};
dec_huffman_lookup(16#f9, 16#d) -> {more, 16#1f, 16#1f};
dec_huffman_lookup(16#f9, 16#e) -> {more, 16#1f, 16#29};
dec_huffman_lookup(16#f9, 16#f) -> {ok, 16#1f, 16#38};
dec_huffman_lookup(16#fa, 16#0) -> {more, 16#7f, 16#02};
dec_huffman_lookup(16#fa, 16#1) -> {more, 16#7f, 16#09};
dec_huffman_lookup(16#fa, 16#2) -> {more, 16#7f, 16#17};
dec_huffman_lookup(16#fa, 16#3) -> {ok, 16#7f, 16#28};
dec_huffman_lookup(16#fa, 16#4) -> {more, 16#dc, 16#02};
dec_huffman_lookup(16#fa, 16#5) -> {more, 16#dc, 16#09};
dec_huffman_lookup(16#fa, 16#6) -> {more, 16#dc, 16#17};
dec_huffman_lookup(16#fa, 16#7) -> {ok, 16#dc, 16#28};
dec_huffman_lookup(16#fa, 16#8) -> {more, 16#f9, 16#02};
dec_huffman_lookup(16#fa, 16#9) -> {more, 16#f9, 16#09};
dec_huffman_lookup(16#fa, 16#a) -> {more, 16#f9, 16#17};
dec_huffman_lookup(16#fa, 16#b) -> {ok, 16#f9, 16#28};
dec_huffman_lookup(16#fa, 16#c) -> {ok, 16#0a, 16#00};
dec_huffman_lookup(16#fa, 16#d) -> {ok, 16#0d, 16#00};
dec_huffman_lookup(16#fa, 16#e) -> {ok, 16#16, 16#00};
dec_huffman_lookup(16#fa, 16#f) -> error;
dec_huffman_lookup(16#fb, 16#0) -> {more, 16#7f, 16#03};
dec_huffman_lookup(16#fb, 16#1) -> {more, 16#7f, 16#06};
dec_huffman_lookup(16#fb, 16#2) -> {more, 16#7f, 16#0a};
dec_huffman_lookup(16#fb, 16#3) -> {more, 16#7f, 16#0f};
dec_huffman_lookup(16#fb, 16#4) -> {more, 16#7f, 16#18};
dec_huffman_lookup(16#fb, 16#5) -> {more, 16#7f, 16#1f};
dec_huffman_lookup(16#fb, 16#6) -> {more, 16#7f, 16#29};
dec_huffman_lookup(16#fb, 16#7) -> {ok, 16#7f, 16#38};
dec_huffman_lookup(16#fb, 16#8) -> {more, 16#dc, 16#03};
dec_huffman_lookup(16#fb, 16#9) -> {more, 16#dc, 16#06};
dec_huffman_lookup(16#fb, 16#a) -> {more, 16#dc, 16#0a};
dec_huffman_lookup(16#fb, 16#b) -> {more, 16#dc, 16#0f};
dec_huffman_lookup(16#fb, 16#c) -> {more, 16#dc, 16#18};
dec_huffman_lookup(16#fb, 16#d) -> {more, 16#dc, 16#1f};
dec_huffman_lookup(16#fb, 16#e) -> {more, 16#dc, 16#29};
dec_huffman_lookup(16#fb, 16#f) -> {ok, 16#dc, 16#38};
dec_huffman_lookup(16#fc, 16#0) -> {more, 16#f9, 16#03};
dec_huffman_lookup(16#fc, 16#1) -> {more, 16#f9, 16#06};
dec_huffman_lookup(16#fc, 16#2) -> {more, 16#f9, 16#0a};
dec_huffman_lookup(16#fc, 16#3) -> {more, 16#f9, 16#0f};
dec_huffman_lookup(16#fc, 16#4) -> {more, 16#f9, 16#18};
dec_huffman_lookup(16#fc, 16#5) -> {more, 16#f9, 16#1f};
dec_huffman_lookup(16#fc, 16#6) -> {more, 16#f9, 16#29};
dec_huffman_lookup(16#fc, 16#7) -> {ok, 16#f9, 16#38};
dec_huffman_lookup(16#fc, 16#8) -> {more, 16#0a, 16#01};
dec_huffman_lookup(16#fc, 16#9) -> {ok, 16#0a, 16#16};
dec_huffman_lookup(16#fc, 16#a) -> {more, 16#0d, 16#01};
dec_huffman_lookup(16#fc, 16#b) -> {ok, 16#0d, 16#16};
dec_huffman_lookup(16#fc, 16#c) -> {more, 16#16, 16#01};
dec_huffman_lookup(16#fc, 16#d) -> {ok, 16#16, 16#16};
dec_huffman_lookup(16#fc, 16#e) -> error;
dec_huffman_lookup(16#fc, 16#f) -> error;
dec_huffman_lookup(16#fd, 16#0) -> {more, 16#0a, 16#02};
dec_huffman_lookup(16#fd, 16#1) -> {more, 16#0a, 16#09};
dec_huffman_lookup(16#fd, 16#2) -> {more, 16#0a, 16#17};
dec_huffman_lookup(16#fd, 16#3) -> {ok, 16#0a, 16#28};
dec_huffman_lookup(16#fd, 16#4) -> {more, 16#0d, 16#02};
dec_huffman_lookup(16#fd, 16#5) -> {more, 16#0d, 16#09};
dec_huffman_lookup(16#fd, 16#6) -> {more, 16#0d, 16#17};
dec_huffman_lookup(16#fd, 16#7) -> {ok, 16#0d, 16#28};
dec_huffman_lookup(16#fd, 16#8) -> {more, 16#16, 16#02};
dec_huffman_lookup(16#fd, 16#9) -> {more, 16#16, 16#09};
dec_huffman_lookup(16#fd, 16#a) -> {more, 16#16, 16#17};
dec_huffman_lookup(16#fd, 16#b) -> {ok, 16#16, 16#28};
dec_huffman_lookup(16#fd, 16#c) -> error;
dec_huffman_lookup(16#fd, 16#d) -> error;
dec_huffman_lookup(16#fd, 16#e) -> error;
dec_huffman_lookup(16#fd, 16#f) -> error;
dec_huffman_lookup(16#fe, 16#0) -> {more, 16#0a, 16#03};
dec_huffman_lookup(16#fe, 16#1) -> {more, 16#0a, 16#06};
dec_huffman_lookup(16#fe, 16#2) -> {more, 16#0a, 16#0a};
dec_huffman_lookup(16#fe, 16#3) -> {more, 16#0a, 16#0f};
dec_huffman_lookup(16#fe, 16#4) -> {more, 16#0a, 16#18};
dec_huffman_lookup(16#fe, 16#5) -> {more, 16#0a, 16#1f};
dec_huffman_lookup(16#fe, 16#6) -> {more, 16#0a, 16#29};
dec_huffman_lookup(16#fe, 16#7) -> {ok, 16#0a, 16#38};
dec_huffman_lookup(16#fe, 16#8) -> {more, 16#0d, 16#03};
dec_huffman_lookup(16#fe, 16#9) -> {more, 16#0d, 16#06};
dec_huffman_lookup(16#fe, 16#a) -> {more, 16#0d, 16#0a};
dec_huffman_lookup(16#fe, 16#b) -> {more, 16#0d, 16#0f};
dec_huffman_lookup(16#fe, 16#c) -> {more, 16#0d, 16#18};
dec_huffman_lookup(16#fe, 16#d) -> {more, 16#0d, 16#1f};
dec_huffman_lookup(16#fe, 16#e) -> {more, 16#0d, 16#29};
dec_huffman_lookup(16#fe, 16#f) -> {ok, 16#0d, 16#38};
dec_huffman_lookup(16#ff, 16#0) -> {more, 16#16, 16#03};
dec_huffman_lookup(16#ff, 16#1) -> {more, 16#16, 16#06};
dec_huffman_lookup(16#ff, 16#2) -> {more, 16#16, 16#0a};
dec_huffman_lookup(16#ff, 16#3) -> {more, 16#16, 16#0f};
dec_huffman_lookup(16#ff, 16#4) -> {more, 16#16, 16#18};
dec_huffman_lookup(16#ff, 16#5) -> {more, 16#16, 16#1f};
dec_huffman_lookup(16#ff, 16#6) -> {more, 16#16, 16#29};
dec_huffman_lookup(16#ff, 16#7) -> {ok, 16#16, 16#38};
dec_huffman_lookup(16#ff, 16#8) -> error;
dec_huffman_lookup(16#ff, 16#9) -> error;
dec_huffman_lookup(16#ff, 16#a) -> error;
dec_huffman_lookup(16#ff, 16#b) -> error;
dec_huffman_lookup(16#ff, 16#c) -> error;
dec_huffman_lookup(16#ff, 16#d) -> error;
dec_huffman_lookup(16#ff, 16#e) -> error;
dec_huffman_lookup(16#ff, 16#f) -> error.

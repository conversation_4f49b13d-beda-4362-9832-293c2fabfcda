defmodule N8nElixirWeb.BinaryDataController do
  @moduledoc """
  二进制数据API控制器

  处理文件上传、下载和二进制数据管理的HTTP端点
  """

  use N8nElixirWeb, :controller

  alias N8nElixir.{BinaryData, BinaryDataManager}

  @doc """
  上传文件
  POST /api/binary-data/upload
  """
  def upload(conn, %{"file" => upload, "execution_id" => execution_id} = params) do
    case File.read(upload.path) do
      {:ok, file_data} ->
        opts = [
          max_size: Map.get(params, "max_size", 50 * 1024 * 1024),
          created_by: "api_upload"
        ]
        
        case BinaryDataManager.handle_file_upload(file_data, upload.filename, execution_id, opts) do
          {:ok, upload_id} ->
            conn
            |> put_status(:accepted)
            |> json(%{
              success: true,
              upload_id: upload_id,
              message: "File upload started"
            })
          
          {:error, reason} ->
            conn
            |> put_status(:bad_request)
            |> json(%{
              success: false,
              error: reason
            })
        end
      
      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{
          success: false,
          error: "Failed to read uploaded file: #{reason}"
        })
    end
  end

  @doc """
  下载文件
  GET /api/binary-data/:id/download
  """
  def download(conn, %{"id" => binary_data_id} = params) do
    case BinaryDataManager.handle_file_download(binary_data_id, Map.to_list(params)) do
      {:ok, download_info} ->
        conn
        |> put_resp_content_type(download_info.mime_type)
        |> put_resp_header("content-disposition", "attachment; filename=\"#{download_info.filename}\"")
        |> put_resp_header("content-length", to_string(download_info.file_size))
        |> send_resp(200, download_info.data)
      
      {:error, :not_found} ->
        conn
        |> put_status(:not_found)
        |> json(%{
          success: false,
          error: "Binary data not found"
        })
      
      {:error, reason} ->
        conn
        |> put_status(:internal_server_error)
        |> json(%{
          success: false,
          error: reason
        })
    end
  end

  @doc """
  获取二进制数据信息
  GET /api/binary-data/:id
  """
  def show(conn, %{"id" => binary_data_id}) do
    case BinaryData.get_binary_data_info(binary_data_id) do
      {:ok, info} ->
        json(conn, %{
          success: true,
          data: info
        })
      
      {:error, :not_found} ->
        conn
        |> put_status(:not_found)
        |> json(%{
          success: false,
          error: "Binary data not found"
        })
      
      {:error, reason} ->
        conn
        |> put_status(:internal_server_error)
        |> json(%{
          success: false,
          error: reason
        })
    end
  end

  @doc """
  删除二进制数据
  DELETE /api/binary-data/:id
  """
  def delete(conn, %{"id" => binary_data_id}) do
    case BinaryData.delete_binary_data(binary_data_id) do
      :ok ->
        json(conn, %{
          success: true,
          message: "Binary data deleted successfully"
        })
      
      {:error, :not_found} ->
        conn
        |> put_status(:not_found)
        |> json(%{
          success: false,
          error: "Binary data not found"
        })
      
      {:error, reason} ->
        conn
        |> put_status(:internal_server_error)
        |> json(%{
          success: false,
          error: reason
        })
    end
  end

  @doc """
  批量上传文件
  POST /api/binary-data/batch-upload
  """
  def batch_upload(conn, %{"files" => files, "execution_id" => execution_id} = params) do
    opts = [
      max_size: Map.get(params, "max_size", 50 * 1024 * 1024),
      created_by: "api_batch_upload"
    ]
    
    file_data = Enum.map(files, fn upload ->
      case File.read(upload.path) do
        {:ok, data} ->
          %{data: data, filename: upload.filename}
        {:error, _reason} ->
          nil
      end
    end)
    |> Enum.reject(&is_nil/1)
    
    case BinaryDataManager.handle_batch_operation(:upload_multiple, file_data, execution_id, opts) do
      {:ok, results} ->
        json(conn, %{
          success: true,
          results: results
        })
      
      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{
          success: false,
          error: reason
        })
    end
  end

  @doc """
  转换二进制数据格式
  POST /api/binary-data/:id/convert
  """
  def convert(conn, %{"id" => binary_data_id, "target_format" => target_format} = params) do
    opts = [
      execution_id: Map.get(params, "execution_id"),
      source_id: binary_data_id
    ]
    
    case BinaryData.convert_binary_data(binary_data_id, target_format, opts) do
      {:ok, new_binary_id} ->
        json(conn, %{
          success: true,
          original_id: binary_data_id,
          converted_id: new_binary_id,
          target_format: target_format
        })
      
      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{
          success: false,
          error: reason
        })
    end
  end

  @doc """
  创建缩略图
  POST /api/binary-data/:id/thumbnail
  """
  def create_thumbnail(conn, %{"id" => binary_data_id} = params) do
    opts = [
      width: Map.get(params, "width", 200),
      height: Map.get(params, "height", 200),
      execution_id: Map.get(params, "execution_id")
    ]
    
    case BinaryData.create_thumbnail(binary_data_id, opts) do
      {:ok, thumbnail_id} ->
        json(conn, %{
          success: true,
          original_id: binary_data_id,
          thumbnail_id: thumbnail_id
        })
      
      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{
          success: false,
          error: reason
        })
    end
  end

  @doc """
  获取执行的二进制数据统计
  GET /api/binary-data/execution/:execution_id/stats
  """
  def execution_stats(conn, %{"execution_id" => execution_id}) do
    case BinaryDataManager.get_execution_stats(execution_id) do
      {:ok, stats} ->
        json(conn, %{
          success: true,
          execution_id: execution_id,
          stats: stats
        })
      
      {:error, reason} ->
        conn
        |> put_status(:internal_server_error)
        |> json(%{
          success: false,
          error: reason
        })
    end
  end

  @doc """
  清理执行的二进制数据
  DELETE /api/binary-data/execution/:execution_id
  """
  def cleanup_execution(conn, %{"execution_id" => execution_id}) do
    BinaryDataManager.cleanup_execution_data(execution_id)
    
    json(conn, %{
      success: true,
      message: "Cleanup initiated for execution #{execution_id}"
    })
  end

  @doc """
  压缩多个文件为ZIP
  POST /api/binary-data/compress
  """
  def compress(conn, %{"binary_ids" => binary_ids, "execution_id" => execution_id} = params) do
    opts = [
      execution_id: execution_id,
      archive_name: Map.get(params, "archive_name", "archive.zip")
    ]
    
    case BinaryDataManager.handle_batch_operation(:compress, binary_ids, execution_id, opts) do
      {:ok, %{archive_id: archive_id}} ->
        json(conn, %{
          success: true,
          archive_id: archive_id,
          compressed_files: length(binary_ids)
        })
      
      {:error, reason} ->
        conn
        |> put_status(:bad_request)
        |> json(%{
          success: false,
          error: reason
        })
    end
  end

  @doc """
  流式下载大文件
  GET /api/binary-data/:id/stream
  """
  def stream_download(conn, %{"id" => binary_data_id}) do
    case BinaryData.get_binary_data_stream(binary_data_id) do
      {:ok, stream, info} ->
        conn
        |> put_resp_content_type(info.mime_type)
        |> put_resp_header("content-disposition", "attachment; filename=\"#{info.file_name}\"")
        |> put_resp_header("content-length", to_string(info.file_size))
        |> send_chunked(200)
        |> stream_file_chunks(stream)
      
      {:error, :not_found} ->
        conn
        |> put_status(:not_found)
        |> json(%{
          success: false,
          error: "Binary data not found"
        })
      
      {:error, reason} ->
        conn
        |> put_status(:internal_server_error)
        |> json(%{
          success: false,
          error: reason
        })
    end
  end

  # 私有辅助函数

  defp stream_file_chunks(conn, stream) do
    Enum.reduce_while(stream, conn, fn chunk, acc_conn ->
      case Plug.Conn.chunk(acc_conn, chunk) do
        {:ok, new_conn} -> {:cont, new_conn}
        {:error, _reason} -> {:halt, acc_conn}
      end
    end)
  end
end
defmodule N8nElixir.Repo.Migrations.CreateTriggers do
  use Ecto.Migration

  def change do
    create table(:triggers, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :name, :string, null: false
      add :type, :string, null: false
      add :status, :string, default: "active"
      add :configuration, :map, default: %{}
      add :trigger_count, :integer, default: 0
      add :last_triggered_at, :utc_datetime
      add :last_trigger_status, :string, default: "success"
      add :workflow_id, references(:workflows, type: :binary_id), null: false
      
      timestamps()
    end

    create index(:triggers, [:workflow_id])
    create index(:triggers, [:type])
    create index(:triggers, [:status])
    create index(:triggers, [:last_triggered_at])
  end
end
defmodule N8nElixirWeb.NodePropertiesComponent do
  @moduledoc """
  节点属性编辑组件

  提供节点参数配置界面，支持不同类型的参数输入
  """
  use N8nElixirWeb, :live_component

  alias N8nElixir.NodeTypes

  @impl true
  def update(%{node_id: node_id, node: node, node_types: node_types} = assigns, socket) do
    # 获取节点类型描述
    {:ok, node_description} = NodeTypes.get_node_description(node["type"])
    
    socket = 
      socket
      |> assign(assigns)
      |> assign(:node_description, node_description)
      |> assign(:parameters, node["parameters"] || %{})
      |> assign(:credentials, node["credentials"] || %{})

    {:ok, socket}
  end

  @impl true
  def handle_event("update_parameter", %{"name" => param_name, "value" => value}, socket) do
    updated_parameters = Map.put(socket.assigns.parameters, param_name, value)
    
    # 发送参数更新事件到父组件
    send(self(), {:update_node_parameters, socket.assigns.node_id, updated_parameters})
    
    {:noreply, assign(socket, :parameters, updated_parameters)}
  end

  @impl true
  def handle_event("update_credential", %{"type" => cred_type, "value" => value}, socket) do
    updated_credentials = Map.put(socket.assigns.credentials, cred_type, value)
    
    # 发送凭证更新事件到父组件
    send(self(), {:update_node_credentials, socket.assigns.node_id, updated_credentials})
    
    {:noreply, assign(socket, :credentials, updated_credentials)}
  end

  @impl true
  def handle_event("test_node", _params, socket) do
    # 测试节点连接
    node_type = socket.assigns.node["type"]
    parameters = socket.assigns.parameters
    credentials = socket.assigns.credentials

    case NodeTypes.get_node_type(node_type) do
      {:ok, node_module} ->
        if function_exported?(node_module, :test_connection, 2) do
          case node_module.test_connection(credentials, parameters) do
            :ok ->
              send(self(), {:show_flash, :info, "Node connection test successful"})
            {:error, reason} ->
              send(self(), {:show_flash, :error, "Connection test failed: #{reason}"})
          end
        else
          send(self(), {:show_flash, :info, "Test connection not available for this node type"})
        end
      {:error, reason} ->
        send(self(), {:show_flash, :error, "Node type not found: #{reason}"})
    end

    {:noreply, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="node-properties">
      <div class="properties-header">
        <h3><%= @node["name"] %></h3>
        <div class="node-type-info">
          <span class="node-type"><%= @node["type"] %></span>
          <button class="btn btn-sm btn-secondary" phx-click="test_node" phx-target={@myself}>
            <i class="fa fa-plug"></i> Test
          </button>
        </div>
      </div>

      <div class="properties-content">
        <!-- 基本设置 -->
        <div class="property-section">
          <h4>Node Settings</h4>
          
          <div class="form-group">
            <label>Node Name</label>
            <input type="text" 
                   value={@node["name"]} 
                   phx-blur="update_node_name"
                   phx-value-node_id={@node_id}
                   class="form-control" />
          </div>

          <div class="form-group">
            <label>
              <input type="checkbox" 
                     checked={@node["disabled"] || false}
                     phx-click="toggle_node_disabled"
                     phx-value-node_id={@node_id} />
              Disable Node
            </label>
          </div>
        </div>

        <!-- 凭证设置 -->
        <%= if length(@node_description[:credentials] || []) > 0 do %>
          <div class="property-section">
            <h4>Credentials</h4>
            
            <%= for credential_def <- @node_description[:credentials] do %>
              <div class="form-group">
                <label><%= credential_def[:displayName] || credential_def[:name] %></label>
                <select class="form-control" 
                        phx-change="update_credential"
                        phx-value-type={credential_def[:name]}
                        phx-target={@myself}>
                  <option value="">Select credential...</option>
                  <!-- 这里应该列出可用的凭证 -->
                  <option value="test-credential">Test Credential</option>
                </select>
              </div>
            <% end %>
          </div>
        <% end %>

        <!-- 参数设置 -->
        <%= if length(@node_description[:properties] || []) > 0 do %>
          <div class="property-section">
            <h4>Parameters</h4>
            
            <%= for property <- @node_description[:properties] do %>
              <%= render_property(property, @parameters, @myself) %>
            <% end %>
          </div>
        <% end %>

        <!-- 执行数据 -->
        <%= if @execution_data do %>
          <div class="property-section">
            <h4>Execution Data</h4>
            <div class="execution-info">
              <div class="execution-status">
                <span class="status-label">Status:</span>
                <span class="status-value success">Executed</span>
              </div>
              <div class="execution-time">
                <span class="time-label">Execution Time:</span>
                <span class="time-value"><%= get_execution_time(@execution_data) %>ms</span>
              </div>
            </div>
            
            <div class="data-preview">
              <h5>Output Data</h5>
              <pre class="code-block"><%= format_execution_data(@execution_data) %></pre>
            </div>
          </div>
        <% end %>
      </div>
    </div>

    <style>
    .node-properties {
      height: 100%;
      display: flex;
      flex-direction: column;
    }

    .properties-header {
      padding: 20px;
      border-bottom: 1px solid #eee;
      background: #f8f9fa;
    }

    .properties-header h3 {
      margin: 0 0 8px 0;
      color: #333;
    }

    .node-type-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .node-type {
      font-size: 12px;
      color: #666;
      background: #e9ecef;
      padding: 2px 8px;
      border-radius: 12px;
    }

    .properties-content {
      flex: 1;
      overflow-y: auto;
      padding: 20px;
    }

    .property-section {
      margin-bottom: 30px;
    }

    .property-section h4 {
      margin: 0 0 15px 0;
      color: #555;
      font-size: 14px;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      border-bottom: 1px solid #eee;
      padding-bottom: 8px;
    }

    .form-group {
      margin-bottom: 15px;
    }

    .form-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
      color: #333;
      font-size: 13px;
    }

    .form-control {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 13px;
      transition: border-color 0.2s ease;
    }

    .form-control:focus {
      outline: none;
      border-color: #007bff;
      box-shadow: 0 0 0 2px rgba(0,123,255,0.1);
    }

    .checkbox-group {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .checkbox-group input[type="checkbox"] {
      margin-right: 8px;
    }

    .execution-info {
      background: #f8f9fa;
      padding: 12px;
      border-radius: 4px;
      margin-bottom: 15px;
    }

    .execution-status, .execution-time {
      display: flex;
      justify-content: space-between;
      margin-bottom: 8px;
    }

    .status-label, .time-label {
      font-weight: 500;
      color: #666;
    }

    .status-value.success {
      color: #28a745;
      font-weight: 600;
    }

    .data-preview h5 {
      margin: 0 0 10px 0;
      color: #555;
      font-size: 13px;
    }

    .code-block {
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 4px;
      padding: 12px;
      font-size: 11px;
      line-height: 1.4;
      max-height: 200px;
      overflow-y: auto;
      white-space: pre-wrap;
      word-break: break-word;
    }

    .parameter-description {
      font-size: 11px;
      color: #666;
      margin-top: 4px;
      line-height: 1.3;
    }
    </style>
    """
  end

  # 渲染不同类型的属性输入控件
  defp render_property(property, parameters, target) do
    param_name = property[:name]
    param_type = property[:type]
    current_value = Map.get(parameters, param_name, property[:default])
    
    assigns = %{
      property: property,
      param_name: param_name,
      param_type: param_type,
      current_value: current_value,
      target: target
    }

    case param_type do
      "string" -> render_string_property(assigns)
      "number" -> render_number_property(assigns)
      "boolean" -> render_boolean_property(assigns)
      "options" -> render_options_property(assigns)
      "json" -> render_json_property(assigns)
      "multipleValues" -> render_multiple_values_property(assigns)
      "fixedCollection" -> render_fixed_collection_property(assigns)
      _ -> render_string_property(assigns)
    end
  end

  defp render_string_property(assigns) do
    ~H"""
    <div class="form-group">
      <label><%= @property[:displayName] || @param_name %></label>
      <%= if @property[:typeOptions][:editor] do %>
        <textarea class="form-control" 
                  rows="4"
                  phx-blur="update_parameter"
                  phx-value-name={@param_name}
                  phx-value-value={@current_value}
                  phx-target={@target}><%= @current_value %></textarea>
      <% else %>
        <input type="text" 
               class="form-control"
               value={@current_value}
               placeholder={@property[:placeholder]}
               phx-blur="update_parameter"
               phx-value-name={@param_name}
               phx-target={@target} />
      <% end %>
      <%= if @property[:description] do %>
        <div class="parameter-description"><%= @property[:description] %></div>
      <% end %>
    </div>
    """
  end

  defp render_number_property(assigns) do
    ~H"""
    <div class="form-group">
      <label><%= @property[:displayName] || @param_name %></label>
      <input type="number" 
             class="form-control"
             value={@current_value}
             phx-blur="update_parameter"
             phx-value-name={@param_name}
             phx-target={@target} />
      <%= if @property[:description] do %>
        <div class="parameter-description"><%= @property[:description] %></div>
      <% end %>
    </div>
    """
  end

  defp render_boolean_property(assigns) do
    ~H"""
    <div class="form-group">
      <label class="checkbox-group">
        <input type="checkbox" 
               checked={@current_value}
               phx-click="update_parameter"
               phx-value-name={@param_name}
               phx-value-value={!@current_value}
               phx-target={@target} />
        <%= @property[:displayName] || @param_name %>
      </label>
      <%= if @property[:description] do %>
        <div class="parameter-description"><%= @property[:description] %></div>
      <% end %>
    </div>
    """
  end

  defp render_options_property(assigns) do
    ~H"""
    <div class="form-group">
      <label><%= @property[:displayName] || @param_name %></label>
      <select class="form-control"
              phx-change="update_parameter"
              phx-value-name={@param_name}
              phx-target={@target}>
        <%= for option <- @property[:options] || [] do %>
          <option value={option[:value]} selected={@current_value == option[:value]}>
            <%= option[:name] %>
          </option>
        <% end %>
      </select>
      <%= if @property[:description] do %>
        <div class="parameter-description"><%= @property[:description] %></div>
      <% end %>
    </div>
    """
  end

  defp render_json_property(assigns) do
    formatted_value = if is_binary(assigns.current_value) do
      assigns.current_value
    else
      Jason.encode!(assigns.current_value, pretty: true)
    end

    assigns = assign(assigns, :formatted_value, formatted_value)

    ~H"""
    <div class="form-group">
      <label><%= @property[:displayName] || @param_name %></label>
      <textarea class="form-control" 
                rows="6"
                phx-blur="update_parameter"
                phx-value-name={@param_name}
                phx-target={@target}><%= @formatted_value %></textarea>
      <%= if @property[:description] do %>
        <div class="parameter-description"><%= @property[:description] %></div>
      <% end %>
    </div>
    """
  end

  defp render_multiple_values_property(assigns) do
    # 简化版本的多值输入
    ~H"""
    <div class="form-group">
      <label><%= @property[:displayName] || @param_name %></label>
      <textarea class="form-control" 
                rows="4"
                placeholder="Enter multiple values (one per line)"
                phx-blur="update_parameter"
                phx-value-name={@param_name}
                phx-target={@target}><%= format_multiple_values(@current_value) %></textarea>
      <%= if @property[:description] do %>
        <div class="parameter-description"><%= @property[:description] %></div>
      <% end %>
    </div>
    """
  end

  defp render_fixed_collection_property(assigns) do
    # 简化版本的固定集合输入
    ~H"""
    <div class="form-group">
      <label><%= @property[:displayName] || @param_name %></label>
      <div class="collection-editor">
        <textarea class="form-control" 
                  rows="6"
                  placeholder="Enter collection data as JSON"
                  phx-blur="update_parameter"
                  phx-value-name={@param_name}
                  phx-target={@target}><%= format_collection_value(@current_value) %></textarea>
      </div>
      <%= if @property[:description] do %>
        <div class="parameter-description"><%= @property[:description] %></div>
      <% end %>
    </div>
    """
  end

  # 辅助函数
  defp format_execution_data(execution_data) do
    case Jason.encode(execution_data, pretty: true) do
      {:ok, json} -> json
      {:error, _} -> inspect(execution_data, pretty: true)
    end
  end

  defp get_execution_time(execution_data) do
    Map.get(execution_data, "execution_time", 0)
  end

  defp format_multiple_values(value) when is_list(value) do
    Enum.join(value, "\n")
  end
  defp format_multiple_values(value) when is_binary(value) do
    value
  end
  defp format_multiple_values(_), do: ""

  defp format_collection_value(value) when is_map(value) or is_list(value) do
    case Jason.encode(value, pretty: true) do
      {:ok, json} -> json
      {:error, _} -> ""
    end
  end
  defp format_collection_value(value) when is_binary(value), do: value
  defp format_collection_value(_), do: ""
end
{<<"links">>,[{<<"GitHub">>,<<"https://github.com/falood/file_system">>}]}.
{<<"name">>,<<"file_system">>}.
{<<"version">>,<<"1.1.0">>}.
{<<"description">>,
 <<"An Elixir file system change watcher wrapper based on FS, the native file\nsystem listener.">>}.
{<<"elixir">>,<<"~> 1.11">>}.
{<<"app">>,<<"file_system">>}.
{<<"files">>,
 [<<"lib">>,<<"lib/file_system">>,<<"lib/file_system/worker.ex">>,
  <<"lib/file_system/backends">>,<<"lib/file_system/backends/fs_inotify.ex">>,
  <<"lib/file_system/backends/fs_mac.ex">>,
  <<"lib/file_system/backends/fs_poll.ex">>,
  <<"lib/file_system/backends/fs_windows.ex">>,
  <<"lib/file_system/backend.ex">>,<<"lib/file_system.ex">>,<<"README.md">>,
  <<"mix.exs">>,<<"c_src/mac/cli.c">>,<<"c_src/mac/cli.h">>,
  <<"c_src/mac/common.h">>,<<"c_src/mac/compat.c">>,<<"c_src/mac/compat.h">>,
  <<"c_src/mac/main.c">>,<<"priv/inotifywait.exe">>]}.
{<<"licenses">>,[<<"Apache-2.0">>]}.
{<<"requirements">>,[]}.
{<<"build_tools">>,[<<"mix">>]}.

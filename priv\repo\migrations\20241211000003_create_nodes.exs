defmodule N8nElixir.Repo.Migrations.CreateNodes do
  use Ecto.Migration

  def change do
    create table(:nodes, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :node_id, :string, null: false
      add :name, :string, null: false
      add :type, :string, null: false
      add :type_version, :integer, default: 1
      add :position, :map
      add :parameters, :map, default: %{}
      add :credentials, :map, default: %{}
      add :webhook_id, :string
      add :disabled, :boolean, default: false
      add :notes, :string
      add :continue_on_fail, :boolean, default: false
      add :always_output_data, :boolean, default: false
      add :retry_on_fail, :boolean, default: false
      add :max_tries, :integer, default: 3
      add :wait_between_tries, :integer, default: 1000
      add :workflow_id, references(:workflows, type: :binary_id), null: false
      
      timestamps()
    end

    create unique_index(:nodes, [:node_id, :workflow_id])
    create index(:nodes, [:workflow_id])
    create index(:nodes, [:type])
  end
end
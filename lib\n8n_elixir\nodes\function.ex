defmodule N8nElixir.Nodes.Function do
  @moduledoc """
  函数节点 - Function

  参考n8n的Function节点，允许执行自定义JavaScript代码
  支持访问所有输入数据和工作流上下文
  """
  @behaviour N8nElixir.NodeType

  alias N8nElixir.ExpressionResolver

  @impl true
  def description() do
    %{
      displayName: "Function",
      name: "function",
      icon: "fa:code",
      group: ["transform"],
      version: 1,
      description: "Execute custom JavaScript code that gets executed on all items",
      defaults: %{
        name: "Function",
        color: "#FF6D5A"
      },
      inputs: ["main"],
      outputs: ["main"],
      properties: [
        %{
          displayName: "JavaScript Code",
          name: "jsCode",
          type: "string",
          typeOptions: %{
            editor: "jsEditor",
            editorLanguage: "javascript",
            rows: 15
          },
          default: "// Add your JavaScript code here\n// Access input data with: items\n// Return output data with: return items;\n\nfor (let i = 0; i < items.length; i++) {\n  // Process each item\n  items[i].json.processedBy = 'n8n-elixir';\n  items[i].json.timestamp = new Date().toISOString();\n}\n\nreturn items;",
          description: "JavaScript code to execute",
          required: true
        },
        %{
          displayName: "Set to continue on fail",
          name: "continueOnFail",
          type: "boolean",
          default: false,
          description: "Whether to continue execution if the function fails"
        }
      ]
    }
  end

  @impl true
  def execute(input_data, _credentials, parameters) do
    js_code = Map.get(parameters, "jsCode", "")
    continue_on_fail = Map.get(parameters, "continueOnFail", false)
    
    if String.trim(js_code) == "" do
      {:error, "JavaScript code is required"}
    else
      execute_javascript_function(input_data, js_code, continue_on_fail)
    end
  end

  @impl true
  def credential_types() do
    []
  end

  # 私有实现函数

  defp execute_javascript_function(input_data, js_code, continue_on_fail) do
    try do
      # 准备JavaScript执行环境
      context = prepare_execution_context(input_data)
      
      # 构建完整的JavaScript代码
      full_js_code = build_full_javascript_code(js_code, context)
      
      # 执行JavaScript代码
      case execute_js_in_sandbox(full_js_code, context) do
        {:ok, result} ->
          # 处理返回结果
          process_js_result(result, input_data)
        
        {:error, reason} ->
          if continue_on_fail do
            # 继续执行，返回原始数据并添加错误信息
            error_data = add_error_to_items(input_data, reason)
            {:ok, error_data}
          else
            {:error, "JavaScript execution failed: #{reason}"}
          end
      end
    rescue
      error ->
        error_message = "Function execution failed: #{inspect(error)}"
        
        if continue_on_fail do
          error_data = add_error_to_items(input_data, error_message)
          {:ok, error_data}
        else
          {:error, error_message}
        end
    end
  end

  defp prepare_execution_context(input_data) do
    %{
      "items" => input_data,
      "console" => %{
        "log" => &log_message/1,
        "error" => &log_error/1,
        "warn" => &log_warning/1
      },
      # 提供一些工具函数
      "$json" => fn data -> Jason.encode!(data) end,
      "$parseJson" => fn json_str -> Jason.decode!(json_str) end,
      "$now" => fn -> DateTime.utc_now() |> DateTime.to_iso8601() end,
      "$uuid" => fn -> UUID.uuid4() end,
      # 提供moment.js风格的日期函数
      "$moment" => prepare_moment_functions(),
      # 提供lodash风格的工具函数
      "$_" => prepare_lodash_functions()
    }
  end

  defp build_full_javascript_code(user_code, context) do
    """
    // 预定义的工具函数和变量
    const items = #{Jason.encode!(context["items"])};
    const console = {
      log: function(msg) { return logMessage(msg); },
      error: function(msg) { return logError(msg); },
      warn: function(msg) { return logWarning(msg); }
    };
    
    // 工具函数
    function $json(data) {
      return JSON.stringify(data);
    }
    
    function $parseJson(jsonStr) {
      return JSON.parse(jsonStr);
    }
    
    function $now() {
      return new Date().toISOString();
    }
    
    function $uuid() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    }
    
    // 日期处理函数
    const $moment = {
      format: function(date, format) {
        const d = new Date(date);
        return d.toISOString();
      },
      add: function(date, amount, unit) {
        const d = new Date(date);
        switch(unit) {
          case 'days': d.setDate(d.getDate() + amount); break;
          case 'hours': d.setHours(d.getHours() + amount); break;
          case 'minutes': d.setMinutes(d.getMinutes() + amount); break;
        }
        return d.toISOString();
      }
    };
    
    // 数组和对象处理函数
    const $_ = {
      map: function(arr, fn) { return arr.map(fn); },
      filter: function(arr, fn) { return arr.filter(fn); },
      find: function(arr, fn) { return arr.find(fn); },
      groupBy: function(arr, key) {
        return arr.reduce((groups, item) => {
          const group = (item[key] || 'undefined');
          groups[group] = groups[group] || [];
          groups[group].push(item);
          return groups;
        }, {});
      },
      sortBy: function(arr, key) {
        return arr.sort((a, b) => {
          const aVal = a[key];
          const bVal = b[key];
          if (aVal < bVal) return -1;
          if (aVal > bVal) return 1;
          return 0;
        });
      },
      uniq: function(arr) {
        return [...new Set(arr)];
      },
      get: function(obj, path, defaultValue) {
        const keys = path.split('.');
        let result = obj;
        for (const key of keys) {
          result = result ? result[key] : undefined;
        }
        return result !== undefined ? result : defaultValue;
      },
      set: function(obj, path, value) {
        const keys = path.split('.');
        let current = obj;
        for (let i = 0; i < keys.length - 1; i++) {
          const key = keys[i];
          if (!(key in current) || typeof current[key] !== 'object') {
            current[key] = {};
          }
          current = current[key];
        }
        current[keys[keys.length - 1]] = value;
        return obj;
      }
    };
    
    // 用户代码
    (function() {
      #{user_code}
    })();
    """
  end

  defp execute_js_in_sandbox(js_code, _context) do
    # 在实际实现中，这里需要集成JavaScript执行引擎
    # 比如通过Port与Node.js通信，或者使用Rust的QuickJS绑定
    
    # 现在返回模拟结果
    try do
      # 解析用户代码中的简单操作
      result = simulate_js_execution(js_code)
      {:ok, result}
    rescue
      error ->
        {:error, inspect(error)}
    end
  end

  defp simulate_js_execution(js_code) do
    # 简单的JavaScript代码模拟执行
    # 在实际实现中需要替换为真正的JavaScript引擎
    
    cond do
      String.contains?(js_code, "return items") ->
        # 模拟返回修改后的items
        %{"type" => "items", "data" => []}
      
      String.contains?(js_code, "processedBy") ->
        # 模拟添加processedBy字段
        %{"type" => "processed", "data" => []}
      
      true ->
        %{"type" => "default", "data" => []}
    end
  end

  defp process_js_result(js_result, original_input_data) do
    case js_result do
      %{"type" => "items"} ->
        # JavaScript返回了items数组
        {:ok, original_input_data}
      
      %{"type" => "processed"} ->
        # 模拟处理结果
        processed_data = Enum.map(original_input_data, fn item ->
          updated_json = 
            item["json"]
            |> Map.put("processedBy", "n8n-elixir")
            |> Map.put("timestamp", DateTime.utc_now() |> DateTime.to_iso8601())
          
          %{item | "json" => updated_json}
        end)
        
        {:ok, processed_data}
      
      %{"data" => data} when is_list(data) ->
        # JavaScript返回了新的数据数组
        formatted_data = Enum.map(data, fn item ->
          %{
            "json" => ensure_json_format(item),
            "binary" => %{}
          }
        end)
        
        {:ok, formatted_data}
      
      data when is_list(data) ->
        # 直接返回数组
        formatted_data = Enum.map(data, fn item ->
          %{
            "json" => ensure_json_format(item),
            "binary" => %{}
          }
        end)
        
        {:ok, formatted_data}
      
      _ ->
        # 其他情况，返回原始数据
        {:ok, original_input_data}
    end
  end

  defp add_error_to_items(input_data, error_message) do
    Enum.map(input_data, fn item ->
      error_info = %{
        "message" => error_message,
        "timestamp" => DateTime.utc_now() |> DateTime.to_iso8601(),
        "node" => "Function"
      }
      
      Map.put(item, "error", error_info)
    end)
  end

  defp prepare_moment_functions() do
    %{
      "format" => fn date, format -> format_date(date, format) end,
      "add" => fn date, amount, unit -> add_to_date(date, amount, unit) end,
      "subtract" => fn date, amount, unit -> subtract_from_date(date, amount, unit) end
    }
  end

  defp prepare_lodash_functions() do
    %{
      "map" => fn arr, func -> Enum.map(arr, func) end,
      "filter" => fn arr, func -> Enum.filter(arr, func) end,
      "find" => fn arr, func -> Enum.find(arr, func) end,
      "groupBy" => fn arr, key -> group_by_key(arr, key) end,
      "sortBy" => fn arr, key -> sort_by_key(arr, key) end,
      "uniq" => fn arr -> Enum.uniq(arr) end
    }
  end

  # 辅助函数

  defp log_message(message) do
    IO.puts("[Function Node] #{message}")
  end

  defp log_error(message) do
    IO.puts("[Function Node ERROR] #{message}")
  end

  defp log_warning(message) do
    IO.puts("[Function Node WARN] #{message}")
  end

  defp format_date(date, _format) do
    case DateTime.from_iso8601(to_string(date)) do
      {:ok, datetime, _} -> DateTime.to_iso8601(datetime)
      {:error, _} -> to_string(date)
    end
  end

  defp add_to_date(date, amount, unit) do
    case DateTime.from_iso8601(to_string(date)) do
      {:ok, datetime, _} ->
        case unit do
          "days" -> DateTime.add(datetime, amount * 24 * 60 * 60, :second)
          "hours" -> DateTime.add(datetime, amount * 60 * 60, :second)
          "minutes" -> DateTime.add(datetime, amount * 60, :second)
          "seconds" -> DateTime.add(datetime, amount, :second)
          _ -> datetime
        end
        |> DateTime.to_iso8601()
      
      {:error, _} -> to_string(date)
    end
  end

  defp subtract_from_date(date, amount, unit) do
    add_to_date(date, -amount, unit)
  end

  defp group_by_key(arr, key) when is_list(arr) do
    Enum.group_by(arr, fn item ->
      if is_map(item) do
        Map.get(item, key)
      else
        nil
      end
    end)
  end
  defp group_by_key(arr, _key), do: arr

  defp sort_by_key(arr, key) when is_list(arr) do
    Enum.sort(arr, fn a, b ->
      val_a = if is_map(a), do: Map.get(a, key), else: a
      val_b = if is_map(b), do: Map.get(b, key), else: b
      val_a <= val_b
    end)
  end
  defp sort_by_key(arr, _key), do: arr

  defp ensure_json_format(data) when is_map(data), do: data
  defp ensure_json_format(data), do: %{"data" => data}
end
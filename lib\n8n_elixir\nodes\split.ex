defmodule N8nElixir.Nodes.Split do
  @moduledoc """
  分割节点 - Split Out

  参考n8n的Split Out节点，将数组数据分割成单独的项目
  支持多种分割模式和数据处理策略
  """
  @behaviour N8nElixir.NodeType

  alias N8nElixir.{DataProcessor, ExpressionResolver}

  @impl true
  def description() do
    %{
      displayName: "Split Out",
      name: "splitOut",
      icon: "fa:split",
      group: ["transform"],
      version: 1,
      description: "Split out data into multiple items",
      defaults: %{
        name: "Split Out",
        color: "#0099FF"
      },
      inputs: ["main"],
      outputs: ["main"],
      properties: [
        %{
          displayName: "Split Mode",
          name: "mode",
          type: "options",
          options: [
            %{
              name: "Split Binary Data",
              value: "binary",
              description: "Split binary data into separate items"
            },
            %{
              name: "Split Array",
              value: "array",
              description: "Split array field into separate items"
            },
            %{
              name: "Split Each Item",
              value: "each",
              description: "Create separate items for each property"
            },
            %{
              name: "Split by Expression",
              value: "expression",
              description: "Split based on custom expression"
            }
          ],
          default: "array"
        },
        %{
          displayName: "Field to Split",
          name: "fieldToSplit",
          type: "string",
          displayOptions: %{
            show: %{
              mode: ["array"]
            }
          },
          default: "data",
          description: "Name of the field that contains the array to split",
          required: true
        },
        %{
          displayName: "Include Other Fields",
          name: "includeOtherFields",
          type: "boolean",
          displayOptions: %{
            show: %{
              mode: ["array"]
            }
          },
          default: false,
          description: "Whether to include other fields from the original item"
        },
        %{
          displayName: "Destination Field Name",
          name: "destinationFieldName",
          type: "string",
          displayOptions: %{
            show: %{
              mode: ["array"],
              includeOtherFields: [true]
            }
          },
          default: "item",
          description: "Name for the field that will contain the split item"
        },
        %{
          displayName: "Split Expression",
          name: "splitExpression",
          type: "string",
          displayOptions: %{
            show: %{
              mode: ["expression"]
            }
          },
          default: "",
          placeholder: "$json.items",
          description: "Expression that returns an array to split",
          required: true
        }
      ]
    }
  end

  @impl true
  def execute(input_data, _credentials, parameters) do
    mode = Map.get(parameters, "mode", "array")
    
    case mode do
      "binary" ->
        split_binary_data(input_data, parameters)
      
      "array" ->
        split_array_data(input_data, parameters)
      
      "each" ->
        split_each_item(input_data, parameters)
      
      "expression" ->
        split_by_expression(input_data, parameters)
      
      _ ->
        {:error, "Unknown split mode: #{mode}"}
    end
  end

  @impl true
  def credential_types() do
    []
  end

  # 私有实现函数

  defp split_binary_data(input_data, _parameters) do
    result = 
      input_data
      |> Enum.reduce([], fn item, acc ->
        binary_data = Map.get(item, "binary", %{})
        
        if map_size(binary_data) > 0 do
          binary_items = 
            Enum.map(binary_data, fn {key, binary_info} ->
              %{
                "json" => %{
                  "fileName" => Map.get(binary_info, "fileName", key),
                  "mimeType" => Map.get(binary_info, "mimeType", "application/octet-stream"),
                  "fileSize" => Map.get(binary_info, "fileSize", 0)
                },
                "binary" => %{key => binary_info},
                "pairedItem" => Map.get(item, "pairedItem")
              }
            end)
          
          acc ++ binary_items
        else
          [item | acc]
        end
      end)
      |> Enum.reverse()
    
    {:ok, result}
  end

  defp split_array_data(input_data, parameters) do
    field_to_split = Map.get(parameters, "fieldToSplit", "data")
    include_other_fields = Map.get(parameters, "includeOtherFields", false)
    destination_field_name = Map.get(parameters, "destinationFieldName", "item")
    
    try do
      result = 
        input_data
        |> Enum.reduce([], fn item, acc ->
          json_data = Map.get(item, "json", %{})
          array_data = get_nested_value(json_data, field_to_split)
          
          if is_list(array_data) and not Enum.empty?(array_data) do
            split_items = 
              Enum.map(array_data, fn array_item ->
                if include_other_fields do
                  # 保留其他字段，将分割的项目放在指定字段中
                  other_fields = Map.delete(json_data, field_to_split)
                  new_json = Map.put(other_fields, destination_field_name, array_item)
                  
                  %{
                    "json" => new_json,
                    "binary" => Map.get(item, "binary", %{}),
                    "pairedItem" => Map.get(item, "pairedItem")
                  }
                else
                  # 只保留分割的项目
                  %{
                    "json" => ensure_json_format(array_item),
                    "binary" => Map.get(item, "binary", %{}),
                    "pairedItem" => Map.get(item, "pairedItem")
                  }
                end
              end)
            
            acc ++ split_items
          else
            # 如果字段不是数组或为空，保留原项目
            [item | acc]
          end
        end)
        |> Enum.reverse()
      
      {:ok, result}
    rescue
      error ->
        {:error, "Array split failed: #{inspect(error)}"}
    end
  end

  defp split_each_item(input_data, _parameters) do
    result = 
      input_data
      |> Enum.reduce([], fn item, acc ->
        json_data = Map.get(item, "json", %{})
        
        property_items = 
          Enum.map(json_data, fn {key, value} ->
            %{
              "json" => %{
                "key" => key,
                "value" => value
              },
              "binary" => Map.get(item, "binary", %{}),
              "pairedItem" => Map.get(item, "pairedItem")
            }
          end)
        
        acc ++ property_items
      end)
      |> Enum.reverse()
    
    {:ok, result}
  end

  defp split_by_expression(input_data, parameters) do
    split_expression = Map.get(parameters, "splitExpression", "")
    
    if split_expression == "" do
      {:error, "Split expression is required"}
    else
      try do
        result = 
          input_data
          |> Enum.reduce([], fn item, acc ->
            context = %{
              "$json" => Map.get(item, "json", %{}),
              "$binary" => Map.get(item, "binary", %{}),
              "$item" => item
            }
            
            case ExpressionResolver.resolve_expression(split_expression, context) do
              {:ok, array_data} when is_list(array_data) ->
                split_items = 
                  Enum.map(array_data, fn array_item ->
                    %{
                      "json" => ensure_json_format(array_item),
                      "binary" => Map.get(item, "binary", %{}),
                      "pairedItem" => Map.get(item, "pairedItem")
                    }
                  end)
                
                acc ++ split_items
              
              {:ok, _non_array} ->
                # 如果表达式结果不是数组，保留原项目
                [item | acc]
              
              {:error, _reason} ->
                # 表达式执行失败，保留原项目
                [item | acc]
            end
          end)
          |> Enum.reverse()
        
        {:ok, result}
      rescue
        error ->
          {:error, "Expression split failed: #{inspect(error)}"}
      end
    end
  end

  # 辅助函数

  defp get_nested_value(data, path) when is_binary(path) do
    path_parts = String.split(path, ".")
    get_nested_value(data, path_parts)
  end
  defp get_nested_value(data, []), do: data
  defp get_nested_value(data, [key | rest]) when is_map(data) do
    case Map.get(data, key) do
      nil -> nil
      value -> get_nested_value(value, rest)
    end
  end
  defp get_nested_value(_, _), do: nil

  defp ensure_json_format(data) when is_map(data), do: data
  defp ensure_json_format(data), do: %{"data" => data}
end
{application,plug_cowboy,
             [{modules,['Elixir.Plug.Cowboy','Elixir.Plug.Cowboy.Conn',
                        'Elixir.Plug.Cowboy.Drainer',
                        'Elixir.Plug.Cowboy.Handler',
                        'Elixir.Plug.Cowboy.Translator']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger,plug,cowboy,
                             cowboy_telemetry]},
              {description,"A Plug adapter for Cowboy"},
              {registered,[]},
              {vsn,"2.7.4"},
              {mod,{'Elixir.Plug.Cowboy',[]}}]}.

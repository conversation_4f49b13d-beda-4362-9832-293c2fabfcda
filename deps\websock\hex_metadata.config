{<<"app">>,<<"websock">>}.
{<<"build_tools">>,[<<"mix">>]}.
{<<"description">>,<<"A specification for WebSocket connections">>}.
{<<"elixir">>,<<"~> 1.9">>}.
{<<"files">>,
 [<<"lib">>,<<"lib/websock.ex">>,<<"test">>,<<"test/test_helper.exs">>,
  <<"mix.exs">>,<<"README.md">>,<<"LICENSE">>]}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"links">>,
 [{<<"GitHub">>,<<"https://github.com/phoenixframework/websock">>}]}.
{<<"name">>,<<"websock">>}.
{<<"requirements">>,[]}.
{<<"version">>,<<"0.5.3">>}.

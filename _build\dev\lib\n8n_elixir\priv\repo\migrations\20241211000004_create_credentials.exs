defmodule N8nElixir.Repo.Migrations.CreateCredentials do
  use Ecto.Migration

  def change do
    create table(:credentials, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :name, :string, null: false
      add :type, :string, null: false
      add :description, :text
      add :encrypted_data, :binary
      add :encryption_key_id, :string
      add :status, :string, default: "active"
      add :last_used_at, :utc_datetime
      add :expires_at, :utc_datetime
      add :owner_id, references(:users, type: :binary_id), null: false
      add :team_id, references(:teams, type: :binary_id)
      
      timestamps()
    end

    create unique_index(:credentials, [:name, :owner_id])
    create index(:credentials, [:owner_id])
    create index(:credentials, [:team_id])
    create index(:credentials, [:type])
    create index(:credentials, [:status])

    create table(:workflow_credentials, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :node_name, :string
      add :credential_key, :string
      add :workflow_id, references(:workflows, type: :binary_id), null: false
      add :credential_id, references(:credentials, type: :binary_id), null: false
      
      timestamps()
    end

    create unique_index(:workflow_credentials, [:workflow_id, :credential_id, :node_name])
    create index(:workflow_credentials, [:workflow_id])
    create index(:workflow_credentials, [:credential_id])
  end
end
%% -*- erlang -*-
%%%
%%% This file is part of mimerl released under the MIT license.
%%% See the LICENSE for more information.
-module(mimerl).

-export([extension/1]).
-export([web_extension/1]).
-export([filename/1]).
-export([web/1]).
-export([mime_to_exts/1]).

%% @doc Transform an extension to a mimetype
%%
%%      Example:
%%
%% ```
%% 1> mimerl:extension(<<"c">>).
%% <<"text/x-c">>
%% '''
-spec extension(binary()) -> binary().
extension(Ext) ->
    extensions(Ext).

%% @doc transform a web extension to a mimetype
web_extension(Ext) ->
    web_extensions(Ext).


%% @doc Return the mimetype for any file by looking at its extension.
%% Example:
%%
%% ```
%% 1> mimerl:filename(<<"test.cpp">>).
%% <<"text/x-c">>
%% '''
-spec filename(file:filename_all()) -> binary().
filename(Path) ->
	case filename:extension(Path) of
		<<>> -> <<"application/octet-stream">>;
		<< $., Ext/binary >> -> extension(Ext)
	end.

web(Path) ->
    case filename:extension(Path) of
		<<>> -> <<"application/octet-stream">>;
		<< $., Ext/binary >> -> web_extension(Ext)
	end.

%% @doc Return the list of extensions for a mimetype.
%% Example:
%%
%% ```
%% 1> mimerl:mime_to_exts(<<"text/plain">>).
%% [<<"txt">>,<<"text">>,<<"conf">>,<<"def">>,<<"list">>,<<"log">>,<<"in">>]
%% '''
-spec mime_to_exts(binary()) -> [binary()].
mime_to_exts(Mimetype) ->
    mimetypes(Mimetype).


%% GENERATED
extensions(<<"123">>) -> <<"application/vnd.lotus-1-2-3">>;
extensions(<<"3dml">>) -> <<"text/vnd.in3d.3dml">>;
extensions(<<"3ds">>) -> <<"image/x-3ds">>;
extensions(<<"3g2">>) -> <<"video/3gpp2">>;
extensions(<<"3gp">>) -> <<"video/3gpp">>;
extensions(<<"7z">>) -> <<"application/x-7z-compressed">>;
extensions(<<"aab">>) -> <<"application/x-authorware-bin">>;
extensions(<<"aac">>) -> <<"audio/x-aac">>;
extensions(<<"aam">>) -> <<"application/x-authorware-map">>;
extensions(<<"aas">>) -> <<"application/x-authorware-seg">>;
extensions(<<"abw">>) -> <<"application/x-abiword">>;
extensions(<<"ac">>) -> <<"application/pkix-attr-cert">>;
extensions(<<"acc">>) -> <<"application/vnd.americandynamics.acc">>;
extensions(<<"ace">>) -> <<"application/x-ace-compressed">>;
extensions(<<"acu">>) -> <<"application/vnd.acucobol">>;
extensions(<<"acutc">>) -> <<"application/vnd.acucorp">>;
extensions(<<"adp">>) -> <<"audio/adpcm">>;
extensions(<<"aep">>) -> <<"application/vnd.audiograph">>;
extensions(<<"afm">>) -> <<"application/x-font-type1">>;
extensions(<<"afp">>) -> <<"application/vnd.ibm.modcap">>;
extensions(<<"ahead">>) -> <<"application/vnd.ahead.space">>;
extensions(<<"ai">>) -> <<"application/postscript">>;
extensions(<<"aif">>) -> <<"audio/x-aiff">>;
extensions(<<"aifc">>) -> <<"audio/x-aiff">>;
extensions(<<"aiff">>) -> <<"audio/x-aiff">>;
extensions(<<"air">>) -> <<"application/vnd.adobe.air-application-installer-package+zip">>;
extensions(<<"ait">>) -> <<"application/vnd.dvb.ait">>;
extensions(<<"ami">>) -> <<"application/vnd.amiga.ami">>;
extensions(<<"apk">>) -> <<"application/vnd.android.package-archive">>;
extensions(<<"appcache">>) -> <<"text/cache-manifest">>;
extensions(<<"application">>) -> <<"application/x-ms-application">>;
extensions(<<"apr">>) -> <<"application/vnd.lotus-approach">>;
extensions(<<"arc">>) -> <<"application/x-freearc">>;
extensions(<<"asc">>) -> <<"application/pgp-signature">>;
extensions(<<"asf">>) -> <<"video/x-ms-asf">>;
extensions(<<"asm">>) -> <<"text/x-asm">>;
extensions(<<"aso">>) -> <<"application/vnd.accpac.simply.aso">>;
extensions(<<"asx">>) -> <<"video/x-ms-asf">>;
extensions(<<"atc">>) -> <<"application/vnd.acucorp">>;
extensions(<<"atom">>) -> <<"application/atom+xml">>;
extensions(<<"atomcat">>) -> <<"application/atomcat+xml">>;
extensions(<<"atomsvc">>) -> <<"application/atomsvc+xml">>;
extensions(<<"atx">>) -> <<"application/vnd.antix.game-component">>;
extensions(<<"au">>) -> <<"audio/basic">>;
extensions(<<"avif">>) -> <<"image/avif">>;
extensions(<<"avi">>) -> <<"video/x-msvideo">>;
extensions(<<"aw">>) -> <<"application/applixware">>;
extensions(<<"azf">>) -> <<"application/vnd.airzip.filesecure.azf">>;
extensions(<<"azs">>) -> <<"application/vnd.airzip.filesecure.azs">>;
extensions(<<"azw">>) -> <<"application/vnd.amazon.ebook">>;
extensions(<<"bat">>) -> <<"application/x-msdownload">>;
extensions(<<"bcpio">>) -> <<"application/x-bcpio">>;
extensions(<<"bdf">>) -> <<"application/x-font-bdf">>;
extensions(<<"bdm">>) -> <<"application/vnd.syncml.dm+wbxml">>;
extensions(<<"bed">>) -> <<"application/vnd.realvnc.bed">>;
extensions(<<"bh2">>) -> <<"application/vnd.fujitsu.oasysprs">>;
extensions(<<"bin">>) -> <<"application/octet-stream">>;
extensions(<<"blb">>) -> <<"application/x-blorb">>;
extensions(<<"blorb">>) -> <<"application/x-blorb">>;
extensions(<<"bmi">>) -> <<"application/vnd.bmi">>;
extensions(<<"bmp">>) -> <<"image/bmp">>;
extensions(<<"book">>) -> <<"application/vnd.framemaker">>;
extensions(<<"box">>) -> <<"application/vnd.previewsystems.box">>;
extensions(<<"boz">>) -> <<"application/x-bzip2">>;
extensions(<<"bpk">>) -> <<"application/octet-stream">>;
extensions(<<"btif">>) -> <<"image/prs.btif">>;
extensions(<<"bz2">>) -> <<"application/x-bzip2">>;
extensions(<<"bz">>) -> <<"application/x-bzip">>;
extensions(<<"c11amc">>) -> <<"application/vnd.cluetrust.cartomobile-config">>;
extensions(<<"c11amz">>) -> <<"application/vnd.cluetrust.cartomobile-config-pkg">>;
extensions(<<"c4d">>) -> <<"application/vnd.clonk.c4group">>;
extensions(<<"c4f">>) -> <<"application/vnd.clonk.c4group">>;
extensions(<<"c4g">>) -> <<"application/vnd.clonk.c4group">>;
extensions(<<"c4p">>) -> <<"application/vnd.clonk.c4group">>;
extensions(<<"c4u">>) -> <<"application/vnd.clonk.c4group">>;
extensions(<<"cab">>) -> <<"application/vnd.ms-cab-compressed">>;
extensions(<<"caf">>) -> <<"audio/x-caf">>;
extensions(<<"cap">>) -> <<"application/vnd.tcpdump.pcap">>;
extensions(<<"car">>) -> <<"application/vnd.curl.car">>;
extensions(<<"cat">>) -> <<"application/vnd.ms-pki.seccat">>;
extensions(<<"cb7">>) -> <<"application/x-cbr">>;
extensions(<<"cba">>) -> <<"application/x-cbr">>;
extensions(<<"cbr">>) -> <<"application/x-cbr">>;
extensions(<<"cbt">>) -> <<"application/x-cbr">>;
extensions(<<"cbz">>) -> <<"application/x-cbr">>;
extensions(<<"cct">>) -> <<"application/x-director">>;
extensions(<<"cc">>) -> <<"text/x-c">>;
extensions(<<"ccxml">>) -> <<"application/ccxml+xml">>;
extensions(<<"cdbcmsg">>) -> <<"application/vnd.contact.cmsg">>;
extensions(<<"cdf">>) -> <<"application/x-netcdf">>;
extensions(<<"cdkey">>) -> <<"application/vnd.mediastation.cdkey">>;
extensions(<<"cdmia">>) -> <<"application/cdmi-capability">>;
extensions(<<"cdmic">>) -> <<"application/cdmi-container">>;
extensions(<<"cdmid">>) -> <<"application/cdmi-domain">>;
extensions(<<"cdmio">>) -> <<"application/cdmi-object">>;
extensions(<<"cdmiq">>) -> <<"application/cdmi-queue">>;
extensions(<<"cdx">>) -> <<"chemical/x-cdx">>;
extensions(<<"cdxml">>) -> <<"application/vnd.chemdraw+xml">>;
extensions(<<"cdy">>) -> <<"application/vnd.cinderella">>;
extensions(<<"cer">>) -> <<"application/pkix-cert">>;
extensions(<<"cfs">>) -> <<"application/x-cfs-compressed">>;
extensions(<<"cgm">>) -> <<"image/cgm">>;
extensions(<<"chat">>) -> <<"application/x-chat">>;
extensions(<<"chm">>) -> <<"application/vnd.ms-htmlhelp">>;
extensions(<<"chrt">>) -> <<"application/vnd.kde.kchart">>;
extensions(<<"cif">>) -> <<"chemical/x-cif">>;
extensions(<<"cii">>) -> <<"application/vnd.anser-web-certificate-issue-initiation">>;
extensions(<<"cil">>) -> <<"application/vnd.ms-artgalry">>;
extensions(<<"cla">>) -> <<"application/vnd.claymore">>;
extensions(<<"class">>) -> <<"application/java-vm">>;
extensions(<<"clkk">>) -> <<"application/vnd.crick.clicker.keyboard">>;
extensions(<<"clkp">>) -> <<"application/vnd.crick.clicker.palette">>;
extensions(<<"clkt">>) -> <<"application/vnd.crick.clicker.template">>;
extensions(<<"clkw">>) -> <<"application/vnd.crick.clicker.wordbank">>;
extensions(<<"clkx">>) -> <<"application/vnd.crick.clicker">>;
extensions(<<"clp">>) -> <<"application/x-msclip">>;
extensions(<<"cmc">>) -> <<"application/vnd.cosmocaller">>;
extensions(<<"cmdf">>) -> <<"chemical/x-cmdf">>;
extensions(<<"cml">>) -> <<"chemical/x-cml">>;
extensions(<<"cmp">>) -> <<"application/vnd.yellowriver-custom-menu">>;
extensions(<<"cmx">>) -> <<"image/x-cmx">>;
extensions(<<"cod">>) -> <<"application/vnd.rim.cod">>;
extensions(<<"com">>) -> <<"application/x-msdownload">>;
extensions(<<"conf">>) -> <<"text/plain">>;
extensions(<<"cpio">>) -> <<"application/x-cpio">>;
extensions(<<"cpp">>) -> <<"text/x-c">>;
extensions(<<"cpt">>) -> <<"application/mac-compactpro">>;
extensions(<<"crd">>) -> <<"application/x-mscardfile">>;
extensions(<<"crl">>) -> <<"application/pkix-crl">>;
extensions(<<"crt">>) -> <<"application/x-x509-ca-cert">>;
extensions(<<"cryptonote">>) -> <<"application/vnd.rig.cryptonote">>;
extensions(<<"csh">>) -> <<"application/x-csh">>;
extensions(<<"csml">>) -> <<"chemical/x-csml">>;
extensions(<<"csp">>) -> <<"application/vnd.commonspace">>;
extensions(<<"css">>) -> <<"text/css">>;
extensions(<<"cst">>) -> <<"application/x-director">>;
extensions(<<"csv">>) -> <<"text/csv">>;
extensions(<<"c">>) -> <<"text/x-c">>;
extensions(<<"cu">>) -> <<"application/cu-seeme">>;
extensions(<<"curl">>) -> <<"text/vnd.curl">>;
extensions(<<"cww">>) -> <<"application/prs.cww">>;
extensions(<<"cxt">>) -> <<"application/x-director">>;
extensions(<<"cxx">>) -> <<"text/x-c">>;
extensions(<<"dae">>) -> <<"model/vnd.collada+xml">>;
extensions(<<"daf">>) -> <<"application/vnd.mobius.daf">>;
extensions(<<"dart">>) -> <<"application/vnd.dart">>;
extensions(<<"dataless">>) -> <<"application/vnd.fdsn.seed">>;
extensions(<<"davmount">>) -> <<"application/davmount+xml">>;
extensions(<<"dbk">>) -> <<"application/docbook+xml">>;
extensions(<<"dcr">>) -> <<"application/x-director">>;
extensions(<<"dcurl">>) -> <<"text/vnd.curl.dcurl">>;
extensions(<<"dd2">>) -> <<"application/vnd.oma.dd2+xml">>;
extensions(<<"ddd">>) -> <<"application/vnd.fujixerox.ddd">>;
extensions(<<"deb">>) -> <<"application/x-debian-package">>;
extensions(<<"def">>) -> <<"text/plain">>;
extensions(<<"deploy">>) -> <<"application/octet-stream">>;
extensions(<<"der">>) -> <<"application/x-x509-ca-cert">>;
extensions(<<"dfac">>) -> <<"application/vnd.dreamfactory">>;
extensions(<<"dgc">>) -> <<"application/x-dgc-compressed">>;
extensions(<<"dic">>) -> <<"text/x-c">>;
extensions(<<"dir">>) -> <<"application/x-director">>;
extensions(<<"dis">>) -> <<"application/vnd.mobius.dis">>;
extensions(<<"dist">>) -> <<"application/octet-stream">>;
extensions(<<"distz">>) -> <<"application/octet-stream">>;
extensions(<<"djv">>) -> <<"image/vnd.djvu">>;
extensions(<<"djvu">>) -> <<"image/vnd.djvu">>;
extensions(<<"dll">>) -> <<"application/x-msdownload">>;
extensions(<<"dmg">>) -> <<"application/x-apple-diskimage">>;
extensions(<<"dmp">>) -> <<"application/vnd.tcpdump.pcap">>;
extensions(<<"dms">>) -> <<"application/octet-stream">>;
extensions(<<"dna">>) -> <<"application/vnd.dna">>;
extensions(<<"doc">>) -> <<"application/msword">>;
extensions(<<"docm">>) -> <<"application/vnd.ms-word.document.macroenabled.12">>;
extensions(<<"docx">>) -> <<"application/vnd.openxmlformats-officedocument.wordprocessingml.document">>;
extensions(<<"dot">>) -> <<"application/msword">>;
extensions(<<"dotm">>) -> <<"application/vnd.ms-word.template.macroenabled.12">>;
extensions(<<"dotx">>) -> <<"application/vnd.openxmlformats-officedocument.wordprocessingml.template">>;
extensions(<<"dp">>) -> <<"application/vnd.osgi.dp">>;
extensions(<<"dpg">>) -> <<"application/vnd.dpgraph">>;
extensions(<<"dra">>) -> <<"audio/vnd.dra">>;
extensions(<<"dsc">>) -> <<"text/prs.lines.tag">>;
extensions(<<"dssc">>) -> <<"application/dssc+der">>;
extensions(<<"dtb">>) -> <<"application/x-dtbook+xml">>;
extensions(<<"dtd">>) -> <<"application/xml-dtd">>;
extensions(<<"dts">>) -> <<"audio/vnd.dts">>;
extensions(<<"dtshd">>) -> <<"audio/vnd.dts.hd">>;
extensions(<<"dump">>) -> <<"application/octet-stream">>;
extensions(<<"dvb">>) -> <<"video/vnd.dvb.file">>;
extensions(<<"dvi">>) -> <<"application/x-dvi">>;
extensions(<<"dwf">>) -> <<"model/vnd.dwf">>;
extensions(<<"dwg">>) -> <<"image/vnd.dwg">>;
extensions(<<"dxf">>) -> <<"image/vnd.dxf">>;
extensions(<<"dxp">>) -> <<"application/vnd.spotfire.dxp">>;
extensions(<<"dxr">>) -> <<"application/x-director">>;
extensions(<<"ecelp4800">>) -> <<"audio/vnd.nuera.ecelp4800">>;
extensions(<<"ecelp7470">>) -> <<"audio/vnd.nuera.ecelp7470">>;
extensions(<<"ecelp9600">>) -> <<"audio/vnd.nuera.ecelp9600">>;
extensions(<<"ecma">>) -> <<"application/ecmascript">>;
extensions(<<"edm">>) -> <<"application/vnd.novadigm.edm">>;
extensions(<<"edx">>) -> <<"application/vnd.novadigm.edx">>;
extensions(<<"efif">>) -> <<"application/vnd.picsel">>;
extensions(<<"ei6">>) -> <<"application/vnd.pg.osasli">>;
extensions(<<"elc">>) -> <<"application/octet-stream">>;
extensions(<<"emf">>) -> <<"application/x-msmetafile">>;
extensions(<<"eml">>) -> <<"message/rfc822">>;
extensions(<<"emma">>) -> <<"application/emma+xml">>;
extensions(<<"emz">>) -> <<"application/x-msmetafile">>;
extensions(<<"eol">>) -> <<"audio/vnd.digital-winds">>;
extensions(<<"eot">>) -> <<"application/vnd.ms-fontobject">>;
extensions(<<"eps">>) -> <<"application/postscript">>;
extensions(<<"epub">>) -> <<"application/epub+zip">>;
extensions(<<"es3">>) -> <<"application/vnd.eszigno3+xml">>;
extensions(<<"esa">>) -> <<"application/vnd.osgi.subsystem">>;
extensions(<<"esf">>) -> <<"application/vnd.epson.esf">>;
extensions(<<"et3">>) -> <<"application/vnd.eszigno3+xml">>;
extensions(<<"etx">>) -> <<"text/x-setext">>;
extensions(<<"eva">>) -> <<"application/x-eva">>;
extensions(<<"evy">>) -> <<"application/x-envoy">>;
extensions(<<"exe">>) -> <<"application/x-msdownload">>;
extensions(<<"exi">>) -> <<"application/exi">>;
extensions(<<"ext">>) -> <<"application/vnd.novadigm.ext">>;
extensions(<<"ez2">>) -> <<"application/vnd.ezpix-album">>;
extensions(<<"ez3">>) -> <<"application/vnd.ezpix-package">>;
extensions(<<"ez">>) -> <<"application/andrew-inset">>;
extensions(<<"f4v">>) -> <<"video/x-f4v">>;
extensions(<<"f77">>) -> <<"text/x-fortran">>;
extensions(<<"f90">>) -> <<"text/x-fortran">>;
extensions(<<"fbs">>) -> <<"image/vnd.fastbidsheet">>;
extensions(<<"fcdt">>) -> <<"application/vnd.adobe.formscentral.fcdt">>;
extensions(<<"fcs">>) -> <<"application/vnd.isac.fcs">>;
extensions(<<"fdf">>) -> <<"application/vnd.fdf">>;
extensions(<<"fe_launch">>) -> <<"application/vnd.denovo.fcselayout-link">>;
extensions(<<"fg5">>) -> <<"application/vnd.fujitsu.oasysgp">>;
extensions(<<"fgd">>) -> <<"application/x-director">>;
extensions(<<"fh4">>) -> <<"image/x-freehand">>;
extensions(<<"fh5">>) -> <<"image/x-freehand">>;
extensions(<<"fh7">>) -> <<"image/x-freehand">>;
extensions(<<"fhc">>) -> <<"image/x-freehand">>;
extensions(<<"fh">>) -> <<"image/x-freehand">>;
extensions(<<"fig">>) -> <<"application/x-xfig">>;
extensions(<<"flac">>) -> <<"audio/x-flac">>;
extensions(<<"fli">>) -> <<"video/x-fli">>;
extensions(<<"flo">>) -> <<"application/vnd.micrografx.flo">>;
extensions(<<"flv">>) -> <<"video/x-flv">>;
extensions(<<"flw">>) -> <<"application/vnd.kde.kivio">>;
extensions(<<"flx">>) -> <<"text/vnd.fmi.flexstor">>;
extensions(<<"fly">>) -> <<"text/vnd.fly">>;
extensions(<<"fm">>) -> <<"application/vnd.framemaker">>;
extensions(<<"fnc">>) -> <<"application/vnd.frogans.fnc">>;
extensions(<<"for">>) -> <<"text/x-fortran">>;
extensions(<<"fpx">>) -> <<"image/vnd.fpx">>;
extensions(<<"frame">>) -> <<"application/vnd.framemaker">>;
extensions(<<"fsc">>) -> <<"application/vnd.fsc.weblaunch">>;
extensions(<<"fst">>) -> <<"image/vnd.fst">>;
extensions(<<"ftc">>) -> <<"application/vnd.fluxtime.clip">>;
extensions(<<"f">>) -> <<"text/x-fortran">>;
extensions(<<"fti">>) -> <<"application/vnd.anser-web-funds-transfer-initiation">>;
extensions(<<"fvt">>) -> <<"video/vnd.fvt">>;
extensions(<<"fxp">>) -> <<"application/vnd.adobe.fxp">>;
extensions(<<"fxpl">>) -> <<"application/vnd.adobe.fxp">>;
extensions(<<"fzs">>) -> <<"application/vnd.fuzzysheet">>;
extensions(<<"g2w">>) -> <<"application/vnd.geoplan">>;
extensions(<<"g3">>) -> <<"image/g3fax">>;
extensions(<<"g3w">>) -> <<"application/vnd.geospace">>;
extensions(<<"gac">>) -> <<"application/vnd.groove-account">>;
extensions(<<"gam">>) -> <<"application/x-tads">>;
extensions(<<"gbr">>) -> <<"application/rpki-ghostbusters">>;
extensions(<<"gca">>) -> <<"application/x-gca-compressed">>;
extensions(<<"gdl">>) -> <<"model/vnd.gdl">>;
extensions(<<"geo">>) -> <<"application/vnd.dynageo">>;
extensions(<<"gex">>) -> <<"application/vnd.geometry-explorer">>;
extensions(<<"ggb">>) -> <<"application/vnd.geogebra.file">>;
extensions(<<"ggs">>) -> <<"application/vnd.geogebra.slides">>;
extensions(<<"ggt">>) -> <<"application/vnd.geogebra.tool">>;
extensions(<<"ghf">>) -> <<"application/vnd.groove-help">>;
extensions(<<"gif">>) -> <<"image/gif">>;
extensions(<<"gim">>) -> <<"application/vnd.groove-identity-message">>;
extensions(<<"gml">>) -> <<"application/gml+xml">>;
extensions(<<"gmx">>) -> <<"application/vnd.gmx">>;
extensions(<<"gnumeric">>) -> <<"application/x-gnumeric">>;
extensions(<<"gph">>) -> <<"application/vnd.flographit">>;
extensions(<<"gpx">>) -> <<"application/gpx+xml">>;
extensions(<<"gqf">>) -> <<"application/vnd.grafeq">>;
extensions(<<"gqs">>) -> <<"application/vnd.grafeq">>;
extensions(<<"gram">>) -> <<"application/srgs">>;
extensions(<<"gramps">>) -> <<"application/x-gramps-xml">>;
extensions(<<"gre">>) -> <<"application/vnd.geometry-explorer">>;
extensions(<<"grv">>) -> <<"application/vnd.groove-injector">>;
extensions(<<"grxml">>) -> <<"application/srgs+xml">>;
extensions(<<"gsf">>) -> <<"application/x-font-ghostscript">>;
extensions(<<"gtar">>) -> <<"application/x-gtar">>;
extensions(<<"gtm">>) -> <<"application/vnd.groove-tool-message">>;
extensions(<<"gtw">>) -> <<"model/vnd.gtw">>;
extensions(<<"gv">>) -> <<"text/vnd.graphviz">>;
extensions(<<"gxf">>) -> <<"application/gxf">>;
extensions(<<"gxt">>) -> <<"application/vnd.geonext">>;
extensions(<<"h261">>) -> <<"video/h261">>;
extensions(<<"h263">>) -> <<"video/h263">>;
extensions(<<"h264">>) -> <<"video/h264">>;
extensions(<<"hal">>) -> <<"application/vnd.hal+xml">>;
extensions(<<"hbci">>) -> <<"application/vnd.hbci">>;
extensions(<<"hdf">>) -> <<"application/x-hdf">>;
extensions(<<"hh">>) -> <<"text/x-c">>;
extensions(<<"hlp">>) -> <<"application/winhlp">>;
extensions(<<"hpgl">>) -> <<"application/vnd.hp-hpgl">>;
extensions(<<"hpid">>) -> <<"application/vnd.hp-hpid">>;
extensions(<<"hps">>) -> <<"application/vnd.hp-hps">>;
extensions(<<"hqx">>) -> <<"application/mac-binhex40">>;
extensions(<<"h">>) -> <<"text/x-c">>;
extensions(<<"htke">>) -> <<"application/vnd.kenameaapp">>;
extensions(<<"html">>) -> <<"text/html">>;
extensions(<<"htm">>) -> <<"text/html">>;
extensions(<<"hvd">>) -> <<"application/vnd.yamaha.hv-dic">>;
extensions(<<"hvp">>) -> <<"application/vnd.yamaha.hv-voice">>;
extensions(<<"hvs">>) -> <<"application/vnd.yamaha.hv-script">>;
extensions(<<"i2g">>) -> <<"application/vnd.intergeo">>;
extensions(<<"icc">>) -> <<"application/vnd.iccprofile">>;
extensions(<<"ice">>) -> <<"x-conference/x-cooltalk">>;
extensions(<<"icm">>) -> <<"application/vnd.iccprofile">>;
extensions(<<"ico">>) -> <<"image/x-icon">>;
extensions(<<"ics">>) -> <<"text/calendar">>;
extensions(<<"ief">>) -> <<"image/ief">>;
extensions(<<"ifb">>) -> <<"text/calendar">>;
extensions(<<"ifm">>) -> <<"application/vnd.shana.informed.formdata">>;
extensions(<<"iges">>) -> <<"model/iges">>;
extensions(<<"igl">>) -> <<"application/vnd.igloader">>;
extensions(<<"igm">>) -> <<"application/vnd.insors.igm">>;
extensions(<<"igs">>) -> <<"model/iges">>;
extensions(<<"igx">>) -> <<"application/vnd.micrografx.igx">>;
extensions(<<"iif">>) -> <<"application/vnd.shana.informed.interchange">>;
extensions(<<"imp">>) -> <<"application/vnd.accpac.simply.imp">>;
extensions(<<"ims">>) -> <<"application/vnd.ms-ims">>;
extensions(<<"ink">>) -> <<"application/inkml+xml">>;
extensions(<<"inkml">>) -> <<"application/inkml+xml">>;
extensions(<<"install">>) -> <<"application/x-install-instructions">>;
extensions(<<"in">>) -> <<"text/plain">>;
extensions(<<"iota">>) -> <<"application/vnd.astraea-software.iota">>;
extensions(<<"ipfix">>) -> <<"application/ipfix">>;
extensions(<<"ipk">>) -> <<"application/vnd.shana.informed.package">>;
extensions(<<"irm">>) -> <<"application/vnd.ibm.rights-management">>;
extensions(<<"irp">>) -> <<"application/vnd.irepository.package+xml">>;
extensions(<<"iso">>) -> <<"application/x-iso9660-image">>;
extensions(<<"itp">>) -> <<"application/vnd.shana.informed.formtemplate">>;
extensions(<<"ivp">>) -> <<"application/vnd.immervision-ivp">>;
extensions(<<"ivu">>) -> <<"application/vnd.immervision-ivu">>;
extensions(<<"jad">>) -> <<"text/vnd.sun.j2me.app-descriptor">>;
extensions(<<"jam">>) -> <<"application/vnd.jam">>;
extensions(<<"jar">>) -> <<"application/java-archive">>;
extensions(<<"java">>) -> <<"text/x-java-source">>;
extensions(<<"jisp">>) -> <<"application/vnd.jisp">>;
extensions(<<"jlt">>) -> <<"application/vnd.hp-jlyt">>;
extensions(<<"jnlp">>) -> <<"application/x-java-jnlp-file">>;
extensions(<<"joda">>) -> <<"application/vnd.joost.joda-archive">>;
extensions(<<"jpeg">>) -> <<"image/jpeg">>;
extensions(<<"jpe">>) -> <<"image/jpeg">>;
extensions(<<"jpg">>) -> <<"image/jpeg">>;
extensions(<<"jpgm">>) -> <<"video/jpm">>;
extensions(<<"jpgv">>) -> <<"video/jpeg">>;
extensions(<<"jpm">>) -> <<"video/jpm">>;
extensions(<<"json">>) -> <<"application/json">>;
extensions(<<"jsonml">>) -> <<"application/jsonml+json">>;
extensions(<<"js">>) -> <<"text/javascript">>;
extensions(<<"jxl">>) -> <<"image/jxl">>;
extensions(<<"kar">>) -> <<"audio/midi">>;
extensions(<<"karbon">>) -> <<"application/vnd.kde.karbon">>;
extensions(<<"kfo">>) -> <<"application/vnd.kde.kformula">>;
extensions(<<"kia">>) -> <<"application/vnd.kidspiration">>;
extensions(<<"kml">>) -> <<"application/vnd.google-earth.kml+xml">>;
extensions(<<"kmz">>) -> <<"application/vnd.google-earth.kmz">>;
extensions(<<"kne">>) -> <<"application/vnd.kinar">>;
extensions(<<"knp">>) -> <<"application/vnd.kinar">>;
extensions(<<"kon">>) -> <<"application/vnd.kde.kontour">>;
extensions(<<"kpr">>) -> <<"application/vnd.kde.kpresenter">>;
extensions(<<"kpt">>) -> <<"application/vnd.kde.kpresenter">>;
extensions(<<"kpxx">>) -> <<"application/vnd.ds-keypoint">>;
extensions(<<"ksp">>) -> <<"application/vnd.kde.kspread">>;
extensions(<<"ktr">>) -> <<"application/vnd.kahootz">>;
extensions(<<"ktx">>) -> <<"image/ktx">>;
extensions(<<"ktz">>) -> <<"application/vnd.kahootz">>;
extensions(<<"kwd">>) -> <<"application/vnd.kde.kword">>;
extensions(<<"kwt">>) -> <<"application/vnd.kde.kword">>;
extensions(<<"lasxml">>) -> <<"application/vnd.las.las+xml">>;
extensions(<<"latex">>) -> <<"application/x-latex">>;
extensions(<<"lbd">>) -> <<"application/vnd.llamagraphics.life-balance.desktop">>;
extensions(<<"lbe">>) -> <<"application/vnd.llamagraphics.life-balance.exchange+xml">>;
extensions(<<"les">>) -> <<"application/vnd.hhe.lesson-player">>;
extensions(<<"lha">>) -> <<"application/x-lzh-compressed">>;
extensions(<<"link66">>) -> <<"application/vnd.route66.link66+xml">>;
extensions(<<"list3820">>) -> <<"application/vnd.ibm.modcap">>;
extensions(<<"listafp">>) -> <<"application/vnd.ibm.modcap">>;
extensions(<<"list">>) -> <<"text/plain">>;
extensions(<<"lnk">>) -> <<"application/x-ms-shortcut">>;
extensions(<<"log">>) -> <<"text/plain">>;
extensions(<<"lostxml">>) -> <<"application/lost+xml">>;
extensions(<<"lrf">>) -> <<"application/octet-stream">>;
extensions(<<"lrm">>) -> <<"application/vnd.ms-lrm">>;
extensions(<<"ltf">>) -> <<"application/vnd.frogans.ltf">>;
extensions(<<"lvp">>) -> <<"audio/vnd.lucent.voice">>;
extensions(<<"lwp">>) -> <<"application/vnd.lotus-wordpro">>;
extensions(<<"lzh">>) -> <<"application/x-lzh-compressed">>;
extensions(<<"m13">>) -> <<"application/x-msmediaview">>;
extensions(<<"m14">>) -> <<"application/x-msmediaview">>;
extensions(<<"m1v">>) -> <<"video/mpeg">>;
extensions(<<"m21">>) -> <<"application/mp21">>;
extensions(<<"m2a">>) -> <<"audio/mpeg">>;
extensions(<<"m2ts">>) -> <<"video/mp2t">>;
extensions(<<"m2t">>) -> <<"video/mp2t">>;
extensions(<<"m2v">>) -> <<"video/mpeg">>;
extensions(<<"m3a">>) -> <<"audio/mpeg">>;
extensions(<<"m3u8">>) -> <<"application/vnd.apple.mpegurl">>;
extensions(<<"m3u">>) -> <<"audio/x-mpegurl">>;
extensions(<<"m4a">>) -> <<"audio/mp4">>;
extensions(<<"m4u">>) -> <<"video/vnd.mpegurl">>;
extensions(<<"m4v">>) -> <<"video/x-m4v">>;
extensions(<<"ma">>) -> <<"application/mathematica">>;
extensions(<<"mads">>) -> <<"application/mads+xml">>;
extensions(<<"mag">>) -> <<"application/vnd.ecowin.chart">>;
extensions(<<"maker">>) -> <<"application/vnd.framemaker">>;
extensions(<<"man">>) -> <<"text/troff">>;
extensions(<<"mar">>) -> <<"application/octet-stream">>;
extensions(<<"mathml">>) -> <<"application/mathml+xml">>;
extensions(<<"mb">>) -> <<"application/mathematica">>;
extensions(<<"mbk">>) -> <<"application/vnd.mobius.mbk">>;
extensions(<<"mbox">>) -> <<"application/mbox">>;
extensions(<<"mc1">>) -> <<"application/vnd.medcalcdata">>;
extensions(<<"mcd">>) -> <<"application/vnd.mcd">>;
extensions(<<"mcurl">>) -> <<"text/vnd.curl.mcurl">>;
extensions(<<"mdb">>) -> <<"application/x-msaccess">>;
extensions(<<"mdi">>) -> <<"image/vnd.ms-modi">>;
extensions(<<"mesh">>) -> <<"model/mesh">>;
extensions(<<"meta4">>) -> <<"application/metalink4+xml">>;
extensions(<<"metalink">>) -> <<"application/metalink+xml">>;
extensions(<<"me">>) -> <<"text/troff">>;
extensions(<<"mets">>) -> <<"application/mets+xml">>;
extensions(<<"mfm">>) -> <<"application/vnd.mfmp">>;
extensions(<<"mft">>) -> <<"application/rpki-manifest">>;
extensions(<<"mgp">>) -> <<"application/vnd.osgeo.mapguide.package">>;
extensions(<<"mgz">>) -> <<"application/vnd.proteus.magazine">>;
extensions(<<"mid">>) -> <<"audio/midi">>;
extensions(<<"midi">>) -> <<"audio/midi">>;
extensions(<<"mie">>) -> <<"application/x-mie">>;
extensions(<<"mif">>) -> <<"application/vnd.mif">>;
extensions(<<"mime">>) -> <<"message/rfc822">>;
extensions(<<"mj2">>) -> <<"video/mj2">>;
extensions(<<"mjp2">>) -> <<"video/mj2">>;
extensions(<<"mjs">>) -> <<"text/javascript">>;
extensions(<<"mk3d">>) -> <<"video/x-matroska">>;
extensions(<<"mka">>) -> <<"audio/x-matroska">>;
extensions(<<"mks">>) -> <<"video/x-matroska">>;
extensions(<<"mkv">>) -> <<"video/x-matroska">>;
extensions(<<"mlp">>) -> <<"application/vnd.dolby.mlp">>;
extensions(<<"mmd">>) -> <<"application/vnd.chipnuts.karaoke-mmd">>;
extensions(<<"mmf">>) -> <<"application/vnd.smaf">>;
extensions(<<"mmr">>) -> <<"image/vnd.fujixerox.edmics-mmr">>;
extensions(<<"mng">>) -> <<"video/x-mng">>;
extensions(<<"mny">>) -> <<"application/x-msmoney">>;
extensions(<<"mobi">>) -> <<"application/x-mobipocket-ebook">>;
extensions(<<"mods">>) -> <<"application/mods+xml">>;
extensions(<<"movie">>) -> <<"video/x-sgi-movie">>;
extensions(<<"mov">>) -> <<"video/quicktime">>;
extensions(<<"mp21">>) -> <<"application/mp21">>;
extensions(<<"mp2a">>) -> <<"audio/mpeg">>;
extensions(<<"mp2">>) -> <<"audio/mpeg">>;
extensions(<<"mp3">>) -> <<"audio/mpeg">>;
extensions(<<"mp4a">>) -> <<"audio/mp4">>;
extensions(<<"mp4s">>) -> <<"application/mp4">>;
extensions(<<"mp4">>) -> <<"video/mp4">>;
extensions(<<"mp4v">>) -> <<"video/mp4">>;
extensions(<<"mpc">>) -> <<"application/vnd.mophun.certificate">>;
extensions(<<"mpeg">>) -> <<"video/mpeg">>;
extensions(<<"mpe">>) -> <<"video/mpeg">>;
extensions(<<"mpg4">>) -> <<"video/mp4">>;
extensions(<<"mpga">>) -> <<"audio/mpeg">>;
extensions(<<"mpg">>) -> <<"video/mpeg">>;
extensions(<<"mpkg">>) -> <<"application/vnd.apple.installer+xml">>;
extensions(<<"mpm">>) -> <<"application/vnd.blueice.multipass">>;
extensions(<<"mpn">>) -> <<"application/vnd.mophun.application">>;
extensions(<<"mpp">>) -> <<"application/vnd.ms-project">>;
extensions(<<"mpt">>) -> <<"application/vnd.ms-project">>;
extensions(<<"mpy">>) -> <<"application/vnd.ibm.minipay">>;
extensions(<<"mqy">>) -> <<"application/vnd.mobius.mqy">>;
extensions(<<"mrc">>) -> <<"application/marc">>;
extensions(<<"mrcx">>) -> <<"application/marcxml+xml">>;
extensions(<<"mscml">>) -> <<"application/mediaservercontrol+xml">>;
extensions(<<"mseed">>) -> <<"application/vnd.fdsn.mseed">>;
extensions(<<"mseq">>) -> <<"application/vnd.mseq">>;
extensions(<<"msf">>) -> <<"application/vnd.epson.msf">>;
extensions(<<"msh">>) -> <<"model/mesh">>;
extensions(<<"msi">>) -> <<"application/x-msdownload">>;
extensions(<<"msl">>) -> <<"application/vnd.mobius.msl">>;
extensions(<<"ms">>) -> <<"text/troff">>;
extensions(<<"msty">>) -> <<"application/vnd.muvee.style">>;
extensions(<<"mts">>) -> <<"video/mp2t">>;
extensions(<<"mus">>) -> <<"application/vnd.musician">>;
extensions(<<"musicxml">>) -> <<"application/vnd.recordare.musicxml+xml">>;
extensions(<<"mvb">>) -> <<"application/x-msmediaview">>;
extensions(<<"mwf">>) -> <<"application/vnd.mfer">>;
extensions(<<"mxf">>) -> <<"application/mxf">>;
extensions(<<"mxl">>) -> <<"application/vnd.recordare.musicxml">>;
extensions(<<"mxml">>) -> <<"application/xv+xml">>;
extensions(<<"mxs">>) -> <<"application/vnd.triscape.mxs">>;
extensions(<<"mxu">>) -> <<"video/vnd.mpegurl">>;
extensions(<<"n3">>) -> <<"text/n3">>;
extensions(<<"nb">>) -> <<"application/mathematica">>;
extensions(<<"nbp">>) -> <<"application/vnd.wolfram.player">>;
extensions(<<"nc">>) -> <<"application/x-netcdf">>;
extensions(<<"ncx">>) -> <<"application/x-dtbncx+xml">>;
extensions(<<"nfo">>) -> <<"text/x-nfo">>;
extensions(<<"n-gage">>) -> <<"application/vnd.nokia.n-gage.symbian.install">>;
extensions(<<"ngdat">>) -> <<"application/vnd.nokia.n-gage.data">>;
extensions(<<"nitf">>) -> <<"application/vnd.nitf">>;
extensions(<<"nlu">>) -> <<"application/vnd.neurolanguage.nlu">>;
extensions(<<"nml">>) -> <<"application/vnd.enliven">>;
extensions(<<"nnd">>) -> <<"application/vnd.noblenet-directory">>;
extensions(<<"nns">>) -> <<"application/vnd.noblenet-sealer">>;
extensions(<<"nnw">>) -> <<"application/vnd.noblenet-web">>;
extensions(<<"npx">>) -> <<"image/vnd.net-fpx">>;
extensions(<<"nsc">>) -> <<"application/x-conference">>;
extensions(<<"nsf">>) -> <<"application/vnd.lotus-notes">>;
extensions(<<"ntf">>) -> <<"application/vnd.nitf">>;
extensions(<<"nzb">>) -> <<"application/x-nzb">>;
extensions(<<"oa2">>) -> <<"application/vnd.fujitsu.oasys2">>;
extensions(<<"oa3">>) -> <<"application/vnd.fujitsu.oasys3">>;
extensions(<<"oas">>) -> <<"application/vnd.fujitsu.oasys">>;
extensions(<<"obd">>) -> <<"application/x-msbinder">>;
extensions(<<"obj">>) -> <<"application/x-tgif">>;
extensions(<<"oda">>) -> <<"application/oda">>;
extensions(<<"odb">>) -> <<"application/vnd.oasis.opendocument.database">>;
extensions(<<"odc">>) -> <<"application/vnd.oasis.opendocument.chart">>;
extensions(<<"odf">>) -> <<"application/vnd.oasis.opendocument.formula">>;
extensions(<<"odft">>) -> <<"application/vnd.oasis.opendocument.formula-template">>;
extensions(<<"odg">>) -> <<"application/vnd.oasis.opendocument.graphics">>;
extensions(<<"odi">>) -> <<"application/vnd.oasis.opendocument.image">>;
extensions(<<"odm">>) -> <<"application/vnd.oasis.opendocument.text-master">>;
extensions(<<"odp">>) -> <<"application/vnd.oasis.opendocument.presentation">>;
extensions(<<"ods">>) -> <<"application/vnd.oasis.opendocument.spreadsheet">>;
extensions(<<"odt">>) -> <<"application/vnd.oasis.opendocument.text">>;
extensions(<<"oga">>) -> <<"audio/ogg">>;
extensions(<<"ogg">>) -> <<"audio/ogg">>;
extensions(<<"ogv">>) -> <<"video/ogg">>;
extensions(<<"ogx">>) -> <<"application/ogg">>;
extensions(<<"omdoc">>) -> <<"application/omdoc+xml">>;
extensions(<<"onepkg">>) -> <<"application/onenote">>;
extensions(<<"onetmp">>) -> <<"application/onenote">>;
extensions(<<"onetoc2">>) -> <<"application/onenote">>;
extensions(<<"onetoc">>) -> <<"application/onenote">>;
extensions(<<"opf">>) -> <<"application/oebps-package+xml">>;
extensions(<<"opml">>) -> <<"text/x-opml">>;
extensions(<<"oprc">>) -> <<"application/vnd.palm">>;
extensions(<<"opus">>) -> <<"audio/ogg">>;
extensions(<<"org">>) -> <<"application/vnd.lotus-organizer">>;
extensions(<<"osf">>) -> <<"application/vnd.yamaha.openscoreformat">>;
extensions(<<"osfpvg">>) -> <<"application/vnd.yamaha.openscoreformat.osfpvg+xml">>;
extensions(<<"otc">>) -> <<"application/vnd.oasis.opendocument.chart-template">>;
extensions(<<"otf">>) -> <<"font/otf">>;
extensions(<<"otg">>) -> <<"application/vnd.oasis.opendocument.graphics-template">>;
extensions(<<"oth">>) -> <<"application/vnd.oasis.opendocument.text-web">>;
extensions(<<"oti">>) -> <<"application/vnd.oasis.opendocument.image-template">>;
extensions(<<"otp">>) -> <<"application/vnd.oasis.opendocument.presentation-template">>;
extensions(<<"ots">>) -> <<"application/vnd.oasis.opendocument.spreadsheet-template">>;
extensions(<<"ott">>) -> <<"application/vnd.oasis.opendocument.text-template">>;
extensions(<<"oxps">>) -> <<"application/oxps">>;
extensions(<<"oxt">>) -> <<"application/vnd.openofficeorg.extension">>;
extensions(<<"p10">>) -> <<"application/pkcs10">>;
extensions(<<"p12">>) -> <<"application/x-pkcs12">>;
extensions(<<"p7b">>) -> <<"application/x-pkcs7-certificates">>;
extensions(<<"p7c">>) -> <<"application/pkcs7-mime">>;
extensions(<<"p7m">>) -> <<"application/pkcs7-mime">>;
extensions(<<"p7r">>) -> <<"application/x-pkcs7-certreqresp">>;
extensions(<<"p7s">>) -> <<"application/pkcs7-signature">>;
extensions(<<"p8">>) -> <<"application/pkcs8">>;
extensions(<<"pas">>) -> <<"text/x-pascal">>;
extensions(<<"paw">>) -> <<"application/vnd.pawaafile">>;
extensions(<<"pbd">>) -> <<"application/vnd.powerbuilder6">>;
extensions(<<"pbm">>) -> <<"image/x-portable-bitmap">>;
extensions(<<"pcap">>) -> <<"application/vnd.tcpdump.pcap">>;
extensions(<<"pcf">>) -> <<"application/x-font-pcf">>;
extensions(<<"pcl">>) -> <<"application/vnd.hp-pcl">>;
extensions(<<"pclxl">>) -> <<"application/vnd.hp-pclxl">>;
extensions(<<"pct">>) -> <<"image/x-pict">>;
extensions(<<"pcurl">>) -> <<"application/vnd.curl.pcurl">>;
extensions(<<"pcx">>) -> <<"image/x-pcx">>;
extensions(<<"pdb">>) -> <<"application/vnd.palm">>;
extensions(<<"pdf">>) -> <<"application/pdf">>;
extensions(<<"pfa">>) -> <<"application/x-font-type1">>;
extensions(<<"pfb">>) -> <<"application/x-font-type1">>;
extensions(<<"pfm">>) -> <<"application/x-font-type1">>;
extensions(<<"pfr">>) -> <<"application/font-tdpfr">>;
extensions(<<"pfx">>) -> <<"application/x-pkcs12">>;
extensions(<<"pgm">>) -> <<"image/x-portable-graymap">>;
extensions(<<"pgn">>) -> <<"application/x-chess-pgn">>;
extensions(<<"pgp">>) -> <<"application/pgp-encrypted">>;
extensions(<<"pic">>) -> <<"image/x-pict">>;
extensions(<<"pkg">>) -> <<"application/octet-stream">>;
extensions(<<"pki">>) -> <<"application/pkixcmp">>;
extensions(<<"pkipath">>) -> <<"application/pkix-pkipath">>;
extensions(<<"plb">>) -> <<"application/vnd.3gpp.pic-bw-large">>;
extensions(<<"plc">>) -> <<"application/vnd.mobius.plc">>;
extensions(<<"plf">>) -> <<"application/vnd.pocketlearn">>;
extensions(<<"pls">>) -> <<"application/pls+xml">>;
extensions(<<"pml">>) -> <<"application/vnd.ctc-posml">>;
extensions(<<"png">>) -> <<"image/png">>;
extensions(<<"pnm">>) -> <<"image/x-portable-anymap">>;
extensions(<<"portpkg">>) -> <<"application/vnd.macports.portpkg">>;
extensions(<<"pot">>) -> <<"application/vnd.ms-powerpoint">>;
extensions(<<"potm">>) -> <<"application/vnd.ms-powerpoint.template.macroenabled.12">>;
extensions(<<"potx">>) -> <<"application/vnd.openxmlformats-officedocument.presentationml.template">>;
extensions(<<"ppam">>) -> <<"application/vnd.ms-powerpoint.addin.macroenabled.12">>;
extensions(<<"ppd">>) -> <<"application/vnd.cups-ppd">>;
extensions(<<"ppm">>) -> <<"image/x-portable-pixmap">>;
extensions(<<"pps">>) -> <<"application/vnd.ms-powerpoint">>;
extensions(<<"ppsm">>) -> <<"application/vnd.ms-powerpoint.slideshow.macroenabled.12">>;
extensions(<<"ppsx">>) -> <<"application/vnd.openxmlformats-officedocument.presentationml.slideshow">>;
extensions(<<"ppt">>) -> <<"application/vnd.ms-powerpoint">>;
extensions(<<"pptm">>) -> <<"application/vnd.ms-powerpoint.presentation.macroenabled.12">>;
extensions(<<"pptx">>) -> <<"application/vnd.openxmlformats-officedocument.presentationml.presentation">>;
extensions(<<"pqa">>) -> <<"application/vnd.palm">>;
extensions(<<"prc">>) -> <<"application/x-mobipocket-ebook">>;
extensions(<<"pre">>) -> <<"application/vnd.lotus-freelance">>;
extensions(<<"prf">>) -> <<"application/pics-rules">>;
extensions(<<"ps">>) -> <<"application/postscript">>;
extensions(<<"psb">>) -> <<"application/vnd.3gpp.pic-bw-small">>;
extensions(<<"psd">>) -> <<"image/vnd.adobe.photoshop">>;
extensions(<<"psf">>) -> <<"application/x-font-linux-psf">>;
extensions(<<"pskcxml">>) -> <<"application/pskc+xml">>;
extensions(<<"p">>) -> <<"text/x-pascal">>;
extensions(<<"ptid">>) -> <<"application/vnd.pvi.ptid1">>;
extensions(<<"pub">>) -> <<"application/x-mspublisher">>;
extensions(<<"pvb">>) -> <<"application/vnd.3gpp.pic-bw-var">>;
extensions(<<"pwn">>) -> <<"application/vnd.3m.post-it-notes">>;
extensions(<<"pya">>) -> <<"audio/vnd.ms-playready.media.pya">>;
extensions(<<"pyv">>) -> <<"video/vnd.ms-playready.media.pyv">>;
extensions(<<"qam">>) -> <<"application/vnd.epson.quickanime">>;
extensions(<<"qbo">>) -> <<"application/vnd.intu.qbo">>;
extensions(<<"qfx">>) -> <<"application/vnd.intu.qfx">>;
extensions(<<"qps">>) -> <<"application/vnd.publishare-delta-tree">>;
extensions(<<"qt">>) -> <<"video/quicktime">>;
extensions(<<"qwd">>) -> <<"application/vnd.quark.quarkxpress">>;
extensions(<<"qwt">>) -> <<"application/vnd.quark.quarkxpress">>;
extensions(<<"qxb">>) -> <<"application/vnd.quark.quarkxpress">>;
extensions(<<"qxd">>) -> <<"application/vnd.quark.quarkxpress">>;
extensions(<<"qxl">>) -> <<"application/vnd.quark.quarkxpress">>;
extensions(<<"qxt">>) -> <<"application/vnd.quark.quarkxpress">>;
extensions(<<"ra">>) -> <<"audio/x-pn-realaudio">>;
extensions(<<"ram">>) -> <<"audio/x-pn-realaudio">>;
extensions(<<"rar">>) -> <<"application/x-rar-compressed">>;
extensions(<<"ras">>) -> <<"image/x-cmu-raster">>;
extensions(<<"rcprofile">>) -> <<"application/vnd.ipunplugged.rcprofile">>;
extensions(<<"rdf">>) -> <<"application/rdf+xml">>;
extensions(<<"rdz">>) -> <<"application/vnd.data-vision.rdz">>;
extensions(<<"rep">>) -> <<"application/vnd.businessobjects">>;
extensions(<<"res">>) -> <<"application/x-dtbresource+xml">>;
extensions(<<"rgb">>) -> <<"image/x-rgb">>;
extensions(<<"rif">>) -> <<"application/reginfo+xml">>;
extensions(<<"rip">>) -> <<"audio/vnd.rip">>;
extensions(<<"ris">>) -> <<"application/x-research-info-systems">>;
extensions(<<"rl">>) -> <<"application/resource-lists+xml">>;
extensions(<<"rlc">>) -> <<"image/vnd.fujixerox.edmics-rlc">>;
extensions(<<"rld">>) -> <<"application/resource-lists-diff+xml">>;
extensions(<<"rm">>) -> <<"application/vnd.rn-realmedia">>;
extensions(<<"rmi">>) -> <<"audio/midi">>;
extensions(<<"rmp">>) -> <<"audio/x-pn-realaudio-plugin">>;
extensions(<<"rms">>) -> <<"application/vnd.jcp.javame.midlet-rms">>;
extensions(<<"rmvb">>) -> <<"application/vnd.rn-realmedia-vbr">>;
extensions(<<"rnc">>) -> <<"application/relax-ng-compact-syntax">>;
extensions(<<"roa">>) -> <<"application/rpki-roa">>;
extensions(<<"roff">>) -> <<"text/troff">>;
extensions(<<"rp9">>) -> <<"application/vnd.cloanto.rp9">>;
extensions(<<"rpss">>) -> <<"application/vnd.nokia.radio-presets">>;
extensions(<<"rpst">>) -> <<"application/vnd.nokia.radio-preset">>;
extensions(<<"rq">>) -> <<"application/sparql-query">>;
extensions(<<"rs">>) -> <<"application/rls-services+xml">>;
extensions(<<"rsd">>) -> <<"application/rsd+xml">>;
extensions(<<"rss">>) -> <<"application/rss+xml">>;
extensions(<<"rtf">>) -> <<"application/rtf">>;
extensions(<<"rtx">>) -> <<"text/richtext">>;
extensions(<<"s3m">>) -> <<"audio/s3m">>;
extensions(<<"saf">>) -> <<"application/vnd.yamaha.smaf-audio">>;
extensions(<<"sbml">>) -> <<"application/sbml+xml">>;
extensions(<<"sc">>) -> <<"application/vnd.ibm.secure-container">>;
extensions(<<"scd">>) -> <<"application/x-msschedule">>;
extensions(<<"scm">>) -> <<"application/vnd.lotus-screencam">>;
extensions(<<"scq">>) -> <<"application/scvp-cv-request">>;
extensions(<<"scs">>) -> <<"application/scvp-cv-response">>;
extensions(<<"scurl">>) -> <<"text/vnd.curl.scurl">>;
extensions(<<"sda">>) -> <<"application/vnd.stardivision.draw">>;
extensions(<<"sdc">>) -> <<"application/vnd.stardivision.calc">>;
extensions(<<"sdd">>) -> <<"application/vnd.stardivision.impress">>;
extensions(<<"sdkd">>) -> <<"application/vnd.solent.sdkm+xml">>;
extensions(<<"sdkm">>) -> <<"application/vnd.solent.sdkm+xml">>;
extensions(<<"sdp">>) -> <<"application/sdp">>;
extensions(<<"sdw">>) -> <<"application/vnd.stardivision.writer">>;
extensions(<<"see">>) -> <<"application/vnd.seemail">>;
extensions(<<"seed">>) -> <<"application/vnd.fdsn.seed">>;
extensions(<<"sema">>) -> <<"application/vnd.sema">>;
extensions(<<"semd">>) -> <<"application/vnd.semd">>;
extensions(<<"semf">>) -> <<"application/vnd.semf">>;
extensions(<<"ser">>) -> <<"application/java-serialized-object">>;
extensions(<<"setpay">>) -> <<"application/set-payment-initiation">>;
extensions(<<"setreg">>) -> <<"application/set-registration-initiation">>;
extensions(<<"sfd-hdstx">>) -> <<"application/vnd.hydrostatix.sof-data">>;
extensions(<<"sfs">>) -> <<"application/vnd.spotfire.sfs">>;
extensions(<<"sfv">>) -> <<"text/x-sfv">>;
extensions(<<"sgi">>) -> <<"image/sgi">>;
extensions(<<"sgl">>) -> <<"application/vnd.stardivision.writer-global">>;
extensions(<<"sgml">>) -> <<"text/sgml">>;
extensions(<<"sgm">>) -> <<"text/sgml">>;
extensions(<<"sh">>) -> <<"application/x-sh">>;
extensions(<<"shar">>) -> <<"application/x-shar">>;
extensions(<<"shf">>) -> <<"application/shf+xml">>;
extensions(<<"sid">>) -> <<"image/x-mrsid-image">>;
extensions(<<"sig">>) -> <<"application/pgp-signature">>;
extensions(<<"sil">>) -> <<"audio/silk">>;
extensions(<<"silo">>) -> <<"model/mesh">>;
extensions(<<"sis">>) -> <<"application/vnd.symbian.install">>;
extensions(<<"sisx">>) -> <<"application/vnd.symbian.install">>;
extensions(<<"sit">>) -> <<"application/x-stuffit">>;
extensions(<<"sitx">>) -> <<"application/x-stuffitx">>;
extensions(<<"skd">>) -> <<"application/vnd.koan">>;
extensions(<<"skm">>) -> <<"application/vnd.koan">>;
extensions(<<"skp">>) -> <<"application/vnd.koan">>;
extensions(<<"skt">>) -> <<"application/vnd.koan">>;
extensions(<<"sldm">>) -> <<"application/vnd.ms-powerpoint.slide.macroenabled.12">>;
extensions(<<"sldx">>) -> <<"application/vnd.openxmlformats-officedocument.presentationml.slide">>;
extensions(<<"slt">>) -> <<"application/vnd.epson.salt">>;
extensions(<<"sm">>) -> <<"application/vnd.stepmania.stepchart">>;
extensions(<<"smf">>) -> <<"application/vnd.stardivision.math">>;
extensions(<<"smi">>) -> <<"application/smil+xml">>;
extensions(<<"smil">>) -> <<"application/smil+xml">>;
extensions(<<"smv">>) -> <<"video/x-smv">>;
extensions(<<"smzip">>) -> <<"application/vnd.stepmania.package">>;
extensions(<<"snd">>) -> <<"audio/basic">>;
extensions(<<"snf">>) -> <<"application/x-font-snf">>;
extensions(<<"so">>) -> <<"application/octet-stream">>;
extensions(<<"spc">>) -> <<"application/x-pkcs7-certificates">>;
extensions(<<"spf">>) -> <<"application/vnd.yamaha.smaf-phrase">>;
extensions(<<"spl">>) -> <<"application/x-futuresplash">>;
extensions(<<"spot">>) -> <<"text/vnd.in3d.spot">>;
extensions(<<"spp">>) -> <<"application/scvp-vp-response">>;
extensions(<<"spq">>) -> <<"application/scvp-vp-request">>;
extensions(<<"spx">>) -> <<"audio/ogg">>;
extensions(<<"sql">>) -> <<"application/x-sql">>;
extensions(<<"src">>) -> <<"application/x-wais-source">>;
extensions(<<"srt">>) -> <<"application/x-subrip">>;
extensions(<<"sru">>) -> <<"application/sru+xml">>;
extensions(<<"srx">>) -> <<"application/sparql-results+xml">>;
extensions(<<"ssdl">>) -> <<"application/ssdl+xml">>;
extensions(<<"sse">>) -> <<"application/vnd.kodak-descriptor">>;
extensions(<<"ssf">>) -> <<"application/vnd.epson.ssf">>;
extensions(<<"ssml">>) -> <<"application/ssml+xml">>;
extensions(<<"st">>) -> <<"application/vnd.sailingtracker.track">>;
extensions(<<"stc">>) -> <<"application/vnd.sun.xml.calc.template">>;
extensions(<<"std">>) -> <<"application/vnd.sun.xml.draw.template">>;
extensions(<<"s">>) -> <<"text/x-asm">>;
extensions(<<"stf">>) -> <<"application/vnd.wt.stf">>;
extensions(<<"sti">>) -> <<"application/vnd.sun.xml.impress.template">>;
extensions(<<"stk">>) -> <<"application/hyperstudio">>;
extensions(<<"stl">>) -> <<"application/vnd.ms-pki.stl">>;
extensions(<<"str">>) -> <<"application/vnd.pg.format">>;
extensions(<<"stw">>) -> <<"application/vnd.sun.xml.writer.template">>;
extensions(<<"sub">>) -> <<"image/vnd.dvb.subtitle">>;
extensions(<<"sus">>) -> <<"application/vnd.sus-calendar">>;
extensions(<<"susp">>) -> <<"application/vnd.sus-calendar">>;
extensions(<<"sv4cpio">>) -> <<"application/x-sv4cpio">>;
extensions(<<"sv4crc">>) -> <<"application/x-sv4crc">>;
extensions(<<"svc">>) -> <<"application/vnd.dvb.service">>;
extensions(<<"svd">>) -> <<"application/vnd.svd">>;
extensions(<<"svg">>) -> <<"image/svg+xml">>;
extensions(<<"svgz">>) -> <<"image/svg+xml">>;
extensions(<<"swa">>) -> <<"application/x-director">>;
extensions(<<"swf">>) -> <<"application/x-shockwave-flash">>;
extensions(<<"swi">>) -> <<"application/vnd.aristanetworks.swi">>;
extensions(<<"sxc">>) -> <<"application/vnd.sun.xml.calc">>;
extensions(<<"sxd">>) -> <<"application/vnd.sun.xml.draw">>;
extensions(<<"sxg">>) -> <<"application/vnd.sun.xml.writer.global">>;
extensions(<<"sxi">>) -> <<"application/vnd.sun.xml.impress">>;
extensions(<<"sxm">>) -> <<"application/vnd.sun.xml.math">>;
extensions(<<"sxw">>) -> <<"application/vnd.sun.xml.writer">>;
extensions(<<"t3">>) -> <<"application/x-t3vm-image">>;
extensions(<<"taglet">>) -> <<"application/vnd.mynfc">>;
extensions(<<"tao">>) -> <<"application/vnd.tao.intent-module-archive">>;
extensions(<<"tar">>) -> <<"application/x-tar">>;
extensions(<<"tcap">>) -> <<"application/vnd.3gpp2.tcap">>;
extensions(<<"tcl">>) -> <<"application/x-tcl">>;
extensions(<<"teacher">>) -> <<"application/vnd.smart.teacher">>;
extensions(<<"tei">>) -> <<"application/tei+xml">>;
extensions(<<"teicorpus">>) -> <<"application/tei+xml">>;
extensions(<<"tex">>) -> <<"application/x-tex">>;
extensions(<<"texi">>) -> <<"application/x-texinfo">>;
extensions(<<"texinfo">>) -> <<"application/x-texinfo">>;
extensions(<<"text">>) -> <<"text/plain">>;
extensions(<<"tfi">>) -> <<"application/thraud+xml">>;
extensions(<<"tfm">>) -> <<"application/x-tex-tfm">>;
extensions(<<"tga">>) -> <<"image/x-tga">>;
extensions(<<"thmx">>) -> <<"application/vnd.ms-officetheme">>;
extensions(<<"tiff">>) -> <<"image/tiff">>;
extensions(<<"tif">>) -> <<"image/tiff">>;
extensions(<<"tmo">>) -> <<"application/vnd.tmobile-livetv">>;
extensions(<<"torrent">>) -> <<"application/x-bittorrent">>;
extensions(<<"tpl">>) -> <<"application/vnd.groove-tool-template">>;
extensions(<<"tpt">>) -> <<"application/vnd.trid.tpt">>;
extensions(<<"tra">>) -> <<"application/vnd.trueapp">>;
extensions(<<"trm">>) -> <<"application/x-msterminal">>;
extensions(<<"tr">>) -> <<"text/troff">>;
extensions(<<"tsd">>) -> <<"application/timestamped-data">>;
extensions(<<"ts">>) -> <<"video/mp2t">>;
extensions(<<"tsv">>) -> <<"text/tab-separated-values">>;
extensions(<<"ttc">>) -> <<"font/collection">>;
extensions(<<"t">>) -> <<"text/troff">>;
extensions(<<"ttf">>) -> <<"font/ttf">>;
extensions(<<"ttl">>) -> <<"text/turtle">>;
extensions(<<"twd">>) -> <<"application/vnd.simtech-mindmapper">>;
extensions(<<"twds">>) -> <<"application/vnd.simtech-mindmapper">>;
extensions(<<"txd">>) -> <<"application/vnd.genomatix.tuxedo">>;
extensions(<<"txf">>) -> <<"application/vnd.mobius.txf">>;
extensions(<<"txt">>) -> <<"text/plain">>;
extensions(<<"u32">>) -> <<"application/x-authorware-bin">>;
extensions(<<"udeb">>) -> <<"application/x-debian-package">>;
extensions(<<"ufd">>) -> <<"application/vnd.ufdl">>;
extensions(<<"ufdl">>) -> <<"application/vnd.ufdl">>;
extensions(<<"ulx">>) -> <<"application/x-glulx">>;
extensions(<<"umj">>) -> <<"application/vnd.umajin">>;
extensions(<<"unityweb">>) -> <<"application/vnd.unity">>;
extensions(<<"uoml">>) -> <<"application/vnd.uoml+xml">>;
extensions(<<"uris">>) -> <<"text/uri-list">>;
extensions(<<"uri">>) -> <<"text/uri-list">>;
extensions(<<"urls">>) -> <<"text/uri-list">>;
extensions(<<"ustar">>) -> <<"application/x-ustar">>;
extensions(<<"utz">>) -> <<"application/vnd.uiq.theme">>;
extensions(<<"uu">>) -> <<"text/x-uuencode">>;
extensions(<<"uva">>) -> <<"audio/vnd.dece.audio">>;
extensions(<<"uvd">>) -> <<"application/vnd.dece.data">>;
extensions(<<"uvf">>) -> <<"application/vnd.dece.data">>;
extensions(<<"uvg">>) -> <<"image/vnd.dece.graphic">>;
extensions(<<"uvh">>) -> <<"video/vnd.dece.hd">>;
extensions(<<"uvi">>) -> <<"image/vnd.dece.graphic">>;
extensions(<<"uvm">>) -> <<"video/vnd.dece.mobile">>;
extensions(<<"uvp">>) -> <<"video/vnd.dece.pd">>;
extensions(<<"uvs">>) -> <<"video/vnd.dece.sd">>;
extensions(<<"uvt">>) -> <<"application/vnd.dece.ttml+xml">>;
extensions(<<"uvu">>) -> <<"video/vnd.uvvu.mp4">>;
extensions(<<"uvva">>) -> <<"audio/vnd.dece.audio">>;
extensions(<<"uvvd">>) -> <<"application/vnd.dece.data">>;
extensions(<<"uvvf">>) -> <<"application/vnd.dece.data">>;
extensions(<<"uvvg">>) -> <<"image/vnd.dece.graphic">>;
extensions(<<"uvvh">>) -> <<"video/vnd.dece.hd">>;
extensions(<<"uvvi">>) -> <<"image/vnd.dece.graphic">>;
extensions(<<"uvvm">>) -> <<"video/vnd.dece.mobile">>;
extensions(<<"uvvp">>) -> <<"video/vnd.dece.pd">>;
extensions(<<"uvvs">>) -> <<"video/vnd.dece.sd">>;
extensions(<<"uvvt">>) -> <<"application/vnd.dece.ttml+xml">>;
extensions(<<"uvvu">>) -> <<"video/vnd.uvvu.mp4">>;
extensions(<<"uvv">>) -> <<"video/vnd.dece.video">>;
extensions(<<"uvvv">>) -> <<"video/vnd.dece.video">>;
extensions(<<"uvvx">>) -> <<"application/vnd.dece.unspecified">>;
extensions(<<"uvvz">>) -> <<"application/vnd.dece.zip">>;
extensions(<<"uvx">>) -> <<"application/vnd.dece.unspecified">>;
extensions(<<"uvz">>) -> <<"application/vnd.dece.zip">>;
extensions(<<"vcard">>) -> <<"text/vcard">>;
extensions(<<"vcd">>) -> <<"application/x-cdlink">>;
extensions(<<"vcf">>) -> <<"text/x-vcard">>;
extensions(<<"vcg">>) -> <<"application/vnd.groove-vcard">>;
extensions(<<"vcs">>) -> <<"text/x-vcalendar">>;
extensions(<<"vcx">>) -> <<"application/vnd.vcx">>;
extensions(<<"vis">>) -> <<"application/vnd.visionary">>;
extensions(<<"viv">>) -> <<"video/vnd.vivo">>;
extensions(<<"vob">>) -> <<"video/x-ms-vob">>;
extensions(<<"vor">>) -> <<"application/vnd.stardivision.writer">>;
extensions(<<"vox">>) -> <<"application/x-authorware-bin">>;
extensions(<<"vrml">>) -> <<"model/vrml">>;
extensions(<<"vsd">>) -> <<"application/vnd.visio">>;
extensions(<<"vsf">>) -> <<"application/vnd.vsf">>;
extensions(<<"vss">>) -> <<"application/vnd.visio">>;
extensions(<<"vst">>) -> <<"application/vnd.visio">>;
extensions(<<"vsw">>) -> <<"application/vnd.visio">>;
extensions(<<"vtu">>) -> <<"model/vnd.vtu">>;
extensions(<<"vxml">>) -> <<"application/voicexml+xml">>;
extensions(<<"w3d">>) -> <<"application/x-director">>;
extensions(<<"wad">>) -> <<"application/x-doom">>;
extensions(<<"wasm">>) -> <<"application/wasm">>;
extensions(<<"wav">>) -> <<"audio/x-wav">>;
extensions(<<"wax">>) -> <<"audio/x-ms-wax">>;
extensions(<<"wbmp">>) -> <<"image/vnd.wap.wbmp">>;
extensions(<<"wbs">>) -> <<"application/vnd.criticaltools.wbs+xml">>;
extensions(<<"wbxml">>) -> <<"application/vnd.wap.wbxml">>;
extensions(<<"wcm">>) -> <<"application/vnd.ms-works">>;
extensions(<<"wdb">>) -> <<"application/vnd.ms-works">>;
extensions(<<"wdp">>) -> <<"image/vnd.ms-photo">>;
extensions(<<"weba">>) -> <<"audio/webm">>;
extensions(<<"webm">>) -> <<"video/webm">>;
extensions(<<"webp">>) -> <<"image/webp">>;
extensions(<<"wg">>) -> <<"application/vnd.pmi.widget">>;
extensions(<<"wgt">>) -> <<"application/widget">>;
extensions(<<"wks">>) -> <<"application/vnd.ms-works">>;
extensions(<<"wma">>) -> <<"audio/x-ms-wma">>;
extensions(<<"wmd">>) -> <<"application/x-ms-wmd">>;
extensions(<<"wmf">>) -> <<"application/x-msmetafile">>;
extensions(<<"wmlc">>) -> <<"application/vnd.wap.wmlc">>;
extensions(<<"wmlsc">>) -> <<"application/vnd.wap.wmlscriptc">>;
extensions(<<"wmls">>) -> <<"text/vnd.wap.wmlscript">>;
extensions(<<"wml">>) -> <<"text/vnd.wap.wml">>;
extensions(<<"wm">>) -> <<"video/x-ms-wm">>;
extensions(<<"wmv">>) -> <<"video/x-ms-wmv">>;
extensions(<<"wmx">>) -> <<"video/x-ms-wmx">>;
extensions(<<"wmz">>) -> <<"application/x-msmetafile">>;
extensions(<<"woff2">>) -> <<"font/woff2">>;
extensions(<<"woff">>) -> <<"font/woff">>;
extensions(<<"wpd">>) -> <<"application/vnd.wordperfect">>;
extensions(<<"wpl">>) -> <<"application/vnd.ms-wpl">>;
extensions(<<"wps">>) -> <<"application/vnd.ms-works">>;
extensions(<<"wqd">>) -> <<"application/vnd.wqd">>;
extensions(<<"wri">>) -> <<"application/x-mswrite">>;
extensions(<<"wrl">>) -> <<"model/vrml">>;
extensions(<<"wsdl">>) -> <<"application/wsdl+xml">>;
extensions(<<"wspolicy">>) -> <<"application/wspolicy+xml">>;
extensions(<<"wtb">>) -> <<"application/vnd.webturbo">>;
extensions(<<"wvx">>) -> <<"video/x-ms-wvx">>;
extensions(<<"x32">>) -> <<"application/x-authorware-bin">>;
extensions(<<"x3db">>) -> <<"model/x3d+binary">>;
extensions(<<"x3dbz">>) -> <<"model/x3d+binary">>;
extensions(<<"x3d">>) -> <<"model/x3d+xml">>;
extensions(<<"x3dv">>) -> <<"model/x3d+vrml">>;
extensions(<<"x3dvz">>) -> <<"model/x3d+vrml">>;
extensions(<<"x3dz">>) -> <<"model/x3d+xml">>;
extensions(<<"xaml">>) -> <<"application/xaml+xml">>;
extensions(<<"xap">>) -> <<"application/x-silverlight-app">>;
extensions(<<"xar">>) -> <<"application/vnd.xara">>;
extensions(<<"xbap">>) -> <<"application/x-ms-xbap">>;
extensions(<<"xbd">>) -> <<"application/vnd.fujixerox.docuworks.binder">>;
extensions(<<"xbm">>) -> <<"image/x-xbitmap">>;
extensions(<<"xdf">>) -> <<"application/xcap-diff+xml">>;
extensions(<<"xdm">>) -> <<"application/vnd.syncml.dm+xml">>;
extensions(<<"xdp">>) -> <<"application/vnd.adobe.xdp+xml">>;
extensions(<<"xdssc">>) -> <<"application/dssc+xml">>;
extensions(<<"xdw">>) -> <<"application/vnd.fujixerox.docuworks">>;
extensions(<<"xenc">>) -> <<"application/xenc+xml">>;
extensions(<<"xer">>) -> <<"application/patch-ops-error+xml">>;
extensions(<<"xfdf">>) -> <<"application/vnd.adobe.xfdf">>;
extensions(<<"xfdl">>) -> <<"application/vnd.xfdl">>;
extensions(<<"xht">>) -> <<"application/xhtml+xml">>;
extensions(<<"xhtml">>) -> <<"application/xhtml+xml">>;
extensions(<<"xhvml">>) -> <<"application/xv+xml">>;
extensions(<<"xif">>) -> <<"image/vnd.xiff">>;
extensions(<<"xla">>) -> <<"application/vnd.ms-excel">>;
extensions(<<"xlam">>) -> <<"application/vnd.ms-excel.addin.macroenabled.12">>;
extensions(<<"xlc">>) -> <<"application/vnd.ms-excel">>;
extensions(<<"xlf">>) -> <<"application/x-xliff+xml">>;
extensions(<<"xlm">>) -> <<"application/vnd.ms-excel">>;
extensions(<<"xls">>) -> <<"application/vnd.ms-excel">>;
extensions(<<"xlsb">>) -> <<"application/vnd.ms-excel.sheet.binary.macroenabled.12">>;
extensions(<<"xlsm">>) -> <<"application/vnd.ms-excel.sheet.macroenabled.12">>;
extensions(<<"xlsx">>) -> <<"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet">>;
extensions(<<"xlt">>) -> <<"application/vnd.ms-excel">>;
extensions(<<"xltm">>) -> <<"application/vnd.ms-excel.template.macroenabled.12">>;
extensions(<<"xltx">>) -> <<"application/vnd.openxmlformats-officedocument.spreadsheetml.template">>;
extensions(<<"xlw">>) -> <<"application/vnd.ms-excel">>;
extensions(<<"xm">>) -> <<"audio/xm">>;
extensions(<<"xml">>) -> <<"application/xml">>;
extensions(<<"xo">>) -> <<"application/vnd.olpc-sugar">>;
extensions(<<"xop">>) -> <<"application/xop+xml">>;
extensions(<<"xpi">>) -> <<"application/x-xpinstall">>;
extensions(<<"xpl">>) -> <<"application/xproc+xml">>;
extensions(<<"xpm">>) -> <<"image/x-xpixmap">>;
extensions(<<"xpr">>) -> <<"application/vnd.is-xpr">>;
extensions(<<"xps">>) -> <<"application/vnd.ms-xpsdocument">>;
extensions(<<"xpw">>) -> <<"application/vnd.intercon.formnet">>;
extensions(<<"xpx">>) -> <<"application/vnd.intercon.formnet">>;
extensions(<<"xsl">>) -> <<"application/xml">>;
extensions(<<"xslt">>) -> <<"application/xslt+xml">>;
extensions(<<"xsm">>) -> <<"application/vnd.syncml+xml">>;
extensions(<<"xspf">>) -> <<"application/xspf+xml">>;
extensions(<<"xul">>) -> <<"application/vnd.mozilla.xul+xml">>;
extensions(<<"xvm">>) -> <<"application/xv+xml">>;
extensions(<<"xvml">>) -> <<"application/xv+xml">>;
extensions(<<"xwd">>) -> <<"image/x-xwindowdump">>;
extensions(<<"xyz">>) -> <<"chemical/x-xyz">>;
extensions(<<"xz">>) -> <<"application/x-xz">>;
extensions(<<"yang">>) -> <<"application/yang">>;
extensions(<<"yin">>) -> <<"application/yin+xml">>;
extensions(<<"z1">>) -> <<"application/x-zmachine">>;
extensions(<<"z2">>) -> <<"application/x-zmachine">>;
extensions(<<"z3">>) -> <<"application/x-zmachine">>;
extensions(<<"z4">>) -> <<"application/x-zmachine">>;
extensions(<<"z5">>) -> <<"application/x-zmachine">>;
extensions(<<"z6">>) -> <<"application/x-zmachine">>;
extensions(<<"z7">>) -> <<"application/x-zmachine">>;
extensions(<<"z8">>) -> <<"application/x-zmachine">>;
extensions(<<"zaz">>) -> <<"application/vnd.zzazz.deck+xml">>;
extensions(<<"zip">>) -> <<"application/zip">>;
extensions(<<"zir">>) -> <<"application/vnd.zul">>;
extensions(<<"zirz">>) -> <<"application/vnd.zul">>;
extensions(<<"zmm">>) -> <<"application/vnd.handheld-entertainment+xml">>;
extensions(_) -> <<"application/octet-stream">>.

mimetypes(<<"application/andrew-inset">>) -> [<<"ez">>];
mimetypes(<<"application/applixware">>) -> [<<"aw">>];
mimetypes(<<"application/atomcat+xml">>) -> [<<"atomcat">>];
mimetypes(<<"application/atomsvc+xml">>) -> [<<"atomsvc">>];
mimetypes(<<"application/atom+xml">>) -> [<<"atom">>];
mimetypes(<<"application/ccxml+xml">>) -> [<<"ccxml">>];
mimetypes(<<"application/cdmi-capability">>) -> [<<"cdmia">>];
mimetypes(<<"application/cdmi-container">>) -> [<<"cdmic">>];
mimetypes(<<"application/cdmi-domain">>) -> [<<"cdmid">>];
mimetypes(<<"application/cdmi-object">>) -> [<<"cdmio">>];
mimetypes(<<"application/cdmi-queue">>) -> [<<"cdmiq">>];
mimetypes(<<"application/cu-seeme">>) -> [<<"cu">>];
mimetypes(<<"application/davmount+xml">>) -> [<<"davmount">>];
mimetypes(<<"application/docbook+xml">>) -> [<<"dbk">>];
mimetypes(<<"application/dssc+der">>) -> [<<"dssc">>];
mimetypes(<<"application/dssc+xml">>) -> [<<"xdssc">>];
mimetypes(<<"application/ecmascript">>) -> [<<"ecma">>];
mimetypes(<<"application/emma+xml">>) -> [<<"emma">>];
mimetypes(<<"application/epub+zip">>) -> [<<"epub">>];
mimetypes(<<"application/exi">>) -> [<<"exi">>];
mimetypes(<<"application/font-tdpfr">>) -> [<<"pfr">>];
mimetypes(<<"application/gml+xml">>) -> [<<"gml">>];
mimetypes(<<"application/gpx+xml">>) -> [<<"gpx">>];
mimetypes(<<"application/gxf">>) -> [<<"gxf">>];
mimetypes(<<"application/hyperstudio">>) -> [<<"stk">>];
mimetypes(<<"application/inkml+xml">>) -> [<<"ink">>,<<"inkml">>];
mimetypes(<<"application/ipfix">>) -> [<<"ipfix">>];
mimetypes(<<"application/java-archive">>) -> [<<"jar">>];
mimetypes(<<"application/java-serialized-object">>) -> [<<"ser">>];
mimetypes(<<"application/java-vm">>) -> [<<"class">>];
mimetypes(<<"application/json">>) -> [<<"json">>];
mimetypes(<<"application/jsonml+json">>) -> [<<"jsonml">>];
mimetypes(<<"application/lost+xml">>) -> [<<"lostxml">>];
mimetypes(<<"application/mac-binhex40">>) -> [<<"hqx">>];
mimetypes(<<"application/mac-compactpro">>) -> [<<"cpt">>];
mimetypes(<<"application/mads+xml">>) -> [<<"mads">>];
mimetypes(<<"application/marc">>) -> [<<"mrc">>];
mimetypes(<<"application/marcxml+xml">>) -> [<<"mrcx">>];
mimetypes(<<"application/mathematica">>) -> [<<"ma">>,<<"nb">>,<<"mb">>];
mimetypes(<<"application/mathml+xml">>) -> [<<"mathml">>];
mimetypes(<<"application/mbox">>) -> [<<"mbox">>];
mimetypes(<<"application/mediaservercontrol+xml">>) -> [<<"mscml">>];
mimetypes(<<"application/metalink4+xml">>) -> [<<"meta4">>];
mimetypes(<<"application/metalink+xml">>) -> [<<"metalink">>];
mimetypes(<<"application/mets+xml">>) -> [<<"mets">>];
mimetypes(<<"application/mods+xml">>) -> [<<"mods">>];
mimetypes(<<"application/mp21">>) -> [<<"m21">>,<<"mp21">>];
mimetypes(<<"application/mp4">>) -> [<<"mp4s">>];
mimetypes(<<"application/msword">>) -> [<<"doc">>,<<"dot">>];
mimetypes(<<"application/mxf">>) -> [<<"mxf">>];
mimetypes(<<"application/octet-stream">>) -> [<<"bin">>,<<"dms">>,<<"lrf">>,<<"mar">>,<<"so">>,<<"dist">>,<<"distz">>,<<"pkg">>,<<"bpk">>,<<"dump">>,<<"elc">>,<<"deploy">>];
mimetypes(<<"application/oda">>) -> [<<"oda">>];
mimetypes(<<"application/oebps-package+xml">>) -> [<<"opf">>];
mimetypes(<<"application/ogg">>) -> [<<"ogx">>];
mimetypes(<<"application/omdoc+xml">>) -> [<<"omdoc">>];
mimetypes(<<"application/onenote">>) -> [<<"onetoc">>,<<"onetoc2">>,<<"onetmp">>,<<"onepkg">>];
mimetypes(<<"application/oxps">>) -> [<<"oxps">>];
mimetypes(<<"application/patch-ops-error+xml">>) -> [<<"xer">>];
mimetypes(<<"application/pdf">>) -> [<<"pdf">>];
mimetypes(<<"application/pgp-encrypted">>) -> [<<"pgp">>];
mimetypes(<<"application/pgp-signature">>) -> [<<"asc">>,<<"sig">>];
mimetypes(<<"application/pics-rules">>) -> [<<"prf">>];
mimetypes(<<"application/pkcs10">>) -> [<<"p10">>];
mimetypes(<<"application/pkcs7-mime">>) -> [<<"p7m">>,<<"p7c">>];
mimetypes(<<"application/pkcs7-signature">>) -> [<<"p7s">>];
mimetypes(<<"application/pkcs8">>) -> [<<"p8">>];
mimetypes(<<"application/pkix-attr-cert">>) -> [<<"ac">>];
mimetypes(<<"application/pkix-cert">>) -> [<<"cer">>];
mimetypes(<<"application/pkixcmp">>) -> [<<"pki">>];
mimetypes(<<"application/pkix-crl">>) -> [<<"crl">>];
mimetypes(<<"application/pkix-pkipath">>) -> [<<"pkipath">>];
mimetypes(<<"application/pls+xml">>) -> [<<"pls">>];
mimetypes(<<"application/postscript">>) -> [<<"ai">>,<<"eps">>,<<"ps">>];
mimetypes(<<"application/prs.cww">>) -> [<<"cww">>];
mimetypes(<<"application/pskc+xml">>) -> [<<"pskcxml">>];
mimetypes(<<"application/rdf+xml">>) -> [<<"rdf">>];
mimetypes(<<"application/reginfo+xml">>) -> [<<"rif">>];
mimetypes(<<"application/relax-ng-compact-syntax">>) -> [<<"rnc">>];
mimetypes(<<"application/resource-lists-diff+xml">>) -> [<<"rld">>];
mimetypes(<<"application/resource-lists+xml">>) -> [<<"rl">>];
mimetypes(<<"application/rls-services+xml">>) -> [<<"rs">>];
mimetypes(<<"application/rpki-ghostbusters">>) -> [<<"gbr">>];
mimetypes(<<"application/rpki-manifest">>) -> [<<"mft">>];
mimetypes(<<"application/rpki-roa">>) -> [<<"roa">>];
mimetypes(<<"application/rsd+xml">>) -> [<<"rsd">>];
mimetypes(<<"application/rss+xml">>) -> [<<"rss">>];
mimetypes(<<"application/rtf">>) -> [<<"rtf">>];
mimetypes(<<"application/sbml+xml">>) -> [<<"sbml">>];
mimetypes(<<"application/scvp-cv-request">>) -> [<<"scq">>];
mimetypes(<<"application/scvp-cv-response">>) -> [<<"scs">>];
mimetypes(<<"application/scvp-vp-request">>) -> [<<"spq">>];
mimetypes(<<"application/scvp-vp-response">>) -> [<<"spp">>];
mimetypes(<<"application/sdp">>) -> [<<"sdp">>];
mimetypes(<<"application/set-payment-initiation">>) -> [<<"setpay">>];
mimetypes(<<"application/set-registration-initiation">>) -> [<<"setreg">>];
mimetypes(<<"application/shf+xml">>) -> [<<"shf">>];
mimetypes(<<"application/smil+xml">>) -> [<<"smi">>,<<"smil">>];
mimetypes(<<"application/sparql-query">>) -> [<<"rq">>];
mimetypes(<<"application/sparql-results+xml">>) -> [<<"srx">>];
mimetypes(<<"application/srgs">>) -> [<<"gram">>];
mimetypes(<<"application/srgs+xml">>) -> [<<"grxml">>];
mimetypes(<<"application/sru+xml">>) -> [<<"sru">>];
mimetypes(<<"application/ssdl+xml">>) -> [<<"ssdl">>];
mimetypes(<<"application/ssml+xml">>) -> [<<"ssml">>];
mimetypes(<<"application/tei+xml">>) -> [<<"tei">>,<<"teicorpus">>];
mimetypes(<<"application/thraud+xml">>) -> [<<"tfi">>];
mimetypes(<<"application/timestamped-data">>) -> [<<"tsd">>];
mimetypes(<<"application/vnd.3gpp2.tcap">>) -> [<<"tcap">>];
mimetypes(<<"application/vnd.3gpp.pic-bw-large">>) -> [<<"plb">>];
mimetypes(<<"application/vnd.3gpp.pic-bw-small">>) -> [<<"psb">>];
mimetypes(<<"application/vnd.3gpp.pic-bw-var">>) -> [<<"pvb">>];
mimetypes(<<"application/vnd.3m.post-it-notes">>) -> [<<"pwn">>];
mimetypes(<<"application/vnd.accpac.simply.aso">>) -> [<<"aso">>];
mimetypes(<<"application/vnd.accpac.simply.imp">>) -> [<<"imp">>];
mimetypes(<<"application/vnd.acucobol">>) -> [<<"acu">>];
mimetypes(<<"application/vnd.acucorp">>) -> [<<"atc">>,<<"acutc">>];
mimetypes(<<"application/vnd.adobe.air-application-installer-package+zip">>) -> [<<"air">>];
mimetypes(<<"application/vnd.adobe.formscentral.fcdt">>) -> [<<"fcdt">>];
mimetypes(<<"application/vnd.adobe.fxp">>) -> [<<"fxp">>,<<"fxpl">>];
mimetypes(<<"application/vnd.adobe.xdp+xml">>) -> [<<"xdp">>];
mimetypes(<<"application/vnd.adobe.xfdf">>) -> [<<"xfdf">>];
mimetypes(<<"application/vnd.ahead.space">>) -> [<<"ahead">>];
mimetypes(<<"application/vnd.airzip.filesecure.azf">>) -> [<<"azf">>];
mimetypes(<<"application/vnd.airzip.filesecure.azs">>) -> [<<"azs">>];
mimetypes(<<"application/vnd.amazon.ebook">>) -> [<<"azw">>];
mimetypes(<<"application/vnd.americandynamics.acc">>) -> [<<"acc">>];
mimetypes(<<"application/vnd.amiga.ami">>) -> [<<"ami">>];
mimetypes(<<"application/vnd.android.package-archive">>) -> [<<"apk">>];
mimetypes(<<"application/vnd.anser-web-certificate-issue-initiation">>) -> [<<"cii">>];
mimetypes(<<"application/vnd.anser-web-funds-transfer-initiation">>) -> [<<"fti">>];
mimetypes(<<"application/vnd.antix.game-component">>) -> [<<"atx">>];
mimetypes(<<"application/vnd.apple.installer+xml">>) -> [<<"mpkg">>];
mimetypes(<<"application/vnd.apple.mpegurl">>) -> [<<"m3u8">>];
mimetypes(<<"application/vnd.aristanetworks.swi">>) -> [<<"swi">>];
mimetypes(<<"application/vnd.astraea-software.iota">>) -> [<<"iota">>];
mimetypes(<<"application/vnd.audiograph">>) -> [<<"aep">>];
mimetypes(<<"application/vnd.blueice.multipass">>) -> [<<"mpm">>];
mimetypes(<<"application/vnd.bmi">>) -> [<<"bmi">>];
mimetypes(<<"application/vnd.businessobjects">>) -> [<<"rep">>];
mimetypes(<<"application/vnd.chemdraw+xml">>) -> [<<"cdxml">>];
mimetypes(<<"application/vnd.chipnuts.karaoke-mmd">>) -> [<<"mmd">>];
mimetypes(<<"application/vnd.cinderella">>) -> [<<"cdy">>];
mimetypes(<<"application/vnd.claymore">>) -> [<<"cla">>];
mimetypes(<<"application/vnd.cloanto.rp9">>) -> [<<"rp9">>];
mimetypes(<<"application/vnd.clonk.c4group">>) -> [<<"c4g">>,<<"c4d">>,<<"c4f">>,<<"c4p">>,<<"c4u">>];
mimetypes(<<"application/vnd.cluetrust.cartomobile-config">>) -> [<<"c11amc">>];
mimetypes(<<"application/vnd.cluetrust.cartomobile-config-pkg">>) -> [<<"c11amz">>];
mimetypes(<<"application/vnd.commonspace">>) -> [<<"csp">>];
mimetypes(<<"application/vnd.contact.cmsg">>) -> [<<"cdbcmsg">>];
mimetypes(<<"application/vnd.cosmocaller">>) -> [<<"cmc">>];
mimetypes(<<"application/vnd.crick.clicker">>) -> [<<"clkx">>];
mimetypes(<<"application/vnd.crick.clicker.keyboard">>) -> [<<"clkk">>];
mimetypes(<<"application/vnd.crick.clicker.palette">>) -> [<<"clkp">>];
mimetypes(<<"application/vnd.crick.clicker.template">>) -> [<<"clkt">>];
mimetypes(<<"application/vnd.crick.clicker.wordbank">>) -> [<<"clkw">>];
mimetypes(<<"application/vnd.criticaltools.wbs+xml">>) -> [<<"wbs">>];
mimetypes(<<"application/vnd.ctc-posml">>) -> [<<"pml">>];
mimetypes(<<"application/vnd.cups-ppd">>) -> [<<"ppd">>];
mimetypes(<<"application/vnd.curl.car">>) -> [<<"car">>];
mimetypes(<<"application/vnd.curl.pcurl">>) -> [<<"pcurl">>];
mimetypes(<<"application/vnd.dart">>) -> [<<"dart">>];
mimetypes(<<"application/vnd.data-vision.rdz">>) -> [<<"rdz">>];
mimetypes(<<"application/vnd.dece.data">>) -> [<<"uvf">>,<<"uvvf">>,<<"uvd">>,<<"uvvd">>];
mimetypes(<<"application/vnd.dece.ttml+xml">>) -> [<<"uvt">>,<<"uvvt">>];
mimetypes(<<"application/vnd.dece.unspecified">>) -> [<<"uvx">>,<<"uvvx">>];
mimetypes(<<"application/vnd.dece.zip">>) -> [<<"uvz">>,<<"uvvz">>];
mimetypes(<<"application/vnd.denovo.fcselayout-link">>) -> [<<"fe_launch">>];
mimetypes(<<"application/vnd.dna">>) -> [<<"dna">>];
mimetypes(<<"application/vnd.dolby.mlp">>) -> [<<"mlp">>];
mimetypes(<<"application/vnd.dpgraph">>) -> [<<"dpg">>];
mimetypes(<<"application/vnd.dreamfactory">>) -> [<<"dfac">>];
mimetypes(<<"application/vnd.ds-keypoint">>) -> [<<"kpxx">>];
mimetypes(<<"application/vnd.dvb.ait">>) -> [<<"ait">>];
mimetypes(<<"application/vnd.dvb.service">>) -> [<<"svc">>];
mimetypes(<<"application/vnd.dynageo">>) -> [<<"geo">>];
mimetypes(<<"application/vnd.ecowin.chart">>) -> [<<"mag">>];
mimetypes(<<"application/vnd.enliven">>) -> [<<"nml">>];
mimetypes(<<"application/vnd.epson.esf">>) -> [<<"esf">>];
mimetypes(<<"application/vnd.epson.msf">>) -> [<<"msf">>];
mimetypes(<<"application/vnd.epson.quickanime">>) -> [<<"qam">>];
mimetypes(<<"application/vnd.epson.salt">>) -> [<<"slt">>];
mimetypes(<<"application/vnd.epson.ssf">>) -> [<<"ssf">>];
mimetypes(<<"application/vnd.eszigno3+xml">>) -> [<<"es3">>,<<"et3">>];
mimetypes(<<"application/vnd.ezpix-album">>) -> [<<"ez2">>];
mimetypes(<<"application/vnd.ezpix-package">>) -> [<<"ez3">>];
mimetypes(<<"application/vnd.fdf">>) -> [<<"fdf">>];
mimetypes(<<"application/vnd.fdsn.mseed">>) -> [<<"mseed">>];
mimetypes(<<"application/vnd.fdsn.seed">>) -> [<<"seed">>,<<"dataless">>];
mimetypes(<<"application/vnd.flographit">>) -> [<<"gph">>];
mimetypes(<<"application/vnd.fluxtime.clip">>) -> [<<"ftc">>];
mimetypes(<<"application/vnd.framemaker">>) -> [<<"fm">>,<<"frame">>,<<"maker">>,<<"book">>];
mimetypes(<<"application/vnd.frogans.fnc">>) -> [<<"fnc">>];
mimetypes(<<"application/vnd.frogans.ltf">>) -> [<<"ltf">>];
mimetypes(<<"application/vnd.fsc.weblaunch">>) -> [<<"fsc">>];
mimetypes(<<"application/vnd.fujitsu.oasys2">>) -> [<<"oa2">>];
mimetypes(<<"application/vnd.fujitsu.oasys3">>) -> [<<"oa3">>];
mimetypes(<<"application/vnd.fujitsu.oasysgp">>) -> [<<"fg5">>];
mimetypes(<<"application/vnd.fujitsu.oasys">>) -> [<<"oas">>];
mimetypes(<<"application/vnd.fujitsu.oasysprs">>) -> [<<"bh2">>];
mimetypes(<<"application/vnd.fujixerox.ddd">>) -> [<<"ddd">>];
mimetypes(<<"application/vnd.fujixerox.docuworks.binder">>) -> [<<"xbd">>];
mimetypes(<<"application/vnd.fujixerox.docuworks">>) -> [<<"xdw">>];
mimetypes(<<"application/vnd.fuzzysheet">>) -> [<<"fzs">>];
mimetypes(<<"application/vnd.genomatix.tuxedo">>) -> [<<"txd">>];
mimetypes(<<"application/vnd.geogebra.file">>) -> [<<"ggb">>];
mimetypes(<<"application/vnd.geogebra.slides">>) -> [<<"ggs">>];
mimetypes(<<"application/vnd.geogebra.tool">>) -> [<<"ggt">>];
mimetypes(<<"application/vnd.geometry-explorer">>) -> [<<"gex">>,<<"gre">>];
mimetypes(<<"application/vnd.geonext">>) -> [<<"gxt">>];
mimetypes(<<"application/vnd.geoplan">>) -> [<<"g2w">>];
mimetypes(<<"application/vnd.geospace">>) -> [<<"g3w">>];
mimetypes(<<"application/vnd.gmx">>) -> [<<"gmx">>];
mimetypes(<<"application/vnd.google-earth.kml+xml">>) -> [<<"kml">>];
mimetypes(<<"application/vnd.google-earth.kmz">>) -> [<<"kmz">>];
mimetypes(<<"application/vnd.grafeq">>) -> [<<"gqf">>,<<"gqs">>];
mimetypes(<<"application/vnd.groove-account">>) -> [<<"gac">>];
mimetypes(<<"application/vnd.groove-help">>) -> [<<"ghf">>];
mimetypes(<<"application/vnd.groove-identity-message">>) -> [<<"gim">>];
mimetypes(<<"application/vnd.groove-injector">>) -> [<<"grv">>];
mimetypes(<<"application/vnd.groove-tool-message">>) -> [<<"gtm">>];
mimetypes(<<"application/vnd.groove-tool-template">>) -> [<<"tpl">>];
mimetypes(<<"application/vnd.groove-vcard">>) -> [<<"vcg">>];
mimetypes(<<"application/vnd.hal+xml">>) -> [<<"hal">>];
mimetypes(<<"application/vnd.handheld-entertainment+xml">>) -> [<<"zmm">>];
mimetypes(<<"application/vnd.hbci">>) -> [<<"hbci">>];
mimetypes(<<"application/vnd.hhe.lesson-player">>) -> [<<"les">>];
mimetypes(<<"application/vnd.hp-hpgl">>) -> [<<"hpgl">>];
mimetypes(<<"application/vnd.hp-hpid">>) -> [<<"hpid">>];
mimetypes(<<"application/vnd.hp-hps">>) -> [<<"hps">>];
mimetypes(<<"application/vnd.hp-jlyt">>) -> [<<"jlt">>];
mimetypes(<<"application/vnd.hp-pcl">>) -> [<<"pcl">>];
mimetypes(<<"application/vnd.hp-pclxl">>) -> [<<"pclxl">>];
mimetypes(<<"application/vnd.hydrostatix.sof-data">>) -> [<<"sfd-hdstx">>];
mimetypes(<<"application/vnd.ibm.minipay">>) -> [<<"mpy">>];
mimetypes(<<"application/vnd.ibm.modcap">>) -> [<<"afp">>,<<"listafp">>,<<"list3820">>];
mimetypes(<<"application/vnd.ibm.rights-management">>) -> [<<"irm">>];
mimetypes(<<"application/vnd.ibm.secure-container">>) -> [<<"sc">>];
mimetypes(<<"application/vnd.iccprofile">>) -> [<<"icc">>,<<"icm">>];
mimetypes(<<"application/vnd.igloader">>) -> [<<"igl">>];
mimetypes(<<"application/vnd.immervision-ivp">>) -> [<<"ivp">>];
mimetypes(<<"application/vnd.immervision-ivu">>) -> [<<"ivu">>];
mimetypes(<<"application/vnd.insors.igm">>) -> [<<"igm">>];
mimetypes(<<"application/vnd.intercon.formnet">>) -> [<<"xpw">>,<<"xpx">>];
mimetypes(<<"application/vnd.intergeo">>) -> [<<"i2g">>];
mimetypes(<<"application/vnd.intu.qbo">>) -> [<<"qbo">>];
mimetypes(<<"application/vnd.intu.qfx">>) -> [<<"qfx">>];
mimetypes(<<"application/vnd.ipunplugged.rcprofile">>) -> [<<"rcprofile">>];
mimetypes(<<"application/vnd.irepository.package+xml">>) -> [<<"irp">>];
mimetypes(<<"application/vnd.isac.fcs">>) -> [<<"fcs">>];
mimetypes(<<"application/vnd.is-xpr">>) -> [<<"xpr">>];
mimetypes(<<"application/vnd.jam">>) -> [<<"jam">>];
mimetypes(<<"application/vnd.jcp.javame.midlet-rms">>) -> [<<"rms">>];
mimetypes(<<"application/vnd.jisp">>) -> [<<"jisp">>];
mimetypes(<<"application/vnd.joost.joda-archive">>) -> [<<"joda">>];
mimetypes(<<"application/vnd.kahootz">>) -> [<<"ktz">>,<<"ktr">>];
mimetypes(<<"application/vnd.kde.karbon">>) -> [<<"karbon">>];
mimetypes(<<"application/vnd.kde.kchart">>) -> [<<"chrt">>];
mimetypes(<<"application/vnd.kde.kformula">>) -> [<<"kfo">>];
mimetypes(<<"application/vnd.kde.kivio">>) -> [<<"flw">>];
mimetypes(<<"application/vnd.kde.kontour">>) -> [<<"kon">>];
mimetypes(<<"application/vnd.kde.kpresenter">>) -> [<<"kpr">>,<<"kpt">>];
mimetypes(<<"application/vnd.kde.kspread">>) -> [<<"ksp">>];
mimetypes(<<"application/vnd.kde.kword">>) -> [<<"kwd">>,<<"kwt">>];
mimetypes(<<"application/vnd.kenameaapp">>) -> [<<"htke">>];
mimetypes(<<"application/vnd.kidspiration">>) -> [<<"kia">>];
mimetypes(<<"application/vnd.kinar">>) -> [<<"kne">>,<<"knp">>];
mimetypes(<<"application/vnd.koan">>) -> [<<"skp">>,<<"skd">>,<<"skt">>,<<"skm">>];
mimetypes(<<"application/vnd.kodak-descriptor">>) -> [<<"sse">>];
mimetypes(<<"application/vnd.las.las+xml">>) -> [<<"lasxml">>];
mimetypes(<<"application/vnd.llamagraphics.life-balance.desktop">>) -> [<<"lbd">>];
mimetypes(<<"application/vnd.llamagraphics.life-balance.exchange+xml">>) -> [<<"lbe">>];
mimetypes(<<"application/vnd.lotus-1-2-3">>) -> [<<"123">>];
mimetypes(<<"application/vnd.lotus-approach">>) -> [<<"apr">>];
mimetypes(<<"application/vnd.lotus-freelance">>) -> [<<"pre">>];
mimetypes(<<"application/vnd.lotus-notes">>) -> [<<"nsf">>];
mimetypes(<<"application/vnd.lotus-organizer">>) -> [<<"org">>];
mimetypes(<<"application/vnd.lotus-screencam">>) -> [<<"scm">>];
mimetypes(<<"application/vnd.lotus-wordpro">>) -> [<<"lwp">>];
mimetypes(<<"application/vnd.macports.portpkg">>) -> [<<"portpkg">>];
mimetypes(<<"application/vnd.mcd">>) -> [<<"mcd">>];
mimetypes(<<"application/vnd.medcalcdata">>) -> [<<"mc1">>];
mimetypes(<<"application/vnd.mediastation.cdkey">>) -> [<<"cdkey">>];
mimetypes(<<"application/vnd.mfer">>) -> [<<"mwf">>];
mimetypes(<<"application/vnd.mfmp">>) -> [<<"mfm">>];
mimetypes(<<"application/vnd.micrografx.flo">>) -> [<<"flo">>];
mimetypes(<<"application/vnd.micrografx.igx">>) -> [<<"igx">>];
mimetypes(<<"application/vnd.mif">>) -> [<<"mif">>];
mimetypes(<<"application/vnd.mobius.daf">>) -> [<<"daf">>];
mimetypes(<<"application/vnd.mobius.dis">>) -> [<<"dis">>];
mimetypes(<<"application/vnd.mobius.mbk">>) -> [<<"mbk">>];
mimetypes(<<"application/vnd.mobius.mqy">>) -> [<<"mqy">>];
mimetypes(<<"application/vnd.mobius.msl">>) -> [<<"msl">>];
mimetypes(<<"application/vnd.mobius.plc">>) -> [<<"plc">>];
mimetypes(<<"application/vnd.mobius.txf">>) -> [<<"txf">>];
mimetypes(<<"application/vnd.mophun.application">>) -> [<<"mpn">>];
mimetypes(<<"application/vnd.mophun.certificate">>) -> [<<"mpc">>];
mimetypes(<<"application/vnd.mozilla.xul+xml">>) -> [<<"xul">>];
mimetypes(<<"application/vnd.ms-artgalry">>) -> [<<"cil">>];
mimetypes(<<"application/vnd.ms-cab-compressed">>) -> [<<"cab">>];
mimetypes(<<"application/vnd.mseq">>) -> [<<"mseq">>];
mimetypes(<<"application/vnd.ms-excel.addin.macroenabled.12">>) -> [<<"xlam">>];
mimetypes(<<"application/vnd.ms-excel.sheet.binary.macroenabled.12">>) -> [<<"xlsb">>];
mimetypes(<<"application/vnd.ms-excel.sheet.macroenabled.12">>) -> [<<"xlsm">>];
mimetypes(<<"application/vnd.ms-excel.template.macroenabled.12">>) -> [<<"xltm">>];
mimetypes(<<"application/vnd.ms-excel">>) -> [<<"xls">>,<<"xlm">>,<<"xla">>,<<"xlc">>,<<"xlt">>,<<"xlw">>];
mimetypes(<<"application/vnd.ms-fontobject">>) -> [<<"eot">>];
mimetypes(<<"application/vnd.ms-htmlhelp">>) -> [<<"chm">>];
mimetypes(<<"application/vnd.ms-ims">>) -> [<<"ims">>];
mimetypes(<<"application/vnd.ms-lrm">>) -> [<<"lrm">>];
mimetypes(<<"application/vnd.ms-officetheme">>) -> [<<"thmx">>];
mimetypes(<<"application/vnd.ms-pki.seccat">>) -> [<<"cat">>];
mimetypes(<<"application/vnd.ms-pki.stl">>) -> [<<"stl">>];
mimetypes(<<"application/vnd.ms-powerpoint.addin.macroenabled.12">>) -> [<<"ppam">>];
mimetypes(<<"application/vnd.ms-powerpoint">>) -> [<<"ppt">>,<<"pps">>,<<"pot">>];
mimetypes(<<"application/vnd.ms-powerpoint.presentation.macroenabled.12">>) -> [<<"pptm">>];
mimetypes(<<"application/vnd.ms-powerpoint.slide.macroenabled.12">>) -> [<<"sldm">>];
mimetypes(<<"application/vnd.ms-powerpoint.slideshow.macroenabled.12">>) -> [<<"ppsm">>];
mimetypes(<<"application/vnd.ms-powerpoint.template.macroenabled.12">>) -> [<<"potm">>];
mimetypes(<<"application/vnd.ms-project">>) -> [<<"mpp">>,<<"mpt">>];
mimetypes(<<"application/vnd.ms-word.document.macroenabled.12">>) -> [<<"docm">>];
mimetypes(<<"application/vnd.ms-word.template.macroenabled.12">>) -> [<<"dotm">>];
mimetypes(<<"application/vnd.ms-works">>) -> [<<"wps">>,<<"wks">>,<<"wcm">>,<<"wdb">>];
mimetypes(<<"application/vnd.ms-wpl">>) -> [<<"wpl">>];
mimetypes(<<"application/vnd.ms-xpsdocument">>) -> [<<"xps">>];
mimetypes(<<"application/vnd.musician">>) -> [<<"mus">>];
mimetypes(<<"application/vnd.muvee.style">>) -> [<<"msty">>];
mimetypes(<<"application/vnd.mynfc">>) -> [<<"taglet">>];
mimetypes(<<"application/vnd.neurolanguage.nlu">>) -> [<<"nlu">>];
mimetypes(<<"application/vnd.nitf">>) -> [<<"ntf">>,<<"nitf">>];
mimetypes(<<"application/vnd.noblenet-directory">>) -> [<<"nnd">>];
mimetypes(<<"application/vnd.noblenet-sealer">>) -> [<<"nns">>];
mimetypes(<<"application/vnd.noblenet-web">>) -> [<<"nnw">>];
mimetypes(<<"application/vnd.nokia.n-gage.data">>) -> [<<"ngdat">>];
mimetypes(<<"application/vnd.nokia.n-gage.symbian.install">>) -> [<<"n-gage">>];
mimetypes(<<"application/vnd.nokia.radio-preset">>) -> [<<"rpst">>];
mimetypes(<<"application/vnd.nokia.radio-presets">>) -> [<<"rpss">>];
mimetypes(<<"application/vnd.novadigm.edm">>) -> [<<"edm">>];
mimetypes(<<"application/vnd.novadigm.edx">>) -> [<<"edx">>];
mimetypes(<<"application/vnd.novadigm.ext">>) -> [<<"ext">>];
mimetypes(<<"application/vnd.oasis.opendocument.chart">>) -> [<<"odc">>];
mimetypes(<<"application/vnd.oasis.opendocument.chart-template">>) -> [<<"otc">>];
mimetypes(<<"application/vnd.oasis.opendocument.database">>) -> [<<"odb">>];
mimetypes(<<"application/vnd.oasis.opendocument.formula">>) -> [<<"odf">>];
mimetypes(<<"application/vnd.oasis.opendocument.formula-template">>) -> [<<"odft">>];
mimetypes(<<"application/vnd.oasis.opendocument.graphics">>) -> [<<"odg">>];
mimetypes(<<"application/vnd.oasis.opendocument.graphics-template">>) -> [<<"otg">>];
mimetypes(<<"application/vnd.oasis.opendocument.image">>) -> [<<"odi">>];
mimetypes(<<"application/vnd.oasis.opendocument.image-template">>) -> [<<"oti">>];
mimetypes(<<"application/vnd.oasis.opendocument.presentation">>) -> [<<"odp">>];
mimetypes(<<"application/vnd.oasis.opendocument.presentation-template">>) -> [<<"otp">>];
mimetypes(<<"application/vnd.oasis.opendocument.spreadsheet">>) -> [<<"ods">>];
mimetypes(<<"application/vnd.oasis.opendocument.spreadsheet-template">>) -> [<<"ots">>];
mimetypes(<<"application/vnd.oasis.opendocument.text-master">>) -> [<<"odm">>];
mimetypes(<<"application/vnd.oasis.opendocument.text">>) -> [<<"odt">>];
mimetypes(<<"application/vnd.oasis.opendocument.text-template">>) -> [<<"ott">>];
mimetypes(<<"application/vnd.oasis.opendocument.text-web">>) -> [<<"oth">>];
mimetypes(<<"application/vnd.olpc-sugar">>) -> [<<"xo">>];
mimetypes(<<"application/vnd.oma.dd2+xml">>) -> [<<"dd2">>];
mimetypes(<<"application/vnd.openofficeorg.extension">>) -> [<<"oxt">>];
mimetypes(<<"application/vnd.openxmlformats-officedocument.presentationml.presentation">>) -> [<<"pptx">>];
mimetypes(<<"application/vnd.openxmlformats-officedocument.presentationml.slideshow">>) -> [<<"ppsx">>];
mimetypes(<<"application/vnd.openxmlformats-officedocument.presentationml.slide">>) -> [<<"sldx">>];
mimetypes(<<"application/vnd.openxmlformats-officedocument.presentationml.template">>) -> [<<"potx">>];
mimetypes(<<"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet">>) -> [<<"xlsx">>];
mimetypes(<<"application/vnd.openxmlformats-officedocument.spreadsheetml.template">>) -> [<<"xltx">>];
mimetypes(<<"application/vnd.openxmlformats-officedocument.wordprocessingml.document">>) -> [<<"docx">>];
mimetypes(<<"application/vnd.openxmlformats-officedocument.wordprocessingml.template">>) -> [<<"dotx">>];
mimetypes(<<"application/vnd.osgeo.mapguide.package">>) -> [<<"mgp">>];
mimetypes(<<"application/vnd.osgi.dp">>) -> [<<"dp">>];
mimetypes(<<"application/vnd.osgi.subsystem">>) -> [<<"esa">>];
mimetypes(<<"application/vnd.palm">>) -> [<<"pdb">>,<<"pqa">>,<<"oprc">>];
mimetypes(<<"application/vnd.pawaafile">>) -> [<<"paw">>];
mimetypes(<<"application/vnd.pg.format">>) -> [<<"str">>];
mimetypes(<<"application/vnd.pg.osasli">>) -> [<<"ei6">>];
mimetypes(<<"application/vnd.picsel">>) -> [<<"efif">>];
mimetypes(<<"application/vnd.pmi.widget">>) -> [<<"wg">>];
mimetypes(<<"application/vnd.pocketlearn">>) -> [<<"plf">>];
mimetypes(<<"application/vnd.powerbuilder6">>) -> [<<"pbd">>];
mimetypes(<<"application/vnd.previewsystems.box">>) -> [<<"box">>];
mimetypes(<<"application/vnd.proteus.magazine">>) -> [<<"mgz">>];
mimetypes(<<"application/vnd.publishare-delta-tree">>) -> [<<"qps">>];
mimetypes(<<"application/vnd.pvi.ptid1">>) -> [<<"ptid">>];
mimetypes(<<"application/vnd.quark.quarkxpress">>) -> [<<"qxd">>,<<"qxt">>,<<"qwd">>,<<"qwt">>,<<"qxl">>,<<"qxb">>];
mimetypes(<<"application/vnd.realvnc.bed">>) -> [<<"bed">>];
mimetypes(<<"application/vnd.recordare.musicxml">>) -> [<<"mxl">>];
mimetypes(<<"application/vnd.recordare.musicxml+xml">>) -> [<<"musicxml">>];
mimetypes(<<"application/vnd.rig.cryptonote">>) -> [<<"cryptonote">>];
mimetypes(<<"application/vnd.rim.cod">>) -> [<<"cod">>];
mimetypes(<<"application/vnd.rn-realmedia">>) -> [<<"rm">>];
mimetypes(<<"application/vnd.rn-realmedia-vbr">>) -> [<<"rmvb">>];
mimetypes(<<"application/vnd.route66.link66+xml">>) -> [<<"link66">>];
mimetypes(<<"application/vnd.sailingtracker.track">>) -> [<<"st">>];
mimetypes(<<"application/vnd.seemail">>) -> [<<"see">>];
mimetypes(<<"application/vnd.sema">>) -> [<<"sema">>];
mimetypes(<<"application/vnd.semd">>) -> [<<"semd">>];
mimetypes(<<"application/vnd.semf">>) -> [<<"semf">>];
mimetypes(<<"application/vnd.shana.informed.formdata">>) -> [<<"ifm">>];
mimetypes(<<"application/vnd.shana.informed.formtemplate">>) -> [<<"itp">>];
mimetypes(<<"application/vnd.shana.informed.interchange">>) -> [<<"iif">>];
mimetypes(<<"application/vnd.shana.informed.package">>) -> [<<"ipk">>];
mimetypes(<<"application/vnd.simtech-mindmapper">>) -> [<<"twd">>,<<"twds">>];
mimetypes(<<"application/vnd.smaf">>) -> [<<"mmf">>];
mimetypes(<<"application/vnd.smart.teacher">>) -> [<<"teacher">>];
mimetypes(<<"application/vnd.solent.sdkm+xml">>) -> [<<"sdkm">>,<<"sdkd">>];
mimetypes(<<"application/vnd.spotfire.dxp">>) -> [<<"dxp">>];
mimetypes(<<"application/vnd.spotfire.sfs">>) -> [<<"sfs">>];
mimetypes(<<"application/vnd.stardivision.calc">>) -> [<<"sdc">>];
mimetypes(<<"application/vnd.stardivision.draw">>) -> [<<"sda">>];
mimetypes(<<"application/vnd.stardivision.impress">>) -> [<<"sdd">>];
mimetypes(<<"application/vnd.stardivision.math">>) -> [<<"smf">>];
mimetypes(<<"application/vnd.stardivision.writer-global">>) -> [<<"sgl">>];
mimetypes(<<"application/vnd.stardivision.writer">>) -> [<<"sdw">>,<<"vor">>];
mimetypes(<<"application/vnd.stepmania.package">>) -> [<<"smzip">>];
mimetypes(<<"application/vnd.stepmania.stepchart">>) -> [<<"sm">>];
mimetypes(<<"application/vnd.sun.xml.calc">>) -> [<<"sxc">>];
mimetypes(<<"application/vnd.sun.xml.calc.template">>) -> [<<"stc">>];
mimetypes(<<"application/vnd.sun.xml.draw">>) -> [<<"sxd">>];
mimetypes(<<"application/vnd.sun.xml.draw.template">>) -> [<<"std">>];
mimetypes(<<"application/vnd.sun.xml.impress">>) -> [<<"sxi">>];
mimetypes(<<"application/vnd.sun.xml.impress.template">>) -> [<<"sti">>];
mimetypes(<<"application/vnd.sun.xml.math">>) -> [<<"sxm">>];
mimetypes(<<"application/vnd.sun.xml.writer.global">>) -> [<<"sxg">>];
mimetypes(<<"application/vnd.sun.xml.writer">>) -> [<<"sxw">>];
mimetypes(<<"application/vnd.sun.xml.writer.template">>) -> [<<"stw">>];
mimetypes(<<"application/vnd.sus-calendar">>) -> [<<"sus">>,<<"susp">>];
mimetypes(<<"application/vnd.svd">>) -> [<<"svd">>];
mimetypes(<<"application/vnd.symbian.install">>) -> [<<"sis">>,<<"sisx">>];
mimetypes(<<"application/vnd.syncml.dm+wbxml">>) -> [<<"bdm">>];
mimetypes(<<"application/vnd.syncml.dm+xml">>) -> [<<"xdm">>];
mimetypes(<<"application/vnd.syncml+xml">>) -> [<<"xsm">>];
mimetypes(<<"application/vnd.tao.intent-module-archive">>) -> [<<"tao">>];
mimetypes(<<"application/vnd.tcpdump.pcap">>) -> [<<"pcap">>,<<"cap">>,<<"dmp">>];
mimetypes(<<"application/vnd.tmobile-livetv">>) -> [<<"tmo">>];
mimetypes(<<"application/vnd.trid.tpt">>) -> [<<"tpt">>];
mimetypes(<<"application/vnd.triscape.mxs">>) -> [<<"mxs">>];
mimetypes(<<"application/vnd.trueapp">>) -> [<<"tra">>];
mimetypes(<<"application/vnd.ufdl">>) -> [<<"ufd">>,<<"ufdl">>];
mimetypes(<<"application/vnd.uiq.theme">>) -> [<<"utz">>];
mimetypes(<<"application/vnd.umajin">>) -> [<<"umj">>];
mimetypes(<<"application/vnd.unity">>) -> [<<"unityweb">>];
mimetypes(<<"application/vnd.uoml+xml">>) -> [<<"uoml">>];
mimetypes(<<"application/vnd.vcx">>) -> [<<"vcx">>];
mimetypes(<<"application/vnd.visionary">>) -> [<<"vis">>];
mimetypes(<<"application/vnd.visio">>) -> [<<"vsd">>,<<"vst">>,<<"vss">>,<<"vsw">>];
mimetypes(<<"application/vnd.vsf">>) -> [<<"vsf">>];
mimetypes(<<"application/vnd.wap.wbxml">>) -> [<<"wbxml">>];
mimetypes(<<"application/vnd.wap.wmlc">>) -> [<<"wmlc">>];
mimetypes(<<"application/vnd.wap.wmlscriptc">>) -> [<<"wmlsc">>];
mimetypes(<<"application/vnd.webturbo">>) -> [<<"wtb">>];
mimetypes(<<"application/vnd.wolfram.player">>) -> [<<"nbp">>];
mimetypes(<<"application/vnd.wordperfect">>) -> [<<"wpd">>];
mimetypes(<<"application/vnd.wqd">>) -> [<<"wqd">>];
mimetypes(<<"application/vnd.wt.stf">>) -> [<<"stf">>];
mimetypes(<<"application/vnd.xara">>) -> [<<"xar">>];
mimetypes(<<"application/vnd.xfdl">>) -> [<<"xfdl">>];
mimetypes(<<"application/vnd.yamaha.hv-dic">>) -> [<<"hvd">>];
mimetypes(<<"application/vnd.yamaha.hv-script">>) -> [<<"hvs">>];
mimetypes(<<"application/vnd.yamaha.hv-voice">>) -> [<<"hvp">>];
mimetypes(<<"application/vnd.yamaha.openscoreformat">>) -> [<<"osf">>];
mimetypes(<<"application/vnd.yamaha.openscoreformat.osfpvg+xml">>) -> [<<"osfpvg">>];
mimetypes(<<"application/vnd.yamaha.smaf-audio">>) -> [<<"saf">>];
mimetypes(<<"application/vnd.yamaha.smaf-phrase">>) -> [<<"spf">>];
mimetypes(<<"application/vnd.yellowriver-custom-menu">>) -> [<<"cmp">>];
mimetypes(<<"application/vnd.zul">>) -> [<<"zir">>,<<"zirz">>];
mimetypes(<<"application/vnd.zzazz.deck+xml">>) -> [<<"zaz">>];
mimetypes(<<"application/voicexml+xml">>) -> [<<"vxml">>];
mimetypes(<<"application/wasm">>) -> [<<"wasm">>];
mimetypes(<<"application/widget">>) -> [<<"wgt">>];
mimetypes(<<"application/winhlp">>) -> [<<"hlp">>];
mimetypes(<<"application/wsdl+xml">>) -> [<<"wsdl">>];
mimetypes(<<"application/wspolicy+xml">>) -> [<<"wspolicy">>];
mimetypes(<<"application/x-7z-compressed">>) -> [<<"7z">>];
mimetypes(<<"application/x-abiword">>) -> [<<"abw">>];
mimetypes(<<"application/x-ace-compressed">>) -> [<<"ace">>];
mimetypes(<<"application/xaml+xml">>) -> [<<"xaml">>];
mimetypes(<<"application/x-apple-diskimage">>) -> [<<"dmg">>];
mimetypes(<<"application/x-authorware-bin">>) -> [<<"aab">>,<<"x32">>,<<"u32">>,<<"vox">>];
mimetypes(<<"application/x-authorware-map">>) -> [<<"aam">>];
mimetypes(<<"application/x-authorware-seg">>) -> [<<"aas">>];
mimetypes(<<"application/x-bcpio">>) -> [<<"bcpio">>];
mimetypes(<<"application/x-bittorrent">>) -> [<<"torrent">>];
mimetypes(<<"application/x-blorb">>) -> [<<"blb">>,<<"blorb">>];
mimetypes(<<"application/x-bzip2">>) -> [<<"bz2">>,<<"boz">>];
mimetypes(<<"application/x-bzip">>) -> [<<"bz">>];
mimetypes(<<"application/xcap-diff+xml">>) -> [<<"xdf">>];
mimetypes(<<"application/x-cbr">>) -> [<<"cbr">>,<<"cba">>,<<"cbt">>,<<"cbz">>,<<"cb7">>];
mimetypes(<<"application/x-cdlink">>) -> [<<"vcd">>];
mimetypes(<<"application/x-cfs-compressed">>) -> [<<"cfs">>];
mimetypes(<<"application/x-chat">>) -> [<<"chat">>];
mimetypes(<<"application/x-chess-pgn">>) -> [<<"pgn">>];
mimetypes(<<"application/x-conference">>) -> [<<"nsc">>];
mimetypes(<<"application/x-cpio">>) -> [<<"cpio">>];
mimetypes(<<"application/x-csh">>) -> [<<"csh">>];
mimetypes(<<"application/x-debian-package">>) -> [<<"deb">>,<<"udeb">>];
mimetypes(<<"application/x-dgc-compressed">>) -> [<<"dgc">>];
mimetypes(<<"application/x-director">>) -> [<<"dir">>,<<"dcr">>,<<"dxr">>,<<"cst">>,<<"cct">>,<<"cxt">>,<<"w3d">>,<<"fgd">>,<<"swa">>];
mimetypes(<<"application/x-doom">>) -> [<<"wad">>];
mimetypes(<<"application/x-dtbncx+xml">>) -> [<<"ncx">>];
mimetypes(<<"application/x-dtbook+xml">>) -> [<<"dtb">>];
mimetypes(<<"application/x-dtbresource+xml">>) -> [<<"res">>];
mimetypes(<<"application/x-dvi">>) -> [<<"dvi">>];
mimetypes(<<"application/xenc+xml">>) -> [<<"xenc">>];
mimetypes(<<"application/x-envoy">>) -> [<<"evy">>];
mimetypes(<<"application/x-eva">>) -> [<<"eva">>];
mimetypes(<<"application/x-font-bdf">>) -> [<<"bdf">>];
mimetypes(<<"application/x-font-ghostscript">>) -> [<<"gsf">>];
mimetypes(<<"application/x-font-linux-psf">>) -> [<<"psf">>];
mimetypes(<<"application/x-font-pcf">>) -> [<<"pcf">>];
mimetypes(<<"application/x-font-snf">>) -> [<<"snf">>];
mimetypes(<<"application/x-font-type1">>) -> [<<"pfa">>,<<"pfb">>,<<"pfm">>,<<"afm">>];
mimetypes(<<"application/x-freearc">>) -> [<<"arc">>];
mimetypes(<<"application/x-futuresplash">>) -> [<<"spl">>];
mimetypes(<<"application/x-gca-compressed">>) -> [<<"gca">>];
mimetypes(<<"application/x-glulx">>) -> [<<"ulx">>];
mimetypes(<<"application/x-gnumeric">>) -> [<<"gnumeric">>];
mimetypes(<<"application/x-gramps-xml">>) -> [<<"gramps">>];
mimetypes(<<"application/x-gtar">>) -> [<<"gtar">>];
mimetypes(<<"application/x-hdf">>) -> [<<"hdf">>];
mimetypes(<<"application/xhtml+xml">>) -> [<<"xhtml">>,<<"xht">>];
mimetypes(<<"application/x-install-instructions">>) -> [<<"install">>];
mimetypes(<<"application/x-iso9660-image">>) -> [<<"iso">>];
mimetypes(<<"application/x-java-jnlp-file">>) -> [<<"jnlp">>];
mimetypes(<<"application/x-latex">>) -> [<<"latex">>];
mimetypes(<<"application/x-lzh-compressed">>) -> [<<"lzh">>,<<"lha">>];
mimetypes(<<"application/x-mie">>) -> [<<"mie">>];
mimetypes(<<"application/xml-dtd">>) -> [<<"dtd">>];
mimetypes(<<"application/xml">>) -> [<<"xml">>,<<"xsl">>];
mimetypes(<<"application/x-mobipocket-ebook">>) -> [<<"prc">>,<<"mobi">>];
mimetypes(<<"application/x-msaccess">>) -> [<<"mdb">>];
mimetypes(<<"application/x-ms-application">>) -> [<<"application">>];
mimetypes(<<"application/x-msbinder">>) -> [<<"obd">>];
mimetypes(<<"application/x-mscardfile">>) -> [<<"crd">>];
mimetypes(<<"application/x-msclip">>) -> [<<"clp">>];
mimetypes(<<"application/x-msdownload">>) -> [<<"exe">>,<<"dll">>,<<"com">>,<<"bat">>,<<"msi">>];
mimetypes(<<"application/x-msmediaview">>) -> [<<"mvb">>,<<"m13">>,<<"m14">>];
mimetypes(<<"application/x-msmetafile">>) -> [<<"wmf">>,<<"wmz">>,<<"emf">>,<<"emz">>];
mimetypes(<<"application/x-msmoney">>) -> [<<"mny">>];
mimetypes(<<"application/x-mspublisher">>) -> [<<"pub">>];
mimetypes(<<"application/x-msschedule">>) -> [<<"scd">>];
mimetypes(<<"application/x-ms-shortcut">>) -> [<<"lnk">>];
mimetypes(<<"application/x-msterminal">>) -> [<<"trm">>];
mimetypes(<<"application/x-ms-wmd">>) -> [<<"wmd">>];
mimetypes(<<"application/x-ms-wmz">>) -> [<<"wmz">>];
mimetypes(<<"application/x-mswrite">>) -> [<<"wri">>];
mimetypes(<<"application/x-ms-xbap">>) -> [<<"xbap">>];
mimetypes(<<"application/x-netcdf">>) -> [<<"nc">>,<<"cdf">>];
mimetypes(<<"application/x-nzb">>) -> [<<"nzb">>];
mimetypes(<<"application/xop+xml">>) -> [<<"xop">>];
mimetypes(<<"application/x-pkcs12">>) -> [<<"p12">>,<<"pfx">>];
mimetypes(<<"application/x-pkcs7-certificates">>) -> [<<"p7b">>,<<"spc">>];
mimetypes(<<"application/x-pkcs7-certreqresp">>) -> [<<"p7r">>];
mimetypes(<<"application/xproc+xml">>) -> [<<"xpl">>];
mimetypes(<<"application/x-rar-compressed">>) -> [<<"rar">>];
mimetypes(<<"application/x-research-info-systems">>) -> [<<"ris">>];
mimetypes(<<"application/x-shar">>) -> [<<"shar">>];
mimetypes(<<"application/x-shockwave-flash">>) -> [<<"swf">>];
mimetypes(<<"application/x-sh">>) -> [<<"sh">>];
mimetypes(<<"application/x-silverlight-app">>) -> [<<"xap">>];
mimetypes(<<"application/xslt+xml">>) -> [<<"xslt">>];
mimetypes(<<"application/xspf+xml">>) -> [<<"xspf">>];
mimetypes(<<"application/x-sql">>) -> [<<"sql">>];
mimetypes(<<"application/x-stuffit">>) -> [<<"sit">>];
mimetypes(<<"application/x-stuffitx">>) -> [<<"sitx">>];
mimetypes(<<"application/x-subrip">>) -> [<<"srt">>];
mimetypes(<<"application/x-sv4cpio">>) -> [<<"sv4cpio">>];
mimetypes(<<"application/x-sv4crc">>) -> [<<"sv4crc">>];
mimetypes(<<"application/x-t3vm-image">>) -> [<<"t3">>];
mimetypes(<<"application/x-tads">>) -> [<<"gam">>];
mimetypes(<<"application/x-tar">>) -> [<<"tar">>];
mimetypes(<<"application/x-tcl">>) -> [<<"tcl">>];
mimetypes(<<"application/x-texinfo">>) -> [<<"texinfo">>,<<"texi">>];
mimetypes(<<"application/x-tex">>) -> [<<"tex">>];
mimetypes(<<"application/x-tex-tfm">>) -> [<<"tfm">>];
mimetypes(<<"application/x-tgif">>) -> [<<"obj">>];
mimetypes(<<"application/x-ustar">>) -> [<<"ustar">>];
mimetypes(<<"application/xv+xml">>) -> [<<"mxml">>,<<"xhvml">>,<<"xvml">>,<<"xvm">>];
mimetypes(<<"application/x-wais-source">>) -> [<<"src">>];
mimetypes(<<"application/x-x509-ca-cert">>) -> [<<"der">>,<<"crt">>];
mimetypes(<<"application/x-xfig">>) -> [<<"fig">>];
mimetypes(<<"application/x-xliff+xml">>) -> [<<"xlf">>];
mimetypes(<<"application/x-xpinstall">>) -> [<<"xpi">>];
mimetypes(<<"application/x-xz">>) -> [<<"xz">>];
mimetypes(<<"application/x-zmachine">>) -> [<<"z1">>,<<"z2">>,<<"z3">>,<<"z4">>,<<"z5">>,<<"z6">>,<<"z7">>,<<"z8">>];
mimetypes(<<"application/yang">>) -> [<<"yang">>];
mimetypes(<<"application/yin+xml">>) -> [<<"yin">>];
mimetypes(<<"application/zip">>) -> [<<"zip">>];
mimetypes(<<"audio/adpcm">>) -> [<<"adp">>];
mimetypes(<<"audio/basic">>) -> [<<"au">>,<<"snd">>];
mimetypes(<<"audio/midi">>) -> [<<"mid">>,<<"midi">>,<<"kar">>,<<"rmi">>];
mimetypes(<<"audio/mp4">>) -> [<<"m4a">>,<<"mp4a">>];
mimetypes(<<"audio/mpeg">>) -> [<<"mpga">>,<<"mp2">>,<<"mp2a">>,<<"mp3">>,<<"m2a">>,<<"m3a">>];
mimetypes(<<"audio/ogg">>) -> [<<"oga">>,<<"ogg">>,<<"spx">>,<<"opus">>];
mimetypes(<<"audio/s3m">>) -> [<<"s3m">>];
mimetypes(<<"audio/silk">>) -> [<<"sil">>];
mimetypes(<<"audio/vnd.dece.audio">>) -> [<<"uva">>,<<"uvva">>];
mimetypes(<<"audio/vnd.digital-winds">>) -> [<<"eol">>];
mimetypes(<<"audio/vnd.dra">>) -> [<<"dra">>];
mimetypes(<<"audio/vnd.dts">>) -> [<<"dts">>];
mimetypes(<<"audio/vnd.dts.hd">>) -> [<<"dtshd">>];
mimetypes(<<"audio/vnd.lucent.voice">>) -> [<<"lvp">>];
mimetypes(<<"audio/vnd.ms-playready.media.pya">>) -> [<<"pya">>];
mimetypes(<<"audio/vnd.nuera.ecelp4800">>) -> [<<"ecelp4800">>];
mimetypes(<<"audio/vnd.nuera.ecelp7470">>) -> [<<"ecelp7470">>];
mimetypes(<<"audio/vnd.nuera.ecelp9600">>) -> [<<"ecelp9600">>];
mimetypes(<<"audio/vnd.rip">>) -> [<<"rip">>];
mimetypes(<<"audio/webm">>) -> [<<"weba">>];
mimetypes(<<"audio/x-aac">>) -> [<<"aac">>];
mimetypes(<<"audio/x-aiff">>) -> [<<"aif">>,<<"aiff">>,<<"aifc">>];
mimetypes(<<"audio/x-caf">>) -> [<<"caf">>];
mimetypes(<<"audio/x-flac">>) -> [<<"flac">>];
mimetypes(<<"audio/x-matroska">>) -> [<<"mka">>];
mimetypes(<<"audio/x-mpegurl">>) -> [<<"m3u">>];
mimetypes(<<"audio/x-ms-wax">>) -> [<<"wax">>];
mimetypes(<<"audio/x-ms-wma">>) -> [<<"wma">>];
mimetypes(<<"audio/xm">>) -> [<<"xm">>];
mimetypes(<<"audio/x-pn-realaudio-plugin">>) -> [<<"rmp">>];
mimetypes(<<"audio/x-pn-realaudio">>) -> [<<"ram">>,<<"ra">>];
mimetypes(<<"audio/x-wav">>) -> [<<"wav">>];
mimetypes(<<"chemical/x-cdx">>) -> [<<"cdx">>];
mimetypes(<<"chemical/x-cif">>) -> [<<"cif">>];
mimetypes(<<"chemical/x-cmdf">>) -> [<<"cmdf">>];
mimetypes(<<"chemical/x-cml">>) -> [<<"cml">>];
mimetypes(<<"chemical/x-csml">>) -> [<<"csml">>];
mimetypes(<<"chemical/x-xyz">>) -> [<<"xyz">>];
mimetypes(<<"font/collection">>) -> [<<"ttc">>];
mimetypes(<<"font/otf">>) -> [<<"otf">>];
mimetypes(<<"font/ttf">>) -> [<<"ttf">>];
mimetypes(<<"font/woff2">>) -> [<<"woff2">>];
mimetypes(<<"font/woff">>) -> [<<"woff">>];
mimetypes(<<"image/avif">>) -> [<<"avif">>];
mimetypes(<<"image/bmp">>) -> [<<"bmp">>];
mimetypes(<<"image/cgm">>) -> [<<"cgm">>];
mimetypes(<<"image/g3fax">>) -> [<<"g3">>];
mimetypes(<<"image/gif">>) -> [<<"gif">>];
mimetypes(<<"image/ief">>) -> [<<"ief">>];
mimetypes(<<"image/jpeg">>) -> [<<"jpeg">>,<<"jpg">>,<<"jpe">>];
mimetypes(<<"image/jxl">>) -> [<<"jxl">>];
mimetypes(<<"image/ktx">>) -> [<<"ktx">>];
mimetypes(<<"image/png">>) -> [<<"png">>];
mimetypes(<<"image/prs.btif">>) -> [<<"btif">>];
mimetypes(<<"image/sgi">>) -> [<<"sgi">>];
mimetypes(<<"image/svg+xml">>) -> [<<"svg">>,<<"svgz">>];
mimetypes(<<"image/tiff">>) -> [<<"tiff">>,<<"tif">>];
mimetypes(<<"image/vnd.adobe.photoshop">>) -> [<<"psd">>];
mimetypes(<<"image/vnd.dece.graphic">>) -> [<<"uvi">>,<<"uvvi">>,<<"uvg">>,<<"uvvg">>];
mimetypes(<<"image/vnd.djvu">>) -> [<<"djvu">>,<<"djv">>];
mimetypes(<<"image/vnd.dvb.subtitle">>) -> [<<"sub">>];
mimetypes(<<"image/vnd.dwg">>) -> [<<"dwg">>];
mimetypes(<<"image/vnd.dxf">>) -> [<<"dxf">>];
mimetypes(<<"image/vnd.fastbidsheet">>) -> [<<"fbs">>];
mimetypes(<<"image/vnd.fpx">>) -> [<<"fpx">>];
mimetypes(<<"image/vnd.fst">>) -> [<<"fst">>];
mimetypes(<<"image/vnd.fujixerox.edmics-mmr">>) -> [<<"mmr">>];
mimetypes(<<"image/vnd.fujixerox.edmics-rlc">>) -> [<<"rlc">>];
mimetypes(<<"image/vnd.ms-modi">>) -> [<<"mdi">>];
mimetypes(<<"image/vnd.ms-photo">>) -> [<<"wdp">>];
mimetypes(<<"image/vnd.net-fpx">>) -> [<<"npx">>];
mimetypes(<<"image/vnd.wap.wbmp">>) -> [<<"wbmp">>];
mimetypes(<<"image/vnd.xiff">>) -> [<<"xif">>];
mimetypes(<<"image/webp">>) -> [<<"webp">>];
mimetypes(<<"image/x-3ds">>) -> [<<"3ds">>];
mimetypes(<<"image/x-cmu-raster">>) -> [<<"ras">>];
mimetypes(<<"image/x-cmx">>) -> [<<"cmx">>];
mimetypes(<<"image/x-freehand">>) -> [<<"fh">>,<<"fhc">>,<<"fh4">>,<<"fh5">>,<<"fh7">>];
mimetypes(<<"image/x-icon">>) -> [<<"ico">>];
mimetypes(<<"image/x-mrsid-image">>) -> [<<"sid">>];
mimetypes(<<"image/x-pcx">>) -> [<<"pcx">>];
mimetypes(<<"image/x-pict">>) -> [<<"pic">>,<<"pct">>];
mimetypes(<<"image/x-portable-anymap">>) -> [<<"pnm">>];
mimetypes(<<"image/x-portable-bitmap">>) -> [<<"pbm">>];
mimetypes(<<"image/x-portable-graymap">>) -> [<<"pgm">>];
mimetypes(<<"image/x-portable-pixmap">>) -> [<<"ppm">>];
mimetypes(<<"image/x-rgb">>) -> [<<"rgb">>];
mimetypes(<<"image/x-tga">>) -> [<<"tga">>];
mimetypes(<<"image/x-xbitmap">>) -> [<<"xbm">>];
mimetypes(<<"image/x-xpixmap">>) -> [<<"xpm">>];
mimetypes(<<"image/x-xwindowdump">>) -> [<<"xwd">>];
mimetypes(<<"message/rfc822">>) -> [<<"eml">>,<<"mime">>];
mimetypes(<<"model/iges">>) -> [<<"igs">>,<<"iges">>];
mimetypes(<<"model/mesh">>) -> [<<"msh">>,<<"mesh">>,<<"silo">>];
mimetypes(<<"model/vnd.collada+xml">>) -> [<<"dae">>];
mimetypes(<<"model/vnd.dwf">>) -> [<<"dwf">>];
mimetypes(<<"model/vnd.gdl">>) -> [<<"gdl">>];
mimetypes(<<"model/vnd.gtw">>) -> [<<"gtw">>];
mimetypes(<<"model/vnd.vtu">>) -> [<<"vtu">>];
mimetypes(<<"model/vrml">>) -> [<<"wrl">>,<<"vrml">>];
mimetypes(<<"model/x3d+binary">>) -> [<<"x3db">>,<<"x3dbz">>];
mimetypes(<<"model/x3d+vrml">>) -> [<<"x3dv">>,<<"x3dvz">>];
mimetypes(<<"model/x3d+xml">>) -> [<<"x3d">>,<<"x3dz">>];
mimetypes(<<"text/cache-manifest">>) -> [<<"appcache">>];
mimetypes(<<"text/calendar">>) -> [<<"ics">>,<<"ifb">>];
mimetypes(<<"text/css">>) -> [<<"css">>];
mimetypes(<<"text/csv">>) -> [<<"csv">>];
mimetypes(<<"text/html">>) -> [<<"html">>,<<"htm">>];
mimetypes(<<"text/javascript">>) -> [<<"js">>,<<"mjs">>];
mimetypes(<<"text/n3">>) -> [<<"n3">>];
mimetypes(<<"text/plain">>) -> [<<"txt">>,<<"text">>,<<"conf">>,<<"def">>,<<"list">>,<<"log">>,<<"in">>];
mimetypes(<<"text/prs.lines.tag">>) -> [<<"dsc">>];
mimetypes(<<"text/richtext">>) -> [<<"rtx">>];
mimetypes(<<"text/sgml">>) -> [<<"sgml">>,<<"sgm">>];
mimetypes(<<"text/tab-separated-values">>) -> [<<"tsv">>];
mimetypes(<<"text/troff">>) -> [<<"t">>,<<"tr">>,<<"roff">>,<<"man">>,<<"me">>,<<"ms">>];
mimetypes(<<"text/turtle">>) -> [<<"ttl">>];
mimetypes(<<"text/uri-list">>) -> [<<"uri">>,<<"uris">>,<<"urls">>];
mimetypes(<<"text/vcard">>) -> [<<"vcard">>];
mimetypes(<<"text/vnd.curl">>) -> [<<"curl">>];
mimetypes(<<"text/vnd.curl.dcurl">>) -> [<<"dcurl">>];
mimetypes(<<"text/vnd.curl.mcurl">>) -> [<<"mcurl">>];
mimetypes(<<"text/vnd.curl.scurl">>) -> [<<"scurl">>];
mimetypes(<<"text/vnd.dvb.subtitle">>) -> [<<"sub">>];
mimetypes(<<"text/vnd.fly">>) -> [<<"fly">>];
mimetypes(<<"text/vnd.fmi.flexstor">>) -> [<<"flx">>];
mimetypes(<<"text/vnd.graphviz">>) -> [<<"gv">>];
mimetypes(<<"text/vnd.in3d.3dml">>) -> [<<"3dml">>];
mimetypes(<<"text/vnd.in3d.spot">>) -> [<<"spot">>];
mimetypes(<<"text/vnd.sun.j2me.app-descriptor">>) -> [<<"jad">>];
mimetypes(<<"text/vnd.wap.wmlscript">>) -> [<<"wmls">>];
mimetypes(<<"text/vnd.wap.wml">>) -> [<<"wml">>];
mimetypes(<<"text/x-asm">>) -> [<<"s">>,<<"asm">>];
mimetypes(<<"text/x-c">>) -> [<<"c">>,<<"cc">>,<<"cxx">>,<<"cpp">>,<<"h">>,<<"hh">>,<<"dic">>];
mimetypes(<<"text/x-fortran">>) -> [<<"f">>,<<"for">>,<<"f77">>,<<"f90">>];
mimetypes(<<"text/x-java-source">>) -> [<<"java">>];
mimetypes(<<"text/x-nfo">>) -> [<<"nfo">>];
mimetypes(<<"text/x-opml">>) -> [<<"opml">>];
mimetypes(<<"text/x-pascal">>) -> [<<"p">>,<<"pas">>];
mimetypes(<<"text/x-setext">>) -> [<<"etx">>];
mimetypes(<<"text/x-sfv">>) -> [<<"sfv">>];
mimetypes(<<"text/x-uuencode">>) -> [<<"uu">>];
mimetypes(<<"text/x-vcalendar">>) -> [<<"vcs">>];
mimetypes(<<"text/x-vcard">>) -> [<<"vcf">>];
mimetypes(<<"video/3gpp2">>) -> [<<"3g2">>];
mimetypes(<<"video/3gpp">>) -> [<<"3gp">>];
mimetypes(<<"video/h261">>) -> [<<"h261">>];
mimetypes(<<"video/h263">>) -> [<<"h263">>];
mimetypes(<<"video/h264">>) -> [<<"h264">>];
mimetypes(<<"video/jpeg">>) -> [<<"jpgv">>];
mimetypes(<<"video/jpm">>) -> [<<"jpm">>,<<"jpgm">>];
mimetypes(<<"video/mj2">>) -> [<<"mj2">>,<<"mjp2">>];
mimetypes(<<"video/mp2t">>) -> [<<"ts">>,<<"m2t">>,<<"m2ts">>,<<"mts">>];
mimetypes(<<"video/mp4">>) -> [<<"mp4">>,<<"mp4v">>,<<"mpg4">>];
mimetypes(<<"video/mpeg">>) -> [<<"mpeg">>,<<"mpg">>,<<"mpe">>,<<"m1v">>,<<"m2v">>];
mimetypes(<<"video/ogg">>) -> [<<"ogv">>];
mimetypes(<<"video/quicktime">>) -> [<<"qt">>,<<"mov">>];
mimetypes(<<"video/vnd.dece.hd">>) -> [<<"uvh">>,<<"uvvh">>];
mimetypes(<<"video/vnd.dece.mobile">>) -> [<<"uvm">>,<<"uvvm">>];
mimetypes(<<"video/vnd.dece.pd">>) -> [<<"uvp">>,<<"uvvp">>];
mimetypes(<<"video/vnd.dece.sd">>) -> [<<"uvs">>,<<"uvvs">>];
mimetypes(<<"video/vnd.dece.video">>) -> [<<"uvv">>,<<"uvvv">>];
mimetypes(<<"video/vnd.dvb.file">>) -> [<<"dvb">>];
mimetypes(<<"video/vnd.fvt">>) -> [<<"fvt">>];
mimetypes(<<"video/vnd.mpegurl">>) -> [<<"mxu">>,<<"m4u">>];
mimetypes(<<"video/vnd.ms-playready.media.pyv">>) -> [<<"pyv">>];
mimetypes(<<"video/vnd.uvvu.mp4">>) -> [<<"uvu">>,<<"uvvu">>];
mimetypes(<<"video/vnd.vivo">>) -> [<<"viv">>];
mimetypes(<<"video/webm">>) -> [<<"webm">>];
mimetypes(<<"video/x-f4v">>) -> [<<"f4v">>];
mimetypes(<<"video/x-fli">>) -> [<<"fli">>];
mimetypes(<<"video/x-flv">>) -> [<<"flv">>];
mimetypes(<<"video/x-m4v">>) -> [<<"m4v">>];
mimetypes(<<"video/x-matroska">>) -> [<<"mkv">>,<<"mk3d">>,<<"mks">>];
mimetypes(<<"video/x-mng">>) -> [<<"mng">>];
mimetypes(<<"video/x-ms-asf">>) -> [<<"asf">>,<<"asx">>];
mimetypes(<<"video/x-msvideo">>) -> [<<"avi">>];
mimetypes(<<"video/x-ms-vob">>) -> [<<"vob">>];
mimetypes(<<"video/x-ms-wmv">>) -> [<<"wmv">>];
mimetypes(<<"video/x-ms-wm">>) -> [<<"wm">>];
mimetypes(<<"video/x-ms-wmx">>) -> [<<"wmx">>];
mimetypes(<<"video/x-ms-wvx">>) -> [<<"wvx">>];
mimetypes(<<"video/x-sgi-movie">>) -> [<<"movie">>];
mimetypes(<<"video/x-smv">>) -> [<<"smv">>];
mimetypes(<<"x-conference/x-cooltalk">>) -> [<<"ice">>];
mimetypes(_) -> [<<>>].
%% GENERATED

web_extensions(<<"css">>) -> {<<"text">>, <<"css">>};
web_extensions(<<"gif">>) -> {<<"image">>, <<"gif">>};
web_extensions(<<"html">>) -> {<<"text">>, <<"html">>};
web_extensions(<<"htm">>) -> {<<"text">>, <<"html">>};
web_extensions(<<"ico">>) -> {<<"image">>, <<"x-icon">>};
web_extensions(<<"jpeg">>) -> {<<"image">>, <<"jpeg">>};
web_extensions(<<"jpg">>) -> {<<"image">>, <<"jpeg">>};
web_extensions(<<"js">>) -> {<<"application">>, <<"javascript">>};
web_extensions(<<"mp3">>) -> {<<"audio">>, <<"mpeg">>};
web_extensions(<<"mp4">>) -> {<<"video">>, <<"mp4">>};
web_extensions(<<"ogg">>) -> {<<"audio">>, <<"ogg">>};
web_extensions(<<"ogv">>) -> {<<"video">>, <<"ogg">>};
web_extensions(<<"png">>) -> {<<"image">>, <<"png">>};
web_extensions(<<"svg">>) -> {<<"image">>, <<"svg+xml">>};
web_extensions(<<"wav">>) -> {<<"audio">>, <<"x-wav">>};
web_extensions(<<"webm">>) -> {<<"video">>, <<"webm">>};
web_extensions(_) -> {<<"application">>, <<"octet-stream">>}.

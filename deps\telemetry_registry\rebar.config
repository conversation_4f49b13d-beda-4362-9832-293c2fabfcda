{erl_opts, [debug_info]}.
{deps, [{telemetry, "~> 1.0"}]}.

{profiles, [
    {docs, [{deps, [edown]},
          {edoc_opts,
           [{doclet, edown_doclet},
            {preprocess, true},
            {dir, "edoc"},
            {subpackages, true}]}]},
    {test, [{erl_opts, [nowarn_export_all]},
            {deps, [
                {test_app, "~> 0.1"}
            ]},
            %% create junit xml for circleci
            {ct_opts, [{ct_hooks, [cth_surefire]}]},
            {cover_enabled, true},
            {cover_opts, [verbose]},
            {paths, ["src"]},
            %% convert to data codecov understands
            {plugins, [covertool]},
            {covertool, [{coverdata_files, ["ct.coverdata"]}]}
    ]}
]}.

%% take out warnings for unused exported functions
{xref_checks,[undefined_function_calls, undefined_functions, locals_not_used,
              deprecated_function_calls, deprecated_functions]}.


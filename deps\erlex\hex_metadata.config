{<<"links">>,
 [{<<"Changelog">>,<<"https://hexdocs.pm/erlex/changelog.html">>},
  {<<"GitHub">>,<<"https://github.com/christhekeele/erlex">>}]}.
{<<"name">>,<<"erlex">>}.
{<<"version">>,<<"0.2.7">>}.
{<<"description">>,
 <<"Convert Erlang style structs and error messages to equivalent Elixir.">>}.
{<<"elixir">>,<<"~> 1.6">>}.
{<<"app">>,<<"erlex">>}.
{<<"files">>,
 [<<"lib">>,<<"lib/erlex.ex">>,<<"mix.exs">>,<<"README.md">>,<<"LICENSE.md">>,
  <<"VERSION">>,<<"src/erlex_lexer.xrl">>,<<"src/erlex_parser.yrl">>]}.
{<<"licenses">>,[<<"Apache-2.0">>]}.
{<<"requirements">>,[]}.
{<<"build_tools">>,[<<"mix">>]}.

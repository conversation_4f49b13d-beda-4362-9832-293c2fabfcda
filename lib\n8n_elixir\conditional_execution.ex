defmodule N8nElixir.ConditionalExecution do
  @moduledoc """
  条件执行管理器

  管理工作流中的条件分支和循环逻辑
  支持复杂的执行路径控制
  """

  alias N8nElixir.{ExpressionResolver, DataProcessor}

  @doc """
  处理条件分支执行

  根据条件节点的结果确定执行路径
  """
  def handle_conditional_branch(node_output, branch_config) do
    case branch_config do
      %{type: "if", outputs: outputs} ->
        handle_if_branch(node_output, outputs)
      
      %{type: "switch", outputs: outputs} ->
        handle_switch_branch(node_output, outputs)
      
      %{type: "loop", outputs: outputs} ->
        handle_loop_branch(node_output, outputs)
      
      _ ->
        {:ok, node_output}
    end
  end

  @doc """
  处理循环执行状态

  管理循环节点的状态和数据流
  """
  def manage_loop_execution(loop_state, loop_config) do
    case loop_config.mode do
      :while_loop ->
        handle_while_loop(loop_state, loop_config)
      
      :for_loop ->
        handle_for_loop(loop_state, loop_config)
      
      :for_each_loop ->
        handle_for_each_loop(loop_state, loop_config)
      
      _ ->
        {:error, "Unknown loop mode"}
    end
  end

  @doc """
  评估执行条件

  解析和评估控制流的条件表达式
  """
  def evaluate_execution_condition(condition, context) do
    case condition do
      %{type: "expression", expression: expr} ->
        ExpressionResolver.resolve_expression(expr, context)
      
      %{type: "data_check", checks: checks} ->
        evaluate_data_checks(checks, context)
      
      %{type: "node_status", node_id: node_id, status: expected_status} ->
        evaluate_node_status(node_id, expected_status, context)
      
      _ ->
        {:ok, true}
    end
  end

  @doc """
  处理并行执行分支

  管理同时执行的多个分支
  """
  def handle_parallel_execution(branches, execution_context) do
    # 启动所有分支的并行执行
    tasks = Enum.map(branches, fn branch ->
      Task.async(fn ->
        execute_branch(branch, execution_context)
      end)
    end)
    
    # 等待所有分支完成
    results = Task.await_many(tasks, :infinity)
    
    # 合并分支结果
    merge_parallel_results(results)
  end

  @doc """
  处理执行同步点

  等待多个分支在同步点会合
  """
  def handle_synchronization_point(sync_config, branch_states) do
    case sync_config.strategy do
      :wait_all ->
        wait_for_all_branches(branch_states, sync_config)
      
      :wait_any ->
        wait_for_any_branch(branch_states, sync_config)
      
      :timeout ->
        wait_with_timeout(branch_states, sync_config)
      
      _ ->
        {:error, "Unknown synchronization strategy"}
    end
  end

  # 私有实现函数

  defp handle_if_branch(node_output, outputs) do
    # IF节点通常有两个输出：true和false
    case node_output do
      [true_items, false_items] ->
        {:ok, %{
          true_branch: true_items,
          false_branch: false_items,
          execution_paths: determine_execution_paths(outputs, [true_items, false_items])
        }}
      
      _ ->
        {:error, "Invalid IF node output format"}
    end
  end

  defp handle_switch_branch(node_output, outputs) when is_list(node_output) do
    # Switch节点可能有多个输出端口
    execution_paths = 
      node_output
      |> Enum.with_index()
      |> Enum.reduce(%{}, fn {items, index}, acc ->
        if not Enum.empty?(items) do
          output_config = Enum.at(outputs, index, %{})
          Map.put(acc, "output_#{index}", %{
            items: items,
            next_nodes: Map.get(output_config, :next_nodes, [])
          })
        else
          acc
        end
      end)
    
    {:ok, %{
      switch_outputs: node_output,
      execution_paths: execution_paths
    }}
  end

  defp handle_loop_branch(node_output, outputs) do
    case node_output do
      [done_items, loop_items] ->
        {:ok, %{
          done_items: done_items,
          loop_items: loop_items,
          execution_paths: %{
            "done" => %{
              items: done_items,
              next_nodes: Map.get(Enum.at(outputs, 0, %{}), :next_nodes, [])
            },
            "loop" => %{
              items: loop_items,
              next_nodes: Map.get(Enum.at(outputs, 1, %{}), :next_nodes, [])
            }
          }
        }}
      
      _ ->
        {:error, "Invalid Loop node output format"}
    end
  end

  defp handle_while_loop(loop_state, loop_config) do
    condition = loop_config.condition
    max_iterations = Map.get(loop_config, :max_iterations, 1000)
    
    if loop_state.iteration >= max_iterations do
      {:halt, loop_state}
    else
      case evaluate_execution_condition(condition, loop_state.context) do
        {:ok, true} ->
          {:continue, %{loop_state | iteration: loop_state.iteration + 1}}
        
        {:ok, false} ->
          {:halt, loop_state}
        
        {:error, reason} ->
          {:error, reason}
      end
    end
  end

  defp handle_for_loop(loop_state, loop_config) do
    target_count = loop_config.count
    
    if loop_state.iteration >= target_count do
      {:halt, loop_state}
    else
      {:continue, %{loop_state | iteration: loop_state.iteration + 1}}
    end
  end

  defp handle_for_each_loop(loop_state, loop_config) do
    items = loop_config.items
    
    if loop_state.iteration >= length(items) do
      {:halt, loop_state}
    else
      current_item = Enum.at(items, loop_state.iteration)
      updated_context = Map.put(loop_state.context, "$item", current_item)
      
      updated_state = %{loop_state | 
        iteration: loop_state.iteration + 1,
        context: updated_context
      }
      
      {:continue, updated_state}
    end
  end

  defp evaluate_data_checks(checks, context) do
    results = Enum.map(checks, fn check ->
      case check do
        %{field: field, condition: condition, value: expected} ->
          actual_value = get_context_value(context, field)
          evaluate_condition(actual_value, condition, expected)
        
        _ ->
          false
      end
    end)
    
    {:ok, Enum.all?(results)}
  end

  defp evaluate_node_status(node_id, expected_status, context) do
    execution_states = Map.get(context, "$execution_states", %{})
    actual_status = Map.get(execution_states, node_id, :pending)
    
    {:ok, actual_status == expected_status}
  end

  defp execute_branch(branch, execution_context) do
    # 模拟分支执行
    try do
      # 这里会调用实际的工作流执行逻辑
      {:ok, %{
        branch_id: branch.id,
        result: "Branch executed successfully",
        context: execution_context
      }}
    rescue
      error ->
        {:error, %{
          branch_id: branch.id,
          error: inspect(error)
        }}
    end
  end

  defp merge_parallel_results(results) do
    {successful, failed} = Enum.split_with(results, fn
      {:ok, _} -> true
      _ -> false
    end)
    
    if Enum.empty?(failed) do
      merged_data = 
        successful
        |> Enum.map(fn {:ok, result} -> result end)
        |> DataProcessor.merge_data_sources(:append)
      
      {:ok, merged_data}
    else
      {:error, %{
        successful: length(successful),
        failed: failed
      }}
    end
  end

  defp wait_for_all_branches(branch_states, sync_config) do
    timeout = Map.get(sync_config, :timeout, 30_000)
    
    # 等待所有分支完成
    case wait_for_completion(branch_states, timeout) do
      {:ok, completed_states} ->
        {:ok, %{
          sync_point: sync_config.id,
          completed_branches: completed_states,
          strategy: :wait_all
        }}
      
      {:timeout, partial_states} ->
        {:timeout, %{
          sync_point: sync_config.id,
          completed_branches: partial_states,
          timeout_duration: timeout
        }}
    end
  end

  defp wait_for_any_branch(branch_states, sync_config) do
    # 等待任意一个分支完成
    case find_first_completed_branch(branch_states) do
      {:ok, completed_branch} ->
        {:ok, %{
          sync_point: sync_config.id,
          first_completed: completed_branch,
          strategy: :wait_any
        }}
      
      :no_completion ->
        {:error, "No branches completed"}
    end
  end

  defp wait_with_timeout(branch_states, sync_config) do
    timeout = sync_config.timeout
    
    case wait_for_completion(branch_states, timeout) do
      {:ok, completed_states} ->
        {:ok, completed_states}
      
      {:timeout, partial_states} ->
        {:timeout, partial_states}
    end
  end

  defp determine_execution_paths(outputs, node_outputs) do
    outputs
    |> Enum.with_index()
    |> Enum.reduce(%{}, fn {output_config, index}, acc ->
      output_data = Enum.at(node_outputs, index, [])
      
      if not Enum.empty?(output_data) do
        Map.put(acc, "output_#{index}", %{
          items: output_data,
          next_nodes: Map.get(output_config, :next_nodes, [])
        })
      else
        acc
      end
    end)
  end

  defp get_context_value(context, field) do
    case String.split(field, ".", parts: 2) do
      [source] ->
        Map.get(context, source)
      
      [source, path] ->
        source_data = Map.get(context, source, %{})
        get_nested_value(source_data, path)
    end
  end

  defp get_nested_value(data, path) when is_binary(path) do
    path_parts = String.split(path, ".")
    get_nested_value(data, path_parts)
  end
  defp get_nested_value(data, []), do: data
  defp get_nested_value(data, [key | rest]) when is_map(data) do
    case Map.get(data, key) do
      nil -> nil
      value -> get_nested_value(value, rest)
    end
  end
  defp get_nested_value(_, _), do: nil

  defp evaluate_condition(actual, condition, expected) do
    case condition do
      "equal" -> actual == expected
      "not_equal" -> actual != expected
      "greater_than" -> actual > expected
      "less_than" -> actual < expected
      "contains" -> String.contains?(to_string(actual), to_string(expected))
      "exists" -> actual != nil
      _ -> false
    end
  end

  defp wait_for_completion(branch_states, timeout) do
    # 模拟等待逻辑
    # 在实际实现中，这里会使用Process.monitor或其他机制
    :timer.sleep(100)  # 模拟等待
    
    completed = Enum.filter(branch_states, fn state ->
      Map.get(state, :status) in [:completed, :success, :error]
    end)
    
    if length(completed) == length(branch_states) do
      {:ok, completed}
    else
      {:timeout, completed}
    end
  end

  defp find_first_completed_branch(branch_states) do
    case Enum.find(branch_states, fn state ->
      Map.get(state, :status) in [:completed, :success]
    end) do
      nil -> :no_completion
      branch -> {:ok, branch}
    end
  end
end
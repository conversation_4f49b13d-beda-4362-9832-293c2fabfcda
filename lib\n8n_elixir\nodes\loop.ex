defmodule N8nElixir.Nodes.Loop do
  @moduledoc """
  循环节点 - Loop Over Items

  参考n8n的循环概念，实现数据项的循环处理
  支持多种循环模式和条件控制
  """
  @behaviour N8nElixir.NodeType

  alias N8nElixir.{ExpressionResolver, DataProcessor}

  @impl true
  def description() do
    %{
      displayName: "Loop Over Items",
      name: "loop",
      icon: "fa:sync",
      group: ["transform"],
      version: 1,
      description: "Loop over items and execute sub-workflow for each",
      defaults: %{
        name: "Loop",
        color: "#FF9800"
      },
      inputs: ["main"],
      outputs: ["main", "main"],
      outputNames: ["done", "loop"],
      properties: [
        %{
          displayName: "Loop Mode",
          name: "loopMode",
          type: "options",
          options: [
            %{
              name: "Each Item Separately",
              value: "eachItem",
              description: "Execute loop body for each item separately"
            },
            %{
              name: "All Items Together",
              value: "allItems",
              description: "Execute loop body with all items at once"
            },
            %{
              name: "Fixed Count",
              value: "fixedCount",
              description: "Execute loop body a fixed number of times"
            },
            %{
              name: "While Condition",
              value: "whileCondition",
              description: "Execute loop body while condition is true"
            }
          ],
          default: "eachItem"
        },
        %{
          displayName: "Loop Count",
          name: "loopCount",
          type: "number",
          displayOptions: %{
            show: %{
              loopMode: ["fixedCount"]
            }
          },
          default: 5,
          description: "Number of times to execute the loop",
          typeOptions: %{
            minValue: 1,
            maxValue: 1000
          }
        },
        %{
          displayName: "While Condition",
          name: "whileCondition",
          type: "string",
          displayOptions: %{
            show: %{
              loopMode: ["whileCondition"]
            }
          },
          default: "={{$json.continue === true}}",
          placeholder: "={{$json.hasMore === true}}",
          description: "Condition to evaluate for while loop",
          required: true
        },
        %{
          displayName: "Max Iterations",
          name: "maxIterations",
          type: "number",
          displayOptions: %{
            show: %{
              loopMode: ["whileCondition"]
            }
          },
          default: 100,
          description: "Maximum number of iterations to prevent infinite loops",
          typeOptions: %{
            minValue: 1,
            maxValue: 10000
          }
        },
        %{
          displayName: "Break on Empty",
          name: "breakOnEmpty",
          type: "boolean",
          default: true,
          description: "Break the loop when no data is returned"
        },
        %{
          displayName: "Continue on Fail",
          name: "continueOnFail", 
          type: "boolean",
          default: false,
          description: "Continue loop execution even if an iteration fails"
        }
      ]
    }
  end

  @impl true
  def execute(input_data, _credentials, parameters) do
    loop_mode = Map.get(parameters, "loopMode", "eachItem")
    break_on_empty = Map.get(parameters, "breakOnEmpty", true)
    continue_on_fail = Map.get(parameters, "continueOnFail", false)
    
    case loop_mode do
      "eachItem" ->
        execute_each_item_loop(input_data, parameters, break_on_empty, continue_on_fail)
      
      "allItems" ->
        execute_all_items_loop(input_data, parameters, break_on_empty, continue_on_fail)
      
      "fixedCount" ->
        execute_fixed_count_loop(input_data, parameters, break_on_empty, continue_on_fail)
      
      "whileCondition" ->
        execute_while_condition_loop(input_data, parameters, break_on_empty, continue_on_fail)
      
      _ ->
        {:error, "Unknown loop mode: #{loop_mode}"}
    end
  end

  @impl true
  def credential_types() do
    []
  end

  # 私有实现函数

  defp execute_each_item_loop(input_data, _parameters, _break_on_empty, continue_on_fail) do
    try do
      # 为每个输入项创建一个循环迭代
      loop_items = 
        input_data
        |> Enum.with_index()
        |> Enum.map(fn {item, index} ->
          # 添加循环上下文信息
          loop_context = %{
            "index" => index,
            "total" => length(input_data),
            "isFirst" => index == 0,
            "isLast" => index == length(input_data) - 1
          }
          
          updated_json = Map.merge(item["json"] || %{}, %{"$loop" => loop_context})
          %{item | "json" => updated_json}
        end)
      
      # 返回两个输出：done（完成的项目）和loop（需要循环处理的项目）
      done_items = []  # 循环完成后的项目会在这里
      loop_items_output = loop_items  # 需要发送给循环体的项目
      
      {:ok, [done_items, loop_items_output]}
    rescue
      error ->
        if continue_on_fail do
          {:ok, [input_data, []]}
        else
          {:error, "Each item loop failed: #{inspect(error)}"}
        end
    end
  end

  defp execute_all_items_loop(input_data, _parameters, _break_on_empty, continue_on_fail) do
    try do
      # 将所有项目作为一个批次进行循环处理
      loop_context = %{
        "itemCount" => length(input_data),
        "iteration" => 0,
        "mode" => "allItems"
      }
      
      # 为所有项目添加循环上下文
      loop_items = 
        Enum.map(input_data, fn item ->
          updated_json = Map.merge(item["json"] || %{}, %{"$loop" => loop_context})
          %{item | "json" => updated_json}
        end)
      
      {:ok, [[], loop_items]}
    rescue
      error ->
        if continue_on_fail do
          {:ok, [input_data, []]}
        else
          {:error, "All items loop failed: #{inspect(error)}"}
        end
    end
  end

  defp execute_fixed_count_loop(input_data, parameters, _break_on_empty, continue_on_fail) do
    loop_count = Map.get(parameters, "loopCount", 5)
    
    try do
      # 创建固定次数的循环项目
      loop_items = 
        0..(loop_count - 1)
        |> Enum.reduce([], fn iteration, acc ->
          iteration_items = 
            Enum.map(input_data, fn item ->
              loop_context = %{
                "iteration" => iteration,
                "total" => loop_count,
                "isFirst" => iteration == 0,
                "isLast" => iteration == loop_count - 1
              }
              
              updated_json = Map.merge(item["json"] || %{}, %{"$loop" => loop_context})
              %{item | "json" => updated_json}
            end)
          
          acc ++ iteration_items
        end)
      
      {:ok, [[], loop_items]}
    rescue
      error ->
        if continue_on_fail do
          {:ok, [input_data, []]}
        else
          {:error, "Fixed count loop failed: #{inspect(error)}"}
        end
    end
  end

  defp execute_while_condition_loop(input_data, parameters, break_on_empty, continue_on_fail) do
    while_condition = Map.get(parameters, "whileCondition", "")
    max_iterations = Map.get(parameters, "maxIterations", 100)
    
    if String.trim(while_condition) == "" do
      {:error, "While condition is required"}
    else
      try do
        # 初始化循环状态
        loop_state = %{
          iteration: 0,
          items: input_data,
          should_continue: true,
          results: []
        }
        
        # 执行while循环
        final_state = execute_while_loop(loop_state, while_condition, max_iterations, break_on_empty)
        
        {:ok, [final_state.results, final_state.items]}
      rescue
        error ->
          if continue_on_fail do
            {:ok, [input_data, []]}
          else
            {:error, "While condition loop failed: #{inspect(error)}"}
          end
      end
    end
  end

  defp execute_while_loop(state, condition, max_iterations, break_on_empty) do
    if state.iteration >= max_iterations do
      # 达到最大迭代次数，停止循环
      %{state | should_continue: false}
    else
      # 评估while条件
      should_continue = evaluate_while_condition(state.items, condition)
      
      if should_continue and (not break_on_empty or not Enum.empty?(state.items)) do
        # 创建这次迭代的项目
        iteration_items = 
          Enum.map(state.items, fn item ->
            loop_context = %{
              "iteration" => state.iteration,
              "maxIterations" => max_iterations
            }
            
            updated_json = Map.merge(item["json"] || %{}, %{"$loop" => loop_context})
            %{item | "json" => updated_json}
          end)
        
        # 更新循环状态
        updated_state = %{state |
          iteration: state.iteration + 1,
          items: iteration_items,
          results: state.results ++ iteration_items
        }
        
        # 递归继续循环
        execute_while_loop(updated_state, condition, max_iterations, break_on_empty)
      else
        # 条件不满足或数据为空，停止循环
        %{state | should_continue: false}
      end
    end
  end

  defp evaluate_while_condition(items, condition) do
    if Enum.empty?(items) do
      false
    else
      # 使用第一个项目来评估条件
      first_item = List.first(items)
      
      context = %{
        "$json" => Map.get(first_item, "json", %{}),
        "$binary" => Map.get(first_item, "binary", %{}),
        "$item" => first_item
      }
      
      case ExpressionResolver.resolve_expression(condition, context) do
        {:ok, result} -> is_truthy(result)
        {:error, _} -> false
      end
    end
  end

  defp is_truthy(nil), do: false
  defp is_truthy(false), do: false
  defp is_truthy(0), do: false
  defp is_truthy(""), do: false
  defp is_truthy([]), do: false
  defp is_truthy(%{} = map) when map_size(map) == 0, do: false
  defp is_truthy(_), do: true
end
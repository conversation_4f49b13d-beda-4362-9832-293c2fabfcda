{application,makeup,
             [{modules,['Elixir.Makeup','Elixir.Makeup.Application',
                        'Elixir.Makeup.Formatter',
                        'Elixir.Makeup.Formatters.HTML.HTMLFormatter',
                        'Elixir.Makeup.Lexer',
                        'Elixir.Makeup.Lexer.Combinators',
                        'Elixir.Makeup.Lexer.Groups',
                        'Elixir.Makeup.Lexer.Postprocess',
                        'Elixir.Makeup.Lexer.Types','Elixir.Makeup.Registry',
                        'Elixir.Makeup.Styles.HTML.Style',
                        'Elixir.Makeup.Styles.HTML.StyleMap',
                        'Elixir.Makeup.Styles.HTML.TokenStyle',
                        'Elixir.Makeup.Token.Utils',
                        'Elixir.Makeup.Token.Utils.Hierarchy']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,eex,nimble_parsec]},
              {description,"Syntax highlighter for source code in the style of Pygments.\n"},
              {registered,[]},
              {vsn,"1.2.1"},
              {mod,{'Elixir.Makeup.Application',[]}}]}.

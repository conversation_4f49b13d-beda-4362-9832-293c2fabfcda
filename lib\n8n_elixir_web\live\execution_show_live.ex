defmodule N8nElixirWeb.ExecutionShowLive do
  @moduledoc """
  执行详情页面

  显示单个执行的详细信息，包括执行日志、节点状态和性能指标
  """
  use N8nElixirWeb, :live_view

  alias N8nElixir.{Executions, ExecutionLogger}

  @impl true
  def mount(%{"id" => workflow_id, "execution_id" => execution_id}, _session, socket) do
    case Executions.get_execution_with_data(execution_id) do
      nil ->
        {:ok, redirect(socket, to: "/workflows/#{workflow_id}/executions")}
      
      execution ->
        # 获取执行日志
        logs = ExecutionLogger.get_execution_logs(execution_id, limit: 1000)
        
        # 订阅执行事件（如果正在执行）
        if execution.status in [:running, :waiting] do
          Phoenix.PubSub.subscribe(N8nElixir.PubSub, "workflow_executions:#{workflow_id}")
          Phoenix.PubSub.subscribe(N8nElixir.PubSub, "node_executions:#{execution_id}")
        end
        
        socket = 
          socket
          |> assign(:execution, execution)
          |> assign(:workflow_id, workflow_id)
          |> assign(:execution_logs, logs)
          |> assign(:selected_tab, "overview")
          |> assign(:log_filters, %{})
          |> assign(:node_filter, nil)
          |> assign(:auto_refresh, execution.status in [:running, :waiting])
        
        {:ok, socket}
    end
  end

  @impl true
  def handle_params(params, _url, socket) do
    tab = Map.get(params, "tab", "overview")
    {:noreply, assign(socket, :selected_tab, tab)}
  end

  @impl true
  def handle_event("change_tab", %{"tab" => tab}, socket) do
    {:noreply, assign(socket, :selected_tab, tab)}
  end

  @impl true
  def handle_event("filter_logs", %{"level" => level}, socket) do
    filters = if level == "all" do
      Map.delete(socket.assigns.log_filters, :level)
    else
      Map.put(socket.assigns.log_filters, :level, String.to_atom(level))
    end
    
    # 重新获取过滤后的日志
    logs = ExecutionLogger.get_execution_logs(
      socket.assigns.execution.id,
      Map.to_list(filters) ++ [limit: 1000]
    )
    
    socket = 
      socket
      |> assign(:log_filters, filters)
      |> assign(:execution_logs, logs)
    
    {:noreply, socket}
  end

  @impl true
  def handle_event("filter_node", %{"node" => node_name}, socket) do
    node_filter = if node_name == "all", do: nil, else: node_name
    
    logs = if node_filter do
      ExecutionLogger.get_node_logs(socket.assigns.execution.id, node_filter)
    else
      ExecutionLogger.get_execution_logs(socket.assigns.execution.id, limit: 1000)
    end
    
    socket = 
      socket
      |> assign(:node_filter, node_filter)
      |> assign(:execution_logs, logs)
    
    {:noreply, socket}
  end

  @impl true
  def handle_event("toggle_auto_refresh", _params, socket) do
    auto_refresh = not socket.assigns.auto_refresh
    {:noreply, assign(socket, :auto_refresh, auto_refresh)}
  end

  @impl true
  def handle_event("retry_execution", _params, socket) do
    case Executions.retry_execution(socket.assigns.execution.id) do
      {:ok, new_execution} ->
        socket = 
          socket
          |> put_flash(:info, "Execution retry started")
          |> redirect(to: "/workflows/#{socket.assigns.workflow_id}/executions/#{new_execution.id}")
        
        {:noreply, socket}
      
      {:error, reason} ->
        {:noreply, put_flash(socket, :error, "Failed to retry execution: #{reason}")}
    end
  end

  @impl true
  def handle_event("download_logs", _params, socket) do
    # 生成日志文件下载
    logs_json = Jason.encode!(socket.assigns.execution_logs, pretty: true)
    filename = "execution_#{socket.assigns.execution.id}_logs.json"
    
    socket = 
      socket
      |> put_flash(:info, "Logs download started")
      |> push_event("download_file", %{filename: filename, content: logs_json})
    
    {:noreply, socket}
  end

  # 处理实时执行事件
  @impl true
  def handle_info({:execution_completed, execution_id, _state}, socket) do
    if socket.assigns.execution.id == execution_id do
      # 重新加载执行数据
      execution = Executions.get_execution_with_data(execution_id)
      logs = ExecutionLogger.get_execution_logs(execution_id, limit: 1000)
      
      socket = 
        socket
        |> assign(:execution, execution)
        |> assign(:execution_logs, logs)
        |> assign(:auto_refresh, false)
        |> put_flash(:info, "Execution completed successfully")
      
      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:execution_failed, execution_id, _state}, socket) do
    if socket.assigns.execution.id == execution_id do
      execution = Executions.get_execution_with_data(execution_id)
      logs = ExecutionLogger.get_execution_logs(execution_id, limit: 1000)
      
      socket = 
        socket
        |> assign(:execution, execution)
        |> assign(:execution_logs, logs)
        |> assign(:auto_refresh, false)
        |> put_flash(:error, "Execution failed")
      
      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:node_executed, _node_name, _state}, socket) do
    if socket.assigns.auto_refresh do
      # 刷新日志
      logs = ExecutionLogger.get_execution_logs(socket.assigns.execution.id, limit: 1000)
      {:noreply, assign(socket, :execution_logs, logs)}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info(_event, socket) do
    {:noreply, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="execution-details">
      <!-- 执行头部信息 -->
      <div class="execution-header">
        <div class="execution-title">
          <h1>Execution Details</h1>
          <div class="execution-meta">
            <span class="execution-id">ID: <%= @execution.id %></span>
            <span class={"execution-status #{@execution.status}"}>
              <%= String.upcase(to_string(@execution.status)) %>
            </span>
          </div>
        </div>

        <div class="execution-actions">
          <%= if @execution.status in [:error, :canceled] do %>
            <button class="btn btn-primary" phx-click="retry_execution">
              <i class="fa fa-redo"></i> Retry
            </button>
          <% end %>
          
          <button class="btn btn-secondary" phx-click="download_logs">
            <i class="fa fa-download"></i> Download Logs
          </button>
          
          <button class={"btn btn-sm #{if @auto_refresh, do: "btn-success", else: "btn-outline-secondary"}"} 
                  phx-click="toggle_auto_refresh">
            <i class="fa fa-sync"></i> Auto Refresh
          </button>
        </div>
      </div>

      <!-- 执行摘要 -->
      <div class="execution-summary">
        <div class="summary-cards">
          <div class="summary-card">
            <h3>Duration</h3>
            <div class="summary-value"><%= format_duration(@execution) %></div>
          </div>
          
          <div class="summary-card">
            <h3>Started</h3>
            <div class="summary-value"><%= format_datetime(@execution.started_at) %></div>
          </div>
          
          <div class="summary-card">
            <h3>Mode</h3>
            <div class="summary-value"><%= String.upcase(to_string(@execution.mode)) %></div>
          </div>
          
          <div class="summary-card">
            <h3>Nodes</h3>
            <div class="summary-value"><%= count_executed_nodes(@execution_logs) %></div>
          </div>
        </div>
      </div>

      <!-- 标签页导航 -->
      <div class="tab-navigation">
        <nav class="tabs">
          <button class={"tab #{if @selected_tab == "overview", do: "active"}"} 
                  phx-click="change_tab" 
                  phx-value-tab="overview">
            Overview
          </button>
          <button class={"tab #{if @selected_tab == "logs", do: "active"}"} 
                  phx-click="change_tab" 
                  phx-value-tab="logs">
            Execution Logs
          </button>
          <button class={"tab #{if @selected_tab == "data", do: "active"}"} 
                  phx-click="change_tab" 
                  phx-value-tab="data">
            Data Flow
          </button>
          <button class={"tab #{if @selected_tab == "performance", do: "active"}"} 
                  phx-click="change_tab" 
                  phx-value-tab="performance">
            Performance
          </button>
        </nav>
      </div>

      <!-- 标签页内容 -->
      <div class="tab-content">
        <%= case @selected_tab do %>
          <% "overview" -> %>
            <%= render_overview_tab(assigns) %>
          <% "logs" -> %>
            <%= render_logs_tab(assigns) %>
          <% "data" -> %>
            <%= render_data_tab(assigns) %>
          <% "performance" -> %>
            <%= render_performance_tab(assigns) %>
        <% end %>
      </div>
    </div>

    <style>
    .execution-details {
      padding: 20px;
      max-width: 1400px;
      margin: 0 auto;
    }

    .execution-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 1px solid #eee;
    }

    .execution-title h1 {
      margin: 0 0 10px 0;
      color: #333;
    }

    .execution-meta {
      display: flex;
      gap: 15px;
      align-items: center;
    }

    .execution-id {
      font-family: monospace;
      background: #f8f9fa;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
    }

    .execution-status {
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 600;
      text-transform: uppercase;
    }

    .execution-status.success {
      background: #d4edda;
      color: #155724;
    }

    .execution-status.error {
      background: #f8d7da;
      color: #721c24;
    }

    .execution-status.running {
      background: #cce5ff;
      color: #004085;
    }

    .execution-status.waiting {
      background: #fff3cd;
      color: #856404;
    }

    .execution-actions {
      display: flex;
      gap: 10px;
    }

    .execution-summary {
      margin-bottom: 30px;
    }

    .summary-cards {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;
    }

    .summary-card {
      background: white;
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 20px;
      text-align: center;
    }

    .summary-card h3 {
      margin: 0 0 10px 0;
      color: #666;
      font-size: 14px;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .summary-value {
      font-size: 24px;
      font-weight: 600;
      color: #333;
    }

    .tab-navigation {
      margin-bottom: 30px;
    }

    .tabs {
      display: flex;
      border-bottom: 1px solid #ddd;
    }

    .tab {
      padding: 12px 24px;
      border: none;
      background: none;
      cursor: pointer;
      border-bottom: 2px solid transparent;
      transition: all 0.2s ease;
    }

    .tab:hover {
      background: #f8f9fa;
    }

    .tab.active {
      color: #007bff;
      border-bottom-color: #007bff;
    }

    .tab-content {
      min-height: 400px;
    }

    .btn {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      display: inline-flex;
      align-items: center;
      gap: 6px;
      text-decoration: none;
    }

    .btn-primary {
      background: #007bff;
      color: white;
    }

    .btn-primary:hover {
      background: #0056b3;
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background: #545b62;
    }

    .btn-success {
      background: #28a745;
      color: white;
    }

    .btn-outline-secondary {
      background: transparent;
      color: #6c757d;
      border: 1px solid #6c757d;
    }

    .btn-outline-secondary:hover {
      background: #6c757d;
      color: white;
    }

    .btn-sm {
      padding: 4px 8px;
      font-size: 12px;
    }
    </style>
    """
  end

  # 标签页渲染函数
  defp render_overview_tab(assigns) do
    ~H"""
    <div class="overview-tab">
      <div class="execution-timeline">
        <h3>Execution Timeline</h3>
        <div class="timeline">
          <%= for log <- get_timeline_events(@execution_logs) do %>
            <div class={"timeline-item #{log.event_type}"}>
              <div class="timeline-time">
                <%= format_time(log.timestamp) %>
              </div>
              <div class="timeline-content">
                <div class="timeline-title">
                  <%= format_event_title(log) %>
                </div>
                <div class="timeline-description">
                  <%= format_event_description(log) %>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      </div>

      <div class="execution-workflow">
        <h3>Workflow Structure</h3>
        <div class="workflow-preview">
          <!-- 这里显示工作流的简化视图 -->
          <p>Workflow visualization would go here</p>
        </div>
      </div>
    </div>
    """
  end

  defp render_logs_tab(assigns) do
    ~H"""
    <div class="logs-tab">
      <!-- 日志过滤器 -->
      <div class="log-filters">
        <div class="filter-group">
          <label>Log Level:</label>
          <select phx-change="filter_logs" name="level">
            <option value="all">All Levels</option>
            <option value="debug" selected={Map.get(@log_filters, :level) == :debug}>Debug</option>
            <option value="info" selected={Map.get(@log_filters, :level) == :info}>Info</option>
            <option value="warn" selected={Map.get(@log_filters, :level) == :warn}>Warning</option>
            <option value="error" selected={Map.get(@log_filters, :level) == :error}>Error</option>
          </select>
        </div>

        <div class="filter-group">
          <label>Node:</label>
          <select phx-change="filter_node" name="node">
            <option value="all">All Nodes</option>
            <%= for node <- get_unique_nodes(@execution_logs) do %>
              <option value={node} selected={@node_filter == node}><%= node %></option>
            <% end %>
          </select>
        </div>
      </div>

      <!-- 日志列表 -->
      <div class="log-entries">
        <%= for log <- @execution_logs do %>
          <div class={"log-entry #{log.level}"}>
            <div class="log-timestamp">
              <%= format_timestamp(log.timestamp) %>
            </div>
            <div class="log-level">
              <%= String.upcase(to_string(log.level)) %>
            </div>
            <div class="log-node">
              <%= log.node_name || "System" %>
            </div>
            <div class="log-event">
              <%= format_event_name(log.event_type) %>
            </div>
            <div class="log-message">
              <%= format_log_message(log) %>
            </div>
          </div>
        <% end %>
      </div>
    </div>
    """
  end

  defp render_data_tab(assigns) do
    ~H"""
    <div class="data-tab">
      <h3>Data Flow Analysis</h3>
      <p>Data flow visualization and analysis would be implemented here.</p>
    </div>
    """
  end

  defp render_performance_tab(assigns) do
    ~H"""
    <div class="performance-tab">
      <h3>Performance Metrics</h3>
      <p>Performance charts and metrics would be implemented here.</p>
    </div>
    """
  end

  # 辅助函数
  defp format_duration(execution) do
    case execution.stopped_at do
      nil -> "Running..."
      stopped_at ->
        diff = DateTime.diff(stopped_at, execution.started_at, :millisecond)
        format_milliseconds(diff)
    end
  end

  defp format_milliseconds(ms) when ms < 1000, do: "#{ms}ms"
  defp format_milliseconds(ms) when ms < 60000 do
    seconds = Float.round(ms / 1000, 1)
    "#{seconds}s"
  end
  defp format_milliseconds(ms) do
    minutes = div(ms, 60000)
    seconds = div(rem(ms, 60000), 1000)
    "#{minutes}m #{seconds}s"
  end

  defp format_datetime(datetime) do
    Calendar.strftime(datetime, "%Y-%m-%d %H:%M:%S")
  end

  defp format_timestamp(datetime) do
    Calendar.strftime(datetime, "%H:%M:%S.%f")
  end

  defp format_time(datetime) do
    Calendar.strftime(datetime, "%H:%M:%S")
  end

  defp count_executed_nodes(logs) do
    logs
    |> Enum.filter(fn log -> log.event_type == :node_complete end)
    |> Enum.map(fn log -> log.node_name end)
    |> Enum.uniq()
    |> length()
  end

  defp get_timeline_events(logs) do
    logs
    |> Enum.filter(fn log -> 
      log.event_type in [:workflow_start, :workflow_complete, :node_start, :node_complete, :node_error]
    end)
    |> Enum.sort(fn a, b -> DateTime.compare(a.timestamp, b.timestamp) != :gt end)
    |> Enum.take(20)  # 限制显示数量
  end

  defp get_unique_nodes(logs) do
    logs
    |> Enum.map(fn log -> log.node_name end)
    |> Enum.reject(&is_nil/1)
    |> Enum.uniq()
    |> Enum.sort()
  end

  defp format_event_title(log) do
    case log.event_type do
      :workflow_start -> "Workflow Started"
      :workflow_complete -> "Workflow Completed"
      :node_start -> "#{log.node_name} Started"
      :node_complete -> "#{log.node_name} Completed"
      :node_error -> "#{log.node_name} Failed"
      _ -> String.replace(to_string(log.event_type), "_", " ") |> String.capitalize()
    end
  end

  defp format_event_description(log) do
    case log.event_type do
      :node_complete ->
        execution_time = Map.get(log.data, :execution_time, 0)
        "Completed in #{execution_time}ms"
      :node_error ->
        Map.get(log.data, :error_message, "Unknown error")
      _ ->
        ""
    end
  end

  defp format_event_name(event_type) do
    event_type
    |> to_string()
    |> String.replace("_", " ")
    |> String.split(" ")
    |> Enum.map(&String.capitalize/1)
    |> Enum.join(" ")
  end

  defp format_log_message(log) do
    case log.data do
      %{error_message: message} -> message
      %{status: status} -> "Status: #{status}"
      data when is_map(data) and map_size(data) > 0 ->
        Jason.encode!(data, pretty: true)
      _ -> ""
    end
  end
end
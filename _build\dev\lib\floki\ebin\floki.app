{application,floki,
             [{modules,['Elixir.Enumerable.Floki.HTMLTree','Elixir.Floki',
                        'Elixir.Floki.CSSEscape','Elixir.Floki.DeepText',
                        'Elixir.Floki.Entities',
                        'Elixir.Floki.Entities.Codepoints',
                        'Elixir.Floki.FilterOut','Elixir.Floki.Finder',
                        'Elixir.Floki.FlatText',
                        'Elixir.Floki.HTML.NumericCharref',
                        'Elixir.Floki.HTML.Tokenizer',
                        'Elixir.Floki.HTML.Tokenizer.Attribute',
                        'Elixir.Floki.HTML.Tokenizer.CharrefState',
                        'Elixir.Floki.HTML.Tokenizer.Comment',
                        'Elixir.Floki.HTML.Tokenizer.Doctype',
                        'Elixir.Floki.HTML.Tokenizer.EndTag',
                        'Elixir.Floki.HTML.Tokenizer.StartTag',
                        'Elixir.Floki.HTML.Tokenizer.State',
                        'Elixir.Floki.HTMLParser',
                        'Elixir.Floki.HTMLParser.FastHtml',
                        'Elixir.Floki.HTMLParser.Html5ever',
                        'Elixir.Floki.HTMLParser.Mochiweb',
                        'Elixir.Floki.HTMLTree',
                        'Elixir.Floki.HTMLTree.Comment',
                        'Elixir.Floki.HTMLTree.HTMLNode',
                        'Elixir.Floki.HTMLTree.IDSeeder',
                        'Elixir.Floki.HTMLTree.Text',
                        'Elixir.Floki.ParseError','Elixir.Floki.RawHTML',
                        'Elixir.Floki.Selector',
                        'Elixir.Floki.Selector.AttributeSelector',
                        'Elixir.Floki.Selector.Combinator',
                        'Elixir.Floki.Selector.Functional',
                        'Elixir.Floki.Selector.Parser',
                        'Elixir.Floki.Selector.PseudoClass',
                        'Elixir.Floki.Selector.Tokenizer',
                        'Elixir.Floki.TextExtractor','Elixir.Floki.Traversal',
                        'Elixir.Inspect.Floki.HTMLTree',
                        'Elixir.String.Chars.Floki.Selector',
                        'Elixir.String.Chars.Floki.Selector.AttributeSelector',
                        'Elixir.String.Chars.Floki.Selector.Combinator',
                        'Elixir.String.Chars.Floki.Selector.Functional',
                        'Elixir.String.Chars.Floki.Selector.PseudoClass',
                        floki_mochi_html,floki_selector_lexer]},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger]},
              {description,"Floki is a simple HTML parser that enables search for nodes using CSS selectors."},
              {registered,[]},
              {vsn,"0.38.0"}]}.

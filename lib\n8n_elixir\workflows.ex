defmodule N8nElixir.Workflows.Workflow do
  @moduledoc """
  工作流定义模型
  
  参考n8n的Workflow类设计，存储用户创建的工作流定义，包括节点配置、连接关系等
  """
  use Ecto.Schema
  import Ecto.Changeset
  import Ecto.Query

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "workflows" do
    field :name, :string
    field :description, :string
    field :version, :integer, default: 1
    field :status, Ecto.Enum, values: [:draft, :active, :inactive, :archived], default: :draft
    field :tags, {:array, :string}, default: []
    
    # 工作流定义 - 类似n8n的connections和nodes结构
    field :nodes, :map, default: %{}
    field :connections, :map, default: %{}
    
    # 工作流设置 - 参考n8n的IWorkflowSettings
    field :settings, :map, default: %{}
    
    # 静态数据 - 用于节点间共享数据
    field :static_data, :map, default: %{}
    
    # 测试数据 - 用于调试
    field :pin_data, :map, default: %{}
    
    # 执行设置
    field :execution_timeout, :integer, default: 3600 # 默认1小时超时
    field :max_execution_time, :integer, default: 300 # 默认5分钟最大执行时间
    field :retry_on_failure, :boolean, default: false
    field :max_retries, :integer, default: 3
    
    # 所有者和权限
    belongs_to :owner, N8nElixir.Accounts.User
    belongs_to :team, N8nElixir.Accounts.Team
    
    # 关联关系
    has_many :executions, N8nElixir.Executions.Execution
    has_many :triggers, N8nElixir.Triggers.Trigger
    has_many :workflow_credentials, N8nElixir.Credentials.WorkflowCredential
    has_many :credentials, through: [:workflow_credentials, :credential]
    
    # 版本控制
    field :parent_workflow_id, :binary_id
    has_many :versions, __MODULE__, foreign_key: :parent_workflow_id
    belongs_to :parent_workflow, __MODULE__, foreign_key: :parent_workflow_id
    
    timestamps()
  end

  @doc false
  def changeset(workflow, attrs) do
    workflow
    |> cast(attrs, [
      :name, :description, :version, :status, :tags, :nodes, :connections, :settings,
      :static_data, :pin_data, :execution_timeout, :max_execution_time, 
      :retry_on_failure, :max_retries, :owner_id, :team_id, :parent_workflow_id
    ])
    |> validate_required([:name, :nodes, :connections, :owner_id])
    |> validate_length(:name, min: 1, max: 255)
    |> validate_length(:description, max: 1000)
    |> validate_number(:version, greater_than: 0)
    |> validate_number(:execution_timeout, greater_than: 0)
    |> validate_number(:max_execution_time, greater_than: 0)
    |> validate_number(:max_retries, greater_than_or_equal_to: 0)
    |> validate_workflow_structure()
    |> unique_constraint(:name, name: :workflows_name_owner_id_index)
  end

  defp validate_workflow_structure(changeset) do
    nodes = get_field(changeset, :nodes)
    connections = get_field(changeset, :connections)
    
    cond do
      is_nil(nodes) or not is_map(nodes) ->
        add_error(changeset, :nodes, "must be a valid map structure")
      is_nil(connections) or not is_map(connections) ->
        add_error(changeset, :connections, "must be a valid map structure")
      true ->
        validate_nodes_and_connections(changeset, nodes, connections)
    end
  end

  defp validate_nodes_and_connections(changeset, nodes, connections) do
    # 验证所有连接中引用的节点都存在
    node_names = Map.keys(nodes)
    
    connection_errors = 
      Enum.reduce(connections, [], fn {source_node, node_connections}, acc ->
        if source_node not in node_names do
          ["Connection source node '#{source_node}' does not exist" | acc]
        else
          # 检查目标节点
          target_errors = 
            Enum.reduce(node_connections, [], fn connection_list, target_acc ->
              Enum.reduce(connection_list, target_acc, fn connection, inner_acc ->
                target_node = Map.get(connection, "node")
                if target_node && target_node not in node_names do
                  ["Connection target node '#{target_node}' does not exist" | inner_acc]
                else
                  inner_acc
                end
              end)
            end)
          target_errors ++ acc
        end
      end)
    
    if connection_errors != [] do
      add_error(changeset, :connections, Enum.join(connection_errors, "; "))
    else
      changeset
    end
  end
end

defmodule N8nElixir.Workflows.Node do
  @moduledoc """
  工作流节点模型
  
  参考n8n的INode接口设计，存储节点的具体配置
  虽然节点信息主要存储在workflow的nodes字段中，
  但我们也可以单独存储节点配置用于查询和分析
  """
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "nodes" do
    field :node_id, :string # 在工作流中的唯一标识
    field :name, :string
    field :type, :string # 节点类型，如 "http_request", "code", "webhook"
    field :type_version, :integer, default: 1 # 节点类型版本
    field :position, :map # 在画布中的位置 {x: 100, y: 200}
    field :parameters, :map, default: %{} # 节点参数配置
    field :credentials, :map, default: %{} # 节点关联的凭证
    field :webhook_id, :string # webhook节点的ID
    field :disabled, :boolean, default: false # 是否禁用
    field :notes, :string # 节点备注
    field :continue_on_fail, :boolean, default: false # 失败时是否继续
    field :always_output_data, :boolean, default: false # 是否总是输出数据
    field :retry_on_fail, :boolean, default: false # 失败时是否重试
    field :max_tries, :integer, default: 3 # 最大重试次数
    field :wait_between_tries, :integer, default: 1000 # 重试间隔毫秒
    
    belongs_to :workflow, N8nElixir.Workflows.Workflow
    
    timestamps()
  end

  @doc false
  def changeset(node, attrs) do
    node
    |> cast(attrs, [
      :node_id, :name, :type, :type_version, :position, :parameters, :credentials,
      :webhook_id, :disabled, :notes, :continue_on_fail, :always_output_data,
      :retry_on_fail, :max_tries, :wait_between_tries, :workflow_id
    ])
    |> validate_required([:node_id, :name, :type, :workflow_id])
    |> validate_length(:name, min: 1, max: 255)
    |> validate_length(:type, min: 1, max: 100)
    |> validate_number(:type_version, greater_than: 0)
    |> validate_number(:max_tries, greater_than: 0)
    |> validate_number(:wait_between_tries, greater_than_or_equal_to: 0)
    |> validate_position()
    |> unique_constraint(:node_id, name: :nodes_node_id_workflow_id_index)
  end

  defp validate_position(changeset) do
    case get_field(changeset, :position) do
      %{"x" => x, "y" => y} when is_number(x) and is_number(y) ->
        changeset
      %{x: x, y: y} when is_number(x) and is_number(y) ->
        changeset
      _ ->
        add_error(changeset, :position, "must contain valid x and y coordinates")
    end
  end
end

defmodule N8nElixir.Executions.Execution do
  @moduledoc """
  工作流执行记录
  
  参考n8n的WorkflowExecution接口，记录每次工作流执行的基本信息和状态
  """
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "executions" do
    field :status, Ecto.Enum, 
      values: [:new, :running, :success, :error, :canceled, :crashed, :waiting], 
      default: :new
    
    field :mode, Ecto.Enum,
      values: [:manual, :trigger, :webhook, :retry, :cli],
      default: :manual
    
    field :started_at, :utc_datetime
    field :stopped_at, :utc_datetime
    field :finished, :boolean, default: false
    field :retry_of, :binary_id # 如果是重试执行，指向原执行ID
    field :retry_success_id, :binary_id # 成功重试的执行ID
    
    # 执行数据
    field :data, :map, default: %{} # 执行过程中的数据
    field :workflow_data, :map, default: %{} # 工作流数据快照
    
    # 错误信息
    field :error, :map # 错误详情
    
    # 统计信息
    field :node_execution_count, :integer, default: 0
    field :total_duration, :integer # 总执行时间（毫秒）
    
    # 关联关系
    belongs_to :workflow, N8nElixir.Workflows.Workflow
    belongs_to :triggered_by, N8nElixir.Accounts.User
    has_many :execution_data, N8nElixir.Executions.ExecutionData
    
    timestamps()
  end

  @doc false
  def changeset(execution, attrs) do
    execution
    |> cast(attrs, [
      :status, :mode, :started_at, :stopped_at, :finished, :retry_of, :retry_success_id,
      :data, :workflow_data, :error, :node_execution_count, :total_duration,
      :workflow_id, :triggered_by_id
    ])
    |> validate_required([:workflow_id, :mode])
    |> validate_number(:node_execution_count, greater_than_or_equal_to: 0)
    |> validate_number(:total_duration, greater_than_or_equal_to: 0)
    |> validate_execution_times()
  end

  defp validate_execution_times(changeset) do
    started_at = get_field(changeset, :started_at)
    stopped_at = get_field(changeset, :stopped_at)
    
    if started_at && stopped_at && DateTime.compare(stopped_at, started_at) == :lt do
      add_error(changeset, :stopped_at, "must be after started_at")
    else
      changeset
    end
  end
end

defmodule N8nElixir.Executions.ExecutionData do
  @moduledoc """
  执行数据模型
  
  参考n8n的NodeExecutionData，存储每个节点的执行数据
  """
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "execution_data" do
    field :node_name, :string
    field :node_type, :string
    field :start_time, :utc_datetime
    field :execution_time, :integer # 执行时间（毫秒）
    field :source, :map # 来源数据
    field :data, :map # 输出数据
    field :error, :map # 错误信息
    
    belongs_to :execution, N8nElixir.Executions.Execution
    
    timestamps()
  end

  @doc false
  def changeset(execution_data, attrs) do
    execution_data
    |> cast(attrs, [:node_name, :node_type, :start_time, :execution_time, :source, :data, :error, :execution_id])
    |> validate_required([:node_name, :node_type, :execution_id])
    |> validate_number(:execution_time, greater_than_or_equal_to: 0)
  end
end

defmodule N8nElixir.BinaryData do
  @moduledoc """
  二进制数据模型
  
  参考n8n的IBinaryData接口，处理文件上传下载等二进制数据
  """
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "binary_data" do
    field :data, :binary # 二进制数据内容
    field :mime_type, :string
    field :file_type, Ecto.Enum, 
      values: [:text, :json, :image, :audio, :video, :pdf, :html, :other],
      default: :other
    field :file_name, :string
    field :file_extension, :string
    field :file_size, :integer
    field :directory, :string
    field :checksum, :string # 文件校验和
    
    # 关联到执行和节点
    belongs_to :execution, N8nElixir.Executions.Execution
    field :node_name, :string
    
    timestamps()
  end

  @doc false
  def changeset(binary_data, attrs) do
    binary_data
    |> cast(attrs, [:data, :mime_type, :file_type, :file_name, :file_extension, 
                    :file_size, :directory, :checksum, :execution_id, :node_name])
    |> validate_required([:data, :mime_type])
    |> validate_number(:file_size, greater_than_or_equal_to: 0)
    |> validate_length(:file_name, max: 255)
    |> validate_length(:file_extension, max: 10)
  end
end
defmodule N8nElixir.BinaryData do
  @moduledoc """
  二进制数据处理模块

  参考n8n的Binary Data Manager，提供文件上传、下载、存储和处理功能
  支持多种文件格式和存储后端
  """

  require Logger

  @default_storage_path Application.compile_env(:n8n_elixir, :binary_data_storage_path, "/tmp/n8n-binary-data")
  @max_file_size Application.compile_env(:n8n_elixir, :max_binary_file_size, 50 * 1024 * 1024) # 50MB
  @allowed_mime_types Application.compile_env(:n8n_elixir, :allowed_mime_types, [
    "image/jpeg", "image/png", "image/gif", "image/webp",
    "application/pdf", "text/plain", "text/csv",
    "application/json", "application/xml",
    "application/vnd.ms-excel", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/zip", "application/x-zip-compressed"
  ])

  @type binary_data :: %{
    id: String.t(),
    file_name: String.t(),
    mime_type: String.t(),
    file_size: integer(),
    file_path: String.t(),
    checksum: String.t(),
    metadata: map(),
    created_at: DateTime.t(),
    execution_id: String.t() | nil
  }

  @doc """
  存储二进制数据
  """
  def store_binary_data(data, filename, opts \\ []) do
    mime_type = determine_mime_type(data, filename)
    
    # 验证文件类型和大小
    case validate_binary_data(data, mime_type, opts) do
      :ok ->
        do_store_binary_data(data, filename, mime_type, opts)
      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  从文件路径存储二进制数据
  """
  def store_binary_data_from_file(file_path, opts \\ []) do
    case File.read(file_path) do
      {:ok, data} ->
        filename = Path.basename(file_path)
        store_binary_data(data, filename, opts)
      {:error, reason} ->
        {:error, "Failed to read file: #{reason}"}
    end
  end

  @doc """
  获取二进制数据
  """
  def get_binary_data(binary_data_id) do
    case find_binary_data_record(binary_data_id) do
      nil ->
        {:error, :not_found}
      binary_data ->
        case File.read(binary_data.file_path) do
          {:ok, data} ->
            {:ok, data, binary_data}
          {:error, reason} ->
            {:error, "Failed to read binary data: #{reason}"}
        end
    end
  end

  @doc """
  获取二进制数据流
  """
  def get_binary_data_stream(binary_data_id) do
    case find_binary_data_record(binary_data_id) do
      nil ->
        {:error, :not_found}
      binary_data ->
        if File.exists?(binary_data.file_path) do
          {:ok, File.stream!(binary_data.file_path, [], 64_000), binary_data}
        else
          {:error, :file_not_found}
        end
    end
  end

  @doc """
  删除二进制数据
  """
  def delete_binary_data(binary_data_id) do
    case find_binary_data_record(binary_data_id) do
      nil ->
        {:error, :not_found}
      binary_data ->
        # 删除文件
        File.rm(binary_data.file_path)
        
        # 删除数据库记录
        delete_binary_data_record(binary_data_id)
        
        Logger.info("Deleted binary data: #{binary_data_id}")
        :ok
    end
  end

  @doc """
  清理过期的二进制数据
  """
  def cleanup_expired_binary_data(max_age_hours \\ 24) do
    cutoff_time = DateTime.add(DateTime.utc_now(), -max_age_hours * 60 * 60, :second)
    
    expired_records = list_expired_binary_data(cutoff_time)
    
    Enum.each(expired_records, fn binary_data ->
      delete_binary_data(binary_data.id)
    end)
    
    Logger.info("Cleaned up #{length(expired_records)} expired binary data records")
    {:ok, length(expired_records)}
  end

  @doc """
  转换二进制数据格式
  """
  def convert_binary_data(binary_data_id, target_format, opts \\ []) do
    case get_binary_data(binary_data_id) do
      {:ok, data, metadata} ->
        case perform_conversion(data, metadata.mime_type, target_format, opts) do
          {:ok, converted_data, new_mime_type} ->
            # 存储转换后的数据
            new_filename = generate_converted_filename(metadata.file_name, target_format)
            store_binary_data(converted_data, new_filename, 
              execution_id: Map.get(opts, :execution_id),
              source_id: binary_data_id
            )
          {:error, reason} ->
            {:error, reason}
        end
      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  创建二进制数据的缩略图
  """
  def create_thumbnail(binary_data_id, opts \\ []) do
    width = Keyword.get(opts, :width, 200)
    height = Keyword.get(opts, :height, 200)
    
    case get_binary_data(binary_data_id) do
      {:ok, data, metadata} ->
        if is_image?(metadata.mime_type) do
          case generate_thumbnail(data, width, height) do
            {:ok, thumbnail_data} ->
              thumbnail_filename = "thumb_#{metadata.file_name}"
              store_binary_data(thumbnail_data, thumbnail_filename,
                execution_id: Map.get(opts, :execution_id),
                source_id: binary_data_id,
                is_thumbnail: true
              )
            {:error, reason} ->
              {:error, reason}
          end
        else
          {:error, "Not an image file"}
        end
      {:error, reason} ->
        {:error, reason}
    end
  end

  @doc """
  获取二进制数据的元信息
  """
  def get_binary_data_info(binary_data_id) do
    case find_binary_data_record(binary_data_id) do
      nil ->
        {:error, :not_found}
      binary_data ->
        info = %{
          id: binary_data.id,
          file_name: binary_data.file_name,
          mime_type: binary_data.mime_type,
          file_size: binary_data.file_size,
          checksum: binary_data.checksum,
          metadata: binary_data.metadata,
          created_at: binary_data.created_at,
          execution_id: binary_data.execution_id,
          file_exists: File.exists?(binary_data.file_path)
        }
        {:ok, info}
    end
  end

  @doc """
  批量处理二进制数据
  """
  def batch_process_binary_data(binary_data_ids, operation, opts \\ []) do
    results = Enum.map(binary_data_ids, fn id ->
      case operation do
        :delete -> delete_binary_data(id)
        {:convert, format} -> convert_binary_data(id, format, opts)
        {:thumbnail, thumb_opts} -> create_thumbnail(id, Keyword.merge(opts, thumb_opts))
        _ -> {:error, "Unknown operation"}
      end
    end)
    
    successes = Enum.count(results, fn result -> match?({:ok, _}, result) or result == :ok end)
    failures = length(results) - successes
    
    {:ok, %{total: length(results), successes: successes, failures: failures, results: results}}
  end

  # 私有实现函数

  defp do_store_binary_data(data, filename, mime_type, opts) do
    binary_data_id = generate_binary_data_id()
    execution_id = Keyword.get(opts, :execution_id)
    
    # 创建存储目录
    storage_dir = get_storage_directory(execution_id)
    File.mkdir_p!(storage_dir)
    
    # 生成文件路径
    file_path = Path.join(storage_dir, "#{binary_data_id}_#{filename}")
    
    # 写入文件
    case File.write(file_path, data) do
      :ok ->
        # 计算校验和
        checksum = :crypto.hash(:sha256, data) |> Base.encode16()
        
        # 提取元数据
        metadata = extract_metadata(data, mime_type, opts)
        
        # 创建二进制数据记录
        binary_data = %{
          id: binary_data_id,
          file_name: filename,
          mime_type: mime_type,
          file_size: byte_size(data),
          file_path: file_path,
          checksum: checksum,
          metadata: metadata,
          created_at: DateTime.utc_now(),
          execution_id: execution_id
        }
        
        # 保存到数据库
        case create_binary_data_record(binary_data) do
          {:ok, _record} ->
            Logger.debug("Stored binary data: #{binary_data_id} (#{byte_size(data)} bytes)")
            {:ok, binary_data_id}
          {:error, reason} ->
            # 清理文件
            File.rm(file_path)
            {:error, "Failed to create database record: #{reason}"}
        end
      
      {:error, reason} ->
        {:error, "Failed to write file: #{reason}"}
    end
  end

  defp validate_binary_data(data, mime_type, opts) do
    size = byte_size(data)
    max_size = Keyword.get(opts, :max_size, @max_file_size)
    allowed_types = Keyword.get(opts, :allowed_mime_types, @allowed_mime_types)
    
    cond do
      size > max_size ->
        {:error, "File size (#{size} bytes) exceeds maximum allowed size (#{max_size} bytes)"}
      
      not Enum.member?(allowed_types, mime_type) ->
        {:error, "MIME type '#{mime_type}' is not allowed"}
      
      true ->
        :ok
    end
  end

  defp determine_mime_type(data, filename) do
    # 首先尝试从文件扩展名推断
    extension_mime = get_mime_type_from_extension(filename)
    
    # 然后尝试从文件内容推断
    content_mime = get_mime_type_from_content(data)
    
    # 优先使用内容推断的MIME类型
    content_mime || extension_mime || "application/octet-stream"
  end

  defp get_mime_type_from_extension(filename) do
    case Path.extname(filename) |> String.downcase() do
      ".jpg" -> "image/jpeg"
      ".jpeg" -> "image/jpeg"
      ".png" -> "image/png"
      ".gif" -> "image/gif"
      ".webp" -> "image/webp"
      ".pdf" -> "application/pdf"
      ".txt" -> "text/plain"
      ".csv" -> "text/csv"
      ".json" -> "application/json"
      ".xml" -> "application/xml"
      ".xls" -> "application/vnd.ms-excel"
      ".xlsx" -> "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      ".doc" -> "application/msword"
      ".docx" -> "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
      ".zip" -> "application/zip"
      _ -> nil
    end
  end

  defp get_mime_type_from_content(data) do
    # 简单的文件头检测
    case data do
      <<0xFF, 0xD8, 0xFF, _::binary>> -> "image/jpeg"
      <<0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, _::binary>> -> "image/png"
      <<"GIF87a", _::binary>> -> "image/gif"
      <<"GIF89a", _::binary>> -> "image/gif"
      <<"RIFF", _::binary-size(4), "WEBP", _::binary>> -> "image/webp"
      <<"%PDF-", _::binary>> -> "application/pdf"
      <<"PK", 0x03, 0x04, _::binary>> -> "application/zip"
      _ -> nil
    end
  end

  defp extract_metadata(data, mime_type, opts) do
    base_metadata = %{
      size: byte_size(data),
      created_by: Keyword.get(opts, :created_by, "system"),
      source_id: Keyword.get(opts, :source_id),
      is_thumbnail: Keyword.get(opts, :is_thumbnail, false)
    }
    
    # 根据MIME类型提取特定元数据
    type_specific_metadata = case mime_type do
      "image/" <> _ -> extract_image_metadata(data)
      "application/pdf" -> extract_pdf_metadata(data)
      "text/" <> _ -> extract_text_metadata(data)
      _ -> %{}
    end
    
    Map.merge(base_metadata, type_specific_metadata)
  end

  defp extract_image_metadata(data) do
    # 这里可以使用Elixir的图像处理库来提取EXIF数据等
    # 现在只提供基本信息
    %{
      type: "image",
      format: determine_image_format(data)
    }
  end

  defp extract_pdf_metadata(_data) do
    %{
      type: "document",
      format: "pdf"
    }
  end

  defp extract_text_metadata(data) do
    lines = String.split(data, ["\n", "\r\n"]) |> length()
    
    %{
      type: "text",
      lines: lines,
      characters: String.length(data)
    }
  end

  defp determine_image_format(data) do
    case data do
      <<0xFF, 0xD8, 0xFF, _::binary>> -> "jpeg"
      <<0x89, 0x50, 0x4E, 0x47, _::binary>> -> "png"
      <<"GIF", _::binary>> -> "gif"
      <<"RIFF", _::binary-size(4), "WEBP", _::binary>> -> "webp"
      _ -> "unknown"
    end
  end

  defp perform_conversion(data, source_mime, target_format, _opts) do
    case {source_mime, target_format} do
      {"image/" <> _, "png"} ->
        # 使用图像处理库转换
        convert_image_to_png(data)
      
      {"image/" <> _, "jpeg"} ->
        convert_image_to_jpeg(data)
      
      {"text/plain", "json"} ->
        convert_text_to_json(data)
      
      {"text/csv", "json"} ->
        convert_csv_to_json(data)
      
      _ ->
        {:error, "Conversion from #{source_mime} to #{target_format} not supported"}
    end
  end

  defp convert_image_to_png(data) do
    # 在实际实现中，这里会使用图像处理库
    # 现在返回原始数据作为示例
    {:ok, data, "image/png"}
  end

  defp convert_image_to_jpeg(data) do
    # 在实际实现中，这里会使用图像处理库
    {:ok, data, "image/jpeg"}
  end

  defp convert_text_to_json(data) do
    try do
      json_data = %{
        content: data,
        lines: String.split(data, ["\n", "\r\n"]),
        word_count: String.split(data, ~r/\s+/) |> length(),
        character_count: String.length(data)
      }
      
      json_string = Jason.encode!(json_data)
      {:ok, json_string, "application/json"}
    rescue
      error ->
        {:error, "Failed to convert text to JSON: #{inspect(error)}"}
    end
  end

  defp convert_csv_to_json(data) do
    try do
      lines = String.split(data, ["\n", "\r\n"])
      [headers | rows] = Enum.reject(lines, &(&1 == ""))
      
      header_list = String.split(headers, ",")
      
      json_data = Enum.map(rows, fn row ->
        values = String.split(row, ",")
        Enum.zip(header_list, values) |> Map.new()
      end)
      
      json_string = Jason.encode!(json_data)
      {:ok, json_string, "application/json"}
    rescue
      error ->
        {:error, "Failed to convert CSV to JSON: #{inspect(error)}"}
    end
  end

  defp generate_thumbnail(data, width, height) do
    # 在实际实现中，这里会使用图像处理库生成缩略图
    # 现在返回原始数据作为示例
    Logger.info("Generating thumbnail: #{width}x#{height}")
    {:ok, data}
  end

  defp generate_converted_filename(original_filename, target_format) do
    base_name = Path.rootname(original_filename)
    extension = case target_format do
      "png" -> ".png"
      "jpeg" -> ".jpg"
      "json" -> ".json"
      "pdf" -> ".pdf"
      _ -> ".bin"
    end
    
    "#{base_name}_converted#{extension}"
  end

  defp is_image?(mime_type) do
    String.starts_with?(mime_type, "image/")
  end

  defp generate_binary_data_id() do
    UUID.uuid4()
  end

  defp get_storage_directory(execution_id) do
    if execution_id do
      Path.join([@default_storage_path, "executions", execution_id])
    else
      Path.join([@default_storage_path, "global"])
    end
  end

  # 数据库操作 - 这些函数需要与实际的数据库集成

  defp create_binary_data_record(binary_data) do
    # 在实际实现中，这里会创建数据库记录
    # 现在使用ETS表作为临时存储
    :ets.insert(:binary_data_storage, {binary_data.id, binary_data})
    {:ok, binary_data}
  end

  defp find_binary_data_record(binary_data_id) do
    # 从ETS表查找
    case :ets.lookup(:binary_data_storage, binary_data_id) do
      [{^binary_data_id, binary_data}] -> binary_data
      [] -> nil
    end
  end

  defp delete_binary_data_record(binary_data_id) do
    :ets.delete(:binary_data_storage, binary_data_id)
    :ok
  end

  defp list_expired_binary_data(cutoff_time) do
    # 在实际实现中，这里会查询数据库
    :ets.tab2list(:binary_data_storage)
    |> Enum.map(fn {_id, binary_data} -> binary_data end)
    |> Enum.filter(fn binary_data ->
      DateTime.compare(binary_data.created_at, cutoff_time) == :lt
    end)
  end

  # 初始化ETS表用于临时存储
  def init_storage() do
    try do
      :ets.new(:binary_data_storage, [:named_table, :public, :set])
    rescue
      ArgumentError ->
        # 表已存在
        :ok
    end
  end
end
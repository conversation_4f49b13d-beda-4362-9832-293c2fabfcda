defmodule N8nElixir.Repo.Migrations.CreateExecutionData do
  use Ecto.Migration

  def change do
    create table(:execution_data, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :node_name, :string, null: false
      add :node_type, :string, null: false
      add :start_time, :utc_datetime
      add :execution_time, :integer
      add :source, :map
      add :data, :map
      add :error, :map
      add :execution_id, references(:executions, type: :binary_id), null: false
      
      timestamps()
    end

    create index(:execution_data, [:execution_id])
    create index(:execution_data, [:node_name])
    create index(:execution_data, [:start_time])
  end
end
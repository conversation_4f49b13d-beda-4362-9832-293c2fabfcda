{application,hammer,
             [{modules,['Elixir.Hammer','Elixir.Hammer.Application',
                        'Elixir.Hammer.Backend','Elixir.Hammer.Backend.ETS',
                        'Elixir.Hammer.Supervisor','Elixir.Hammer.Utils']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger,runtime_tools,
                             poolboy]},
              {description,"A rate-limiter with plugable backends."},
              {registered,[]},
              {vsn,"6.2.1"},
              {mod,{'Elixir.Hammer.Application',[]}}]}.

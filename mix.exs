defmodule N8nElixir.MixProject do
  use Mix.Project

  def project do
    [
      app: :n8n_elixir,
      version: "0.1.0",
      elixir: "~> 1.15",
      elixirc_paths: elixirc_paths(Mix.env()),
      start_permanent: Mix.env() == :prod,
      aliases: aliases(),
      deps: deps(),
      releases: [
        n8n_elixir: [
          include_executables_for: [:unix],
          applications: [runtime_tools: :permanent]
        ]
      ]
    ]
  end

  def application do
    [
      mod: {N8nElixir.Application, []},
      extra_applications: [:logger, :runtime_tools]
    ]
  end

  defp elixirc_paths(:test), do: ["lib", "test/support"]
  defp elixirc_paths(_), do: ["lib"]

  defp deps do
    [
      # Phoenix Framework
      {:phoenix, "~> 1.7.0"},
      {:phoenix_ecto, "~> 4.4"},
      {:phoenix_html, "~> 3.3"},
      {:phoenix_live_reload, "~> 1.2", only: :dev},
      {:phoenix_live_view, "~> 0.20.0"},
      {:phoenix_live_dashboard, "~> 0.8.0"},
      {:floki, ">= 0.30.0", only: :test},
      {:phoenix_view, "~> 2.0"},
      
      # Database
      {:ecto_sql, "~> 3.10"},
      {:postgrex, ">= 0.0.0"},
      
      # JSON
      {:jason, "~> 1.2"},
      
      # HTTP Client
      {:tesla, "~> 1.7"},
      {:finch, "~> 0.16"},
      {:httpoison, "~> 2.1"},
      
      # Scheduler
      {:quantum, "~> 3.5"},
      
      # Authentication
      {:guardian, "~> 2.3"},
      {:bcrypt_elixir, "~> 3.0"},
      
      # Validation
      {:ex_json_schema, "~> 0.10"},
      
      # Crypto
      {:cloak, "~> 1.1"},
      
      # UUID
      {:uuid, "~> 1.1"},
      
      # Telemetry
      {:telemetry_metrics, "~> 0.6"},
      {:telemetry_poller, "~> 1.0"},
      
      # Monitoring
      {:phoenix_pubsub, "~> 2.1"},
      {:swoosh, "~> 1.3"},
      
      # Development tools
      {:ex_doc, "~> 0.29", only: :dev, runtime: false},
      {:credo, "~> 1.7", only: [:dev, :test], runtime: false},
      {:dialyxir, "~> 1.3", only: [:dev], runtime: false},
      {:excoveralls, "~> 0.16", only: :test},
      
      # Testing
      {:ex_machina, "~> 2.7", only: :test},
      {:bypass, "~> 2.1", only: :test},
      
      # Assets
      {:esbuild, "~> 0.7", runtime: Mix.env() == :dev},
      {:tailwind, "~> 0.2.0", runtime: Mix.env() == :dev},
      
      # CORS
      {:cors_plug, "~> 3.0"},
      
      # Rate limiting
      {:hammer, "~> 6.1"},
      
      # Background jobs
      {:oban, "~> 2.15"},
      
      # Logging
      {:logger_json, "~> 5.1"},
      
      # File upload
      {:arc, "~> 0.11"},
      {:arc_ecto, "~> 0.11"},
      {:ex_aws, "~> 2.4"},
      {:ex_aws_s3, "~> 2.4"},
      {:sweet_xml, "~> 0.7"},
      
      # Cron expressions
      {:crontab, "~> 1.1"},
      
      # XML/HTML parsing
      {:floki, "~> 0.34"},
      {:sweet_xml, "~> 0.7"},
      
      # CSV processing
      {:csv, "~> 3.0"},
      
      # Development
      {:plug_cowboy, "~> 2.5"},
      {:gettext, "~> 0.20"}
    ]
  end

  defp aliases do
    [
      setup: ["deps.get", "ecto.setup", "assets.setup", "assets.build"],
      "ecto.setup": ["ecto.create", "ecto.migrate", "run priv/repo/seeds.exs"],
      "ecto.reset": ["ecto.drop", "ecto.setup"],
      test: ["ecto.create --quiet", "ecto.migrate --quiet", "test"],
      "assets.setup": ["tailwind.install --if-missing", "esbuild.install --if-missing"],
      "assets.build": ["tailwind default", "esbuild default"],
      "assets.deploy": ["tailwind default --minify", "esbuild default --minify", "phx.digest"]
    ]
  end
end
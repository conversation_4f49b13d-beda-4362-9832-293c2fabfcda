defmodule N8nElixir.RetryManager do
  @moduledoc """
  重试管理器

  专门管理工作流执行的重试逻辑和策略
  提供灵活的重试配置和执行监控
  """

  use GenServer
  require Logger

  alias N8nElixir.{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, WorkflowRunner}

  @registry_name __MODULE__

  # 客户端API
  def start_link(init_arg) do
    GenServer.start_link(__MODULE__, init_arg, name: @registry_name)
  end

  @doc """
  注册重试任务
  """
  def schedule_retry(execution_id, retry_config, error_context) do
    GenServer.call(@registry_name, {:schedule_retry, execution_id, retry_config, error_context})
  end

  @doc """
  取消重试任务
  """
  def cancel_retry(execution_id) do
    GenServer.call(@registry_name, {:cancel_retry, execution_id})
  end

  @doc """
  获取重试状态
  """
  def get_retry_status(execution_id) do
    GenServer.call(@registry_name, {:get_retry_status, execution_id})
  end

  @doc """
  列出所有活跃的重试任务
  """
  def list_active_retries() do
    GenServer.call(@registry_name, :list_active_retries)
  end

  # 服务器回调
  @impl true
  def init(_init_arg) do
    # 启动定时器用于处理重试任务
    schedule_retry_processor()
    
    state = %{
      retry_tasks: %{},
      retry_history: %{},
      processor_timer: nil
    }

    {:ok, state}
  end

  @impl true
  def handle_call({:schedule_retry, execution_id, retry_config, error_context}, _from, state) do
    case create_retry_task(execution_id, retry_config, error_context) do
      {:ok, retry_task} ->
        updated_tasks = Map.put(state.retry_tasks, execution_id, retry_task)
        new_state = %{state | retry_tasks: updated_tasks}
        
        Logger.info("Scheduled retry for execution #{execution_id}")
        {:reply, {:ok, retry_task.task_id}, new_state}
      
      {:error, reason} ->
        {:reply, {:error, reason}, state}
    end
  end

  @impl true
  def handle_call({:cancel_retry, execution_id}, _from, state) do
    case Map.get(state.retry_tasks, execution_id) do
      nil ->
        {:reply, {:error, :not_found}, state}
      
      retry_task ->
        # 取消定时器
        if retry_task.timer_ref do
          Process.cancel_timer(retry_task.timer_ref)
        end
        
        # 更新任务状态
        updated_task = %{retry_task | status: :cancelled, cancelled_at: DateTime.utc_now()}
        updated_history = Map.put(state.retry_history, execution_id, updated_task)
        updated_tasks = Map.delete(state.retry_tasks, execution_id)
        
        new_state = %{state | 
          retry_tasks: updated_tasks,
          retry_history: updated_history
        }
        
        Logger.info("Cancelled retry for execution #{execution_id}")
        {:reply, :ok, new_state}
    end
  end

  @impl true
  def handle_call({:get_retry_status, execution_id}, _from, state) do
    case Map.get(state.retry_tasks, execution_id) do
      nil ->
        # 检查历史记录
        case Map.get(state.retry_history, execution_id) do
          nil -> {:reply, {:error, :not_found}, state}
          history -> {:reply, {:ok, format_retry_status(history)}, state}
        end
      
      retry_task ->
        {:reply, {:ok, format_retry_status(retry_task)}, state}
    end
  end

  @impl true
  def handle_call(:list_active_retries, _from, state) do
    active_retries = 
      state.retry_tasks
      |> Enum.map(fn {execution_id, retry_task} ->
        %{
          execution_id: execution_id,
          task_id: retry_task.task_id,
          attempt: retry_task.current_attempt,
          max_attempts: retry_task.max_attempts,
          next_retry_at: retry_task.next_retry_at,
          strategy: retry_task.strategy,
          status: retry_task.status
        }
      end)
    
    {:reply, active_retries, state}
  end

  @impl true
  def handle_info({:retry_execution, execution_id}, state) do
    case Map.get(state.retry_tasks, execution_id) do
      nil ->
        # 任务不存在，可能已被取消
        {:noreply, state}
      
      retry_task ->
        case execute_retry_attempt(retry_task) do
          {:success, updated_task} ->
            # 重试成功，移除任务
            updated_history = Map.put(state.retry_history, execution_id, updated_task)
            updated_tasks = Map.delete(state.retry_tasks, execution_id)
            
            new_state = %{state | 
              retry_tasks: updated_tasks,
              retry_history: updated_history
            }
            
            Logger.info("Retry succeeded for execution #{execution_id}")
            {:noreply, new_state}
          
          {:failed, updated_task} ->
            if updated_task.current_attempt >= updated_task.max_attempts do
              # 达到最大重试次数，停止重试
              final_task = %{updated_task | status: :max_attempts_reached}
              updated_history = Map.put(state.retry_history, execution_id, final_task)
              updated_tasks = Map.delete(state.retry_tasks, execution_id)
              
              new_state = %{state | 
                retry_tasks: updated_tasks,
                retry_history: updated_history
              }
              
              Logger.warn("Max retry attempts reached for execution #{execution_id}")
              {:noreply, new_state}
            else
              # 继续重试，计算下次重试时间
              next_retry_task = schedule_next_retry(updated_task)
              updated_tasks = Map.put(state.retry_tasks, execution_id, next_retry_task)
              
              new_state = %{state | retry_tasks: updated_tasks}
              
              Logger.info("Scheduled next retry for execution #{execution_id} (attempt #{next_retry_task.current_attempt + 1})")
              {:noreply, new_state}
            end
          
          {:error, reason} ->
            # 重试过程中出现错误
            error_task = %{retry_task | 
              status: :error, 
              error: reason,
              failed_at: DateTime.utc_now()
            }
            
            updated_history = Map.put(state.retry_history, execution_id, error_task)
            updated_tasks = Map.delete(state.retry_tasks, execution_id)
            
            new_state = %{state | 
              retry_tasks: updated_tasks,
              retry_history: updated_history
            }
            
            Logger.error("Retry failed with error for execution #{execution_id}: #{inspect(reason)}")
            {:noreply, new_state}
        end
    end
  end

  @impl true
  def handle_info(:process_retries, state) do
    # 定期处理重试任务
    current_time = DateTime.utc_now()
    
    # 检查是否有需要执行的重试任务
    ready_tasks = 
      state.retry_tasks
      |> Enum.filter(fn {_execution_id, retry_task} ->
        DateTime.compare(current_time, retry_task.next_retry_at) != :lt
      end)
    
    # 为准备好的任务发送重试消息
    Enum.each(ready_tasks, fn {execution_id, _retry_task} ->
      send(self(), {:retry_execution, execution_id})
    end)
    
    # 重新调度处理器
    schedule_retry_processor()
    
    {:noreply, state}
  end

  @impl true
  def handle_info(_msg, state) do
    {:noreply, state}
  end

  # 私有实现函数

  defp create_retry_task(execution_id, retry_config, error_context) do
    try do
      strategy = Map.get(retry_config, :strategy, :exponential_backoff)
      max_attempts = Map.get(retry_config, :max_attempts, 3)
      base_delay = Map.get(retry_config, :base_delay, 1000)
      
      # 计算首次重试时间
      first_retry_delay = calculate_retry_delay(strategy, 1, base_delay, retry_config)
      next_retry_at = DateTime.add(DateTime.utc_now(), first_retry_delay, :millisecond)
      
      retry_task = %{
        task_id: UUID.uuid4(),
        execution_id: execution_id,
        strategy: strategy,
        max_attempts: max_attempts,
        current_attempt: 0,
        base_delay: base_delay,
        retry_config: retry_config,
        error_context: error_context,
        created_at: DateTime.utc_now(),
        next_retry_at: next_retry_at,
        status: :scheduled,
        timer_ref: nil,
        attempt_history: []
      }
      
      {:ok, retry_task}
    rescue
      error ->
        {:error, "Failed to create retry task: #{inspect(error)}"}
    end
  end

  defp execute_retry_attempt(retry_task) do
    attempt = retry_task.current_attempt + 1
    
    Logger.info("Executing retry attempt #{attempt} for execution #{retry_task.execution_id}")
    
    # 记录重试尝试
    attempt_record = %{
      attempt: attempt,
      started_at: DateTime.utc_now(),
      strategy: retry_task.strategy
    }
    
    # 执行重试
    case WorkflowRunner.execute_workflow(
      retry_task.error_context.workflow_id,
      execution_id: retry_task.execution_id,
      mode: :retry,
      retry_of: retry_task.error_context.original_execution_id
    ) do
      {:ok, _execution_id} ->
        completed_attempt = %{attempt_record | 
          completed_at: DateTime.utc_now(),
          result: :success
        }
        
        updated_task = %{retry_task |
          current_attempt: attempt,
          status: :success,
          completed_at: DateTime.utc_now(),
          attempt_history: [completed_attempt | retry_task.attempt_history]
        }
        
        {:success, updated_task}
      
      {:error, reason} ->
        failed_attempt = %{attempt_record |
          completed_at: DateTime.utc_now(),
          result: :failed,
          error: reason
        }
        
        updated_task = %{retry_task |
          current_attempt: attempt,
          status: :retrying,
          last_attempt_at: DateTime.utc_now(),
          last_error: reason,
          attempt_history: [failed_attempt | retry_task.attempt_history]
        }
        
        {:failed, updated_task}
    end
  rescue
    error ->
      {:error, "Retry execution failed: #{inspect(error)}"}
  end

  defp schedule_next_retry(retry_task) do
    next_attempt = retry_task.current_attempt + 1
    delay = calculate_retry_delay(
      retry_task.strategy, 
      next_attempt, 
      retry_task.base_delay,
      retry_task.retry_config
    )
    
    next_retry_at = DateTime.add(DateTime.utc_now(), delay, :millisecond)
    
    %{retry_task | 
      next_retry_at: next_retry_at,
      status: :scheduled
    }
  end

  defp calculate_retry_delay(strategy, attempt, base_delay, config) do
    case strategy do
      :fixed_delay ->
        base_delay
      
      :exponential_backoff ->
        max_delay = Map.get(config, :max_delay, 60_000)
        delay = base_delay * :math.pow(2, attempt - 1)
        min(trunc(delay), max_delay)
      
      :linear_backoff ->
        increment = Map.get(config, :increment, 1000)
        base_delay + (attempt - 1) * increment
      
      :custom ->
        custom_function = Map.get(config, :custom_function)
        if is_function(custom_function, 2) do
          custom_function.(attempt, base_delay)
        else
          base_delay
        end
      
      _ ->
        base_delay
    end
  end

  defp format_retry_status(retry_task) do
    %{
      task_id: retry_task.task_id,
      execution_id: retry_task.execution_id,
      status: retry_task.status,
      strategy: retry_task.strategy,
      current_attempt: retry_task.current_attempt,
      max_attempts: retry_task.max_attempts,
      created_at: retry_task.created_at,
      next_retry_at: Map.get(retry_task, :next_retry_at),
      completed_at: Map.get(retry_task, :completed_at),
      cancelled_at: Map.get(retry_task, :cancelled_at),
      last_error: Map.get(retry_task, :last_error),
      attempt_history: Map.get(retry_task, :attempt_history, [])
    }
  end

  defp schedule_retry_processor() do
    # 每30秒检查一次重试任务
    Process.send_after(self(), :process_retries, 30_000)
  end
end
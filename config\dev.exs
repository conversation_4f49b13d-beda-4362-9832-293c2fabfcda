import Config

# 数据库配置
config :n8n_elixir, N8nElixir.Repo,
  username: "postgres",
  password: "postgres",
  hostname: "localhost",
  database: "n8n_elixir_dev",
  stacktrace: true,
  show_sensitive_data_on_connection_error: true,
  pool_size: 10

# Phoenix 端点配置
config :n8n_elixir, N8nElixirWeb.Endpoint,
  http: [ip: {127, 0, 0, 1}, port: 4000],
  check_origin: false,
  code_reloader: true,
  debug_errors: true,
  secret_key_base: "your-secret-key-base-for-dev",
  watchers: [
    esbuild: {Esbuild, :install_and_run, [:default, ~w(--sourcemap=inline --watch)]},
    tailwind: {Tailwind, :install_and_run, [:default, ~w(--watch)]}
  ]

# 实时重载配置
config :n8n_elixir, N8nElixirWeb.Endpoint,
  live_reload: [
    patterns: [
      ~r"priv/static/.*(js|css|png|jpeg|jpg|gif|svg)$",
      ~r"priv/gettext/.*(po)$",
      ~r"lib/n8n_elixir_web/(controllers|live|components)/.*(ex|heex)$",
      ~r"lib/n8n_elixir_web/templates/.*(eex)$"
    ]
  ]

# 日志配置
config :logger, :console, format: "[$level] $message\n"

# Phoenix 配置
config :phoenix, :stacktrace_depth, 20
config :phoenix, :plug_init_mode, :runtime
{<<"links">>,[{<<"GitHub">>,<<"https://github.com/elixir-mint/hpax">>}]}.
{<<"name">>,<<"hpax">>}.
{<<"version">>,<<"1.0.3">>}.
{<<"description">>,
 <<"Implementation of the HPACK protocol (RFC 7541) for Elixir">>}.
{<<"elixir">>,<<"~> 1.12">>}.
{<<"app">>,<<"hpax">>}.
{<<"licenses">>,[<<"Apache-2.0">>]}.
{<<"requirements">>,[]}.
{<<"files">>,
 [<<"lib">>,<<"lib/hpax">>,<<"lib/hpax/huffman_table">>,
  <<"lib/hpax/types.ex">>,<<"lib/hpax/huffman.ex">>,<<"lib/hpax/table.ex">>,
  <<"lib/hpax.ex">>,<<".formatter.exs">>,<<"mix.exs">>,<<"README.md">>,
  <<"LICENSE.txt">>,<<"CHANGELOG.md">>]}.
{<<"build_tools">>,[<<"mix">>]}.

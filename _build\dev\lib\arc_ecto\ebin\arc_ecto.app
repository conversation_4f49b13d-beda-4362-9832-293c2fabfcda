{application,arc_ecto,
             [{modules,['Elixir.Arc.Ecto','Elixir.Arc.Ecto.Definition',
                        'Elixir.Arc.Ecto.Schema','Elixir.Arc.Ecto.Type']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger,arc]},
              {description,"An integration with Arc and Ecto.\n"},
              {registered,[]},
              {vsn,"0.11.3"}]}.

{<<"links">>,
 [{<<"Docs">>,<<"https://hexdocs.pm/jose">>},
  {<<"Github">>,<<"https://github.com/potatosalad/erlang-jose">>}]}.
{<<"name">>,<<"jose">>}.
{<<"version">>,<<"1.11.10">>}.
{<<"description">>,
 <<"JSON Object Signing and Encryption (JOSE) for Erlang and Elixir.">>}.
{<<"elixir">>,<<"~> 1.13">>}.
{<<"app">>,<<"jose">>}.
{<<"files">>,
 [<<"CHANGELOG.md">>,<<"include">>,<<"include/jose_jwk.hrl">>,
  <<"include/jose_jwt.hrl">>,<<"include/jose_jwe.hrl">>,
  <<"include/jose_public_key.hrl">>,<<"include/jose_jws.hrl">>,
  <<"include/jose_compat.hrl">>,<<"include/jose.hrl">>,
  <<"include/jose_base.hrl">>,<<"lib">>,<<"lib/jose">>,<<"lib/jose/jwa.ex">>,
  <<"lib/jose/jwe.ex">>,<<"lib/jose/poison.ex">>,<<"lib/jose/jwk.ex">>,
  <<"lib/jose/jws.ex">>,<<"lib/jose/poison">>,
  <<"lib/jose/poison/lexical_encoder.ex">>,<<"lib/jose/jwt.ex">>,
  <<"lib/jose.ex">>,<<"LICENSE.md">>,<<"priv">>,<<"priv/.keep">>,
  <<"mix.exs">>,<<"README.md">>,<<"rebar.config">>,<<"src">>,<<"src/jws">>,
  <<"src/jws/jose_jws_alg_rsa_pkcs1_v1_5.erl">>,
  <<"src/jws/jose_jws_alg.erl">>,<<"src/jws/jose_jws_alg_eddsa.erl">>,
  <<"src/jws/jose_jws_alg_poly1305.erl">>,<<"src/jws/jose_jws_alg_none.erl">>,
  <<"src/jws/jose_jws_alg_ecdsa.erl">>,<<"src/jws/jose_jws_alg_rsa_pss.erl">>,
  <<"src/jws/jose_jws_alg_hmac.erl">>,<<"src/jws/jose_jws.erl">>,
  <<"src/jwt">>,<<"src/jwt/jose_jwt.erl">>,<<"src/jose_server.erl">>,
  <<"src/jose_app.erl">>,<<"src/jwa">>,<<"src/jwa/xchacha20_poly1305">>,
  <<"src/jwa/xchacha20_poly1305/jose_xchacha20_poly1305_crypto.erl">>,
  <<"src/jwa/xchacha20_poly1305/jose_xchacha20_poly1305.erl">>,
  <<"src/jwa/xchacha20_poly1305/jose_xchacha20_poly1305_libsodium.erl">>,
  <<"src/jwa/xchacha20_poly1305/jose_xchacha20_poly1305_unsupported.erl">>,
  <<"src/jwa/chacha20_poly1305">>,
  <<"src/jwa/chacha20_poly1305/jose_chacha20_poly1305_libsodium.erl">>,
  <<"src/jwa/chacha20_poly1305/jose_chacha20_poly1305_unsupported.erl">>,
  <<"src/jwa/chacha20_poly1305/jose_chacha20_poly1305_crypto.erl">>,
  <<"src/jwa/chacha20_poly1305/jose_chacha20_poly1305.erl">>,
  <<"src/jwa/jose_jwa_x448.erl">>,<<"src/jwa/jose_jwa_aes.erl">>,
  <<"src/jwa/sha3">>,<<"src/jwa/sha3/jose_sha3_keccakf1600_nif.erl">>,
  <<"src/jwa/sha3/jose_sha3_unsupported.erl">>,
  <<"src/jwa/sha3/jose_sha3_keccakf1600_driver.erl">>,
  <<"src/jwa/sha3/jose_sha3.erl">>,<<"src/jwa/sha3/jose_sha3_libdecaf.erl">>,
  <<"src/jwa/jose_jwa_bench.erl">>,<<"src/jwa/jose_jwa_aes_kw.erl">>,
  <<"src/jwa/jose_jwa_pkcs5.erl">>,<<"src/jwa/jose_jwa_concat_kdf.erl">>,
  <<"src/jwa/jose_jwa_chacha20_poly1305.erl">>,
  <<"src/jwa/jose_jwa_pkcs7.erl">>,<<"src/jwa/jose_jwa_ed448.erl">>,
  <<"src/jwa/jose_jwa_xchacha20_poly1305.erl">>,
  <<"src/jwa/jose_jwa_pkcs1.erl">>,<<"src/jwa/jose_jwa_poly1305.erl">>,
  <<"src/jwa/jose_jwa_xchacha20.erl">>,<<"src/jwa/jose_jwa_unsupported.erl">>,
  <<"src/jwa/jose_jwa_sha3.erl">>,<<"src/jwa/jose_jwa_base64url.erl">>,
  <<"src/jwa/jose_jwa_curve25519.erl">>,<<"src/jwa/jose_jwa_math.erl">>,
  <<"src/jwa/jose_jwa_chacha20.erl">>,<<"src/jwa/jose_jwa_ed25519.erl">>,
  <<"src/jwa/jose_jwa_x25519.erl">>,<<"src/jwa/jose_jwa.erl">>,
  <<"src/jwa/curve448">>,<<"src/jwa/curve448/jose_curve448_fallback.erl">>,
  <<"src/jwa/curve448/jose_curve448.erl">>,
  <<"src/jwa/curve448/jose_curve448_unsupported.erl">>,
  <<"src/jwa/curve448/jose_curve448_crypto.erl">>,
  <<"src/jwa/curve448/jose_curve448_libdecaf.erl">>,<<"src/jwa/curve25519">>,
  <<"src/jwa/curve25519/jose_curve25519_unsupported.erl">>,
  <<"src/jwa/curve25519/jose_curve25519_crypto.erl">>,
  <<"src/jwa/curve25519/jose_curve25519.erl">>,
  <<"src/jwa/curve25519/jose_curve25519_fallback.erl">>,
  <<"src/jwa/curve25519/jose_curve25519_libdecaf.erl">>,
  <<"src/jwa/curve25519/jose_curve25519_libsodium.erl">>,
  <<"src/jwa/jose_jwa_hchacha20.erl">>,<<"src/jwa/jose_jwa_curve448.erl">>,
  <<"src/jose_sup.erl">>,<<"src/jose.erl">>,<<"src/jwk">>,
  <<"src/jwk/jose_jwk_kty_okp_ed448.erl">>,
  <<"src/jwk/jose_jwk_openssh_key.erl">>,<<"src/jwk/jose_jwk.erl">>,
  <<"src/jwk/jose_jwk_kty_ec.erl">>,
  <<"src/jwk/jose_jwk_kty_okp_ed25519ph.erl">>,
  <<"src/jwk/jose_jwk_kty_oct.erl">>,<<"src/jwk/jose_jwk_der.erl">>,
  <<"src/jwk/jose_jwk_pem.erl">>,<<"src/jwk/jose_jwk_use_sig.erl">>,
  <<"src/jwk/jose_jwk_use_enc.erl">>,
  <<"src/jwk/jose_jwk_kty_okp_ed448ph.erl">>,
  <<"src/jwk/jose_jwk_kty_rsa.erl">>,<<"src/jwk/jose_jwk_kty.erl">>,
  <<"src/jwk/jose_jwk_oct.erl">>,<<"src/jwk/jose_jwk_kty_okp_ed25519.erl">>,
  <<"src/jwk/jose_jwk_set.erl">>,<<"src/jwk/jose_jwk_kty_okp_x25519.erl">>,
  <<"src/jwk/jose_jwk_kty_okp_x448.erl">>,<<"src/jwe">>,
  <<"src/jwe/jose_jwe_alg_rsa.erl">>,<<"src/jwe/jose_jwe_enc_c20p.erl">>,
  <<"src/jwe/jose_jwe_alg_pbes2.erl">>,<<"src/jwe/jose_jwe_enc_xc20p.erl">>,
  <<"src/jwe/jose_jwe_alg_dir.erl">>,<<"src/jwe/jose_jwe_alg_xc20p_kw.erl">>,
  <<"src/jwe/jose_jwe_alg.erl">>,<<"src/jwe/jose_jwe_alg_ecdh_es.erl">>,
  <<"src/jwe/jose_jwe_alg_ecdh_ss.erl">>,
  <<"src/jwe/jose_jwe_alg_c20p_kw.erl">>,
  <<"src/jwe/jose_jwe_alg_aes_kw.erl">>,<<"src/jwe/jose_jwe_enc.erl">>,
  <<"src/jwe/jose_jwe_enc_aes.erl">>,<<"src/jwe/jose_jwe_alg_ecdh_1pu.erl">>,
  <<"src/jwe/jose_jwe_zip.erl">>,<<"src/jwe/jose_jwe.erl">>,<<"src/json">>,
  <<"src/json/jose_json_jiffy.erl">>,<<"src/json/jose_json_ojson.erl">>,
  <<"src/json/jose_json_jsx.erl">>,<<"src/json/jose_json_jason.erl">>,
  <<"src/json/jose_json.erl">>,
  <<"src/json/jose_json_poison_lexical_encoder.erl">>,
  <<"src/json/jose_json_poison_compat_encoder.erl">>,
  <<"src/json/jose_json_poison.erl">>,<<"src/json/jose_json_thoas.erl">>,
  <<"src/json/jose_json_unsupported.erl">>,<<"src/json/jose_json_jsone.erl">>,
  <<"src/jose_block_encryptor.erl">>,<<"src/jose_crypto_compat.erl">>,
  <<"src/jose.app.src">>,<<"src/base">>,<<"src/base/jose_base64url.erl">>,
  <<"src/base/jose_base64.erl">>,<<"src/jose_public_key.erl">>]}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"requirements">>,[]}.
{<<"build_tools">>,[<<"mix">>,<<"rebar3">>]}.

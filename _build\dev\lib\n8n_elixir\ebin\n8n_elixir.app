{application,n8n_elixir,
    [{modules,
         ['Elixir.N8nElixir.Accounts.Team','Elixir.N8nElixir.Accounts.User',
          'Elixir.N8nElixir.ActiveWorkflows','Elixir.N8nElixir.Application',
          'Elixir.N8nElixir.BinaryData','Elixir.N8nElixir.BinaryDataManager',
          'Elixir.N8nElixir.ConditionalExecution',
          'Elixir.N8nElixir.Core.Supervisor',
          'Elixir.N8nElixir.CredentialManager',
          'Elixir.N8nElixir.Credentials.Credential',
          'Elixir.N8nElixir.Credentials.WorkflowCredential',
          'Elixir.N8nElixir.DataProcessor','Elixir.N8nElixir.ErrorHandler',
          'Elixir.N8nElixir.ExecutionLogger',
          'Elixir.N8nElixir.ExecutionTracker','Elixir.N8nElixir.Executions',
          'Elixir.N8nElixir.Executions.Execution',
          'Elixir.N8nElixir.Executions.ExecutionData',
          'Elixir.N8nElixir.ExpressionResolver',
          'Elixir.N8nElixir.NodeBehaviour','Elixir.N8nElixir.NodeType',
          'Elixir.N8nElixir.NodeTypes','Elixir.N8nElixir.Nodes.Code',
          'Elixir.N8nElixir.Nodes.ConvertBinaryData',
          'Elixir.N8nElixir.Nodes.Function',
          'Elixir.N8nElixir.Nodes.HttpBinary',
          'Elixir.N8nElixir.Nodes.HttpRequest','Elixir.N8nElixir.Nodes.If',
          'Elixir.N8nElixir.Nodes.Loop','Elixir.N8nElixir.Nodes.Merge',
          'Elixir.N8nElixir.Nodes.ReadBinaryFile',
          'Elixir.N8nElixir.Nodes.Set','Elixir.N8nElixir.Nodes.Split',
          'Elixir.N8nElixir.Nodes.Switch',
          'Elixir.N8nElixir.Nodes.WriteBinaryFile','Elixir.N8nElixir.Repo',
          'Elixir.N8nElixir.RetryManager','Elixir.N8nElixir.Scheduler',
          'Elixir.N8nElixir.TriggerManager','Elixir.N8nElixir.Triggers',
          'Elixir.N8nElixir.Triggers.Trigger','Elixir.N8nElixir.Workflow',
          'Elixir.N8nElixir.WorkflowExecution.Server',
          'Elixir.N8nElixir.WorkflowRunner','Elixir.N8nElixir.Workflows',
          'Elixir.N8nElixir.Workflows.Node',
          'Elixir.N8nElixir.Workflows.Workflow','Elixir.N8nElixirWeb',
          'Elixir.N8nElixirWeb.BinaryDataController',
          'Elixir.N8nElixirWeb.CoreComponents','Elixir.N8nElixirWeb.Endpoint',
          'Elixir.N8nElixirWeb.ExecutionShowLive',
          'Elixir.N8nElixirWeb.Gettext','Elixir.N8nElixirWeb.Layouts',
          'Elixir.N8nElixirWeb.NodePropertiesComponent',
          'Elixir.N8nElixirWeb.PageController','Elixir.N8nElixirWeb.PageHTML',
          'Elixir.N8nElixirWeb.Router','Elixir.N8nElixirWeb.Telemetry',
          'Elixir.N8nElixirWeb.WorkflowIndexLive',
          'Elixir.N8nElixirWeb.WorkflowLive',
          'Elixir.N8nElixirWeb.WorkflowLiveHelpers']},
     {compile_env,
         [{n8n_elixir,
              ['Elixir.N8nElixirWeb.Gettext'],
              {ok,[{default_locale,<<"en">>},{locales,[<<"en">>,<<"zh">>]}]}},
          {n8n_elixir,[allowed_mime_types],error},
          {n8n_elixir,[binary_data_storage_path],error},
          {n8n_elixir,[dev_routes],error},
          {n8n_elixir,[max_binary_file_size],error}]},
     {optional_applications,[]},
     {applications,
         [kernel,stdlib,elixir,logger,runtime_tools,phoenix,phoenix_ecto,
          phoenix_html,phoenix_live_reload,phoenix_live_view,
          phoenix_live_dashboard,phoenix_view,ecto_sql,postgrex,jason,tesla,
          finch,httpoison,quantum,guardian,pbkdf2_elixir,ex_json_schema,cloak,
          uuid,telemetry_metrics,telemetry_poller,phoenix_pubsub,swoosh,
          esbuild,tailwind,cors_plug,hammer,oban,logger_json,arc,arc_ecto,
          ex_aws,ex_aws_s3,crontab,sweet_xml,csv,plug_cowboy,gettext]},
     {description,"n8n_elixir"},
     {registered,[]},
     {vsn,"0.1.0"},
     {mod,{'Elixir.N8nElixir.Application',[]}}]}.

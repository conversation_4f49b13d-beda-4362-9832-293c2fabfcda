<div class="mx-auto max-w-sm">
  <.header class="text-center">
    Forgot your password?
    <:subtitle>We'll send a password reset link to your inbox</:subtitle>
  </.header>

  <.simple_form :let={f} for={@conn.params["<%= schema.singular %>"]} as={:<%= schema.singular %>} action={~p"<%= schema.route_prefix %>/reset_password"}>
    <.input field={f[:email]} type="email" placeholder="Email" required />
    <:actions>
      <.button phx-disable-with="Sending..." class="w-full">
        Send password reset instructions
      </.button>
    </:actions>
  </.simple_form>

  <p class="text-center text-sm mt-4">
    <.link href={~p"<%= schema.route_prefix %>/register"}>Register</.link>
    | <.link href={~p"<%= schema.route_prefix %>/log_in"}>Log in</.link>
  </p>
</div>

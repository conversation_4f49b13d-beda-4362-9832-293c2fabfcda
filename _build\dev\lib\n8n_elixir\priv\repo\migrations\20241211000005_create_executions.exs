defmodule N8nElixir.Repo.Migrations.CreateExecutions do
  use Ecto.Migration

  def change do
    create table(:executions, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :status, :string, default: "new"
      add :mode, :string, default: "manual"
      add :started_at, :utc_datetime
      add :stopped_at, :utc_datetime
      add :finished, :boolean, default: false
      add :retry_of, :binary_id
      add :retry_success_id, :binary_id
      add :data, :map, default: %{}
      add :workflow_data, :map, default: %{}
      add :error, :map
      add :node_execution_count, :integer, default: 0
      add :total_duration, :integer
      add :workflow_id, references(:workflows, type: :binary_id), null: false
      add :triggered_by_id, references(:users, type: :binary_id)
      
      timestamps()
    end

    create index(:executions, [:workflow_id])
    create index(:executions, [:triggered_by_id])
    create index(:executions, [:status])
    create index(:executions, [:mode])
    create index(:executions, [:started_at])
    create index(:executions, [:stopped_at])
    create index(:executions, [:retry_of])
  end
end
defmodule N8nElixir.Nodes.ReadBinaryFile do
  @moduledoc """
  读取二进制文件节点

  从文件系统读取文件并转换为二进制数据
  类似n8n的Read Binary File节点
  """

  @behaviour N8nElixir.NodeBehaviour

  @impl true
  def execute(input_data, _credentials, parameters) do
    file_path = Map.get(parameters, "filePath", "")
    
    if String.trim(file_path) == "" do
      {:error, "File path is required"}
    else
      case File.read(file_path) do
        {:ok, file_data} ->
          filename = Path.basename(file_path)
          
          # 存储二进制数据
          case N8nElixir.BinaryData.store_binary_data(file_data, filename) do
            {:ok, binary_data_id} ->
              output_data = create_binary_output(binary_data_id, filename, file_data)
              {:ok, [output_data]}
            
            {:error, reason} ->
              {:error, "Failed to store binary data: #{reason}"}
          end
        
        {:error, reason} ->
          {:error, "Failed to read file: #{reason}"}
      end
    end
  end

  @impl true
  def get_properties() do
    %{
      displayName: "Read Binary File",
      name: "readBinaryFile",
      icon: "fa:file-alt",
      group: ["input"],
      version: 1,
      description: "Reads a binary file from the filesystem",
      defaults: %{
        name: "Read Binary File"
      },
      inputs: ["main"],
      outputs: ["main"],
      parameters: [
        %{
          displayName: "File Path",
          name: "filePath",
          type: "string",
          default: "",
          placeholder: "/path/to/file.pdf",
          description: "Path to the file to read"
        }
      ]
    }
  end

  defp create_binary_output(binary_data_id, filename, file_data) do
    mime_type = determine_mime_type(filename, file_data)
    
    %{
      "json" => %{
        "fileName" => filename,
        "fileSize" => byte_size(file_data),
        "mimeType" => mime_type
      },
      "binary" => %{
        "data" => %{
          "data" => binary_data_id,
          "mimeType" => mime_type,
          "fileName" => filename,
          "fileSize" => byte_size(file_data)
        }
      }
    }
  end

  defp determine_mime_type(filename, file_data) do
    # 简单的MIME类型检测
    case Path.extname(filename) |> String.downcase() do
      ".pdf" -> "application/pdf"
      ".jpg" -> "image/jpeg"
      ".jpeg" -> "image/jpeg"
      ".png" -> "image/png"
      ".gif" -> "image/gif"
      ".txt" -> "text/plain"
      ".csv" -> "text/csv"
      ".json" -> "application/json"
      ".xml" -> "application/xml"
      _ ->
        # 从文件头检测
        case file_data do
          <<"%PDF-", _::binary>> -> "application/pdf"
          <<0xFF, 0xD8, 0xFF, _::binary>> -> "image/jpeg"
          <<0x89, 0x50, 0x4E, 0x47, _::binary>> -> "image/png"
          _ -> "application/octet-stream"
        end
    end
  end
end

defmodule N8nElixir.Nodes.WriteBinaryFile do
  @moduledoc """
  写入二进制文件节点

  将二进制数据写入文件系统
  类似n8n的Write Binary File节点
  """

  @behaviour N8nElixir.NodeBehaviour

  @impl true
  def execute(input_data, _credentials, parameters) do
    output_path = Map.get(parameters, "fileName", "")
    
    if String.trim(output_path) == "" do
      {:error, "Output file path is required"}
    else
      results = Enum.map(input_data, fn item ->
        case extract_binary_data(item) do
          {:ok, binary_data, filename} ->
            final_path = resolve_output_path(output_path, filename)
            
            case write_file(final_path, binary_data) do
              :ok ->
                %{
                  "json" => %{
                    "fileName" => final_path,
                    "fileSize" => byte_size(binary_data),
                    "success" => true
                  }
                }
              
              {:error, reason} ->
                %{
                  "json" => %{
                    "fileName" => final_path,
                    "error" => to_string(reason),
                    "success" => false
                  }
                }
            end
          
          {:error, reason} ->
            %{
              "json" => %{
                "error" => reason,
                "success" => false
              }
            }
        end
      end)
      
      {:ok, results}
    end
  end

  @impl true
  def get_properties() do
    %{
      displayName: "Write Binary File",
      name: "writeBinaryFile",
      icon: "fa:file-export",
      group: ["output"],
      version: 1,
      description: "Writes binary data to a file",
      defaults: %{
        name: "Write Binary File"
      },
      inputs: ["main"],
      outputs: ["main"],
      parameters: [
        %{
          displayName: "File Name",
          name: "fileName",
          type: "string",
          default: "",
          placeholder: "/tmp/output.pdf",
          description: "Path where the file should be saved"
        }
      ]
    }
  end

  defp extract_binary_data(item) do
    case Map.get(item, "binary") do
      nil ->
        {:error, "No binary data found in input"}
      
      binary_map when is_map(binary_map) ->
        case Map.get(binary_map, "data") do
          nil ->
            {:error, "No binary data reference found"}
          
          binary_ref when is_map(binary_ref) ->
            binary_data_id = Map.get(binary_ref, "data")
            filename = Map.get(binary_ref, "fileName", "output.bin")
            
            case N8nElixir.BinaryData.get_binary_data(binary_data_id) do
              {:ok, data, _info} ->
                {:ok, data, filename}
              {:error, reason} ->
                {:error, "Failed to retrieve binary data: #{reason}"}
            end
          
          binary_data_id when is_binary(binary_data_id) ->
            case N8nElixir.BinaryData.get_binary_data(binary_data_id) do
              {:ok, data, info} ->
                {:ok, data, info.file_name}
              {:error, reason} ->
                {:error, "Failed to retrieve binary data: #{reason}"}
            end
        end
    end
  end

  defp resolve_output_path(output_path, default_filename) do
    if String.ends_with?(output_path, "/") do
      Path.join(output_path, default_filename)
    else
      output_path
    end
  end

  defp write_file(file_path, data) do
    # 确保目录存在
    dir = Path.dirname(file_path)
    File.mkdir_p!(dir)
    
    File.write(file_path, data)
  end
end

defmodule N8nElixir.Nodes.ConvertBinaryData do
  @moduledoc """
  转换二进制数据节点

  转换二进制数据格式
  类似n8n的Convert节点的二进制数据转换功能
  """

  @behaviour N8nElixir.NodeBehaviour

  @impl true
  def execute(input_data, _credentials, parameters) do
    target_format = Map.get(parameters, "targetFormat", "json")
    mode = Map.get(parameters, "mode", "convert")
    
    results = Enum.map(input_data, fn item ->
      case mode do
        "convert" ->
          convert_binary_data(item, target_format)
        
        "encode" ->
          encode_binary_data(item, target_format)
        
        "decode" ->
          decode_binary_data(item, target_format)
        
        _ ->
          %{
            "json" => %{
              "error" => "Unknown conversion mode: #{mode}",
              "success" => false
            }
          }
      end
    end)
    
    {:ok, results}
  end

  @impl true
  def get_properties() do
    %{
      displayName: "Convert Binary Data",
      name: "convertBinaryData",
      icon: "fa:exchange-alt",
      group: ["transform"],
      version: 1,
      description: "Converts binary data between different formats",
      defaults: %{
        name: "Convert Binary Data"
      },
      inputs: ["main"],
      outputs: ["main"],
      parameters: [
        %{
          displayName: "Mode",
          name: "mode",
          type: "options",
          default: "convert",
          options: [
            %{name: "Convert Format", value: "convert"},
            %{name: "Encode to Base64", value: "encode"},
            %{name: "Decode from Base64", value: "decode"}
          ]
        },
        %{
          displayName: "Target Format",
          name: "targetFormat",
          type: "options",
          default: "json",
          displayOptions: %{
            show: %{
              mode: ["convert"]
            }
          },
          options: [
            %{name: "JSON", value: "json"},
            %{name: "PNG", value: "png"},
            %{name: "JPEG", value: "jpeg"},
            %{name: "PDF", value: "pdf"}
          ]
        }
      ]
    }
  end

  defp convert_binary_data(item, target_format) do
    case extract_binary_reference(item) do
      {:ok, binary_data_id} ->
        case N8nElixir.BinaryData.convert_binary_data(binary_data_id, target_format) do
          {:ok, new_binary_id} ->
            case N8nElixir.BinaryData.get_binary_data_info(new_binary_id) do
              {:ok, info} ->
                %{
                  "json" => %{
                    "originalId" => binary_data_id,
                    "convertedId" => new_binary_id,
                    "targetFormat" => target_format,
                    "success" => true
                  },
                  "binary" => %{
                    "data" => %{
                      "data" => new_binary_id,
                      "mimeType" => info.mime_type,
                      "fileName" => info.file_name,
                      "fileSize" => info.file_size
                    }
                  }
                }
              
              {:error, reason} ->
                create_error_output("Failed to get converted file info: #{reason}")
            end
          
          {:error, reason} ->
            create_error_output("Conversion failed: #{reason}")
        end
      
      {:error, reason} ->
        create_error_output(reason)
    end
  end

  defp encode_binary_data(item, encoding) do
    case extract_binary_reference(item) do
      {:ok, binary_data_id} ->
        case N8nElixir.BinaryData.get_binary_data(binary_data_id) do
          {:ok, data, _info} ->
            encoded_data = case encoding do
              "base64" -> Base.encode64(data)
              _ -> Base.encode64(data)
            end
            
            %{
              "json" => %{
                "originalId" => binary_data_id,
                "encoding" => encoding,
                "encodedData" => encoded_data,
                "success" => true
              }
            }
          
          {:error, reason} ->
            create_error_output("Failed to get binary data: #{reason}")
        end
      
      {:error, reason} ->
        create_error_output(reason)
    end
  end

  defp decode_binary_data(item, encoding) do
    encoded_data = Map.get(item["json"] || %{}, "encodedData")
    filename = Map.get(item["json"] || %{}, "fileName", "decoded_file.bin")
    
    if encoded_data do
      case decode_data(encoded_data, encoding) do
        {:ok, decoded_data} ->
          case N8nElixir.BinaryData.store_binary_data(decoded_data, filename) do
            {:ok, binary_data_id} ->
              %{
                "json" => %{
                  "decodedId" => binary_data_id,
                  "encoding" => encoding,
                  "success" => true
                },
                "binary" => %{
                  "data" => %{
                    "data" => binary_data_id,
                    "fileName" => filename,
                    "fileSize" => byte_size(decoded_data)
                  }
                }
              }
            
            {:error, reason} ->
              create_error_output("Failed to store decoded data: #{reason}")
          end
        
        {:error, reason} ->
          create_error_output("Decoding failed: #{reason}")
      end
    else
      create_error_output("No encoded data found in input")
    end
  end

  defp extract_binary_reference(item) do
    case get_in(item, ["binary", "data", "data"]) do
      nil -> {:error, "No binary data reference found"}
      binary_id -> {:ok, binary_id}
    end
  end

  defp decode_data(encoded_data, encoding) do
    try do
      case encoding do
        "base64" -> {:ok, Base.decode64!(encoded_data)}
        _ -> {:ok, Base.decode64!(encoded_data)}
      end
    rescue
      _ -> {:error, "Invalid encoded data"}
    end
  end

  defp create_error_output(error_message) do
    %{
      "json" => %{
        "error" => error_message,
        "success" => false
      }
    }
  end
end
{<<"links">>,
 [{<<"GitHub">>,<<"https://github.com/phoenixframework/websock_adapter">>}]}.
{<<"name">>,<<"websock_adapter">>}.
{<<"version">>,<<"0.5.8">>}.
{<<"description">>,<<"A set of WebSock adapters for common web servers">>}.
{<<"elixir">>,<<"~> 1.9">>}.
{<<"app">>,<<"websock_adapter">>}.
{<<"files">>,
 [<<"lib">>,<<"lib/websock_adapter.ex">>,<<"lib/websock_adapter">>,
  <<"lib/websock_adapter/upgrade_error.ex">>,
  <<"lib/websock_adapter/upgrade_validation.ex">>,
  <<"lib/websock_adapter/cowboy_adapter.ex">>,<<"mix.exs">>,<<"README.md">>,
  <<"LICENSE">>,<<"CHANGELOG.md">>]}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"requirements">>,
 [[{<<"name">>,<<"websock">>},
   {<<"app">>,<<"websock">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 0.5">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"plug">>},
   {<<"app">>,<<"plug">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 1.14">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"bandit">>},
   {<<"app">>,<<"bandit">>},
   {<<"optional">>,true},
   {<<"requirement">>,<<">= 0.6.0">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"plug_cowboy">>},
   {<<"app">>,<<"plug_cowboy">>},
   {<<"optional">>,true},
   {<<"requirement">>,<<"~> 2.6">>},
   {<<"repository">>,<<"hexpm">>}]]}.
{<<"build_tools">>,[<<"mix">>]}.

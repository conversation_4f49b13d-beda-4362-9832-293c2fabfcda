{minimum_otp_vsn,"21.3"}.
{erl_first_files,["src/certifi_pt.erl"]}.
{profiles,[{default,[{erl_opts,[deterministic,
                                {platform_define,"^2",'OTP_20_AND_ABOVE'}]}]},
           {docs,[{erl_opts,[]}]}]}.
{hex,[{doc,#{provider => ex_doc}}]}.
{ex_doc,[{extras,[{"README.md",#{title => "Overview"}},
                  {"LICENSE",#{title => "License"}}]},
         {main,"README.md"},
         {source_url,"https://github.com/certifi/erlang-certifi"},
         {assets,"assets"},
         {api_reference,true}]}.
{dialyzer,[{plt_extra_apps,[public_key]}]}.
{overrides,[]}.

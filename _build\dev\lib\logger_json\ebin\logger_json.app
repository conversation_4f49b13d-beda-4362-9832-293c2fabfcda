{application,logger_json,
             [{modules,['Elixir.LoggerJSON','Elixir.LoggerJSON.Ecto',
                        'Elixir.LoggerJSON.Formatter',
                        'Elixir.LoggerJSON.FormatterUtils',
                        'Elixir.LoggerJSON.Formatters.BasicLogger',
                        'Elixir.LoggerJSON.Formatters.DatadogLogger',
                        'Elixir.LoggerJSON.Formatters.GoogleCloudLogger',
                        'Elixir.LoggerJSON.Formatters.GoogleErrorReporter',
                        'Elixir.LoggerJSON.JasonSafeFormatter',
                        'Elixir.LoggerJSON.Plug',
                        'Elixir.LoggerJSON.Plug.MetadataFormatters.DatadogLogger',
                        'Elixir.LoggerJSON.Plug.MetadataFormatters.ELK',
                        'Elixir.LoggerJSON.Plug.MetadataFormatters.GoogleCloudLogger',
                        'Elixir.LoggerJSON.PlugUtils']},
              {optional_applications,[ecto,plug,phoenix,telemetry]},
              {applications,[kernel,stdlib,elixir,logger,jason,ecto,plug,
                             phoenix,telemetry]},
              {description,"logger_json"},
              {registered,[]},
              {vsn,"5.1.4"}]}.

defmodule N8nElixir.DataProcessor do
  @moduledoc """
  数据处理器模块

  参考n8n的数据处理机制，处理节点间的数据流转、转换和合并
  支持表达式解析、数据映射和类型转换
  """

  alias N8nElixir.ExpressionResolver

  @type data_item :: %{
    "json" => map(),
    "binary" => map() | nil,
    "pairedItem" => map() | nil,
    "error" => map() | nil
  }

  @type processing_options :: %{
    mode: :all_items | :each_item,
    continue_on_fail: boolean(),
    always_output_data: boolean(),
    retry_on_fail: boolean()
  }

  @doc """
  处理节点输入数据

  将来自多个父节点的数据进行合并和预处理
  """
  def process_input_data(input_data, processing_options \\ %{}) do
    mode = Map.get(processing_options, :mode, :all_items)
    
    case mode do
      :all_items ->
        # 处理所有数据作为一个批次
        process_all_items(input_data, processing_options)
      
      :each_item ->
        # 分别处理每个数据项
        process_each_item(input_data, processing_options)
    end
  end

  @doc """
  应用数据转换

  根据节点配置对数据进行转换、映射和过滤
  """
  def apply_transformations(data, transformations, context \\ %{}) do
    Enum.reduce(transformations, {:ok, data}, fn
      transformation, {:ok, current_data} ->
        apply_single_transformation(current_data, transformation, context)
      
      _transformation, {:error, _reason} = error ->
        error
    end)
  end

  @doc """
  合并多个数据源

  支持不同的合并策略：追加、交错、等待全部等
  """
  def merge_data_sources(data_sources, merge_strategy \\ :append) do
    case merge_strategy do
      :append ->
        merge_append(data_sources)
      
      :interleave ->
        merge_interleave(data_sources)
      
      :wait_for_all ->
        merge_wait_for_all(data_sources)
      
      :combine_by_key ->
        merge_combine_by_key(data_sources)
    end
  end

  @doc """
  分割数据

  将数据根据指定规则分割成多个输出
  """
  def split_data(data, split_config) do
    case Map.get(split_config, "mode", "each_item") do
      "each_item" ->
        split_each_item(data, split_config)
      
      "by_field" ->
        split_by_field(data, split_config)
      
      "by_expression" ->
        split_by_expression(data, split_config)
      
      "batch" ->
        split_into_batches(data, split_config)
    end
  end

  @doc """
  验证数据结构

  检查数据是否符合预期的结构和类型要求
  """
  def validate_data_structure(data, schema) do
    case schema do
      nil -> {:ok, data}
      %{} -> validate_against_schema(data, schema)
      _ -> {:error, "Invalid schema format"}
    end
  end

  @doc """
  格式化输出数据

  确保输出数据符合n8n的标准格式
  """
  def format_output_data(data, options \\ %{}) do
    include_metadata = Map.get(options, :include_metadata, true)
    preserve_binary = Map.get(options, :preserve_binary, true)
    
    data
    |> ensure_data_format()
    |> add_metadata_if_needed(include_metadata)
    |> handle_binary_data(preserve_binary)
  end

  # 私有实现函数

  defp process_all_items(input_data, _options) do
    # 验证和清理输入数据
    cleaned_data = Enum.filter(input_data, &is_valid_data_item?/1)
    
    {:ok, cleaned_data}
  end

  defp process_each_item(input_data, options) do
    continue_on_fail = Map.get(options, :continue_on_fail, false)
    
    {successful, failed} = 
      input_data
      |> Enum.map(&process_single_item(&1, options))
      |> Enum.split_with(fn {status, _} -> status == :ok end)
    
    if not continue_on_fail and length(failed) > 0 do
      {:error, "Failed to process #{length(failed)} items"}
    else
      processed_data = Enum.map(successful, fn {:ok, item} -> item end)
      {:ok, processed_data}
    end
  end

  defp process_single_item(item, _options) do
    if is_valid_data_item?(item) do
      {:ok, item}
    else
      {:error, "Invalid data item format"}
    end
  end

  defp apply_single_transformation(data, transformation, context) do
    case transformation["type"] do
      "expression" ->
        apply_expression_transformation(data, transformation, context)
      
      "mapping" ->
        apply_mapping_transformation(data, transformation, context)
      
      "filter" ->
        apply_filter_transformation(data, transformation, context)
      
      "sort" ->
        apply_sort_transformation(data, transformation, context)
      
      "group" ->
        apply_group_transformation(data, transformation, context)
      
      _ ->
        {:error, "Unknown transformation type: #{transformation["type"]}"}
    end
  end

  defp apply_expression_transformation(data, transformation, context) do
    expression = Map.get(transformation, "expression", "")
    
    try do
      transformed_data = Enum.map(data, fn item ->
        item_context = Map.merge(context, %{
          "$json" => item["json"],
          "$binary" => item["binary"],
          "$item" => item
        })
        
        case ExpressionResolver.resolve_expression(expression, item_context) do
          {:ok, result} ->
            %{item | "json" => result}
          
          {:error, _reason} ->
            item
        end
      end)
      
      {:ok, transformed_data}
    rescue
      error ->
        {:error, "Expression transformation failed: #{inspect(error)}"}
    end
  end

  defp apply_mapping_transformation(data, transformation, _context) do
    mapping_rules = Map.get(transformation, "rules", [])
    
    try do
      mapped_data = Enum.map(data, fn item ->
        mapped_json = apply_mapping_rules(item["json"], mapping_rules)
        %{item | "json" => mapped_json}
      end)
      
      {:ok, mapped_data}
    rescue
      error ->
        {:error, "Mapping transformation failed: #{inspect(error)}"}
    end
  end

  defp apply_filter_transformation(data, transformation, context) do
    filter_expression = Map.get(transformation, "condition", "true")
    
    try do
      filtered_data = Enum.filter(data, fn item ->
        item_context = Map.merge(context, %{
          "$json" => item["json"],
          "$binary" => item["binary"],
          "$item" => item
        })
        
        case ExpressionResolver.resolve_expression(filter_expression, item_context) do
          {:ok, result} -> is_truthy(result)
          {:error, _} -> false
        end
      end)
      
      {:ok, filtered_data}
    rescue
      error ->
        {:error, "Filter transformation failed: #{inspect(error)}"}
    end
  end

  defp apply_sort_transformation(data, transformation, _context) do
    sort_field = Map.get(transformation, "field", "")
    sort_order = Map.get(transformation, "order", "asc")
    
    if sort_field == "" do
      {:ok, data}
    else
      try do
        sorted_data = Enum.sort(data, fn item1, item2 ->
          value1 = get_nested_value(item1["json"], sort_field)
          value2 = get_nested_value(item2["json"], sort_field)
          
          case sort_order do
            "asc" -> compare_values(value1, value2) <= 0
            "desc" -> compare_values(value1, value2) >= 0
          end
        end)
        
        {:ok, sorted_data}
      rescue
        error ->
          {:error, "Sort transformation failed: #{inspect(error)}"}
      end
    end
  end

  defp apply_group_transformation(data, transformation, _context) do
    group_field = Map.get(transformation, "field", "")
    
    if group_field == "" do
      {:ok, data}
    else
      try do
        grouped_data = 
          data
          |> Enum.group_by(fn item ->
            get_nested_value(item["json"], group_field)
          end)
          |> Enum.map(fn {group_key, group_items} ->
            %{
              "json" => %{
                "group" => group_key,
                "items" => Enum.map(group_items, & &1["json"]),
                "count" => length(group_items)
              }
            }
          end)
        
        {:ok, grouped_data}
      rescue
        error ->
          {:error, "Group transformation failed: #{inspect(error)}"}
      end
    end
  end

  defp apply_mapping_rules(data, rules) do
    Enum.reduce(rules, data, fn rule, acc ->
      source_path = Map.get(rule, "source", "")
      target_path = Map.get(rule, "target", "")
      
      if source_path != "" and target_path != "" do
        source_value = get_nested_value(data, source_path)
        set_nested_value(acc, target_path, source_value)
      else
        acc
      end
    end)
  end

  # 数据合并策略实现

  defp merge_append(data_sources) do
    {:ok, List.flatten(data_sources)}
  end

  defp merge_interleave(data_sources) do
    max_length = data_sources |> Enum.map(&length/1) |> Enum.max(fn -> 0 end)
    
    interleaved = 
      0..(max_length - 1)
      |> Enum.reduce([], fn index, acc ->
        items_at_index = 
          data_sources
          |> Enum.map(&Enum.at(&1, index))
          |> Enum.reject(&is_nil/1)
        
        acc ++ items_at_index
      end)
    
    {:ok, interleaved}
  end

  defp merge_wait_for_all(data_sources) do
    if Enum.any?(data_sources, &Enum.empty?/1) do
      {:ok, []}
    else
      merged = List.flatten(data_sources)
      {:ok, merged}
    end
  end

  defp merge_combine_by_key(data_sources) do
    # 使用第一个字段作为键进行合并
    combined = 
      data_sources
      |> List.flatten()
      |> Enum.group_by(fn item ->
        case item["json"] do
          map when is_map(map) ->
            map |> Map.keys() |> List.first() |> then(&Map.get(map, &1))
          _ -> nil
        end
      end)
      |> Enum.map(fn {_key, items} ->
        combined_json = 
          items
          |> Enum.map(& &1["json"])
          |> Enum.reduce(%{}, &Map.merge(&2, &1))
        
        %{"json" => combined_json}
      end)
    
    {:ok, combined}
  end

  # 数据分割实现

  defp split_each_item(data, _config) do
    {:ok, Enum.map(data, fn item -> [item] end)}
  end

  defp split_by_field(data, config) do
    field_path = Map.get(config, "field", "")
    
    if field_path == "" do
      {:ok, [data]}
    else
      try do
        split_data = 
          data
          |> Enum.map(fn item ->
            field_value = get_nested_value(item["json"], field_path)
            
            if is_list(field_value) do
              Enum.map(field_value, fn value ->
                updated_json = set_nested_value(item["json"], field_path, value)
                %{item | "json" => updated_json}
              end)
            else
              [item]
            end
          end)
          |> List.flatten()
        
        {:ok, [split_data]}
      rescue
        error ->
          {:error, "Split by field failed: #{inspect(error)}"}
      end
    end
  end

  defp split_by_expression(data, config) do
    expression = Map.get(config, "expression", "")
    
    try do
      {matching, non_matching} = 
        Enum.split_with(data, fn item ->
          context = %{
            "$json" => item["json"],
            "$binary" => item["binary"],
            "$item" => item
          }
          
          case ExpressionResolver.resolve_expression(expression, context) do
            {:ok, result} -> is_truthy(result)
            {:error, _} -> false
          end
        end)
      
      {:ok, [matching, non_matching]}
    rescue
      error ->
        {:error, "Split by expression failed: #{inspect(error)}"}
    end
  end

  defp split_into_batches(data, config) do
    batch_size = Map.get(config, "batch_size", 10)
    
    batches = Enum.chunk_every(data, batch_size)
    {:ok, batches}
  end

  # 数据验证

  defp validate_against_schema(data, schema) do
    # 简化的JSON Schema验证
    try do
      validated_data = Enum.map(data, fn item ->
        if validate_item_schema(item["json"], schema) do
          item
        else
          add_error_to_item(item, "Schema validation failed")
        end
      end)
      
      {:ok, validated_data}
    rescue
      error ->
        {:error, "Schema validation failed: #{inspect(error)}"}
    end
  end

  defp validate_item_schema(data, schema) do
    required_fields = Map.get(schema, "required", [])
    
    Enum.all?(required_fields, fn field ->
      get_nested_value(data, field) != nil
    end)
  end

  # 数据格式化

  defp ensure_data_format(data) when is_list(data) do
    Enum.map(data, &ensure_single_item_format/1)
  end
  defp ensure_data_format(data), do: [ensure_single_item_format(data)]

  defp ensure_single_item_format(%{"json" => _} = item), do: item
  defp ensure_single_item_format(item) when is_map(item) do
    %{"json" => item}
  end
  defp ensure_single_item_format(item) do
    %{"json" => %{"data" => item}}
  end

  defp add_metadata_if_needed(data, true) do
    Enum.with_index(data, fn item, index ->
      metadata = %{
        "index" => index,
        "timestamp" => DateTime.utc_now(),
        "id" => UUID.uuid4()
      }
      
      Map.put(item, "metadata", metadata)
    end)
  end
  defp add_metadata_if_needed(data, false), do: data

  defp handle_binary_data(data, true), do: data
  defp handle_binary_data(data, false) do
    Enum.map(data, fn item ->
      Map.delete(item, "binary")
    end)
  end

  # 辅助函数

  defp is_valid_data_item?(item) when is_map(item) do
    Map.has_key?(item, "json")
  end
  defp is_valid_data_item?(_), do: false

  defp add_error_to_item(item, error_message) do
    error_info = %{
      "message" => error_message,
      "timestamp" => DateTime.utc_now()
    }
    
    Map.put(item, "error", error_info)
  end

  defp get_nested_value(data, path) when is_binary(path) do
    path_parts = String.split(path, ".")
    get_nested_value(data, path_parts)
  end
  defp get_nested_value(data, []), do: data
  defp get_nested_value(data, [key | rest]) when is_map(data) do
    case Map.get(data, key) do
      nil -> nil
      value -> get_nested_value(value, rest)
    end
  end
  defp get_nested_value(_, _), do: nil

  defp set_nested_value(data, path, value) when is_binary(path) do
    path_parts = String.split(path, ".")
    set_nested_value(data, path_parts, value)
  end
  defp set_nested_value(data, [key], value) when is_map(data) do
    Map.put(data, key, value)
  end
  defp set_nested_value(data, [key | rest], value) when is_map(data) do
    nested_data = Map.get(data, key, %{})
    updated_nested = set_nested_value(nested_data, rest, value)
    Map.put(data, key, updated_nested)
  end
  defp set_nested_value(data, _, _), do: data

  defp compare_values(val1, val2) do
    cond do
      is_number(val1) and is_number(val2) -> val1 - val2
      is_binary(val1) and is_binary(val2) -> String.compare(val1, val2)
      true -> 0
    end
  end

  defp is_truthy(nil), do: false
  defp is_truthy(false), do: false
  defp is_truthy(0), do: false
  defp is_truthy(""), do: false
  defp is_truthy([]), do: false
  defp is_truthy(%{} = map) when map_size(map) == 0, do: false
  defp is_truthy(_), do: true
end
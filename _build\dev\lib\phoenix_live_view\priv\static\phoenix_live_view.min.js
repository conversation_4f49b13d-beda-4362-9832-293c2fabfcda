var LiveView=(()=>{var pt=Object.defineProperty,Pi=Object.defineProperties;var yi=Object.getOwnPropertyDescriptors;var je=Object.getOwnPropertySymbols;var Dt=Object.prototype.hasOwnProperty,Ht=Object.prototype.propertyIsEnumerable;var Ft=(s,e,t)=>e in s?pt(s,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):s[e]=t,F=(s,e)=>{for(var t in e||(e={}))Dt.call(e,t)&&Ft(s,t,e[t]);if(je)for(var t of je(e))Ht.call(e,t)&&Ft(s,t,e[t]);return s},Nt=(s,e)=>Pi(s,yi(e)),ki=s=>pt(s,"__esModule",{value:!0});var Ut=(s,e)=>{var t={};for(var i in s)Dt.call(s,i)&&e.indexOf(i)<0&&(t[i]=s[i]);if(s!=null&&je)for(var i of je(s))e.indexOf(i)<0&&Ht.call(s,i)&&(t[i]=s[i]);return t};var Ci=(s,e)=>{ki(s);for(var t in e)pt(s,t,{get:e[t],enumerable:!0})};var Gi={};Ci(Gi,{LiveSocket:()=>ct});var We="consecutive-reloads",Mt=10,Xt=5e3,$t=1e4,Bt=3e4,qe=["phx-click-loading","phx-change-loading","phx-submit-loading","phx-keydown-loading","phx-keyup-loading","phx-blur-loading","phx-focus-loading","phx-hook-loading"],N="data-phx-component",Ke="data-phx-link",Vt="track-static",Jt="data-phx-link-state",T="data-phx-ref",Y="data-phx-ref-src",Ge="track-uploads",U="data-phx-upload-ref",be="data-phx-preflighted-refs",jt="data-phx-done-refs",mt="drop-target",Ie="data-phx-active-refs",Ee="phx:live-file:updated",ze="data-phx-skip",Ye="data-phx-id",gt="data-phx-prune",vt="page-loading",bt="phx-connected",Ae="phx-loading",Oe="phx-no-feedback",Le="phx-error",Et="phx-client-error",Qe="phx-server-error",Q="data-phx-parent-id",Se="data-phx-main",W="data-phx-root-id",De="viewport-top",He="viewport-bottom",Wt="trigger-action",de="feedback-for",ce="feedback-group",Fe="phx-has-focused",qt=["text","textarea","number","email","password","search","tel","url","date","time","datetime-local","color","range"],Ze=["checkbox","radio"],_e="phx-has-submitted",M="data-phx-session",Z=`[${M}]`,At="data-phx-sticky",te="data-phx-static",et="data-phx-readonly",ue="data-phx-disabled",Ne="disable-with",Ue="data-phx-disable-with-restore",we="hook",Kt="debounce",Gt="throttle",Pe="update",tt="stream",it="data-phx-stream",zt="key",q="phxPrivate",St="auto-recover",Me="phx:live-socket:debug",st="phx:live-socket:profiling",rt="phx:live-socket:latency-sim",Yt="progress",_t="mounted",Qt=1,Zt=200,ei="phx-",ti=3e4;var ye="debounce-trigger",ke="throttled",wt="debounce-prev-key",ii={debounce:300,throttle:300},Xe="d",K="s",nt="r",R="c",Pt="e",yt="r",kt="t",si="p",Ct="stream";var ot=class{constructor(e,t,i){this.liveSocket=i,this.entry=e,this.offset=0,this.chunkSize=t,this.chunkTimer=null,this.errored=!1,this.uploadChannel=i.channel(`lvu:${e.ref}`,{token:e.metadata()})}error(e){this.errored||(this.uploadChannel.leave(),this.errored=!0,clearTimeout(this.chunkTimer),this.entry.error(e))}upload(){this.uploadChannel.onError(e=>this.error(e)),this.uploadChannel.join().receive("ok",e=>this.readNextChunk()).receive("error",e=>this.error(e))}isDone(){return this.offset>=this.entry.file.size}readNextChunk(){let e=new window.FileReader,t=this.entry.file.slice(this.offset,this.chunkSize+this.offset);e.onload=i=>{if(i.target.error===null)this.offset+=i.target.result.byteLength,this.pushChunk(i.target.result);else return C("Read error: "+i.target.error)},e.readAsArrayBuffer(t)}pushChunk(e){!this.uploadChannel.isJoined()||this.uploadChannel.push("chunk",e).receive("ok",()=>{this.entry.progress(this.offset/this.entry.file.size*100),this.isDone()||(this.chunkTimer=setTimeout(()=>this.readNextChunk(),this.liveSocket.getLatencySim()||0))}).receive("error",({reason:t})=>this.error(t))}};var C=(s,e)=>console.error&&console.error(s,e),$=s=>{let e=typeof s;return e==="number"||e==="string"&&/^(0|[1-9]\d*)$/.test(s)};function ri(){let s=new Set,e=document.querySelectorAll("*[id]");for(let t=0,i=e.length;t<i;t++)s.has(e[t].id)?console.error(`Multiple IDs detected: ${e[t].id}. Ensure unique element ids.`):s.add(e[t].id)}var ni=(s,e,t,i)=>{s.liveSocket.isDebugEnabled()&&console.log(`${s.id} ${e}: ${t} - `,i)},Ce=s=>typeof s=="function"?s:function(){return s},xe=s=>JSON.parse(JSON.stringify(s)),fe=(s,e,t)=>{do{if(s.matches(`[${e}]`)&&!s.disabled)return s;s=s.parentElement||s.parentNode}while(s!==null&&s.nodeType===1&&!(t&&t.isSameNode(s)||s.matches(Z)));return null},pe=s=>s!==null&&typeof s=="object"&&!(s instanceof Array),oi=(s,e)=>JSON.stringify(s)===JSON.stringify(e),xt=s=>{for(let e in s)return!1;return!0},G=(s,e)=>s&&e(s),ai=function(s,e,t,i){s.forEach(r=>{new ot(r,t.config.chunk_size,i).upload()})};var li={canPushState(){return typeof history.pushState!="undefined"},dropLocal(s,e,t){return s.removeItem(this.localKey(e,t))},updateLocal(s,e,t,i,r){let n=this.getLocal(s,e,t),o=this.localKey(e,t),a=n===null?i:r(n);return s.setItem(o,JSON.stringify(a)),a},getLocal(s,e,t){return JSON.parse(s.getItem(this.localKey(e,t)))},updateCurrentState(s){!this.canPushState()||history.replaceState(s(history.state||{}),"",window.location.href)},pushState(s,e,t){if(this.canPushState()){if(t!==window.location.href){if(e.type=="redirect"&&e.scroll){let r=history.state||{};r.scroll=e.scroll,history.replaceState(r,"",window.location.href)}delete e.scroll,history[s+"State"](e,"",t||null);let i=this.getHashTargetEl(window.location.hash);i?i.scrollIntoView():e.type==="redirect"&&window.scroll(0,0)}}else this.redirect(t)},setCookie(s,e){document.cookie=`${s}=${e}`},getCookie(s){return document.cookie.replace(new RegExp(`(?:(?:^|.*;s*)${s}s*=s*([^;]*).*$)|^.*$`),"$1")},redirect(s,e){e&&li.setCookie("__phoenix_flash__",e+"; max-age=60000; path=/"),window.location=s},localKey(s,e){return`${s}-${e}`},getHashTargetEl(s){let e=s.toString().substring(1);if(e!=="")return document.getElementById(e)||document.querySelector(`a[name="${e}"]`)}},B=li;var xi={focusMain(){let s=document.querySelector("main h1, main, h1");if(s){let e=s.tabIndex;s.tabIndex=-1,s.focus(),s.tabIndex=e}},anyOf(s,e){return e.find(t=>s instanceof t)},isFocusable(s,e){return s instanceof HTMLAnchorElement&&s.rel!=="ignore"||s instanceof HTMLAreaElement&&s.href!==void 0||!s.disabled&&this.anyOf(s,[HTMLInputElement,HTMLSelectElement,HTMLTextAreaElement,HTMLButtonElement])||s instanceof HTMLIFrameElement||s.tabIndex>0||!e&&s.getAttribute("tabindex")!==null&&s.getAttribute("aria-hidden")!=="true"},attemptFocus(s,e){if(this.isFocusable(s,e))try{s.focus()}catch(t){}return!!document.activeElement&&document.activeElement.isSameNode(s)},focusFirstInteractive(s){let e=s.firstElementChild;for(;e;){if(this.attemptFocus(e,!0)||this.focusFirstInteractive(e,!0))return!0;e=e.nextElementSibling}},focusFirst(s){let e=s.firstElementChild;for(;e;){if(this.attemptFocus(e)||this.focusFirst(e))return!0;e=e.nextElementSibling}},focusLast(s){let e=s.lastElementChild;for(;e;){if(this.attemptFocus(e)||this.focusLast(e))return!0;e=e.previousElementSibling}}},ne=xi;var hi=[],di=200,Ti={exec(s,e,t,i,r){let[n,o]=r||[null,{callback:r&&r.callback}];(e.charAt(0)==="["?JSON.parse(e):[[n,o]]).forEach(([l,d])=>{l===n&&o.data&&(d.data=Object.assign(d.data||{},o.data),d.callback=d.callback||o.callback),this.filterToEls(i,d).forEach(c=>{this[`exec_${l}`](s,e,t,i,c,d)})})},isVisible(s){return!!(s.offsetWidth||s.offsetHeight||s.getClientRects().length>0)},isInViewport(s){let e=s.getBoundingClientRect(),t=window.innerHeight||document.documentElement.clientHeight,i=window.innerWidth||document.documentElement.clientWidth;return e.right>0&&e.bottom>0&&e.left<i&&e.top<t},exec_exec(s,e,t,i,r,{attr:n,to:o}){(o?h.all(document,o):[i]).forEach(l=>{let d=l.getAttribute(n);if(!d)throw new Error(`expected ${n} to contain JS command on "${o}"`);t.liveSocket.execJS(l,d,s)})},exec_dispatch(s,e,t,i,r,{to:n,event:o,detail:a,bubbles:l}){a=a||{},a.dispatcher=i,h.dispatchEvent(r,o,{detail:a,bubbles:l})},exec_push(s,e,t,i,r,n){let{event:o,data:a,target:l,page_loading:d,loading:c,value:f,dispatcher:v,callback:m}=n,p={loading:c,value:f,target:l,page_loading:!!d},E=s==="change"&&v?v:i,k=l||E.getAttribute(t.binding("target"))||E;t.withinTargets(k,(O,H)=>{if(!!O.isConnected())if(s==="change"){let{newCid:L,_target:J}=n;J=J||(h.isFormInput(i)?i.name:void 0),J&&(p._target=J),O.pushInput(i,H,L,o||e,p,m)}else if(s==="submit"){let{submitter:L}=n;O.submitForm(i,H,o||e,L,p,m)}else O.pushEvent(s,i,H,o||e,a,p,m)})},exec_navigate(s,e,t,i,r,{href:n,replace:o}){t.liveSocket.historyRedirect(n,o?"replace":"push")},exec_patch(s,e,t,i,r,{href:n,replace:o}){t.liveSocket.pushHistoryPatch(n,o?"replace":"push",i)},exec_focus(s,e,t,i,r){window.requestAnimationFrame(()=>ne.attemptFocus(r))},exec_focus_first(s,e,t,i,r){window.requestAnimationFrame(()=>ne.focusFirstInteractive(r)||ne.focusFirst(r))},exec_push_focus(s,e,t,i,r){window.requestAnimationFrame(()=>hi.push(r||i))},exec_pop_focus(s,e,t,i,r){window.requestAnimationFrame(()=>{let n=hi.pop();n&&n.focus()})},exec_add_class(s,e,t,i,r,{names:n,transition:o,time:a}){this.addOrRemoveClasses(r,n,[],o,a,t)},exec_remove_class(s,e,t,i,r,{names:n,transition:o,time:a}){this.addOrRemoveClasses(r,[],n,o,a,t)},exec_toggle_class(s,e,t,i,r,{to:n,names:o,transition:a,time:l}){this.toggleClasses(r,o,a,l,t)},exec_toggle_attr(s,e,t,i,r,{attr:[n,o,a]}){r.hasAttribute(n)?a!==void 0?r.getAttribute(n)===o?this.setOrRemoveAttrs(r,[[n,a]],[]):this.setOrRemoveAttrs(r,[[n,o]],[]):this.setOrRemoveAttrs(r,[],[n]):this.setOrRemoveAttrs(r,[[n,o]],[])},exec_transition(s,e,t,i,r,{time:n,transition:o}){this.addOrRemoveClasses(r,[],[],o,n,t)},exec_toggle(s,e,t,i,r,{display:n,ins:o,outs:a,time:l}){this.toggle(s,t,r,n,o,a,l)},exec_show(s,e,t,i,r,{display:n,transition:o,time:a}){this.show(s,t,r,n,o,a)},exec_hide(s,e,t,i,r,{display:n,transition:o,time:a}){this.hide(s,t,r,n,o,a)},exec_set_attr(s,e,t,i,r,{attr:[n,o]}){this.setOrRemoveAttrs(r,[[n,o]],[])},exec_remove_attr(s,e,t,i,r,{attr:n}){this.setOrRemoveAttrs(r,[],[n])},show(s,e,t,i,r,n){this.isVisible(t)||this.toggle(s,e,t,i,r,null,n)},hide(s,e,t,i,r,n){this.isVisible(t)&&this.toggle(s,e,t,i,null,r,n)},toggle(s,e,t,i,r,n,o){o=o||di;let[a,l,d]=r||[[],[],[]],[c,f,v]=n||[[],[],[]];if(a.length>0||c.length>0)if(this.isVisible(t)){let m=()=>{this.addOrRemoveClasses(t,f,a.concat(l).concat(d)),window.requestAnimationFrame(()=>{this.addOrRemoveClasses(t,c,[]),window.requestAnimationFrame(()=>this.addOrRemoveClasses(t,v,f))})};t.dispatchEvent(new Event("phx:hide-start")),e.transition(o,m,()=>{this.addOrRemoveClasses(t,[],c.concat(v)),h.putSticky(t,"toggle",p=>p.style.display="none"),t.dispatchEvent(new Event("phx:hide-end"))})}else{if(s==="remove")return;let m=()=>{this.addOrRemoveClasses(t,l,c.concat(f).concat(v));let p=i||this.defaultDisplay(t);h.putSticky(t,"toggle",E=>E.style.display=p),window.requestAnimationFrame(()=>{this.addOrRemoveClasses(t,a,[]),window.requestAnimationFrame(()=>this.addOrRemoveClasses(t,d,l))})};t.dispatchEvent(new Event("phx:show-start")),e.transition(o,m,()=>{this.addOrRemoveClasses(t,[],a.concat(d)),t.dispatchEvent(new Event("phx:show-end"))})}else this.isVisible(t)?window.requestAnimationFrame(()=>{t.dispatchEvent(new Event("phx:hide-start")),h.putSticky(t,"toggle",m=>m.style.display="none"),t.dispatchEvent(new Event("phx:hide-end"))}):window.requestAnimationFrame(()=>{t.dispatchEvent(new Event("phx:show-start"));let m=i||this.defaultDisplay(t);h.putSticky(t,"toggle",p=>p.style.display=m),t.dispatchEvent(new Event("phx:show-end"))})},toggleClasses(s,e,t,i,r){window.requestAnimationFrame(()=>{let[n,o]=h.getSticky(s,"classes",[[],[]]),a=e.filter(d=>n.indexOf(d)<0&&!s.classList.contains(d)),l=e.filter(d=>o.indexOf(d)<0&&s.classList.contains(d));this.addOrRemoveClasses(s,a,l,t,i,r)})},addOrRemoveClasses(s,e,t,i,r,n){r=r||di;let[o,a,l]=i||[[],[],[]];if(o.length>0){let d=()=>{this.addOrRemoveClasses(s,a,[].concat(o).concat(l)),window.requestAnimationFrame(()=>{this.addOrRemoveClasses(s,o,[]),window.requestAnimationFrame(()=>this.addOrRemoveClasses(s,l,a))})},c=()=>this.addOrRemoveClasses(s,e.concat(l),t.concat(o).concat(a));return n.transition(r,d,c)}window.requestAnimationFrame(()=>{let[d,c]=h.getSticky(s,"classes",[[],[]]),f=e.filter(E=>d.indexOf(E)<0&&!s.classList.contains(E)),v=t.filter(E=>c.indexOf(E)<0&&s.classList.contains(E)),m=d.filter(E=>t.indexOf(E)<0).concat(f),p=c.filter(E=>e.indexOf(E)<0).concat(v);h.putSticky(s,"classes",E=>(E.classList.remove(...p),E.classList.add(...m),[m,p]))})},setOrRemoveAttrs(s,e,t){let[i,r]=h.getSticky(s,"attrs",[[],[]]),n=e.map(([l,d])=>l).concat(t),o=i.filter(([l,d])=>!n.includes(l)).concat(e),a=r.filter(l=>!n.includes(l)).concat(t);h.putSticky(s,"attrs",l=>(a.forEach(d=>l.removeAttribute(d)),o.forEach(([d,c])=>l.setAttribute(d,c)),[o,a]))},hasAllClasses(s,e){return e.every(t=>s.classList.contains(t))},isToggledOut(s,e){return!this.isVisible(s)||this.hasAllClasses(s,e)},filterToEls(s,{to:e}){return e?h.all(document,e):[s]},defaultDisplay(s){return{tr:"table-row",td:"table-cell"}[s.tagName.toLowerCase()]||"block"}},x=Ti;var V={byId(s){return document.getElementById(s)||C(`no id found for ${s}`)},removeClass(s,e){s.classList.remove(e),s.classList.length===0&&s.removeAttribute("class")},all(s,e,t){if(!s)return[];let i=Array.from(s.querySelectorAll(e));return t?i.forEach(t):i},childNodeLength(s){let e=document.createElement("template");return e.innerHTML=s,e.content.childElementCount},isUploadInput(s){return s.type==="file"&&s.getAttribute(U)!==null},isAutoUpload(s){return s.hasAttribute("data-phx-auto-upload")},findUploadInputs(s){let e=s.id,t=this.all(document,`input[type="file"][${U}][form="${e}"]`);return this.all(s,`input[type="file"][${U}]`).concat(t)},findComponentNodeList(s,e){return this.filterWithinSameLiveView(this.all(s,`[${N}="${e}"]`),s)},isPhxDestroyed(s){return!!(s.id&&V.private(s,"destroyed"))},wantsNewTab(s){let e=s.ctrlKey||s.shiftKey||s.metaKey||s.button&&s.button===1,t=s.target instanceof HTMLAnchorElement&&s.target.hasAttribute("download"),i=s.target.hasAttribute("target")&&s.target.getAttribute("target").toLowerCase()==="_blank",r=s.target.hasAttribute("target")&&!s.target.getAttribute("target").startsWith("_");return e||i||t||r},isUnloadableFormSubmit(s){return s.target&&s.target.getAttribute("method")==="dialog"||s.submitter&&s.submitter.getAttribute("formmethod")==="dialog"?!1:!s.defaultPrevented&&!this.wantsNewTab(s)},isNewPageClick(s,e){let t=s.target instanceof HTMLAnchorElement?s.target.getAttribute("href"):null,i;if(s.defaultPrevented||t===null||this.wantsNewTab(s)||t.startsWith("mailto:")||t.startsWith("tel:")||s.target.isContentEditable)return!1;try{i=new URL(t)}catch(r){try{i=new URL(t,e)}catch(n){return!0}}return i.host===e.host&&i.protocol===e.protocol&&i.pathname===e.pathname&&i.search===e.search?i.hash===""&&!i.href.endsWith("#"):i.protocol.startsWith("http")},markPhxChildDestroyed(s){this.isPhxChild(s)&&s.setAttribute(M,""),this.putPrivate(s,"destroyed",!0)},findPhxChildrenInFragment(s,e){let t=document.createElement("template");return t.innerHTML=s,this.findPhxChildren(t.content,e)},isIgnored(s,e){return(s.getAttribute(e)||s.getAttribute("data-phx-update"))==="ignore"},isPhxUpdate(s,e,t){return s.getAttribute&&t.indexOf(s.getAttribute(e))>=0},findPhxSticky(s){return this.all(s,`[${At}]`)},findPhxChildren(s,e){return this.all(s,`${Z}[${Q}="${e}"]`)},findExistingParentCIDs(s,e){let t=new Set,i=new Set;return e.forEach(r=>{this.filterWithinSameLiveView(this.all(s,`[${N}="${r}"]`),s).forEach(n=>{t.add(r),this.all(n,`[${N}]`).map(o=>parseInt(o.getAttribute(N))).forEach(o=>i.add(o))})}),i.forEach(r=>t.delete(r)),t},filterWithinSameLiveView(s,e){return e.querySelector(Z)?s.filter(t=>this.withinSameLiveView(t,e)):s},withinSameLiveView(s,e){for(;s=s.parentNode;){if(s.isSameNode(e))return!0;if(s.getAttribute(M)!==null)return!1}},private(s,e){return s[q]&&s[q][e]},deletePrivate(s,e){s[q]&&delete s[q][e]},putPrivate(s,e,t){s[q]||(s[q]={}),s[q][e]=t},updatePrivate(s,e,t,i){let r=this.private(s,e);r===void 0?this.putPrivate(s,e,i(t)):this.putPrivate(s,e,i(r))},copyPrivates(s,e){e[q]&&(s[q]=e[q])},putTitle(s){let e=document.querySelector("title");if(e){let{prefix:t,suffix:i}=e.dataset;document.title=`${t||""}${s}${i||""}`}else document.title=s},debounce(s,e,t,i,r,n,o,a){let l=s.getAttribute(t),d=s.getAttribute(r);l===""&&(l=i),d===""&&(d=n);let c=l||d;switch(c){case null:return a();case"blur":this.once(s,"debounce-blur")&&s.addEventListener("blur",()=>{o()&&a()});return;default:let f=parseInt(c),v=()=>d?this.deletePrivate(s,ke):a(),m=this.incCycle(s,ye,v);if(isNaN(f))return C(`invalid throttle/debounce value: ${c}`);if(d){let E=!1;if(e.type==="keydown"){let k=this.private(s,wt);this.putPrivate(s,wt,e.key),E=k!==e.key}if(!E&&this.private(s,ke))return!1;{a();let k=setTimeout(()=>{o()&&this.triggerCycle(s,ye)},f);this.putPrivate(s,ke,k)}}else setTimeout(()=>{o()&&this.triggerCycle(s,ye,m)},f);let p=s.form;p&&this.once(p,"bind-debounce")&&p.addEventListener("submit",()=>{Array.from(new FormData(p).entries(),([E])=>{let k=p.querySelector(`[name="${E}"]`);this.incCycle(k,ye),this.deletePrivate(k,ke)})}),this.once(s,"bind-debounce")&&s.addEventListener("blur",()=>{clearTimeout(this.private(s,ke)),this.triggerCycle(s,ye)})}},triggerCycle(s,e,t){let[i,r]=this.private(s,e);t||(t=i),t===i&&(this.incCycle(s,e),r())},once(s,e){return this.private(s,e)===!0?!1:(this.putPrivate(s,e,!0),!0)},incCycle(s,e,t=function(){}){let[i]=this.private(s,e)||[0,t];return i++,this.putPrivate(s,e,[i,t]),i},maybeAddPrivateHooks(s,e,t){s.hasAttribute&&(s.hasAttribute(e)||s.hasAttribute(t))&&s.setAttribute("data-phx-hook","Phoenix.InfiniteScroll")},isFeedbackContainer(s,e){return s.hasAttribute&&s.hasAttribute(e)},maybeHideFeedback(s,e,t,i){let r={};e.forEach(n=>{if(!s.contains(n))return;let o=n.getAttribute(t);if(!o){x.addOrRemoveClasses(n,[],[Oe]);return}if(r[o]===!0){this.hideFeedback(n);return}r[o]=this.shouldHideFeedback(s,o,i),r[o]===!0&&this.hideFeedback(n)})},hideFeedback(s){x.addOrRemoveClasses(s,[Oe],[])},shouldHideFeedback(s,e,t){let i=`[name="${e}"],
                   [name="${e}[]"],
                   [${t}="${e}"]`,r=!1;return V.all(s,i,n=>{(this.private(n,Fe)||this.private(n,_e))&&(r=!0)}),!r},feedbackSelector(s,e,t){let i=`[${e}="${s.name}"],
                 [${e}="${s.name.replace(/\[\]$/,"")}"]`;return s.getAttribute(t)&&(i+=`,[${e}="${s.getAttribute(t)}"]`),i},resetForm(s,e,t){Array.from(s.elements).forEach(i=>{let r=this.feedbackSelector(i,e,t);this.deletePrivate(i,Fe),this.deletePrivate(i,_e),this.all(document,r,n=>{x.addOrRemoveClasses(n,[Oe],[])})})},showError(s,e,t){if(s.name){let i=this.feedbackSelector(s,e,t);this.all(document,i,r=>{x.addOrRemoveClasses(r,[],[Oe])})}},isPhxChild(s){return s.getAttribute&&s.getAttribute(Q)},isPhxSticky(s){return s.getAttribute&&s.getAttribute(At)!==null},isChildOfAny(s,e){return!!e.find(t=>t.contains(s))},firstPhxChild(s){return this.isPhxChild(s)?s:this.all(s,`[${Q}]`)[0]},dispatchEvent(s,e,t={}){let i=!0;s.nodeName==="INPUT"&&s.type==="file"&&e==="click"&&(i=!1);let o={bubbles:t.bubbles===void 0?i:!!t.bubbles,cancelable:!0,detail:t.detail||{}},a=e==="click"?new MouseEvent("click",o):new CustomEvent(e,o);s.dispatchEvent(a)},cloneNode(s,e){if(typeof e=="undefined")return s.cloneNode(!0);{let t=s.cloneNode(!1);return t.innerHTML=e,t}},mergeAttrs(s,e,t={}){let i=new Set(t.exclude||[]),r=t.isIgnored,n=e.attributes;for(let a=n.length-1;a>=0;a--){let l=n[a].name;if(i.has(l))l==="value"&&s.value===e.value&&s.setAttribute("value",e.getAttribute(l));else{let d=e.getAttribute(l);s.getAttribute(l)!==d&&(!r||r&&l.startsWith("data-"))&&s.setAttribute(l,d)}}let o=s.attributes;for(let a=o.length-1;a>=0;a--){let l=o[a].name;r?l.startsWith("data-")&&!e.hasAttribute(l)&&![T,Y].includes(l)&&s.removeAttribute(l):e.hasAttribute(l)||s.removeAttribute(l)}},mergeFocusedInput(s,e){s instanceof HTMLSelectElement||V.mergeAttrs(s,e,{exclude:["value"]}),e.readOnly?s.setAttribute("readonly",!0):s.removeAttribute("readonly")},hasSelectionRange(s){return s.setSelectionRange&&(s.type==="text"||s.type==="textarea")},restoreFocus(s,e,t){if(s instanceof HTMLSelectElement&&s.focus(),!V.isTextualInput(s))return;s.matches(":focus")||s.focus(),this.hasSelectionRange(s)&&s.setSelectionRange(e,t)},isFormInput(s){return/^(?:input|select|textarea)$/i.test(s.tagName)&&s.type!=="button"},syncAttrsToProps(s){s instanceof HTMLInputElement&&Ze.indexOf(s.type.toLocaleLowerCase())>=0&&(s.checked=s.getAttribute("checked")!==null)},isTextualInput(s){return qt.indexOf(s.type)>=0},isNowTriggerFormExternal(s,e){return s.getAttribute&&s.getAttribute(e)!==null},syncPendingRef(s,e,t){let i=s.getAttribute(T);if(i===null)return!0;let r=s.getAttribute(Y);return V.isFormInput(s)||s.getAttribute(t)!==null?(V.isUploadInput(s)&&V.mergeAttrs(s,e,{isIgnored:!0}),V.putPrivate(s,T,e),!1):(qe.forEach(n=>{s.classList.contains(n)&&e.classList.add(n)}),e.setAttribute(T,i),e.setAttribute(Y,r),!0)},cleanChildNodes(s,e){if(V.isPhxUpdate(s,e,["append","prepend"])){let t=[];s.childNodes.forEach(i=>{i.id||(!(i.nodeType===Node.TEXT_NODE&&i.nodeValue.trim()==="")&&i.nodeType!==Node.COMMENT_NODE&&C(`only HTML element tags with an id are allowed inside containers with phx-update.

removing illegal node: "${(i.outerHTML||i.nodeValue).trim()}"

`),t.push(i))}),t.forEach(i=>i.remove())}},replaceRootContainer(s,e,t){let i=new Set(["id",M,te,Se,W]);if(s.tagName.toLowerCase()===e.toLowerCase())return Array.from(s.attributes).filter(r=>!i.has(r.name.toLowerCase())).forEach(r=>s.removeAttribute(r.name)),Object.keys(t).filter(r=>!i.has(r.toLowerCase())).forEach(r=>s.setAttribute(r,t[r])),s;{let r=document.createElement(e);return Object.keys(t).forEach(n=>r.setAttribute(n,t[n])),i.forEach(n=>r.setAttribute(n,s.getAttribute(n))),r.innerHTML=s.innerHTML,s.replaceWith(r),r}},getSticky(s,e,t){let i=(V.private(s,"sticky")||[]).find(([r])=>e===r);if(i){let[r,n,o]=i;return o}else return typeof t=="function"?t():t},deleteSticky(s,e){this.updatePrivate(s,"sticky",[],t=>t.filter(([i,r])=>i!==e))},putSticky(s,e,t){let i=t(s);this.updatePrivate(s,"sticky",[],r=>{let n=r.findIndex(([o])=>e===o);return n>=0?r[n]=[e,t,i]:r.push([e,t,i]),r})},applyStickyOperations(s){let e=V.private(s,"sticky");!e||e.forEach(([t,i,r])=>this.putSticky(s,t,i))}},h=V;var oe=class{static isActive(e,t){let i=t._phxRef===void 0,n=e.getAttribute(Ie).split(",").indexOf(_.genFileRef(t))>=0;return t.size>0&&(i||n)}static isPreflighted(e,t){return e.getAttribute(be).split(",").indexOf(_.genFileRef(t))>=0&&this.isActive(e,t)}static isPreflightInProgress(e){return e._preflightInProgress===!0}static markPreflightInProgress(e){e._preflightInProgress=!0}constructor(e,t,i,r){this.ref=_.genFileRef(t),this.fileEl=e,this.file=t,this.view=i,this.meta=null,this._isCancelled=!1,this._isDone=!1,this._progress=0,this._lastProgressSent=-1,this._onDone=function(){},this._onElUpdated=this.onElUpdated.bind(this),this.fileEl.addEventListener(Ee,this._onElUpdated),this.autoUpload=r}metadata(){return this.meta}progress(e){this._progress=Math.floor(e),this._progress>this._lastProgressSent&&(this._progress>=100?(this._progress=100,this._lastProgressSent=100,this._isDone=!0,this.view.pushFileProgress(this.fileEl,this.ref,100,()=>{_.untrackFile(this.fileEl,this.file),this._onDone()})):(this._lastProgressSent=this._progress,this.view.pushFileProgress(this.fileEl,this.ref,this._progress)))}isCancelled(){return this._isCancelled}cancel(){this.file._preflightInProgress=!1,this._isCancelled=!0,this._isDone=!0,this._onDone()}isDone(){return this._isDone}error(e="failed"){this.fileEl.removeEventListener(Ee,this._onElUpdated),this.view.pushFileProgress(this.fileEl,this.ref,{error:e}),this.isAutoUpload()||_.clearFiles(this.fileEl)}isAutoUpload(){return this.autoUpload}onDone(e){this._onDone=()=>{this.fileEl.removeEventListener(Ee,this._onElUpdated),e()}}onElUpdated(){this.fileEl.getAttribute(Ie).split(",").indexOf(this.ref)===-1&&(_.untrackFile(this.fileEl,this.file),this.cancel())}toPreflightPayload(){return{last_modified:this.file.lastModified,name:this.file.name,relative_path:this.file.webkitRelativePath,size:this.file.size,type:this.file.type,ref:this.ref,meta:typeof this.file.meta=="function"?this.file.meta():void 0}}uploader(e){if(this.meta.uploader){let t=e[this.meta.uploader]||C(`no uploader configured for ${this.meta.uploader}`);return{name:this.meta.uploader,callback:t}}else return{name:"channel",callback:ai}}zipPostFlight(e){this.meta=e.entries[this.ref],this.meta||C(`no preflight upload response returned with ref ${this.ref}`,{input:this.fileEl,response:e})}};var Ri=0,_=class{static genFileRef(e){let t=e._phxRef;return t!==void 0?t:(e._phxRef=(Ri++).toString(),e._phxRef)}static getEntryDataURL(e,t,i){let r=this.activeFiles(e).find(n=>this.genFileRef(n)===t);i(URL.createObjectURL(r))}static hasUploadsInProgress(e){let t=0;return h.findUploadInputs(e).forEach(i=>{i.getAttribute(be)!==i.getAttribute(jt)&&t++}),t>0}static serializeUploads(e){let t=this.activeFiles(e),i={};return t.forEach(r=>{let n={path:e.name},o=e.getAttribute(U);i[o]=i[o]||[],n.ref=this.genFileRef(r),n.last_modified=r.lastModified,n.name=r.name||n.ref,n.relative_path=r.webkitRelativePath,n.type=r.type,n.size=r.size,typeof r.meta=="function"&&(n.meta=r.meta()),i[o].push(n)}),i}static clearFiles(e){e.value=null,e.removeAttribute(U),h.putPrivate(e,"files",[])}static untrackFile(e,t){h.putPrivate(e,"files",h.private(e,"files").filter(i=>!Object.is(i,t)))}static trackFiles(e,t,i){if(e.getAttribute("multiple")!==null){let r=t.filter(n=>!this.activeFiles(e).find(o=>Object.is(o,n)));h.updatePrivate(e,"files",[],n=>n.concat(r)),e.value=null}else i&&i.files.length>0&&(e.files=i.files),h.putPrivate(e,"files",t)}static activeFileInputs(e){let t=h.findUploadInputs(e);return Array.from(t).filter(i=>i.files&&this.activeFiles(i).length>0)}static activeFiles(e){return(h.private(e,"files")||[]).filter(t=>oe.isActive(e,t))}static inputsAwaitingPreflight(e){let t=h.findUploadInputs(e);return Array.from(t).filter(i=>this.filesAwaitingPreflight(i).length>0)}static filesAwaitingPreflight(e){return this.activeFiles(e).filter(t=>!oe.isPreflighted(e,t)&&!oe.isPreflightInProgress(t))}static markPreflightInProgress(e){e.forEach(t=>oe.markPreflightInProgress(t.file))}constructor(e,t,i){this.autoUpload=h.isAutoUpload(e),this.view=t,this.onComplete=i,this._entries=Array.from(_.filesAwaitingPreflight(e)||[]).map(r=>new oe(e,r,t,this.autoUpload)),_.markPreflightInProgress(this._entries),this.numEntriesInProgress=this._entries.length}isAutoUpload(){return this.autoUpload}entries(){return this._entries}initAdapterUpload(e,t,i){this._entries=this._entries.map(n=>(n.isCancelled()?(this.numEntriesInProgress--,this.numEntriesInProgress===0&&this.onComplete()):(n.zipPostFlight(e),n.onDone(()=>{this.numEntriesInProgress--,this.numEntriesInProgress===0&&this.onComplete()})),n));let r=this._entries.reduce((n,o)=>{if(!o.meta)return n;let{name:a,callback:l}=o.uploader(i.uploaders);return n[a]=n[a]||{callback:l,entries:[]},n[a].entries.push(o),n},{});for(let n in r){let{callback:o,entries:a}=r[n];o(a,t,e,i)}}};var ci={LiveFileUpload:{activeRefs(){return this.el.getAttribute(Ie)},preflightedRefs(){return this.el.getAttribute(be)},mounted(){this.preflightedWas=this.preflightedRefs()},updated(){let s=this.preflightedRefs();this.preflightedWas!==s&&(this.preflightedWas=s,s===""&&this.__view.cancelSubmit(this.el.form)),this.activeRefs()===""&&(this.el.value=null),this.el.dispatchEvent(new CustomEvent(Ee))}},LiveImgPreview:{mounted(){this.ref=this.el.getAttribute("data-phx-entry-ref"),this.inputEl=document.getElementById(this.el.getAttribute(U)),_.getEntryDataURL(this.inputEl,this.ref,s=>{this.url=s,this.el.src=s})},destroyed(){URL.revokeObjectURL(this.url)}},FocusWrap:{mounted(){this.focusStart=this.el.firstElementChild,this.focusEnd=this.el.lastElementChild,this.focusStart.addEventListener("focus",()=>ne.focusLast(this.el)),this.focusEnd.addEventListener("focus",()=>ne.focusFirst(this.el)),this.el.addEventListener("phx:show-end",()=>this.el.focus()),window.getComputedStyle(this.el).display!=="none"&&ne.focusFirst(this.el)}}},ui=s=>["HTML","BODY"].indexOf(s.nodeName.toUpperCase())>=0?null:["scroll","auto"].indexOf(getComputedStyle(s).overflowY)>=0?s:ui(s.parentElement),fi=s=>s?s.scrollTop:document.documentElement.scrollTop||document.body.scrollTop,Tt=s=>s?s.getBoundingClientRect().bottom:window.innerHeight||document.documentElement.clientHeight,Rt=s=>s?s.getBoundingClientRect().top:0,Ii=(s,e)=>{let t=s.getBoundingClientRect();return t.top>=Rt(e)&&t.left>=0&&t.top<=Tt(e)},Oi=(s,e)=>{let t=s.getBoundingClientRect();return t.right>=Rt(e)&&t.left>=0&&t.bottom<=Tt(e)},pi=(s,e)=>{let t=s.getBoundingClientRect();return t.top>=Rt(e)&&t.left>=0&&t.top<=Tt(e)};ci.InfiniteScroll={mounted(){this.scrollContainer=ui(this.el);let s=fi(this.scrollContainer),e=!1,t=500,i=null,r=this.throttle(t,(a,l)=>{i=()=>!0,this.liveSocket.execJSHookPush(this.el,a,{id:l.id,_overran:!0},()=>{i=null})}),n=this.throttle(t,(a,l)=>{i=()=>l.scrollIntoView({block:"start"}),this.liveSocket.execJSHookPush(this.el,a,{id:l.id},()=>{i=null,window.requestAnimationFrame(()=>{pi(l,this.scrollContainer)||l.scrollIntoView({block:"start"})})})}),o=this.throttle(t,(a,l)=>{i=()=>l.scrollIntoView({block:"end"}),this.liveSocket.execJSHookPush(this.el,a,{id:l.id},()=>{i=null,window.requestAnimationFrame(()=>{pi(l,this.scrollContainer)||l.scrollIntoView({block:"end"})})})});this.onScroll=a=>{let l=fi(this.scrollContainer);if(i)return s=l,i();let d=this.el.getBoundingClientRect(),c=this.el.getAttribute(this.liveSocket.binding("viewport-top")),f=this.el.getAttribute(this.liveSocket.binding("viewport-bottom")),v=this.el.lastElementChild,m=this.el.firstElementChild,p=l<s,E=l>s;p&&c&&!e&&d.top>=0?(e=!0,r(c,m)):E&&e&&d.top<=0&&(e=!1),c&&p&&Ii(m,this.scrollContainer)?n(c,m):f&&E&&Oi(v,this.scrollContainer)&&o(f,v),s=l},this.scrollContainer?this.scrollContainer.addEventListener("scroll",this.onScroll):window.addEventListener("scroll",this.onScroll)},destroyed(){this.scrollContainer?this.scrollContainer.removeEventListener("scroll",this.onScroll):window.removeEventListener("scroll",this.onScroll)},throttle(s,e){let t=0,i;return(...r)=>{let n=Date.now(),o=s-(n-t);o<=0||o>s?(i&&(clearTimeout(i),i=null),t=n,e(...r)):i||(i=setTimeout(()=>{t=Date.now(),i=null,e(...r)},o))}}};var mi=ci;var at=class{constructor(e,t,i){let r=new Set,n=new Set([...t.children].map(a=>a.id)),o=[];Array.from(e.children).forEach(a=>{if(a.id&&(r.add(a.id),n.has(a.id))){let l=a.previousElementSibling&&a.previousElementSibling.id;o.push({elementId:a.id,previousElementId:l})}}),this.containerId=t.id,this.updateType=i,this.elementsToModify=o,this.elementIdsToAdd=[...n].filter(a=>!r.has(a))}perform(){let e=h.byId(this.containerId);this.elementsToModify.forEach(t=>{t.previousElementId?G(document.getElementById(t.previousElementId),i=>{G(document.getElementById(t.elementId),r=>{r.previousElementSibling&&r.previousElementSibling.id==i.id||i.insertAdjacentElement("afterend",r)})}):G(document.getElementById(t.elementId),i=>{i.previousElementSibling==null||e.insertAdjacentElement("afterbegin",i)})}),this.updateType=="prepend"&&this.elementIdsToAdd.reverse().forEach(t=>{G(document.getElementById(t),i=>e.insertAdjacentElement("afterbegin",i))})}};var gi=11;function Li(s,e){var t=e.attributes,i,r,n,o,a;if(!(e.nodeType===gi||s.nodeType===gi)){for(var l=t.length-1;l>=0;l--)i=t[l],r=i.name,n=i.namespaceURI,o=i.value,n?(r=i.localName||r,a=s.getAttributeNS(n,r),a!==o&&(i.prefix==="xmlns"&&(r=i.name),s.setAttributeNS(n,r,o))):(a=s.getAttribute(r),a!==o&&s.setAttribute(r,o));for(var d=s.attributes,c=d.length-1;c>=0;c--)i=d[c],r=i.name,n=i.namespaceURI,n?(r=i.localName||r,e.hasAttributeNS(n,r)||s.removeAttributeNS(n,r)):e.hasAttribute(r)||s.removeAttribute(r)}}var lt,Di="http://www.w3.org/1999/xhtml",D=typeof document=="undefined"?void 0:document,Hi=!!D&&"content"in D.createElement("template"),Fi=!!D&&D.createRange&&"createContextualFragment"in D.createRange();function Ni(s){var e=D.createElement("template");return e.innerHTML=s,e.content.childNodes[0]}function Ui(s){lt||(lt=D.createRange(),lt.selectNode(D.body));var e=lt.createContextualFragment(s);return e.childNodes[0]}function Mi(s){var e=D.createElement("body");return e.innerHTML=s,e.childNodes[0]}function Xi(s){return s=s.trim(),Hi?Ni(s):Fi?Ui(s):Mi(s)}function ht(s,e){var t=s.nodeName,i=e.nodeName,r,n;return t===i?!0:(r=t.charCodeAt(0),n=i.charCodeAt(0),r<=90&&n>=97?t===i.toUpperCase():n<=90&&r>=97?i===t.toUpperCase():!1)}function $i(s,e){return!e||e===Di?D.createElement(s):D.createElementNS(e,s)}function Bi(s,e){for(var t=s.firstChild;t;){var i=t.nextSibling;e.appendChild(t),t=i}return e}function It(s,e,t){s[t]!==e[t]&&(s[t]=e[t],s[t]?s.setAttribute(t,""):s.removeAttribute(t))}var vi={OPTION:function(s,e){var t=s.parentNode;if(t){var i=t.nodeName.toUpperCase();i==="OPTGROUP"&&(t=t.parentNode,i=t&&t.nodeName.toUpperCase()),i==="SELECT"&&!t.hasAttribute("multiple")&&(s.hasAttribute("selected")&&!e.selected&&(s.setAttribute("selected","selected"),s.removeAttribute("selected")),t.selectedIndex=-1)}It(s,e,"selected")},INPUT:function(s,e){It(s,e,"checked"),It(s,e,"disabled"),s.value!==e.value&&(s.value=e.value),e.hasAttribute("value")||s.removeAttribute("value")},TEXTAREA:function(s,e){var t=e.value;s.value!==t&&(s.value=t);var i=s.firstChild;if(i){var r=i.nodeValue;if(r==t||!t&&r==s.placeholder)return;i.nodeValue=t}},SELECT:function(s,e){if(!e.hasAttribute("multiple")){for(var t=-1,i=0,r=s.firstChild,n,o;r;)if(o=r.nodeName&&r.nodeName.toUpperCase(),o==="OPTGROUP")n=r,r=n.firstChild;else{if(o==="OPTION"){if(r.hasAttribute("selected")){t=i;break}i++}r=r.nextSibling,!r&&n&&(r=n.nextSibling,n=null)}s.selectedIndex=t}}},$e=1,bi=11,Ei=3,Ai=8;function ae(){}function Vi(s){if(s)return s.getAttribute&&s.getAttribute("id")||s.id}function Ji(s){return function(t,i,r){if(r||(r={}),typeof i=="string")if(t.nodeName==="#document"||t.nodeName==="HTML"||t.nodeName==="BODY"){var n=i;i=D.createElement("html"),i.innerHTML=n}else i=Xi(i);else i.nodeType===bi&&(i=i.firstElementChild);var o=r.getNodeKey||Vi,a=r.onBeforeNodeAdded||ae,l=r.onNodeAdded||ae,d=r.onBeforeElUpdated||ae,c=r.onElUpdated||ae,f=r.onBeforeNodeDiscarded||ae,v=r.onNodeDiscarded||ae,m=r.onBeforeElChildrenUpdated||ae,p=r.skipFromChildren||ae,E=r.addChild||function(A,b){return A.appendChild(b)},k=r.childrenOnly===!0,O=Object.create(null),H=[];function L(A){H.push(A)}function J(A,b){if(A.nodeType===$e)for(var P=A.firstChild;P;){var S=void 0;b&&(S=o(P))?L(S):(v(P),P.firstChild&&J(P,b)),P=P.nextSibling}}function j(A,b,P){f(A)!==!1&&(b&&b.removeChild(A),v(A),J(A,P))}function Re(A){if(A.nodeType===$e||A.nodeType===bi)for(var b=A.firstChild;b;){var P=o(b);P&&(O[P]=b),Re(b),b=b.nextSibling}}Re(t);function y(A){l(A);for(var b=A.firstChild;b;){var P=b.nextSibling,S=o(b);if(S){var w=O[S];w&&ht(b,w)?(b.parentNode.replaceChild(w,b),z(w,b)):y(b)}else y(b);b=P}}function he(A,b,P){for(;b;){var S=b.nextSibling;(P=o(b))?L(P):j(b,A,!0),b=S}}function z(A,b,P){var S=o(b);S&&delete O[S],!(!P&&(d(A,b)===!1||(s(A,b),c(A),m(A,b)===!1)))&&(A.nodeName!=="TEXTAREA"?u(A,b):vi.TEXTAREA(A,b))}function u(A,b){var P=p(A,b),S=b.firstChild,w=A.firstChild,ge,ee,ve,Ve,se;e:for(;S;){for(Ve=S.nextSibling,ge=o(S);!P&&w;){if(ve=w.nextSibling,S.isSameNode&&S.isSameNode(w)){S=Ve,w=ve;continue e}ee=o(w);var Je=w.nodeType,re=void 0;if(Je===S.nodeType&&(Je===$e?(ge?ge!==ee&&((se=O[ge])?ve===se?re=!1:(A.insertBefore(se,w),ee?L(ee):j(w,A,!0),w=se,ee=o(w)):re=!1):ee&&(re=!1),re=re!==!1&&ht(w,S),re&&z(w,S)):(Je===Ei||Je==Ai)&&(re=!0,w.nodeValue!==S.nodeValue&&(w.nodeValue=S.nodeValue))),re){S=Ve,w=ve;continue e}ee?L(ee):j(w,A,!0),w=ve}if(ge&&(se=O[ge])&&ht(se,S))P||E(A,se),z(se,S);else{var ft=a(S);ft!==!1&&(ft&&(S=ft),S.actualize&&(S=S.actualize(A.ownerDocument||D)),E(A,S),y(S))}S=Ve,w=ve}he(A,w,ee);var Lt=vi[A.nodeName];Lt&&Lt(A,b)}var g=t,I=g.nodeType,X=i.nodeType;if(!k){if(I===$e)X===$e?ht(t,i)||(v(t),g=Bi(t,$i(i.nodeName,i.namespaceURI))):g=i;else if(I===Ei||I===Ai){if(X===I)return g.nodeValue!==i.nodeValue&&(g.nodeValue=i.nodeValue),g;g=i}}if(g===i)v(t);else{if(i.isSameNode&&i.isSameNode(g))return;if(z(g,i,k),H)for(var ie=0,wi=H.length;ie<wi;ie++){var ut=O[H[ie]];ut&&j(ut,ut.parentNode,!1)}}return!k&&g!==t&&t.parentNode&&(g.actualize&&(g=g.actualize(t.ownerDocument||D)),t.parentNode.replaceChild(g,t)),g}}var ji=Ji(Li),Ot=ji;var me=class{static patchEl(e,t,i){Ot(e,t,{childrenOnly:!1,onBeforeElUpdated:(r,n)=>{if(i&&i.isSameNode(r)&&h.isFormInput(r))return h.mergeFocusedInput(r,n),!1}})}constructor(e,t,i,r,n,o){this.view=e,this.liveSocket=e.liveSocket,this.container=t,this.id=i,this.rootID=e.root.id,this.html=r,this.streams=n,this.streamInserts={},this.streamComponentRestore={},this.targetCID=o,this.cidPatch=$(this.targetCID),this.pendingRemoves=[],this.phxRemove=this.liveSocket.binding("remove"),this.callbacks={beforeadded:[],beforeupdated:[],beforephxChildAdded:[],afteradded:[],afterupdated:[],afterdiscarded:[],afterphxChildAdded:[],aftertransitionsDiscarded:[]}}before(e,t){this.callbacks[`before${e}`].push(t)}after(e,t){this.callbacks[`after${e}`].push(t)}trackBefore(e,...t){this.callbacks[`before${e}`].forEach(i=>i(...t))}trackAfter(e,...t){this.callbacks[`after${e}`].forEach(i=>i(...t))}markPrunableContentForRemoval(){let e=this.liveSocket.binding(Pe);h.all(this.container,`[${e}=append] > *, [${e}=prepend] > *`,t=>{t.setAttribute(gt,"")})}perform(e){let{view:t,liveSocket:i,container:r,html:n}=this,o=this.isCIDPatch()?this.targetCIDContainer(n):r;if(this.isCIDPatch()&&!o)return;let a=i.getActiveElement(),{selectionStart:l,selectionEnd:d}=a&&h.hasSelectionRange(a)?a:{},c=i.binding(Pe),f=i.binding(de),v=i.binding(ce),m=i.binding(Ne),p=i.binding(De),E=i.binding(He),k=i.binding(Wt),O=[],H=[],L=[],J=[],j=null;function Re(y,he,z=!1){Ot(y,he,{childrenOnly:y.getAttribute(N)===null&&!z,getNodeKey:u=>h.isPhxDestroyed(u)?null:e?u.id:u.id||u.getAttribute&&u.getAttribute(Ye),skipFromChildren:u=>u.getAttribute(c)===tt,addChild:(u,g)=>{let{ref:I,streamAt:X}=this.getStreamInsert(g);if(I===void 0)return u.appendChild(g);if(this.setStreamRef(g,I),X===0)u.insertAdjacentElement("afterbegin",g);else if(X===-1)u.appendChild(g);else if(X>0){let ie=Array.from(u.children)[X];u.insertBefore(g,ie)}},onBeforeNodeAdded:u=>{h.maybeAddPrivateHooks(u,p,E),this.trackBefore("added",u);let g=u;return!e&&this.streamComponentRestore[u.id]&&(g=this.streamComponentRestore[u.id],delete this.streamComponentRestore[u.id],Re.call(this,g,u,!0)),g},onNodeAdded:u=>{u.getAttribute&&this.maybeReOrderStream(u,!0),h.isFeedbackContainer(u,f)&&H.push(u),u instanceof HTMLImageElement&&u.srcset?u.srcset=u.srcset:u instanceof HTMLVideoElement&&u.autoplay&&u.play(),h.isNowTriggerFormExternal(u,k)&&(j=u),(h.isPhxChild(u)&&t.ownsElement(u)||h.isPhxSticky(u)&&t.ownsElement(u.parentNode))&&this.trackAfter("phxChildAdded",u),O.push(u)},onNodeDiscarded:u=>this.onNodeDiscarded(u),onBeforeNodeDiscarded:u=>u.getAttribute&&u.getAttribute(gt)!==null?!0:!(u.parentElement!==null&&u.id&&h.isPhxUpdate(u.parentElement,c,[tt,"append","prepend"])||this.maybePendingRemove(u)||this.skipCIDSibling(u)),onElUpdated:u=>{h.isNowTriggerFormExternal(u,k)&&(j=u),L.push(u),this.maybeReOrderStream(u,!1)},onBeforeElUpdated:(u,g)=>{if(h.maybeAddPrivateHooks(g,p,E),(h.isFeedbackContainer(u,f)||h.isFeedbackContainer(g,f))&&(H.push(u),H.push(g)),h.cleanChildNodes(g,c),this.skipCIDSibling(g))return this.maybeReOrderStream(u),!1;if(h.isPhxSticky(u))return!1;if(h.isIgnored(u,c)||u.form&&u.form.isSameNode(j))return this.trackBefore("updated",u,g),h.mergeAttrs(u,g,{isIgnored:h.isIgnored(u,c)}),L.push(u),h.applyStickyOperations(u),!1;if(u.type==="number"&&u.validity&&u.validity.badInput)return!1;if(!h.syncPendingRef(u,g,m))return h.isUploadInput(u)&&(this.trackBefore("updated",u,g),L.push(u)),h.applyStickyOperations(u),!1;if(h.isPhxChild(g)){let ie=u.getAttribute(M);return h.mergeAttrs(u,g,{exclude:[te]}),ie!==""&&u.setAttribute(M,ie),u.setAttribute(W,this.rootID),h.applyStickyOperations(u),!1}h.copyPrivates(g,u);let I=a&&u.isSameNode(a)&&h.isFormInput(u),X=I&&this.isChangedSelect(u,g);return I&&u.type!=="hidden"&&!X?(this.trackBefore("updated",u,g),h.mergeFocusedInput(u,g),h.syncAttrsToProps(u),L.push(u),h.applyStickyOperations(u),!1):(X&&u.blur(),h.isPhxUpdate(g,c,["append","prepend"])&&J.push(new at(u,g,g.getAttribute(c))),h.syncAttrsToProps(g),h.applyStickyOperations(g),this.trackBefore("updated",u,g),!0)}})}return this.trackBefore("added",r),this.trackBefore("updated",r,r),i.time("morphdom",()=>{this.streams.forEach(([y,he,z,u])=>{he.forEach(([g,I,X])=>{this.streamInserts[g]={ref:y,streamAt:I,limit:X,reset:u}}),u!==void 0&&h.all(r,`[${it}="${y}"]`,g=>{this.removeStreamChildElement(g)}),z.forEach(g=>{let I=r.querySelector(`[id="${g}"]`);I&&this.removeStreamChildElement(I)})}),e&&h.all(this.container,`[${c}=${tt}]`,y=>{this.liveSocket.owner(y,he=>{he===this.view&&Array.from(y.children).forEach(z=>{this.removeStreamChildElement(z)})})}),Re.call(this,o,n)}),i.isDebugEnabled()&&(ri(),Array.from(document.querySelectorAll("input[name=id]")).forEach(y=>{y.form&&console.error(`Detected an input with name="id" inside a form! This will cause problems when patching the DOM.
`,y)})),J.length>0&&i.time("post-morph append/prepend restoration",()=>{J.forEach(y=>y.perform())}),h.maybeHideFeedback(o,H,f,v),i.silenceEvents(()=>h.restoreFocus(a,l,d)),h.dispatchEvent(document,"phx:update"),O.forEach(y=>this.trackAfter("added",y)),L.forEach(y=>this.trackAfter("updated",y)),this.transitionPendingRemoves(),j&&(i.unload(),Object.getPrototypeOf(j).submit.call(j)),!0}onNodeDiscarded(e){(h.isPhxChild(e)||h.isPhxSticky(e))&&this.liveSocket.destroyViewByEl(e),this.trackAfter("discarded",e)}maybePendingRemove(e){return e.getAttribute&&e.getAttribute(this.phxRemove)!==null?(this.pendingRemoves.push(e),!0):!1}removeStreamChildElement(e){this.streamInserts[e.id]?(this.streamComponentRestore[e.id]=e,e.remove()):this.maybePendingRemove(e)||(e.remove(),this.onNodeDiscarded(e))}getStreamInsert(e){return(e.id?this.streamInserts[e.id]:{})||{}}setStreamRef(e,t){h.putSticky(e,it,i=>i.setAttribute(it,t))}maybeReOrderStream(e,t){let{ref:i,streamAt:r,reset:n}=this.getStreamInsert(e);if(r!==void 0&&(this.setStreamRef(e,i),!(!n&&!t)&&!!e.parentElement)){if(r===0)e.parentElement.insertBefore(e,e.parentElement.firstElementChild);else if(r>0){let o=Array.from(e.parentElement.children),a=o.indexOf(e);if(r>=o.length-1)e.parentElement.appendChild(e);else{let l=o[r];a>r?e.parentElement.insertBefore(e,l):e.parentElement.insertBefore(e,l.nextElementSibling)}}this.maybeLimitStream(e)}}maybeLimitStream(e){let{limit:t}=this.getStreamInsert(e),i=t!==null&&Array.from(e.parentElement.children);t&&t<0&&i.length>t*-1?i.slice(0,i.length+t).forEach(r=>this.removeStreamChildElement(r)):t&&t>=0&&i.length>t&&i.slice(t).forEach(r=>this.removeStreamChildElement(r))}transitionPendingRemoves(){let{pendingRemoves:e,liveSocket:t}=this;e.length>0&&(t.transitionRemoves(e),t.requestDOMUpdate(()=>{e.forEach(i=>{let r=h.firstPhxChild(i);r&&t.destroyViewByEl(r),i.remove()}),this.trackAfter("transitionsDiscarded",e)}))}isChangedSelect(e,t){if(!(e instanceof HTMLSelectElement)||e.multiple)return!1;if(e.options.length!==t.options.length)return!0;let i=e.selectedOptions[0],r=t.selectedOptions[0];return i&&i.hasAttribute("selected")&&r.setAttribute("selected",i.getAttribute("selected")),!e.isEqualNode(t)}isCIDPatch(){return this.cidPatch}skipCIDSibling(e){return e.nodeType===Node.ELEMENT_NODE&&e.hasAttribute(ze)}targetCIDContainer(e){if(!this.isCIDPatch())return;let[t,...i]=h.findComponentNodeList(this.container,this.targetCID);return i.length===0&&h.childNodeLength(e)===1?t:t&&t.parentNode}indexOf(e,t){return Array.from(e.children).indexOf(t)}};var Wi=new Set(["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),qi=new Set(["'",'"']),Si=(s,e,t)=>{let i=0,r=!1,n,o,a,l,d,c,f=s.match(/^(\s*(?:<!--.*?-->\s*)*)<([^\s\/>]+)/);if(f===null)throw new Error(`malformed html ${s}`);for(i=f[0].length,n=f[1],a=f[2],l=i,i;i<s.length&&s.charAt(i)!==">";i++)if(s.charAt(i)==="="){let p=s.slice(i-3,i)===" id";i++;let E=s.charAt(i);if(qi.has(E)){let k=i;for(i++,i;i<s.length&&s.charAt(i)!==E;i++);if(p){d=s.slice(k+1,i);break}}}let v=s.length-1;for(r=!1;v>=n.length+a.length;){let p=s.charAt(v);if(r)p==="-"&&s.slice(v-3,v)==="<!-"?(r=!1,v-=4):v-=1;else if(p===">"&&s.slice(v-2,v)==="--")r=!0,v-=3;else{if(p===">")break;v-=1}}o=s.slice(v+1,s.length);let m=Object.keys(e).map(p=>e[p]===!0?p:`${p}="${e[p]}"`).join(" ");if(t){let p=d?` id="${d}"`:"";Wi.has(a)?c=`<${a}${p}${m===""?"":" "}${m}/>`:c=`<${a}${p}${m===""?"":" "}${m}></${a}>`}else{let p=s.slice(l,v+1);c=`<${a}${m===""?"":" "}${m}${p}`}return[c,n,o]},Be=class{static extract(e){let{[yt]:t,[Pt]:i,[kt]:r}=e;return delete e[yt],delete e[Pt],delete e[kt],{diff:e,title:r,reply:t||null,events:i||[]}}constructor(e,t){this.viewId=e,this.rendered={},this.magicId=0,this.mergeDiff(t)}parentViewId(){return this.viewId}toString(e){let[t,i]=this.recursiveToString(this.rendered,this.rendered[R],e,!0,{});return[t,i]}recursiveToString(e,t=e[R],i,r,n){i=i?new Set(i):null;let o={buffer:"",components:t,onlyCids:i,streams:new Set};return this.toOutputBuffer(e,null,o,r,n),[o.buffer,o.streams]}componentCIDs(e){return Object.keys(e[R]||{}).map(t=>parseInt(t))}isComponentOnlyDiff(e){return e[R]?Object.keys(e).length===1:!1}getComponent(e,t){return e[R][t]}resetRender(e){this.rendered[R][e]&&(this.rendered[R][e].reset=!0)}mergeDiff(e){let t=e[R],i={};if(delete e[R],this.rendered=this.mutableMerge(this.rendered,e),this.rendered[R]=this.rendered[R]||{},t){let r=this.rendered[R];for(let n in t)t[n]=this.cachedFindComponent(n,t[n],r,t,i);for(let n in t)r[n]=t[n];e[R]=t}}cachedFindComponent(e,t,i,r,n){if(n[e])return n[e];{let o,a,l=t[K];if($(l)){let d;l>0?d=this.cachedFindComponent(l,r[l],i,r,n):d=i[-l],a=d[K],o=this.cloneMerge(d,t,!0),o[K]=a}else o=t[K]!==void 0||i[e]===void 0?t:this.cloneMerge(i[e],t,!1);return n[e]=o,o}}mutableMerge(e,t){return t[K]!==void 0?t:(this.doMutableMerge(e,t),e)}doMutableMerge(e,t){for(let i in t){let r=t[i],n=e[i];pe(r)&&r[K]===void 0&&pe(n)?this.doMutableMerge(n,r):e[i]=r}e[nt]&&(e.newRender=!0)}cloneMerge(e,t,i){let r=F(F({},e),t);for(let n in r){let o=t[n],a=e[n];pe(o)&&o[K]===void 0&&pe(a)?r[n]=this.cloneMerge(a,o,i):o===void 0&&pe(a)&&(r[n]=this.cloneMerge(a,{},i))}return i?(delete r.magicId,delete r.newRender):e[nt]&&(r.newRender=!0),r}componentToString(e){let[t,i]=this.recursiveCIDToString(this.rendered[R],e,null),[r,n,o]=Si(t,{});return[r,i]}pruneCIDs(e){e.forEach(t=>delete this.rendered[R][t])}get(){return this.rendered}isNewFingerprint(e={}){return!!e[K]}templateStatic(e,t){return typeof e=="number"?t[e]:e}nextMagicID(){return this.magicId++,`m${this.magicId}-${this.parentViewId()}`}toOutputBuffer(e,t,i,r,n={}){if(e[Xe])return this.comprehensionToBuffer(e,t,i);let{[K]:o}=e;o=this.templateStatic(o,t);let a=e[nt],l=i.buffer;a&&(i.buffer=""),r&&a&&!e.magicId&&(e.newRender=!0,e.magicId=this.nextMagicID()),i.buffer+=o[0];for(let d=1;d<o.length;d++)this.dynamicToBuffer(e[d-1],t,i,r),i.buffer+=o[d];if(a){let d=!1,c;r||e.magicId?(d=r&&!e.newRender,c=F({[Ye]:e.magicId},n)):c=n,d&&(c[ze]=!0);let[f,v,m]=Si(i.buffer,c,d);e.newRender=!1,i.buffer=l+v+f+m}}comprehensionToBuffer(e,t,i){let{[Xe]:r,[K]:n,[Ct]:o}=e,[a,l,d,c]=o||[null,{},[],null];n=this.templateStatic(n,t);let f=t||e[si];for(let v=0;v<r.length;v++){let m=r[v];i.buffer+=n[0];for(let p=1;p<n.length;p++){let E=!1;this.dynamicToBuffer(m[p-1],f,i,E),i.buffer+=n[p]}}o!==void 0&&(e[Xe].length>0||d.length>0||c)&&(delete e[Ct],e[Xe]=[],i.streams.add(o))}dynamicToBuffer(e,t,i,r){if(typeof e=="number"){let[n,o]=this.recursiveCIDToString(i.components,e,i.onlyCids);i.buffer+=n,i.streams=new Set([...i.streams,...o])}else pe(e)?this.toOutputBuffer(e,t,i,r,{}):i.buffer+=e}recursiveCIDToString(e,t,i){let r=e[t]||C(`no component for CID ${t}`,e),n={[N]:t},o=i&&!i.has(t);r.newRender=!o,r.magicId=`c${t}-${this.parentViewId()}`;let a=!r.reset,[l,d]=this.recursiveToString(r,e,i,a,n);return delete r.reset,[l,d]}};var Ki=1,le=class{static makeID(){return Ki++}static elementID(e){return e.phxHookId}constructor(e,t,i){this.__view=e,this.liveSocket=e.liveSocket,this.__callbacks=i,this.__listeners=new Set,this.__isDisconnected=!1,this.el=t,this.el.phxHookId=this.constructor.makeID();for(let r in this.__callbacks)this[r]=this.__callbacks[r]}__mounted(){this.mounted&&this.mounted()}__updated(){this.updated&&this.updated()}__beforeUpdate(){this.beforeUpdate&&this.beforeUpdate()}__destroyed(){this.destroyed&&this.destroyed()}__reconnected(){this.__isDisconnected&&(this.__isDisconnected=!1,this.reconnected&&this.reconnected())}__disconnected(){this.__isDisconnected=!0,this.disconnected&&this.disconnected()}pushEvent(e,t={},i=function(){}){return this.__view.pushHookEvent(this.el,null,e,t,i)}pushEventTo(e,t,i={},r=function(){}){return this.__view.withinTargets(e,(n,o)=>n.pushHookEvent(this.el,o,t,i,r))}handleEvent(e,t){let i=(r,n)=>n?e:t(r.detail);return window.addEventListener(`phx:${e}`,i),this.__listeners.add(i),i}removeHandleEvent(e){let t=e(null,!0);window.removeEventListener(`phx:${t}`,e),this.__listeners.delete(e)}upload(e,t){return this.__view.dispatchUploads(null,e,t)}uploadTo(e,t,i){return this.__view.withinTargets(e,(r,n)=>{r.dispatchUploads(n,t,i)})}__cleanup__(){this.__listeners.forEach(e=>this.removeHandleEvent(e))}};var dt=(s,e,t=[])=>{let d=e,{submitter:i}=d,r=Ut(d,["submitter"]),n;if(i&&i.name){let c=document.createElement("input");c.type="hidden";let f=i.getAttribute("form");f&&c.setAttribute("form",f),c.name=i.name,c.value=i.value,i.parentElement.insertBefore(c,i),n=c}let o=new FormData(s),a=[];o.forEach((c,f,v)=>{c instanceof File&&a.push(f)}),a.forEach(c=>o.delete(c));let l=new URLSearchParams;for(let[c,f]of o.entries())(t.length===0||t.indexOf(c)>=0)&&l.append(c,f);i&&n&&i.parentElement.removeChild(n);for(let c in r)l.append(c,r[c]);return l.toString()},Te=class{constructor(e,t,i,r,n){this.isDead=!1,this.liveSocket=t,this.flash=r,this.parent=i,this.root=i?i.root:this,this.el=e,this.id=this.el.id,this.ref=0,this.childJoins=0,this.loaderTimer=null,this.pendingDiffs=[],this.pendingForms=new Set,this.redirect=!1,this.href=null,this.joinCount=this.parent?this.parent.joinCount-1:0,this.joinPending=!0,this.destroyed=!1,this.joinCallback=function(o){o&&o()},this.stopCallback=function(){},this.pendingJoinOps=this.parent?null:[],this.viewHooks={},this.formSubmits=[],this.children=this.parent?null:{},this.root.children[this.id]={},this.formsForRecovery={},this.channel=this.liveSocket.channel(`lv:${this.id}`,()=>{let o=this.href&&this.expandURL(this.href);return{redirect:this.redirect?o:void 0,url:this.redirect?void 0:o||void 0,params:this.connectParams(n),session:this.getSession(),static:this.getStatic(),flash:this.flash}})}setHref(e){this.href=e}setRedirect(e){this.redirect=!0,this.href=e}isMain(){return this.el.hasAttribute(Se)}connectParams(e){let t=this.liveSocket.params(this.el),i=h.all(document,`[${this.binding(Vt)}]`).map(r=>r.src||r.href).filter(r=>typeof r=="string");return i.length>0&&(t._track_static=i),t._mounts=this.joinCount,t._live_referer=e,t}isConnected(){return this.channel.canPush()}getSession(){return this.el.getAttribute(M)}getStatic(){let e=this.el.getAttribute(te);return e===""?null:e}destroy(e=function(){}){this.destroyAllChildren(),this.destroyed=!0,delete this.root.children[this.id],this.parent&&delete this.root.children[this.parent.id][this.id],clearTimeout(this.loaderTimer);let t=()=>{e();for(let i in this.viewHooks)this.destroyHook(this.viewHooks[i])};h.markPhxChildDestroyed(this.el),this.log("destroyed",()=>["the child has been removed from the parent"]),this.channel.leave().receive("ok",t).receive("error",t).receive("timeout",t)}setContainerClasses(...e){this.el.classList.remove(bt,Ae,Le,Et,Qe),this.el.classList.add(...e)}showLoader(e){if(clearTimeout(this.loaderTimer),e)this.loaderTimer=setTimeout(()=>this.showLoader(),e);else{for(let t in this.viewHooks)this.viewHooks[t].__disconnected();this.setContainerClasses(Ae)}}execAll(e){h.all(this.el,`[${e}]`,t=>this.liveSocket.execJS(t,t.getAttribute(e)))}hideLoader(){clearTimeout(this.loaderTimer),this.setContainerClasses(bt),this.execAll(this.binding("connected"))}triggerReconnected(){for(let e in this.viewHooks)this.viewHooks[e].__reconnected()}log(e,t){this.liveSocket.log(this,e,t)}transition(e,t,i=function(){}){this.liveSocket.transition(e,t,i)}withinTargets(e,t,i=document,r){if(e instanceof HTMLElement||e instanceof SVGElement)return this.liveSocket.owner(e,n=>t(n,e));if($(e))h.findComponentNodeList(r||this.el,e).length===0?C(`no component found matching phx-target of ${e}`):t(this,parseInt(e));else{let n=Array.from(i.querySelectorAll(e));n.length===0&&C(`nothing found matching the phx-target selector "${e}"`),n.forEach(o=>this.liveSocket.owner(o,a=>t(a,o)))}}applyDiff(e,t,i){this.log(e,()=>["",xe(t)]);let{diff:r,reply:n,events:o,title:a}=Be.extract(t);i({diff:r,reply:n,events:o}),typeof a=="string"&&window.requestAnimationFrame(()=>h.putTitle(a))}onJoin(e){let{rendered:t,container:i,liveview_version:r}=e;if(i){let[n,o]=i;this.el=h.replaceRootContainer(this.el,n,o)}this.childJoins=0,this.joinPending=!0,this.flash=null,this.root===this&&(this.formsForRecovery=this.getFormsForRecovery()),r!==this.liveSocket.version()&&console.error(`LiveView asset version mismatch. JavaScript version ${this.liveSocket.version()} vs. server ${r}. To avoid issues, please ensure that your assets use the same version as the server.`),B.dropLocal(this.liveSocket.localStorage,window.location.pathname,We),this.applyDiff("mount",t,({diff:n,events:o})=>{this.rendered=new Be(this.id,n);let[a,l]=this.renderContainer(null,"join");this.dropPendingRefs(),this.joinCount++,this.maybeRecoverForms(a,()=>{this.onJoinComplete(e,a,l,o)})})}dropPendingRefs(){h.all(document,`[${Y}="${this.id}"][${T}]`,e=>{e.removeAttribute(T),e.removeAttribute(Y)})}onJoinComplete({live_patch:e},t,i,r){if(this.joinCount>1||this.parent&&!this.parent.isJoinPending())return this.applyJoinPatch(e,t,i,r);h.findPhxChildrenInFragment(t,this.id).filter(o=>{let a=o.id&&this.el.querySelector(`[id="${o.id}"]`),l=a&&a.getAttribute(te);return l&&o.setAttribute(te,l),a&&a.setAttribute(W,this.root.id),this.joinChild(o)}).length===0?this.parent?(this.root.pendingJoinOps.push([this,()=>this.applyJoinPatch(e,t,i,r)]),this.parent.ackJoin(this)):(this.onAllChildJoinsComplete(),this.applyJoinPatch(e,t,i,r)):this.root.pendingJoinOps.push([this,()=>this.applyJoinPatch(e,t,i,r)])}attachTrueDocEl(){this.el=h.byId(this.id),this.el.setAttribute(W,this.root.id)}execNewMounted(){let e=this.binding(De),t=this.binding(He);h.all(this.el,`[${e}], [${t}]`,i=>{this.ownsElement(i)&&(h.maybeAddPrivateHooks(i,e,t),this.maybeAddNewHook(i))}),h.all(this.el,`[${this.binding(we)}], [data-phx-${we}]`,i=>{this.ownsElement(i)&&this.maybeAddNewHook(i)}),h.all(this.el,`[${this.binding(_t)}]`,i=>{this.ownsElement(i)&&this.maybeMounted(i)})}applyJoinPatch(e,t,i,r){this.attachTrueDocEl();let n=new me(this,this.el,this.id,t,i,null);if(n.markPrunableContentForRemoval(),this.performPatch(n,!1,!0),this.joinNewChildren(),this.execNewMounted(),this.joinPending=!1,this.liveSocket.dispatchEvents(r),this.applyPendingUpdates(),e){let{kind:o,to:a}=e;this.liveSocket.historyPatch(a,o)}this.hideLoader(),this.joinCount>1&&this.triggerReconnected(),this.stopCallback()}triggerBeforeUpdateHook(e,t){this.liveSocket.triggerDOM("onBeforeElUpdated",[e,t]);let i=this.getHook(e),r=i&&h.isIgnored(e,this.binding(Pe));if(i&&!e.isEqualNode(t)&&!(r&&oi(e.dataset,t.dataset)))return i.__beforeUpdate(),i}maybeMounted(e){let t=e.getAttribute(this.binding(_t)),i=t&&h.private(e,"mounted");t&&!i&&(this.liveSocket.execJS(e,t),h.putPrivate(e,"mounted",!0))}maybeAddNewHook(e,t){let i=this.addHook(e);i&&i.__mounted()}performPatch(e,t,i=!1){let r=[],n=!1,o=new Set;return e.after("added",a=>{this.liveSocket.triggerDOM("onNodeAdded",[a]);let l=this.binding(De),d=this.binding(He);h.maybeAddPrivateHooks(a,l,d),this.maybeAddNewHook(a),a.getAttribute&&this.maybeMounted(a)}),e.after("phxChildAdded",a=>{h.isPhxSticky(a)?this.liveSocket.joinRootViews():n=!0}),e.before("updated",(a,l)=>{this.triggerBeforeUpdateHook(a,l)&&o.add(a.id)}),e.after("updated",a=>{o.has(a.id)&&this.getHook(a).__updated()}),e.after("discarded",a=>{a.nodeType===Node.ELEMENT_NODE&&r.push(a)}),e.after("transitionsDiscarded",a=>this.afterElementsRemoved(a,t)),e.perform(i),this.afterElementsRemoved(r,t),n}afterElementsRemoved(e,t){let i=[];e.forEach(r=>{let n=h.all(r,`[${N}]`),o=h.all(r,`[${this.binding(we)}]`);n.concat(r).forEach(a=>{let l=this.componentID(a);$(l)&&i.indexOf(l)===-1&&i.push(l)}),o.concat(r).forEach(a=>{let l=this.getHook(a);l&&this.destroyHook(l)})}),t&&this.maybePushComponentsDestroyed(i)}joinNewChildren(){h.findPhxChildren(this.el,this.id).forEach(e=>this.joinChild(e))}maybeRecoverForms(e,t){let i=this.binding("change"),r=this.root.formsForRecovery,n=document.createElement("template");n.innerHTML=e;let o=n.content.firstElementChild;o.id=this.id,o.setAttribute(W,this.root.id),o.setAttribute(M,this.getSession()),o.setAttribute(te,this.getStatic()),o.setAttribute(Q,this.parent?this.parent.id:null);let a=h.all(n.content,"form").filter(l=>l.id&&r[l.id]).filter(l=>!this.pendingForms.has(l.id)).filter(l=>r[l.id].getAttribute(i)===l.getAttribute(i)).map(l=>[r[l.id],l]);if(a.length===0)return t();a.forEach(([l,d],c)=>{this.pendingForms.add(d.id),this.pushFormRecovery(l,d,n.content,()=>{this.pendingForms.delete(d.id),c===a.length-1&&t()})})}getChildById(e){return this.root.children[this.id][e]}getDescendentByEl(e){return e.id===this.id?this:this.children[e.getAttribute(Q)][e.id]}destroyDescendent(e){for(let t in this.root.children)for(let i in this.root.children[t])if(i===e)return this.root.children[t][i].destroy()}joinChild(e){if(!this.getChildById(e.id)){let i=new Te(e,this.liveSocket,this);return this.root.children[this.id][i.id]=i,i.join(),this.childJoins++,!0}}isJoinPending(){return this.joinPending}ackJoin(e){this.childJoins--,this.childJoins===0&&(this.parent?this.parent.ackJoin(this):this.onAllChildJoinsComplete())}onAllChildJoinsComplete(){this.pendingForms.clear(),this.formsForRecovery={},this.joinCallback(()=>{this.pendingJoinOps.forEach(([e,t])=>{e.isDestroyed()||t()}),this.pendingJoinOps=[]})}update(e,t){if(this.isJoinPending()||this.liveSocket.hasPendingLink()&&this.root.isMain())return this.pendingDiffs.push({diff:e,events:t});this.rendered.mergeDiff(e);let i=!1;this.rendered.isComponentOnlyDiff(e)?this.liveSocket.time("component patch complete",()=>{h.findExistingParentCIDs(this.el,this.rendered.componentCIDs(e)).forEach(n=>{this.componentPatch(this.rendered.getComponent(e,n),n)&&(i=!0)})}):xt(e)||this.liveSocket.time("full patch complete",()=>{let[r,n]=this.renderContainer(e,"update"),o=new me(this,this.el,this.id,r,n,null);i=this.performPatch(o,!0)}),this.liveSocket.dispatchEvents(t),i&&this.joinNewChildren()}renderContainer(e,t){return this.liveSocket.time(`toString diff (${t})`,()=>{let i=this.el.tagName,r=e?this.rendered.componentCIDs(e):null,[n,o]=this.rendered.toString(r);return[`<${i}>${n}</${i}>`,o]})}componentPatch(e,t){if(xt(e))return!1;let[i,r]=this.rendered.componentToString(t),n=new me(this,this.el,this.id,i,r,t);return this.performPatch(n,!0)}getHook(e){return this.viewHooks[le.elementID(e)]}addHook(e){if(le.elementID(e)||!e.getAttribute)return;let t=e.getAttribute(`data-phx-${we}`)||e.getAttribute(this.binding(we));if(t&&!this.ownsElement(e))return;let i=this.liveSocket.getHookCallbacks(t);if(i){e.id||C(`no DOM ID for hook "${t}". Hooks require a unique ID on each element.`,e);let r=new le(this,e,i);return this.viewHooks[le.elementID(r.el)]=r,r}else t!==null&&C(`unknown hook found for "${t}"`,e)}destroyHook(e){e.__destroyed(),e.__cleanup__(),delete this.viewHooks[le.elementID(e.el)]}applyPendingUpdates(){this.pendingDiffs.forEach(({diff:e,events:t})=>this.update(e,t)),this.pendingDiffs=[],this.eachChild(e=>e.applyPendingUpdates())}eachChild(e){let t=this.root.children[this.id]||{};for(let i in t)e(this.getChildById(i))}onChannel(e,t){this.liveSocket.onChannel(this.channel,e,i=>{this.isJoinPending()?this.root.pendingJoinOps.push([this,()=>t(i)]):this.liveSocket.requestDOMUpdate(()=>t(i))})}bindChannel(){this.liveSocket.onChannel(this.channel,"diff",e=>{this.liveSocket.requestDOMUpdate(()=>{this.applyDiff("update",e,({diff:t,events:i})=>this.update(t,i))})}),this.onChannel("redirect",({to:e,flash:t})=>this.onRedirect({to:e,flash:t})),this.onChannel("live_patch",e=>this.onLivePatch(e)),this.onChannel("live_redirect",e=>this.onLiveRedirect(e)),this.channel.onError(e=>this.onError(e)),this.channel.onClose(e=>this.onClose(e))}destroyAllChildren(){this.eachChild(e=>e.destroy())}onLiveRedirect(e){let{to:t,kind:i,flash:r}=e,n=this.expandURL(t);this.liveSocket.historyRedirect(n,i,r)}onLivePatch(e){let{to:t,kind:i}=e;this.href=this.expandURL(t),this.liveSocket.historyPatch(t,i)}expandURL(e){return e.startsWith("/")?`${window.location.protocol}//${window.location.host}${e}`:e}onRedirect({to:e,flash:t}){this.liveSocket.redirect(e,t)}isDestroyed(){return this.destroyed}joinDead(){this.isDead=!0}join(e){this.showLoader(this.liveSocket.loaderTimeout),this.bindChannel(),this.isMain()&&(this.stopCallback=this.liveSocket.withPageLoading({to:this.href,kind:"initial"})),this.joinCallback=t=>{t=t||function(){},e?e(this.joinCount,t):t()},this.liveSocket.wrapPush(this,{timeout:!1},()=>this.channel.join().receive("ok",t=>{this.isDestroyed()||this.liveSocket.requestDOMUpdate(()=>this.onJoin(t))}).receive("error",t=>!this.isDestroyed()&&this.onJoinError(t)).receive("timeout",()=>!this.isDestroyed()&&this.onJoinError({reason:"timeout"})))}onJoinError(e){if(e.reason==="reload"){this.log("error",()=>[`failed mount with ${e.status}. Falling back to page request`,e]),this.isMain()&&this.onRedirect({to:this.href});return}else if(e.reason==="unauthorized"||e.reason==="stale"){this.log("error",()=>["unauthorized live_redirect. Falling back to page request",e]),this.isMain()&&this.onRedirect({to:this.href});return}if((e.redirect||e.live_redirect)&&(this.joinPending=!1,this.channel.leave()),e.redirect)return this.onRedirect(e.redirect);if(e.live_redirect)return this.onLiveRedirect(e.live_redirect);this.displayError([Ae,Le,Qe]),this.log("error",()=>["unable to join",e]),this.liveSocket.isConnected()&&this.liveSocket.reloadWithJitter(this)}onClose(e){if(!this.isDestroyed()){if(this.liveSocket.hasPendingLink()&&e!=="leave")return this.liveSocket.reloadWithJitter(this);this.destroyAllChildren(),this.liveSocket.dropActiveElement(this),document.activeElement&&document.activeElement.blur(),this.liveSocket.isUnloaded()&&this.showLoader(Zt)}}onError(e){this.onClose(e),this.liveSocket.isConnected()&&this.log("error",()=>["view crashed",e]),this.liveSocket.isUnloaded()||(this.liveSocket.isConnected()?this.displayError([Ae,Le,Qe]):this.displayError([Ae,Le,Et]))}displayError(e){this.isMain()&&h.dispatchEvent(window,"phx:page-loading-start",{detail:{to:this.href,kind:"error"}}),this.showLoader(),this.setContainerClasses(...e),this.execAll(this.binding("disconnected"))}pushWithReply(e,t,i,r=function(){}){if(!this.isConnected())return;let[n,[o],a]=e?e():[null,[],{}],l=function(){};return(a.page_loading||o&&o.getAttribute(this.binding(vt))!==null)&&(l=this.liveSocket.withPageLoading({kind:"element",target:o})),typeof i.cid!="number"&&delete i.cid,this.liveSocket.wrapPush(this,{timeout:!0},()=>this.channel.push(t,i,ti).receive("ok",d=>{let c=f=>{d.redirect&&this.onRedirect(d.redirect),d.live_patch&&this.onLivePatch(d.live_patch),d.live_redirect&&this.onLiveRedirect(d.live_redirect),l(),r(d,f)};d.diff?this.liveSocket.requestDOMUpdate(()=>{this.applyDiff("update",d.diff,({diff:f,reply:v,events:m})=>{n!==null&&this.undoRefs(n),this.update(f,m),c(v)})}):(n!==null&&this.undoRefs(n),c(null))}))}undoRefs(e,t){t=t?new Set(t):null,!!this.isConnected()&&h.all(document,`[${Y}="${this.id}"][${T}="${e}"]`,i=>{if(t&&!t.has(i))return;i.dispatchEvent(new CustomEvent("phx:unlock",{bubbles:!0,cancelable:!1}));let r=i.getAttribute(ue),n=i.getAttribute(et);i.removeAttribute(T),i.removeAttribute(Y),n!==null&&(i.readOnly=n==="true",i.removeAttribute(et)),r!==null&&(i.disabled=r==="true",i.removeAttribute(ue)),qe.forEach(l=>h.removeClass(i,l));let o=i.getAttribute(Ue);o!==null&&(i.innerText=o,i.removeAttribute(Ue));let a=h.private(i,T);if(a){let l=this.triggerBeforeUpdateHook(i,a);me.patchEl(i,a,this.liveSocket.getActiveElement()),l&&l.__updated(),h.deletePrivate(i,T)}})}putRef(e,t,i={}){let r=this.ref++,n=this.binding(Ne);i.loading&&(e=e.concat(h.all(document,i.loading)));for(let o of e){if(o.setAttribute(T,r),o.setAttribute(Y,this.el.id),i.submitter&&!(o===i.submitter||o===i.form))continue;o.classList.add(`phx-${t}-loading`),o.dispatchEvent(new CustomEvent(`phx:${t}-loading`,{bubbles:!0,cancelable:!1}));let a=o.getAttribute(n);a!==null&&(o.getAttribute(Ue)||o.setAttribute(Ue,o.innerText),a!==""&&(o.innerText=a),o.setAttribute(ue,o.getAttribute(ue)||o.disabled),o.setAttribute("disabled",""))}return[r,e,i]}componentID(e){let t=e.getAttribute&&e.getAttribute(N);return t?parseInt(t):null}targetComponentID(e,t,i={}){if($(t))return t;let r=i.target||e.getAttribute(this.binding("target"));return $(r)?parseInt(r):t&&(r!==null||i.target)?this.closestComponentID(t):null}closestComponentID(e){return $(e)?e:e?G(e.closest(`[${N}]`),t=>this.ownsElement(t)&&this.componentID(t)):null}pushHookEvent(e,t,i,r,n){if(!this.isConnected())return this.log("hook",()=>["unable to push hook event. LiveView not connected",i,r]),!1;let[o,a,l]=this.putRef([e],"hook");return this.pushWithReply(()=>[o,a,l],"event",{type:"hook",event:i,value:r,cid:this.closestComponentID(t)},(d,c)=>n(c,o)),o}extractMeta(e,t,i){let r=this.binding("value-");for(let n=0;n<e.attributes.length;n++){t||(t={});let o=e.attributes[n].name;o.startsWith(r)&&(t[o.replace(r,"")]=e.getAttribute(o))}if(e.value!==void 0&&!(e instanceof HTMLFormElement)&&(t||(t={}),t.value=e.value,e.tagName==="INPUT"&&Ze.indexOf(e.type)>=0&&!e.checked&&delete t.value),i){t||(t={});for(let n in i)t[n]=i[n]}return t}pushEvent(e,t,i,r,n,o={},a){this.pushWithReply(()=>this.putRef([t],e,o),"event",{type:e,event:r,value:this.extractMeta(t,n,o.value),cid:this.targetComponentID(t,i,o)},(l,d)=>a&&a(d))}pushFileProgress(e,t,i,r=function(){}){this.liveSocket.withinOwners(e.form,(n,o)=>{n.pushWithReply(null,"progress",{event:e.getAttribute(n.binding(Yt)),ref:e.getAttribute(U),entry_ref:t,progress:i,cid:n.targetComponentID(e.form,o)},r)})}pushInput(e,t,i,r,n,o){let a,l=$(i)?i:this.targetComponentID(e.form,t,n),d=()=>this.putRef([e,e.form],"change",n),c,f=this.extractMeta(e.form);e instanceof HTMLButtonElement&&(f.submitter=e),e.getAttribute(this.binding("change"))?c=dt(e.form,F({_target:n._target},f),[e.name]):c=dt(e.form,F({_target:n._target},f)),h.isUploadInput(e)&&e.files&&e.files.length>0&&_.trackFiles(e,Array.from(e.files)),a=_.serializeUploads(e);let v={type:"form",event:r,value:c,uploads:a,cid:l};this.pushWithReply(d,"event",v,m=>{if(h.showError(e,this.liveSocket.binding(de),this.liveSocket.binding(ce)),h.isUploadInput(e)&&h.isAutoUpload(e)){if(_.filesAwaitingPreflight(e).length>0){let[p,E]=d();this.undoRefs(p,[e.form]),this.uploadFiles(e.form,t,p,l,k=>{o&&o(m),this.triggerAwaitingSubmit(e.form),this.undoRefs(p)})}}else o&&o(m)})}triggerAwaitingSubmit(e){let t=this.getScheduledSubmit(e);if(t){let[i,r,n,o]=t;this.cancelSubmit(e),o()}}getScheduledSubmit(e){return this.formSubmits.find(([t,i,r,n])=>t.isSameNode(e))}scheduleSubmit(e,t,i,r){if(this.getScheduledSubmit(e))return!0;this.formSubmits.push([e,t,i,r])}cancelSubmit(e){this.formSubmits=this.formSubmits.filter(([t,i,r])=>t.isSameNode(e)?(this.undoRefs(i),!1):!0)}disableForm(e,t={}){let i=f=>!(fe(f,`${this.binding(Pe)}=ignore`,f.form)||fe(f,"data-phx-update=ignore",f.form)),r=f=>f.hasAttribute(this.binding(Ne)),n=f=>f.tagName=="BUTTON",o=f=>["INPUT","TEXTAREA","SELECT"].includes(f.tagName),a=Array.from(e.elements),l=a.filter(r),d=a.filter(n).filter(i),c=a.filter(o).filter(i);return d.forEach(f=>{f.setAttribute(ue,f.disabled),f.disabled=!0}),c.forEach(f=>{f.setAttribute(et,f.readOnly),f.readOnly=!0,f.files&&(f.setAttribute(ue,f.disabled),f.disabled=!0)}),e.setAttribute(this.binding(vt),""),this.putRef([e].concat(l).concat(d).concat(c),"submit",t)}pushFormSubmit(e,t,i,r,n,o){let a=()=>this.disableForm(e,Nt(F({},n),{form:e,submitter:r})),l=this.targetComponentID(e,t);if(_.hasUploadsInProgress(e)){let[d,c]=a(),f=()=>this.pushFormSubmit(e,t,i,r,n,o);return this.scheduleSubmit(e,d,n,f)}else if(_.inputsAwaitingPreflight(e).length>0){let[d,c]=a(),f=()=>[d,c,n];this.uploadFiles(e,t,d,l,v=>{if(_.inputsAwaitingPreflight(e).length>0)return this.undoRefs(d);let m=this.extractMeta(e),p=dt(e,F({submitter:r},m));this.pushWithReply(f,"event",{type:"form",event:i,value:p,cid:l},o)})}else if(!(e.hasAttribute(T)&&e.classList.contains("phx-submit-loading"))){let d=this.extractMeta(e),c=dt(e,F({submitter:r},d));this.pushWithReply(a,"event",{type:"form",event:i,value:c,cid:l},o)}}uploadFiles(e,t,i,r,n){let o=this.joinCount,a=_.activeFileInputs(e),l=a.length;a.forEach(d=>{let c=new _(d,this,()=>{l--,l===0&&n()}),f=c.entries().map(m=>m.toPreflightPayload());if(f.length===0){l--;return}let v={ref:d.getAttribute(U),entries:f,cid:this.targetComponentID(d.form,t)};this.log("upload",()=>["sending preflight request",v]),this.pushWithReply(null,"allow_upload",v,m=>{if(this.log("upload",()=>["got preflight response",m]),c.entries().forEach(p=>{m.entries&&!m.entries[p.ref]&&this.handleFailedEntryPreflight(p.ref,"failed preflight",c)}),m.error||Object.keys(m.entries).length===0)this.undoRefs(i),(m.error||[]).map(([E,k])=>{this.handleFailedEntryPreflight(E,k,c)});else{let p=E=>{this.channel.onError(()=>{this.joinCount===o&&E()})};c.initAdapterUpload(m,p,this.liveSocket)}})})}handleFailedEntryPreflight(e,t,i){if(i.isAutoUpload()){let r=i.entries().find(n=>n.ref===e.toString());r&&r.cancel()}else i.entries().map(r=>r.cancel());this.log("upload",()=>[`error for entry ${e}`,t])}dispatchUploads(e,t,i){let r=this.targetCtxElement(e)||this.el,n=h.findUploadInputs(r).filter(o=>o.name===t);n.length===0?C(`no live file inputs found matching the name "${t}"`):n.length>1?C(`duplicate live file inputs found matching the name "${t}"`):h.dispatchEvent(n[0],Ge,{detail:{files:i}})}targetCtxElement(e){if($(e)){let[t]=h.findComponentNodeList(this.el,e);return t}else return e||null}pushFormRecovery(e,t,i,r){let n=this.binding("change"),o=t.getAttribute(this.binding("target"))||t,a=t.getAttribute(this.binding(St))||t.getAttribute(this.binding("change")),l=Array.from(e.elements).filter(f=>h.isFormInput(f)&&f.name&&!f.hasAttribute(n));if(l.length===0)return;l.forEach(f=>f.hasAttribute(U)&&_.clearFiles(f));let d=l.find(f=>f.type!=="hidden")||l[0],c=0;this.withinTargets(o,(f,v)=>{let m=this.targetComponentID(t,v);c++,f.pushInput(d,v,m,a,{_target:d.name},()=>{c--,c===0&&r()})},i,i)}pushLinkPatch(e,t,i){let r=this.liveSocket.setPendingLink(e),n=t?()=>this.putRef([t],"click"):null,o=()=>this.liveSocket.redirect(window.location.href),a=e.startsWith("/")?`${location.protocol}//${location.host}${e}`:e,l=this.pushWithReply(n,"live_patch",{url:a},d=>{this.liveSocket.requestDOMUpdate(()=>{d.link_redirect?this.liveSocket.replaceMain(e,null,i,r):(this.liveSocket.commitPendingLink(r)&&(this.href=e),this.applyPendingUpdates(),i&&i(r))})});l?l.receive("timeout",o):o()}getFormsForRecovery(){if(this.joinCount===0)return{};let e=this.binding("change");return h.all(this.el,`form[${e}]`).filter(t=>t.id).filter(t=>t.elements.length>0).filter(t=>t.getAttribute(this.binding(St))!=="ignore").map(t=>t.cloneNode(!0)).reduce((t,i)=>(t[i.id]=i,t),{})}maybePushComponentsDestroyed(e){let t=e.filter(i=>h.findComponentNodeList(this.el,i).length===0);t.length>0&&(t.forEach(i=>this.rendered.resetRender(i)),this.pushWithReply(null,"cids_will_destroy",{cids:t},()=>{this.liveSocket.requestDOMUpdate(()=>{let i=t.filter(r=>h.findComponentNodeList(this.el,r).length===0);i.length>0&&this.pushWithReply(null,"cids_destroyed",{cids:i},r=>{this.rendered.pruneCIDs(r.cids)})})}))}ownsElement(e){let t=e.closest(Z);return e.getAttribute(Q)===this.id||t&&t.id===this.id||!t&&this.isDead}submitForm(e,t,i,r,n={}){h.putPrivate(e,_e,!0);let o=this.liveSocket.binding(de),a=this.liveSocket.binding(ce),l=Array.from(e.elements);l.forEach(d=>h.putPrivate(d,_e,!0)),this.liveSocket.blurActiveElement(this),this.pushFormSubmit(e,t,i,r,n,()=>{l.forEach(d=>h.showError(d,o,a)),this.liveSocket.restorePreviouslyActiveFocus()})}binding(e){return this.liveSocket.binding(e)}};var ct=class{constructor(e,t,i={}){if(this.unloaded=!1,!t||t.constructor.name==="Object")throw new Error(`
      a phoenix Socket must be provided as the second argument to the LiveSocket constructor. For example:

          import {Socket} from "phoenix"
          import {LiveSocket} from "phoenix_live_view"
          let liveSocket = new LiveSocket("/live", Socket, {...})
      `);this.socket=new t(e,i),this.bindingPrefix=i.bindingPrefix||ei,this.opts=i,this.params=Ce(i.params||{}),this.viewLogger=i.viewLogger,this.metadataCallbacks=i.metadata||{},this.defaults=Object.assign(xe(ii),i.defaults||{}),this.activeElement=null,this.prevActive=null,this.silenced=!1,this.main=null,this.outgoingMainEl=null,this.clickStartedAtTarget=null,this.linkRef=1,this.roots={},this.href=window.location.href,this.pendingLink=null,this.currentLocation=xe(window.location),this.hooks=i.hooks||{},this.uploaders=i.uploaders||{},this.loaderTimeout=i.loaderTimeout||Qt,this.reloadWithJitterTimer=null,this.maxReloads=i.maxReloads||Mt,this.reloadJitterMin=i.reloadJitterMin||Xt,this.reloadJitterMax=i.reloadJitterMax||$t,this.failsafeJitter=i.failsafeJitter||Bt,this.localStorage=i.localStorage||window.localStorage,this.sessionStorage=i.sessionStorage||window.sessionStorage,this.boundTopLevelEvents=!1,this.serverCloseRef=null,this.domCallbacks=Object.assign({onPatchStart:Ce(),onPatchEnd:Ce(),onNodeAdded:Ce(),onBeforeElUpdated:Ce()},i.dom||{}),this.transitions=new _i,window.addEventListener("pagehide",r=>{this.unloaded=!0}),this.socket.onOpen(()=>{this.isUnloaded()&&window.location.reload()})}version(){return"0.20.17"}isProfileEnabled(){return this.sessionStorage.getItem(st)==="true"}isDebugEnabled(){return this.sessionStorage.getItem(Me)==="true"}isDebugDisabled(){return this.sessionStorage.getItem(Me)==="false"}enableDebug(){this.sessionStorage.setItem(Me,"true")}enableProfiling(){this.sessionStorage.setItem(st,"true")}disableDebug(){this.sessionStorage.setItem(Me,"false")}disableProfiling(){this.sessionStorage.removeItem(st)}enableLatencySim(e){this.enableDebug(),console.log("latency simulator enabled for the duration of this browser session. Call disableLatencySim() to disable"),this.sessionStorage.setItem(rt,e)}disableLatencySim(){this.sessionStorage.removeItem(rt)}getLatencySim(){let e=this.sessionStorage.getItem(rt);return e?parseInt(e):null}getSocket(){return this.socket}connect(){window.location.hostname==="localhost"&&!this.isDebugDisabled()&&this.enableDebug();let e=()=>{this.joinRootViews()?(this.bindTopLevelEvents(),this.socket.connect()):this.main?this.socket.connect():this.bindTopLevelEvents({dead:!0}),this.joinDeadView()};["complete","loaded","interactive"].indexOf(document.readyState)>=0?e():document.addEventListener("DOMContentLoaded",()=>e())}disconnect(e){clearTimeout(this.reloadWithJitterTimer),this.serverCloseRef&&(this.socket.off(this.serverCloseRef),this.serverCloseRef=null),this.socket.disconnect(e)}replaceTransport(e){clearTimeout(this.reloadWithJitterTimer),this.socket.replaceTransport(e),this.connect()}execJS(e,t,i=null){this.owner(e,r=>x.exec(i,t,r,e))}execJSHookPush(e,t,i,r){this.withinOwners(e,n=>{x.exec("hook",t,n,e,["push",{data:i,callback:r}])})}unload(){this.unloaded||(this.main&&this.isConnected()&&this.log(this.main,"socket",()=>["disconnect for page nav"]),this.unloaded=!0,this.destroyAllViews(),this.disconnect())}triggerDOM(e,t){this.domCallbacks[e](...t)}time(e,t){if(!this.isProfileEnabled()||!console.time)return t();console.time(e);let i=t();return console.timeEnd(e),i}log(e,t,i){if(this.viewLogger){let[r,n]=i();this.viewLogger(e,t,r,n)}else if(this.isDebugEnabled()){let[r,n]=i();ni(e,t,r,n)}}requestDOMUpdate(e){this.transitions.after(e)}transition(e,t,i=function(){}){this.transitions.addTransition(e,t,i)}onChannel(e,t,i){e.on(t,r=>{let n=this.getLatencySim();n?setTimeout(()=>i(r),n):i(r)})}wrapPush(e,t,i){let r=this.getLatencySim(),n=e.joinCount;if(!r)return this.isConnected()&&t.timeout?i().receive("timeout",()=>{e.joinCount===n&&!e.isDestroyed()&&this.reloadWithJitter(e,()=>{this.log(e,"timeout",()=>["received timeout while communicating with server. Falling back to hard refresh for recovery"])})}):i();let o={receives:[],receive(a,l){this.receives.push([a,l])}};return setTimeout(()=>{e.isDestroyed()||o.receives.reduce((a,[l,d])=>a.receive(l,d),i())},r),o}reloadWithJitter(e,t){clearTimeout(this.reloadWithJitterTimer),this.disconnect();let i=this.reloadJitterMin,r=this.reloadJitterMax,n=Math.floor(Math.random()*(r-i+1))+i,o=B.updateLocal(this.localStorage,window.location.pathname,We,0,a=>a+1);o>this.maxReloads&&(n=this.failsafeJitter),this.reloadWithJitterTimer=setTimeout(()=>{e.isDestroyed()||e.isConnected()||(e.destroy(),t?t():this.log(e,"join",()=>[`encountered ${o} consecutive reloads`]),o>this.maxReloads&&this.log(e,"join",()=>[`exceeded ${this.maxReloads} consecutive reloads. Entering failsafe mode`]),this.hasPendingLink()?window.location=this.pendingLink:window.location.reload())},n)}getHookCallbacks(e){return e&&e.startsWith("Phoenix.")?mi[e.split(".")[1]]:this.hooks[e]}isUnloaded(){return this.unloaded}isConnected(){return this.socket.isConnected()}getBindingPrefix(){return this.bindingPrefix}binding(e){return`${this.getBindingPrefix()}${e}`}channel(e,t){return this.socket.channel(e,t)}joinDeadView(){let e=document.body;if(e&&!this.isPhxView(e)&&!this.isPhxView(document.firstElementChild)){let t=this.newRootView(e);t.setHref(this.getHref()),t.joinDead(),this.main||(this.main=t),window.requestAnimationFrame(()=>t.execNewMounted())}}joinRootViews(){let e=!1;return h.all(document,`${Z}:not([${Q}])`,t=>{if(!this.getRootById(t.id)){let i=this.newRootView(t);i.setHref(this.getHref()),i.join(),t.hasAttribute(Se)&&(this.main=i)}e=!0}),e}redirect(e,t){this.unload(),B.redirect(e,t)}replaceMain(e,t,i=null,r=this.setPendingLink(e)){let n=this.currentLocation.href;this.outgoingMainEl=this.outgoingMainEl||this.main.el;let o=h.cloneNode(this.outgoingMainEl,"");this.main.showLoader(this.loaderTimeout),this.main.destroy(),this.main=this.newRootView(o,t,n),this.main.setRedirect(e),this.transitionRemoves(null,!0),this.main.join((a,l)=>{a===1&&this.commitPendingLink(r)&&this.requestDOMUpdate(()=>{h.findPhxSticky(document).forEach(d=>o.appendChild(d)),this.outgoingMainEl.replaceWith(o),this.outgoingMainEl=null,i&&i(r),l()})})}transitionRemoves(e,t){let i=this.binding("remove");if(e=e||h.all(document,`[${i}]`),t){let r=h.findPhxSticky(document)||[];e=e.filter(n=>!h.isChildOfAny(n,r))}e.forEach(r=>{this.execJS(r,r.getAttribute(i),"remove")})}isPhxView(e){return e.getAttribute&&e.getAttribute(M)!==null}newRootView(e,t,i){let r=new Te(e,this,null,t,i);return this.roots[r.id]=r,r}owner(e,t){let i=G(e.closest(Z),r=>this.getViewByEl(r))||this.main;i&&t(i)}withinOwners(e,t){this.owner(e,i=>t(i,e))}getViewByEl(e){let t=e.getAttribute(W);return G(this.getRootById(t),i=>i.getDescendentByEl(e))}getRootById(e){return this.roots[e]}destroyAllViews(){for(let e in this.roots)this.roots[e].destroy(),delete this.roots[e];this.main=null}destroyViewByEl(e){let t=this.getRootById(e.getAttribute(W));t&&t.id===e.id?(t.destroy(),delete this.roots[t.id]):t&&t.destroyDescendent(e.id)}setActiveElement(e){if(this.activeElement===e)return;this.activeElement=e;let t=()=>{e===this.activeElement&&(this.activeElement=null),e.removeEventListener("mouseup",this),e.removeEventListener("touchend",this)};e.addEventListener("mouseup",t),e.addEventListener("touchend",t)}getActiveElement(){return document.activeElement===document.body?this.activeElement||document.activeElement:document.activeElement||document.body}dropActiveElement(e){this.prevActive&&e.ownsElement(this.prevActive)&&(this.prevActive=null)}restorePreviouslyActiveFocus(){this.prevActive&&this.prevActive!==document.body&&this.prevActive.focus()}blurActiveElement(){this.prevActive=this.getActiveElement(),this.prevActive!==document.body&&this.prevActive.blur()}bindTopLevelEvents({dead:e}={}){this.boundTopLevelEvents||(this.boundTopLevelEvents=!0,this.serverCloseRef=this.socket.onClose(t=>{if(t&&t.code===1e3&&this.main)return this.reloadWithJitter(this.main)}),document.body.addEventListener("click",function(){}),window.addEventListener("pageshow",t=>{t.persisted&&(this.getSocket().disconnect(),this.withPageLoading({to:window.location.href,kind:"redirect"}),window.location.reload())},!0),e||this.bindNav(),this.bindClicks(),e||this.bindForms(),this.bind({keyup:"keyup",keydown:"keydown"},(t,i,r,n,o,a)=>{let l=n.getAttribute(this.binding(zt)),d=t.key&&t.key.toLowerCase();if(l&&l.toLowerCase()!==d)return;let c=F({key:t.key},this.eventMeta(i,t,n));x.exec(i,o,r,n,["push",{data:c}])}),this.bind({blur:"focusout",focus:"focusin"},(t,i,r,n,o,a)=>{if(!a){let l=F({key:t.key},this.eventMeta(i,t,n));x.exec(i,o,r,n,["push",{data:l}])}}),this.bind({blur:"blur",focus:"focus"},(t,i,r,n,o,a)=>{if(a==="window"){let l=this.eventMeta(i,t,n);x.exec(i,o,r,n,["push",{data:l}])}}),window.addEventListener("dragover",t=>t.preventDefault()),window.addEventListener("drop",t=>{t.preventDefault();let i=G(fe(t.target,this.binding(mt)),o=>o.getAttribute(this.binding(mt))),r=i&&document.getElementById(i),n=Array.from(t.dataTransfer.files||[]);!r||r.disabled||n.length===0||!(r.files instanceof FileList)||(_.trackFiles(r,n,t.dataTransfer),r.dispatchEvent(new Event("input",{bubbles:!0})))}),this.on(Ge,t=>{let i=t.target;if(!h.isUploadInput(i))return;let r=Array.from(t.detail.files||[]).filter(n=>n instanceof File||n instanceof Blob);_.trackFiles(i,r),i.dispatchEvent(new Event("input",{bubbles:!0}))}))}eventMeta(e,t,i){let r=this.metadataCallbacks[e];return r?r(t,i):{}}setPendingLink(e){return this.linkRef++,this.pendingLink=e,this.linkRef}commitPendingLink(e){return this.linkRef!==e?!1:(this.href=this.pendingLink,this.pendingLink=null,!0)}getHref(){return this.href}hasPendingLink(){return!!this.pendingLink}bind(e,t){for(let i in e){let r=e[i];this.on(r,n=>{let o=this.binding(i),a=this.binding(`window-${i}`),l=n.target.getAttribute&&n.target.getAttribute(o);l?this.debounce(n.target,n,r,()=>{this.withinOwners(n.target,d=>{t(n,i,d,n.target,l,null)})}):h.all(document,`[${a}]`,d=>{let c=d.getAttribute(a);this.debounce(d,n,r,()=>{this.withinOwners(d,f=>{t(n,i,f,d,c,"window")})})})})}}bindClicks(){window.addEventListener("mousedown",e=>this.clickStartedAtTarget=e.target),this.bindClick("click","click")}bindClick(e,t){let i=this.binding(t);window.addEventListener(e,r=>{let n=null;r.detail===0&&(this.clickStartedAtTarget=r.target);let o=this.clickStartedAtTarget||r.target;n=fe(o,i),this.dispatchClickAway(r,o),this.clickStartedAtTarget=null;let a=n&&n.getAttribute(i);if(!a){h.isNewPageClick(r,window.location)&&this.unload();return}n.getAttribute("href")==="#"&&r.preventDefault(),!n.hasAttribute(T)&&this.debounce(n,r,"click",()=>{this.withinOwners(n,l=>{x.exec("click",a,l,n,["push",{data:this.eventMeta("click",r,n)}])})})},!1)}dispatchClickAway(e,t){let i=this.binding("click-away");h.all(document,`[${i}]`,r=>{r.isSameNode(t)||r.contains(t)||this.withinOwners(r,n=>{let o=r.getAttribute(i);x.isVisible(r)&&x.isInViewport(r)&&x.exec("click",o,n,r,["push",{data:this.eventMeta("click",e,e.target)}])})})}bindNav(){if(!B.canPushState())return;history.scrollRestoration&&(history.scrollRestoration="manual");let e=null;window.addEventListener("scroll",t=>{clearTimeout(e),e=setTimeout(()=>{B.updateCurrentState(i=>Object.assign(i,{scroll:window.scrollY}))},100)}),window.addEventListener("popstate",t=>{if(!this.registerNewLocation(window.location))return;let{type:i,id:r,root:n,scroll:o}=t.state||{},a=window.location.href;h.dispatchEvent(window,"phx:navigate",{detail:{href:a,patch:i==="patch",pop:!0}}),this.requestDOMUpdate(()=>{this.main.isConnected()&&i==="patch"&&r===this.main.id?this.main.pushLinkPatch(a,null,()=>{this.maybeScroll(o)}):this.replaceMain(a,null,()=>{n&&this.replaceRootHistory(),this.maybeScroll(o)})})},!1),window.addEventListener("click",t=>{let i=fe(t.target,Ke),r=i&&i.getAttribute(Ke);if(!r||!this.isConnected()||!this.main||h.wantsNewTab(t))return;let n=i.href instanceof SVGAnimatedString?i.href.baseVal:i.href,o=i.getAttribute(Jt);t.preventDefault(),t.stopImmediatePropagation(),this.pendingLink!==n&&this.requestDOMUpdate(()=>{if(r==="patch")this.pushHistoryPatch(n,o,i);else if(r==="redirect")this.historyRedirect(n,o);else throw new Error(`expected ${Ke} to be "patch" or "redirect", got: ${r}`);let a=i.getAttribute(this.binding("click"));a&&this.requestDOMUpdate(()=>this.execJS(i,a,"click"))})},!1)}maybeScroll(e){typeof e=="number"&&requestAnimationFrame(()=>{window.scrollTo(0,e)})}dispatchEvent(e,t={}){h.dispatchEvent(window,`phx:${e}`,{detail:t})}dispatchEvents(e){e.forEach(([t,i])=>this.dispatchEvent(t,i))}withPageLoading(e,t){h.dispatchEvent(window,"phx:page-loading-start",{detail:e});let i=()=>h.dispatchEvent(window,"phx:page-loading-stop",{detail:e});return t?t(i):i}pushHistoryPatch(e,t,i){if(!this.isConnected()||!this.main.isMain())return B.redirect(e);this.withPageLoading({to:e,kind:"patch"},r=>{this.main.pushLinkPatch(e,i,n=>{this.historyPatch(e,t,n),r()})})}historyPatch(e,t,i=this.setPendingLink(e)){!this.commitPendingLink(i)||(B.pushState(t,{type:"patch",id:this.main.id},e),h.dispatchEvent(window,"phx:navigate",{detail:{patch:!0,href:e,pop:!1}}),this.registerNewLocation(window.location))}historyRedirect(e,t,i){if(!this.isConnected()||!this.main.isMain())return B.redirect(e,i);if(/^\/$|^\/[^\/]+.*$/.test(e)){let{protocol:n,host:o}=window.location;e=`${n}//${o}${e}`}let r=window.scrollY;this.withPageLoading({to:e,kind:"redirect"},n=>{this.replaceMain(e,i,o=>{o===this.linkRef&&(B.pushState(t,{type:"redirect",id:this.main.id,scroll:r},e),h.dispatchEvent(window,"phx:navigate",{detail:{href:e,patch:!1,pop:!1}}),this.registerNewLocation(window.location)),n()})})}replaceRootHistory(){B.pushState("replace",{root:!0,type:"patch",id:this.main.id})}registerNewLocation(e){let{pathname:t,search:i}=this.currentLocation;return t+i===e.pathname+e.search?!1:(this.currentLocation=xe(e),!0)}bindForms(){let e=0,t=!1;this.on("submit",i=>{let r=i.target.getAttribute(this.binding("submit")),n=i.target.getAttribute(this.binding("change"));!t&&n&&!r&&(t=!0,i.preventDefault(),this.withinOwners(i.target,o=>{o.disableForm(i.target),window.requestAnimationFrame(()=>{h.isUnloadableFormSubmit(i)&&this.unload(),i.target.submit()})}))},!0),this.on("submit",i=>{let r=i.target.getAttribute(this.binding("submit"));if(!r){h.isUnloadableFormSubmit(i)&&this.unload();return}i.preventDefault(),i.target.disabled=!0,this.withinOwners(i.target,n=>{x.exec("submit",r,n,i.target,["push",{submitter:i.submitter}])})},!1);for(let i of["change","input"])this.on(i,r=>{let n=this.binding("change"),o=r.target,a=o.getAttribute(n),l=o.form&&o.form.getAttribute(n),d=a||l;if(!d||o.type==="number"&&o.validity&&o.validity.badInput)return;let c=a?o:o.form,f=e;e++;let{at:v,type:m}=h.private(o,"prev-iteration")||{};v===f-1&&i==="change"&&m==="input"||(h.putPrivate(o,"prev-iteration",{at:f,type:i}),this.debounce(o,r,i,()=>{this.withinOwners(c,p=>{h.putPrivate(o,Fe,!0),h.isTextualInput(o)||this.setActiveElement(o),x.exec("change",d,p,o,["push",{_target:r.target.name,dispatcher:c}])})}))},!1);this.on("reset",i=>{let r=i.target;h.resetForm(r,this.binding(de),this.binding(ce));let n=Array.from(r.elements).find(o=>o.type==="reset");n&&window.requestAnimationFrame(()=>{n.dispatchEvent(new Event("input",{bubbles:!0,cancelable:!1}))})})}debounce(e,t,i,r){if(i==="blur"||i==="focusout")return r();let n=this.binding(Kt),o=this.binding(Gt),a=this.defaults.debounce.toString(),l=this.defaults.throttle.toString();this.withinOwners(e,d=>{let c=()=>!d.isDestroyed()&&document.body.contains(e);h.debounce(e,t,n,a,o,l,c,()=>{r()})})}silenceEvents(e){this.silenced=!0,e(),this.silenced=!1}on(e,t){window.addEventListener(e,i=>{this.silenced||t(i)})}},_i=class{constructor(){this.transitions=new Set,this.pendingOps=[]}reset(){this.transitions.forEach(e=>{clearTimeout(e),this.transitions.delete(e)}),this.flushPendingOps()}after(e){this.size()===0?e():this.pushPendingOp(e)}addTransition(e,t,i){t();let r=setTimeout(()=>{this.transitions.delete(r),i(),this.flushPendingOps()},e);this.transitions.add(r)}pushPendingOp(e){this.pendingOps.push(e)}size(){return this.transitions.size}flushPendingOps(){if(this.size()>0)return;let e=this.pendingOps.shift();e&&(e(),this.flushPendingOps())}};return Gi;})();

defmodule N8nElixir.Nodes.Merge do
  @moduledoc """
  合并节点 - Merge

  参考n8n的Merge节点，支持多种数据合并策略
  可以合并来自不同输入的数据流
  """
  @behaviour N8nElixir.NodeType

  alias N8nElixir.DataProcessor

  @impl true
  def description() do
    %{
      displayName: "Merge",
      name: "merge",
      icon: "fa:code-branch",
      group: ["transform"],
      version: 1,
      description: "Merge data from multiple inputs",
      defaults: %{
        name: "Merge",
        color: "#00D4AA"
      },
      inputs: ["main", "main"],
      inputNames: ["Input 1", "Input 2"],
      outputs: ["main"],
      properties: [
        %{
          displayName: "Mode",
          name: "mode",
          type: "options",
          options: [
            %{
              name: "Append",
              value: "append",
              description: "Append all data from input 2 to input 1"
            },
            %{
              name: "Pass-through",
              value: "passThrough", 
              description: "Pass data from input 1 through, ignore input 2"
            },
            %{
              name: "Wait",
              value: "wait",
              description: "Wait for both inputs before proceeding"
            },
            %{
              name: "Multiplex",
              value: "multiplex",
              description: "Combine corresponding items from both inputs"
            },
            %{
              name: "Combine",
              value: "combine",
              description: "Combine data by matching keys"
            }
          ],
          default: "append"
        },
        %{
          displayName: "Join Mode",
          name: "joinMode",
          type: "options",
          displayOptions: %{
            show: %{
              mode: ["combine"]
            }
          },
          options: [
            %{name: "Inner Join", value: "inner"},
            %{name: "Left Join", value: "left"},
            %{name: "Outer Join", value: "outer"}
          ],
          default: "inner",
          description: "How to join the data when combining"
        },
        %{
          displayName: "Property Input 1",
          name: "propertyName1",
          type: "string",
          displayOptions: %{
            show: %{
              mode: ["combine"]
            }
          },
          default: "id",
          description: "Name of property in input 1 to match on"
        },
        %{
          displayName: "Property Input 2", 
          name: "propertyName2",
          type: "string",
          displayOptions: %{
            show: %{
              mode: ["combine"]
            }
          },
          default: "id",
          description: "Name of property in input 2 to match on"
        }
      ]
    }
  end

  @impl true
  def execute(input_data, _credentials, parameters) do
    mode = Map.get(parameters, "mode", "append")
    
    # 假设input_data是一个列表，包含来自不同输入的数据
    {input1_data, input2_data} = split_input_data(input_data)
    
    case mode do
      "append" ->
        merge_append(input1_data, input2_data)
      
      "passThrough" ->
        merge_pass_through(input1_data, input2_data)
      
      "wait" ->
        merge_wait(input1_data, input2_data)
      
      "multiplex" ->
        merge_multiplex(input1_data, input2_data)
      
      "combine" ->
        join_mode = Map.get(parameters, "joinMode", "inner")
        property1 = Map.get(parameters, "propertyName1", "id")
        property2 = Map.get(parameters, "propertyName2", "id")
        merge_combine(input1_data, input2_data, join_mode, property1, property2)
      
      _ ->
        {:error, "Unknown merge mode: #{mode}"}
    end
  end

  @impl true
  def credential_types() do
    []
  end

  # 私有实现函数

  defp split_input_data(input_data) when is_list(input_data) do
    # 简化版本：假设前一半是input1，后一半是input2
    mid_point = div(length(input_data), 2)
    {Enum.take(input_data, mid_point), Enum.drop(input_data, mid_point)}
  end
  defp split_input_data(input_data) do
    {[input_data], []}
  end

  defp merge_append(input1_data, input2_data) do
    result = input1_data ++ input2_data
    {:ok, result}
  end

  defp merge_pass_through(input1_data, _input2_data) do
    {:ok, input1_data}
  end

  defp merge_wait(input1_data, input2_data) do
    # 只有当两个输入都有数据时才继续
    if Enum.empty?(input1_data) or Enum.empty?(input2_data) do
      {:ok, []}
    else
      {:ok, input1_data ++ input2_data}
    end
  end

  defp merge_multiplex(input1_data, input2_data) do
    # 将对应位置的数据项合并
    max_length = max(length(input1_data), length(input2_data))
    
    result = 
      0..(max_length - 1)
      |> Enum.map(fn index ->
        item1 = Enum.at(input1_data, index, %{"json" => %{}})
        item2 = Enum.at(input2_data, index, %{"json" => %{}})
        
        combined_json = Map.merge(item1["json"] || %{}, item2["json"] || %{})
        combined_binary = Map.merge(item1["binary"] || %{}, item2["binary"] || %{})
        
        %{
          "json" => combined_json,
          "binary" => combined_binary
        }
      end)
    
    {:ok, result}
  end

  defp merge_combine(input1_data, input2_data, join_mode, property1, property2) do
    # 根据指定属性进行数据合并
    try do
      # 为input2数据创建索引
      input2_index = 
        input2_data
        |> Enum.reduce(%{}, fn item, acc ->
          key = get_property_value(item, property2)
          if key != nil do
            Map.put(acc, key, item)
          else
            acc
          end
        end)
      
      # 根据join模式处理数据
      result = case join_mode do
        "inner" ->
          perform_inner_join(input1_data, input2_index, property1)
        
        "left" ->
          perform_left_join(input1_data, input2_index, property1)
        
        "outer" ->
          perform_outer_join(input1_data, input2_data, input2_index, property1, property2)
      end
      
      {:ok, result}
    rescue
      error ->
        {:error, "Combine merge failed: #{inspect(error)}"}
    end
  end

  defp perform_inner_join(input1_data, input2_index, property1) do
    Enum.reduce(input1_data, [], fn item1, acc ->
      key = get_property_value(item1, property1)
      
      case Map.get(input2_index, key) do
        nil -> acc
        item2 ->
          combined = combine_items(item1, item2)
          [combined | acc]
      end
    end)
    |> Enum.reverse()
  end

  defp perform_left_join(input1_data, input2_index, property1) do
    Enum.map(input1_data, fn item1 ->
      key = get_property_value(item1, property1)
      
      case Map.get(input2_index, key) do
        nil -> item1
        item2 -> combine_items(item1, item2)
      end
    end)
  end

  defp perform_outer_join(input1_data, input2_data, input2_index, property1, property2) do
    # 先执行左连接
    left_result = perform_left_join(input1_data, input2_index, property1)
    
    # 找出input1中已匹配的键
    matched_keys = 
      input1_data
      |> Enum.map(&get_property_value(&1, property1))
      |> MapSet.new()
    
    # 添加input2中未匹配的项
    unmatched_input2 = 
      input2_data
      |> Enum.reject(fn item ->
        key = get_property_value(item, property2)
        MapSet.member?(matched_keys, key)
      end)
    
    left_result ++ unmatched_input2
  end

  defp get_property_value(item, property_name) do
    case item do
      %{"json" => json_data} when is_map(json_data) ->
        get_nested_value(json_data, property_name)
      _ -> nil
    end
  end

  defp get_nested_value(data, path) when is_binary(path) do
    path_parts = String.split(path, ".")
    get_nested_value(data, path_parts)
  end
  defp get_nested_value(data, []), do: data
  defp get_nested_value(data, [key | rest]) when is_map(data) do
    case Map.get(data, key) do
      nil -> nil
      value -> get_nested_value(value, rest)
    end
  end
  defp get_nested_value(_, _), do: nil

  defp combine_items(item1, item2) do
    combined_json = Map.merge(item1["json"] || %{}, item2["json"] || %{})
    combined_binary = Map.merge(item1["binary"] || %{}, item2["binary"] || %{})
    
    %{
      "json" => combined_json,
      "binary" => combined_binary,
      "pairedItem" => [
        Map.get(item1, "pairedItem"),
        Map.get(item2, "pairedItem")
      ]
    }
  end
end
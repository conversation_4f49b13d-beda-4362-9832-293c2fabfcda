defmodule N8nElixir.Workflows do
  @moduledoc """
  工作流上下文模块
  
  提供工作流相关的数据库操作和业务逻辑
  """
  import Ecto.Query

  alias N8nElixir.Repo
  alias N8nElixir.Workflows.{Workflow, Node}

  def list_workflows() do
    Repo.all(Workflow)
  end

  def list_workflows_by_owner(owner_id) do
    Workflow
    |> where([w], w.owner_id == ^owner_id)
    |> Repo.all()
  end

  def get_workflow(id) do
    Repo.get(Workflow, id)
  end

  def get_workflow!(id) do
    Repo.get!(Workflow, id)
  end

  def get_workflow_with_nodes(id) do
    Workflow
    |> where([w], w.id == ^id)
    |> preload([:executions, :triggers])
    |> Repo.one()
  end

  def create_workflow(attrs \\ %{}) do
    %Workflow{}
    |> Workflow.changeset(attrs)
    |> Repo.insert()
  end

  def update_workflow(%Workflow{} = workflow, attrs) do
    workflow
    |> Workflow.changeset(attrs)
    |> Repo.update()
  end

  def delete_workflow(%Workflow{} = workflow) do
    Repo.delete(workflow)
  end

  def activate_workflow(workflow_id) do
    workflow = get_workflow!(workflow_id)
    update_workflow(workflow, %{status: :active})
  end

  def deactivate_workflow(workflow_id) do
    workflow = get_workflow!(workflow_id)
    update_workflow(workflow, %{status: :inactive})
  end

  def duplicate_workflow(workflow_id, new_name) do
    workflow = get_workflow!(workflow_id)
    
    new_attrs = %{
      name: new_name,
      description: workflow.description,
      nodes: workflow.nodes,
      connections: workflow.connections,
      settings: workflow.settings,
      static_data: workflow.static_data,
      owner_id: workflow.owner_id,
      team_id: workflow.team_id,
      status: :draft
    }
    
    create_workflow(new_attrs)
  end

  def search_workflows(query, owner_id \\ nil) do
    base_query = from w in Workflow,
      where: ilike(w.name, ^"%#{query}%") or ilike(w.description, ^"%#{query}%")
    
    query = if owner_id do
      where(base_query, [w], w.owner_id == ^owner_id)
    else
      base_query
    end
    
    Repo.all(query)
  end

  def get_workflows_by_tag(tag, owner_id \\ nil) do
    base_query = from w in Workflow,
      where: ^tag in w.tags
    
    query = if owner_id do
      where(base_query, [w], w.owner_id == ^owner_id)
    else
      base_query
    end
    
    Repo.all(query)
  end

  def export_workflow(workflow_id) do
    workflow = get_workflow_with_nodes(workflow_id)
    
    if workflow do
      %{
        "name" => workflow.name,
        "description" => workflow.description,
        "nodes" => workflow.nodes,
        "connections" => workflow.connections,
        "settings" => workflow.settings,
        "staticData" => workflow.static_data,
        "pinData" => workflow.pin_data,
        "tags" => workflow.tags,
        "version" => workflow.version,
        "meta" => %{
          "exportedAt" => DateTime.utc_now(),
          "version" => "1.0"
        }
      }
    else
      nil
    end
  end

  def import_workflow(workflow_data, owner_id) do
    attrs = %{
      name: Map.get(workflow_data, "name", "Imported Workflow"),
      description: Map.get(workflow_data, "description", ""),
      nodes: Map.get(workflow_data, "nodes", %{}),
      connections: Map.get(workflow_data, "connections", %{}),
      settings: Map.get(workflow_data, "settings", %{}),
      static_data: Map.get(workflow_data, "staticData", %{}),
      pin_data: Map.get(workflow_data, "pinData", %{}),
      tags: Map.get(workflow_data, "tags", []),
      owner_id: owner_id,
      status: :draft
    }
    
    create_workflow(attrs)
  end
end

defmodule N8nElixir.Executions do
  @moduledoc """
  执行上下文模块
  
  提供执行相关的数据库操作和业务逻辑
  """
  import Ecto.Query

  alias N8nElixir.Repo
  alias N8nElixir.Executions.{Execution, ExecutionData}

  def list_executions() do
    Repo.all(Execution)
  end

  def list_executions_by_workflow(workflow_id, opts \\ []) do
    limit = Keyword.get(opts, :limit, 50)
    offset = Keyword.get(opts, :offset, 0)
    
    Execution
    |> where([e], e.workflow_id == ^workflow_id)
    |> order_by([e], desc: e.started_at)
    |> limit(^limit)
    |> offset(^offset)
    |> Repo.all()
  end

  def get_execution(id) do
    Repo.get(Execution, id)
  end

  def get_execution!(id) do
    Repo.get!(Execution, id)
  end

  def get_execution_with_data(id) do
    Execution
    |> where([e], e.id == ^id)
    |> preload([:execution_data, :workflow])
    |> Repo.one()
  end

  def create_execution(attrs \\ %{}) do
    %Execution{}
    |> Execution.changeset(attrs)
    |> Repo.insert()
  end

  def update_execution(execution_id, attrs) when is_binary(execution_id) do
    execution = get_execution!(execution_id)
    update_execution(execution, attrs)
  end

  def update_execution(%Execution{} = execution, attrs) do
    execution
    |> Execution.changeset(attrs)
    |> Repo.update()
  end

  def delete_execution(%Execution{} = execution) do
    Repo.delete(execution)
  end

  def create_execution_data(attrs \\ %{}) do
    %ExecutionData{}
    |> ExecutionData.changeset(attrs)
    |> Repo.insert()
  end

  def get_execution_statistics(workflow_id, opts \\ []) do
    days = Keyword.get(opts, :days, 30)
    start_date = DateTime.add(DateTime.utc_now(), -days * 24 * 60 * 60, :second)
    
    base_query = from e in Execution,
      where: e.workflow_id == ^workflow_id and e.started_at >= ^start_date
    
    total_executions = Repo.aggregate(base_query, :count, :id)
    
    success_executions = base_query
    |> where([e], e.status == :success)
    |> Repo.aggregate(:count, :id)
    
    failed_executions = base_query
    |> where([e], e.status == :error)
    |> Repo.aggregate(:count, :id)
    
    avg_duration = base_query
    |> where([e], e.status == :success and not is_nil(e.total_duration))
    |> Repo.aggregate(:avg, :total_duration)
    
    %{
      total_executions: total_executions,
      success_executions: success_executions,
      failed_executions: failed_executions,
      success_rate: if(total_executions > 0, do: success_executions / total_executions * 100, else: 0),
      average_duration: avg_duration || 0
    }
  end

  def get_recent_executions(limit \\ 10) do
    Execution
    |> order_by([e], desc: e.started_at)
    |> limit(^limit)
    |> preload([:workflow])
    |> Repo.all()
  end

  def cleanup_old_executions(days_to_keep \\ 30) do
    cutoff_date = DateTime.add(DateTime.utc_now(), -days_to_keep * 24 * 60 * 60, :second)
    
    from(e in Execution, where: e.started_at < ^cutoff_date and e.status in [:success, :error, :canceled])
    |> Repo.delete_all()
  end

  def retry_execution(execution_id) do
    execution = get_execution!(execution_id)
    
    if execution.status in [:error, :canceled] do
      # 创建重试执行
      retry_attrs = %{
        workflow_id: execution.workflow_id,
        mode: :retry,
        retry_of: execution.id,
        data: execution.data,
        workflow_data: execution.workflow_data
      }
      
      create_execution(retry_attrs)
    else
      {:error, "Cannot retry execution with status: #{execution.status}"}
    end
  end
end

defmodule N8nElixir.TriggerManager do
  @moduledoc """
  触发器管理器
  
  负责管理所有类型的触发器，包括webhook、定时任务等
  """
  use GenServer

  alias N8nElixir.Triggers
  alias N8nElixir.WorkflowRunner

  @registry_name __MODULE__

  # 客户端API
  def start_link(init_arg) do
    GenServer.start_link(__MODULE__, init_arg, name: @registry_name)
  end

  def register_trigger(workflow_id, trigger_config) do
    GenServer.call(@registry_name, {:register_trigger, workflow_id, trigger_config})
  end

  def unregister_trigger(trigger_id) do
    GenServer.call(@registry_name, {:unregister_trigger, trigger_id})
  end

  def trigger_workflow(workflow_id, trigger_data \\ %{}) do
    GenServer.call(@registry_name, {:trigger_workflow, workflow_id, trigger_data})
  end

  def list_active_triggers() do
    GenServer.call(@registry_name, :list_active_triggers)
  end

  # 服务器回调
  @impl true
  def init(_init_arg) do
    # 启动时加载所有活跃的触发器
    active_triggers = Triggers.list_active_triggers()
    
    state = %{
      triggers: Map.new(active_triggers, fn trigger -> 
        {trigger.id, trigger}
      end)
    }

    {:ok, state}
  end

  @impl true
  def handle_call({:register_trigger, workflow_id, trigger_config}, _from, state) do
    case Triggers.create_trigger(Map.put(trigger_config, :workflow_id, workflow_id)) do
      {:ok, trigger} ->
        updated_triggers = Map.put(state.triggers, trigger.id, trigger)
        new_state = %{state | triggers: updated_triggers}
        {:reply, {:ok, trigger}, new_state}
      
      {:error, changeset} ->
        {:reply, {:error, changeset}, state}
    end
  end

  @impl true
  def handle_call({:unregister_trigger, trigger_id}, _from, state) do
    case Map.get(state.triggers, trigger_id) do
      nil ->
        {:reply, {:error, :not_found}, state}
      
      trigger ->
        Triggers.delete_trigger(trigger)
        updated_triggers = Map.delete(state.triggers, trigger_id)
        new_state = %{state | triggers: updated_triggers}
        {:reply, :ok, new_state}
    end
  end

  @impl true
  def handle_call({:trigger_workflow, workflow_id, trigger_data}, _from, state) do
    case WorkflowRunner.start_execution(workflow_id, [
      mode: :trigger,
      trigger_data: trigger_data
    ]) do
      {:ok, execution_id} ->
        # 启动执行
        Task.start(fn ->
          WorkflowRunner.execute_workflow(workflow_id, [execution_id: execution_id])
        end)
        
        {:reply, {:ok, execution_id}, state}
      
      {:error, reason} ->
        {:reply, {:error, reason}, state}
    end
  end

  @impl true
  def handle_call(:list_active_triggers, _from, state) do
    active_triggers = Enum.filter(state.triggers, fn {_id, trigger} ->
      trigger.status == :active
    end)
    
    {:reply, active_triggers, state}
  end
end

defmodule N8nElixir.Triggers do
  @moduledoc """
  触发器上下文模块
  
  提供触发器相关的数据库操作和业务逻辑
  """
  import Ecto.Query

  alias N8nElixir.Repo
  alias N8nElixir.Triggers.Trigger

  def list_triggers() do
    Repo.all(Trigger)
  end

  def list_active_triggers() do
    Trigger
    |> where([t], t.status == :active)
    |> Repo.all()
  end

  def get_trigger(id) do
    Repo.get(Trigger, id)
  end

  def get_trigger!(id) do
    Repo.get!(Trigger, id)
  end

  def create_trigger(attrs \\ %{}) do
    %Trigger{}
    |> Trigger.changeset(attrs)
    |> Repo.insert()
  end

  def update_trigger(%Trigger{} = trigger, attrs) do
    trigger
    |> Trigger.changeset(attrs)
    |> Repo.update()
  end

  def delete_trigger(%Trigger{} = trigger) do
    Repo.delete(trigger)
  end

  def get_triggers_for_workflow(workflow_id) do
    Trigger
    |> where([t], t.workflow_id == ^workflow_id)
    |> Repo.all()
  end
end

defmodule N8nElixir.CredentialManager do
  @moduledoc """
  凭证管理器
  
  安全地管理和提供加密的第三方服务凭证
  """
  use GenServer

  @registry_name __MODULE__

  def start_link(init_arg) do
    GenServer.start_link(__MODULE__, init_arg, name: @registry_name)
  end

  @impl true
  def init(_init_arg) do
    {:ok, %{}}
  end

  def get_credentials_for_node(node_name, workflow_id) do
    GenServer.call(@registry_name, {:get_credentials, node_name, workflow_id})
  end

  @impl true
  def handle_call({:get_credentials, _node_name, _workflow_id}, _from, state) do
    # 这里需要实现从数据库获取加密凭证并解密的逻辑
    credentials = %{}
    
    {:reply, credentials, state}
  end
end

defmodule N8nElixir.ExecutionTracker do
  @moduledoc """
  执行追踪器
  
  监控和追踪所有工作流执行的状态和性能指标
  """
  use GenServer

  @registry_name __MODULE__

  def start_link(init_arg) do
    GenServer.start_link(__MODULE__, init_arg, name: @registry_name)
  end

  @impl true
  def init(_init_arg) do
    # 订阅执行事件
    Phoenix.PubSub.subscribe(N8nElixir.PubSub, "workflow_executions:*")
    
    {:ok, %{active_executions: %{}, metrics: %{}}}
  end

  @impl true
  def handle_info({:execution_started, execution_id, state}, tracker_state) do
    updated_state = %{tracker_state | 
      active_executions: Map.put(tracker_state.active_executions, execution_id, state)
    }
    
    {:noreply, updated_state}
  end

  @impl true
  def handle_info({:execution_completed, execution_id, state}, tracker_state) do
    updated_state = %{tracker_state | 
      active_executions: Map.delete(tracker_state.active_executions, execution_id)
    }
    
    # 更新性能指标
    update_metrics(updated_state, state)
    
    {:noreply, updated_state}
  end

  @impl true
  def handle_info(_event, state) do
    {:noreply, state}
  end

  defp update_metrics(state, _execution_state) do
    # 这里可以实现性能指标的更新逻辑
    state
  end
end

defmodule N8nElixir.ActiveWorkflows do
  @moduledoc """
  活动工作流管理器
  
  管理已激活工作流的状态，类似n8n的ActiveWorkflows
  """
  use GenServer

  @registry_name __MODULE__

  def start_link(init_arg) do
    GenServer.start_link(__MODULE__, init_arg, name: @registry_name)
  end

  @impl true
  def init(_init_arg) do
    # 加载所有活动的工作流
    active_workflows = N8nElixir.Workflows.list_workflows()
    |> Enum.filter(fn workflow -> workflow.status == :active end)
    |> Map.new(fn workflow -> {workflow.id, workflow} end)
    
    {:ok, %{workflows: active_workflows}}
  end

  def activate_workflow(workflow_id) do
    GenServer.call(@registry_name, {:activate_workflow, workflow_id})
  end

  def deactivate_workflow(workflow_id) do
    GenServer.call(@registry_name, {:deactivate_workflow, workflow_id})
  end

  def is_workflow_active?(workflow_id) do
    GenServer.call(@registry_name, {:is_workflow_active, workflow_id})
  end

  @impl true
  def handle_call({:activate_workflow, workflow_id}, _from, state) do
    case N8nElixir.Workflows.get_workflow(workflow_id) do
      nil ->
        {:reply, {:error, :not_found}, state}
      
      workflow ->
        N8nElixir.Workflows.activate_workflow(workflow_id)
        updated_workflows = Map.put(state.workflows, workflow_id, workflow)
        new_state = %{state | workflows: updated_workflows}
        {:reply, :ok, new_state}
    end
  end

  @impl true
  def handle_call({:deactivate_workflow, workflow_id}, _from, state) do
    N8nElixir.Workflows.deactivate_workflow(workflow_id)
    updated_workflows = Map.delete(state.workflows, workflow_id)
    new_state = %{state | workflows: updated_workflows}
    {:reply, :ok, new_state}
  end

  @impl true
  def handle_call({:is_workflow_active, workflow_id}, _from, state) do
    is_active = Map.has_key?(state.workflows, workflow_id)
    {:reply, is_active, state}
  end
end
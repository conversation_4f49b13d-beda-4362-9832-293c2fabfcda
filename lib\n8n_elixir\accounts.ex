defmodule N8nElixir.Accounts.User do
  @moduledoc """
  用户模型
  参考n8n的用户管理系统
  """
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "users" do
    field :email, :string
    field :password_hash, :string
    field :first_name, :string
    field :last_name, :string
    field :role, Ecto.Enum, values: [:admin, :user, :viewer], default: :user
    field :status, Ecto.Enum, values: [:active, :inactive, :pending], default: :pending
    field :last_login_at, :utc_datetime

    # 关联关系
    has_many :workflows, N8nElixir.Workflows.Workflow, foreign_key: :owner_id
    has_many :credentials, N8nElixir.Credentials.Credential, foreign_key: :owner_id
    has_many :executions, N8nElixir.Executions.Execution, foreign_key: :triggered_by_id
    belongs_to :team, N8nElixir.Accounts.Team

    timestamps()
  end

  @doc false
  def changeset(user, attrs) do
    user
    |> cast(attrs, [:email, :password_hash, :first_name, :last_name, :role, :status, :last_login_at, :team_id])
    |> validate_required([:email])
    |> validate_format(:email, ~r/^[^\s]+@[^\s]+\.[^\s]+$/)
    |> validate_length(:first_name, max: 100)
    |> validate_length(:last_name, max: 100)
    |> unique_constraint(:email)
  end
end

defmodule N8nElixir.Accounts.Team do
  @moduledoc """
  团队模型
  """
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "teams" do
    field :name, :string
    field :description, :string
    field :status, Ecto.Enum, values: [:active, :inactive], default: :active

    # 关联关系
    has_many :users, N8nElixir.Accounts.User
    has_many :workflows, N8nElixir.Workflows.Workflow
    has_many :credentials, N8nElixir.Credentials.Credential

    timestamps()
  end

  @doc false
  def changeset(team, attrs) do
    team
    |> cast(attrs, [:name, :description, :status])
    |> validate_required([:name])
    |> validate_length(:name, min: 1, max: 255)
    |> validate_length(:description, max: 1000)
    |> unique_constraint(:name)
  end
end

defmodule N8nElixir.Credentials.Credential do
  @moduledoc """
  凭证模型

  参考n8n的ICredentials接口，存储加密的第三方服务认证信息
  """
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "credentials" do
    field :name, :string
    field :type, :string # "oauth2", "api_key", "basic_auth", "bearer_token"
    field :description, :string

    # 加密的凭证数据
    field :encrypted_data, :binary
    field :encryption_key_id, :string # 用于标识加密密钥的版本

    # 凭证状态
    field :status, Ecto.Enum, values: [:active, :inactive, :expired, :revoked], default: :active
    field :last_used_at, :utc_datetime
    field :expires_at, :utc_datetime

    # 关联关系
    belongs_to :owner, N8nElixir.Accounts.User
    belongs_to :team, N8nElixir.Accounts.Team
    has_many :workflow_credentials, N8nElixir.Credentials.WorkflowCredential
    has_many :workflows, through: [:workflow_credentials, :workflow]

    timestamps()
  end

  @doc false
  def changeset(credential, attrs) do
    credential
    |> cast(attrs, [
      :name, :type, :description, :encrypted_data, :encryption_key_id,
      :status, :last_used_at, :expires_at, :owner_id, :team_id
    ])
    |> validate_required([:name, :type, :owner_id])
    |> validate_length(:name, min: 1, max: 255)
    |> validate_length(:type, min: 1, max: 100)
    |> validate_length(:description, max: 1000)
    |> validate_credential_type()
    |> unique_constraint(:name, name: :credentials_name_owner_id_index)
  end

  defp validate_credential_type(changeset) do
    valid_types = ["oauth2", "api_key", "basic_auth", "bearer_token", "certificate", "ssh_key"]

    case get_field(changeset, :type) do
      type when type in ["oauth2", "api_key", "basic_auth", "bearer_token", "certificate", "ssh_key"] ->
        changeset
      _ ->
        add_error(changeset, :type, "must be one of: #{Enum.join(valid_types, ", ")}")
    end
  end
end

defmodule N8nElixir.Credentials.WorkflowCredential do
  @moduledoc """
  工作流与凭证的关联表

  支持多对多关系，一个工作流可以使用多个凭证，一个凭证可以被多个工作流使用
  """
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "workflow_credentials" do
    field :node_name, :string # 使用该凭证的节点名称
    field :credential_key, :string # 在节点中的凭证键名

    belongs_to :workflow, N8nElixir.Workflows.Workflow
    belongs_to :credential, N8nElixir.Credentials.Credential

    timestamps()
  end

  @doc false
  def changeset(workflow_credential, attrs) do
    workflow_credential
    |> cast(attrs, [:node_name, :credential_key, :workflow_id, :credential_id])
    |> validate_required([:workflow_id, :credential_id])
    |> unique_constraint([:workflow_id, :credential_id, :node_name],
        name: :workflow_credentials_workflow_credential_node_index)
  end
end

defmodule N8nElixir.Triggers.Trigger do
  @moduledoc """
  触发器模型

  参考n8n的触发器系统，定义工作流的触发条件和配置
  """
  use Ecto.Schema
  import Ecto.Changeset

  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id

  schema "triggers" do
    field :name, :string
    field :type, :string # "webhook", "cron", "manual", "poll"
    field :status, Ecto.Enum, values: [:active, :inactive, :paused], default: :active

    # 触发器配置
    field :configuration, :map, default: %{}

    # 触发统计
    field :trigger_count, :integer, default: 0
    field :last_triggered_at, :utc_datetime
    field :last_trigger_status, Ecto.Enum, values: [:success, :failed], default: :success

    # 关联关系
    belongs_to :workflow, N8nElixir.Workflows.Workflow

    timestamps()
  end

  @doc false
  def changeset(trigger, attrs) do
    trigger
    |> cast(attrs, [
      :name, :type, :status, :configuration, :trigger_count, :last_triggered_at,
      :last_trigger_status, :workflow_id
    ])
    |> validate_required([:name, :type, :workflow_id])
    |> validate_length(:name, min: 1, max: 255)
    |> validate_trigger_type()
  end

  defp validate_trigger_type(changeset) do
    valid_types = ["webhook", "cron", "manual", "poll", "email"]

    case get_field(changeset, :type) do
      type when type in ["webhook", "cron", "manual", "poll", "email"] ->
        changeset
      _ ->
        add_error(changeset, :type, "must be one of: #{Enum.join(valid_types, ", ")}")
    end
  end
end

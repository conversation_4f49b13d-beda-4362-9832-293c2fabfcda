{<<"app">>,<<"unicode_util_compat">>}.
{<<"build_tools">>,[<<"rebar3">>]}.
{<<"description">>,<<"unicode_util compatibility library for Erlang < 20">>}.
{<<"files">>,
 [<<"LICENSE">>,<<"README.md">>,<<"rebar.config">>,<<"src">>,
  <<"src/string_compat.erl">>,<<"src/unicode_util_compat.app.src">>,
  <<"src/unicode_util_compat.erl">>]}.
{<<"licenses">>,[<<"Apache 2.0">>]}.
{<<"links">>,
 [{<<"Github">>,<<"https://github.com/benoitc/unicode_util_compat">>}]}.
{<<"name">>,<<"unicode_util_compat">>}.
{<<"requirements">>,[]}.
{<<"version">>,<<"0.7.1">>}.

{minimum_otp_vsn,"24.0"}.
{erl_opts,[debug_info]}.
{deps,[{telemetry,"~> 1.0"}]}.
{profiles,[{test,[{erl_opts,[nowarn_export_all]},
                  {ct_opts,[{ct_hooks,[cth_surefire]}]},
                  {src_dirs,["src","test/support"]}]}]}.
{shell,[{apps,[telemetry_poller]}]}.
{ex_doc,[{main,"README"},
         {extras,[<<"README.md">>,<<"CHANGELOG.md">>,<<"LICENSE">>,
                  <<"NOTICE">>]},
         {source_url,<<"https://github.com/beam-telemetry/telemetry_poller">>},
         {source_ref,<<"v1.3.0">>}]}.
{hex,[{doc,#{provider => ex_doc}}]}.
{xref_checks,[undefined_function_calls,undefined_functions,locals_not_used,
              deprecated_function_calls,deprecated_functions]}.
{overrides,[]}.

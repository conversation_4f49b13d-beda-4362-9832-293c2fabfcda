// 工作流画布交互处理
const WorkflowCanvas = {
  mounted() {
    this.initCanvas();
    this.setupEventListeners();
  },

  initCanvas() {
    this.canvas = this.el;
    this.isDragging = false;
    this.isConnecting = false;
    this.draggedNode = null;
    this.connectionStart = null;
    this.connectionLine = null;
    this.lastMousePos = { x: 0, y: 0 };
    
    // 创建临时连接线
    this.createTempConnectionLine();
  },

  setupEventListeners() {
    // 鼠标事件
    this.canvas.addEventListener('mousedown', this.handleMouseDown.bind(this));
    document.addEventListener('mousemove', this.handleMouseMove.bind(this));
    document.addEventListener('mouseup', this.handleMouseUp.bind(this));
    
    // 缩放事件
    this.canvas.addEventListener('wheel', this.handleWheel.bind(this));
    
    // 节点拖拽
    this.setupNodeDragging();
    
    // 连接点事件
    this.setupConnectionPoints();
  },

  handleMouseDown(e) {
    this.lastMousePos = { x: e.clientX, y: e.clientY };
    
    // 检查是否点击在空白区域（用于画布拖拽）
    if (e.target === this.canvas || e.target.classList.contains('canvas')) {
      this.isDragging = true;
      this.canvas.style.cursor = 'grabbing';
    }
  },

  handleMouseMove(e) {
    const deltaX = e.clientX - this.lastMousePos.x;
    const deltaY = e.clientY - this.lastMousePos.y;
    
    if (this.isDragging) {
      // 画布拖拽
      this.pushEvent('canvas_pan', { dx: deltaX, dy: deltaY });
    } else if (this.isConnecting && this.connectionLine) {
      // 更新连接线
      const rect = this.canvas.getBoundingClientRect();
      const canvasX = e.clientX - rect.left;
      const canvasY = e.clientY - rect.top;
      
      this.updateConnectionLine(canvasX, canvasY);
    }
    
    this.lastMousePos = { x: e.clientX, y: e.clientY };
  },

  handleMouseUp(e) {
    if (this.isDragging) {
      this.isDragging = false;
      this.canvas.style.cursor = 'default';
    }
    
    if (this.isConnecting) {
      this.finishConnection(e);
    }
  },

  handleWheel(e) {
    e.preventDefault();
    const delta = e.deltaY > 0 ? -1 : 1;
    this.pushEvent('canvas_zoom', { delta: delta });
  },

  setupNodeDragging() {
    // 为所有节点设置拖拽
    const observer = new MutationObserver(() => {
      this.attachNodeDragListeners();
    });
    
    observer.observe(this.canvas, { childList: true, subtree: true });
    this.attachNodeDragListeners();
  },

  attachNodeDragListeners() {
    const nodes = this.canvas.querySelectorAll('.workflow-node');
    
    nodes.forEach(node => {
      if (node.dataset.dragListenerAttached) return;
      
      node.dataset.dragListenerAttached = 'true';
      let isDraggingNode = false;
      let startPos = { x: 0, y: 0 };
      let initialNodePos = { x: 0, y: 0 };
      
      const onMouseDown = (e) => {
        if (e.target.closest('.connection-point') || 
            e.target.closest('.node-menu')) {
          return;
        }
        
        e.stopPropagation();
        isDraggingNode = true;
        startPos = { x: e.clientX, y: e.clientY };
        
        // 获取节点当前位置
        const style = window.getComputedStyle(node);
        initialNodePos = {
          x: parseInt(style.left) || 0,
          y: parseInt(style.top) || 0
        };
        
        node.style.cursor = 'grabbing';
        node.style.zIndex = '1000';
      };
      
      const onMouseMove = (e) => {
        if (!isDraggingNode) return;
        
        const deltaX = e.clientX - startPos.x;
        const deltaY = e.clientY - startPos.y;
        
        const newX = initialNodePos.x + deltaX;
        const newY = initialNodePos.y + deltaY;
        
        node.style.left = `${newX}px`;
        node.style.top = `${newY}px`;
      };
      
      const onMouseUp = (e) => {
        if (!isDraggingNode) return;
        
        isDraggingNode = false;
        node.style.cursor = 'move';
        node.style.zIndex = '';
        
        const deltaX = e.clientX - startPos.x;
        const deltaY = e.clientY - startPos.y;
        
        const newX = initialNodePos.x + deltaX;
        const newY = initialNodePos.y + deltaY;
        
        // 发送位置更新事件
        const nodeId = node.dataset.nodeId;
        this.pushEvent('update_node_position', {
          node_id: nodeId,
          x: newX,
          y: newY
        });
      };
      
      node.addEventListener('mousedown', onMouseDown);
      document.addEventListener('mousemove', onMouseMove);
      document.addEventListener('mouseup', onMouseUp);
    });
  },

  setupConnectionPoints() {
    // 为连接点设置事件监听
    const observer = new MutationObserver(() => {
      this.attachConnectionListeners();
    });
    
    observer.observe(this.canvas, { childList: true, subtree: true });
    this.attachConnectionListeners();
  },

  attachConnectionListeners() {
    const connectionPoints = this.canvas.querySelectorAll('.connection-point');
    
    connectionPoints.forEach(point => {
      if (point.dataset.connectionListenerAttached) return;
      
      point.dataset.connectionListenerAttached = 'true';
      
      point.addEventListener('mousedown', (e) => {
        e.stopPropagation();
        this.startConnection(point, e);
      });
      
      point.addEventListener('mouseup', (e) => {
        e.stopPropagation();
        this.endConnection(point, e);
      });
    });
  },

  startConnection(point, e) {
    if (!point.classList.contains('output')) return;
    
    this.isConnecting = true;
    this.connectionStart = {
      point: point,
      node: point.closest('.workflow-node'),
      index: parseInt(point.dataset.outputIndex) || 0
    };
    
    const rect = point.getBoundingClientRect();
    const canvasRect = this.canvas.getBoundingClientRect();
    
    this.connectionStart.pos = {
      x: rect.left + rect.width / 2 - canvasRect.left,
      y: rect.top + rect.height / 2 - canvasRect.top
    };
    
    this.showConnectionLine();
  },

  endConnection(point, e) {
    if (!this.isConnecting || !point.classList.contains('input')) {
      this.cancelConnection();
      return;
    }
    
    const targetNode = point.closest('.workflow-node');
    const sourceNode = this.connectionStart.node;
    
    if (targetNode === sourceNode) {
      this.cancelConnection();
      return;
    }
    
    // 创建连接
    const sourceId = sourceNode.dataset.nodeId;
    const targetId = targetNode.dataset.nodeId;
    const outputIndex = this.connectionStart.index;
    const inputIndex = parseInt(point.dataset.inputIndex) || 0;
    
    this.pushEvent('connect_nodes', {
      source: sourceId,
      target: targetId,
      output: outputIndex,
      input: inputIndex
    });
    
    this.cancelConnection();
  },

  finishConnection(e) {
    // 检查是否结束在输入连接点上
    const elementUnderMouse = document.elementFromPoint(e.clientX, e.clientY);
    const inputPoint = elementUnderMouse?.closest('.connection-point.input');
    
    if (inputPoint) {
      this.endConnection(inputPoint, e);
    } else {
      this.cancelConnection();
    }
  },

  cancelConnection() {
    this.isConnecting = false;
    this.connectionStart = null;
    this.hideConnectionLine();
  },

  createTempConnectionLine() {
    const svg = this.canvas.querySelector('.connections-layer');
    if (!svg) return;
    
    this.connectionLine = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    this.connectionLine.setAttribute('stroke', '#007bff');
    this.connectionLine.setAttribute('stroke-width', '2');
    this.connectionLine.setAttribute('fill', 'none');
    this.connectionLine.setAttribute('stroke-dasharray', '5,5');
    this.connectionLine.style.display = 'none';
    
    svg.appendChild(this.connectionLine);
  },

  showConnectionLine() {
    if (this.connectionLine) {
      this.connectionLine.style.display = 'block';
    }
  },

  hideConnectionLine() {
    if (this.connectionLine) {
      this.connectionLine.style.display = 'none';
    }
  },

  updateConnectionLine(endX, endY) {
    if (!this.connectionLine || !this.connectionStart) return;
    
    const startX = this.connectionStart.pos.x;
    const startY = this.connectionStart.pos.y;
    
    // 创建贝塞尔曲线路径
    const controlPointOffset = Math.abs(endX - startX) * 0.5;
    const cp1X = startX + controlPointOffset;
    const cp1Y = startY;
    const cp2X = endX - controlPointOffset;
    const cp2Y = endY;
    
    const pathData = `M ${startX} ${startY} C ${cp1X} ${cp1Y}, ${cp2X} ${cp2Y}, ${endX} ${endY}`;
    this.connectionLine.setAttribute('d', pathData);
  },

  // 处理从LiveView发来的更新
  updated() {
    // 当组件更新时重新绑定事件监听器
    this.attachNodeDragListeners();
    this.attachConnectionListeners();
  }
};

export default WorkflowCanvas;
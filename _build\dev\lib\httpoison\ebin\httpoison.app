{application,httpoison,
             [{modules,['Elixir.HTTPoison','Elixir.HTTPoison.AsyncChunk',
                        'Elixir.HTTPoison.AsyncEnd',
                        'Elixir.HTTPoison.AsyncHeaders',
                        'Elixir.HTTPoison.AsyncRedirect',
                        'Elixir.HTTPoison.AsyncResponse',
                        'Elixir.HTTPoison.AsyncStatus',
                        'Elixir.HTTPoison.Base','Elixir.HTTPoison.Error',
                        'Elixir.HTTPoison.Handlers.Multipart',
                        'Elixir.HTTPoison.MaybeRedirect',
                        'Elixir.HTTPoison.Request',
                        'Elixir.HTTPoison.Response']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,hackney]},
              {description,"Yet Another HTTP client for Elixir powered by hackney"},
              {registered,[]},
              {vsn,"2.2.3"}]}.

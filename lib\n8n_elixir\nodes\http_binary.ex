defmodule N8nElixir.Nodes.HttpBinary do
  @moduledoc """
  HTTP二进制数据节点

  支持上传和下载二进制文件的HTTP请求节点
  类似n8n的HTTP Request节点的二进制数据处理功能
  """

  @behaviour N8nElixir.NodeBehaviour

  @impl true
  def execute(input_data, _credentials, parameters) do
    request_method = Map.get(parameters, "requestMethod", "GET") |> String.upcase()
    url = Map.get(parameters, "url", "")
    response_format = Map.get(parameters, "responseFormat", "json")
    
    if String.trim(url) == "" do
      {:error, "URL is required"}
    else
      results = Enum.map(input_data, fn item ->
        execute_http_request(request_method, url, item, parameters, response_format)
      end)
      
      {:ok, results}
    end
  end

  @impl true
  def get_properties() do
    %{
      displayName: "HTTP Binary",
      name: "httpBinary",
      icon: "fa:cloud-download-alt",
      group: ["input"],
      version: 1,
      description: "Makes HTTP requests and handles binary data",
      defaults: %{
        name: "HTTP Binary"
      },
      inputs: ["main"],
      outputs: ["main"],
      parameters: [
        %{
          displayName: "Request Method",
          name: "requestMethod",
          type: "options",
          default: "GET",
          options: [
            %{name: "GET", value: "GET"},
            %{name: "POST", value: "POST"},
            %{name: "PUT", value: "PUT"},
            %{name: "DELETE", value: "DELETE"}
          ]
        },
        %{
          displayName: "URL",
          name: "url",
          type: "string",
          default: "",
          placeholder: "https://example.com/api/file",
          description: "The URL to make the request to"
        },
        %{
          displayName: "Response Format",
          name: "responseFormat",
          type: "options",
          default: "json",
          options: [
            %{name: "JSON", value: "json"},
            %{name: "Binary", value: "binary"},
            %{name: "Text", value: "text"}
          ]
        },
        %{
          displayName: "Send Binary Data",
          name: "sendBinaryData",
          type: "boolean",
          default: false,
          displayOptions: %{
            show: %{
              requestMethod: ["POST", "PUT"]
            }
          }
        },
        %{
          displayName: "Binary Data Field",
          name: "binaryDataField",
          type: "string",
          default: "data",
          displayOptions: %{
            show: %{
              sendBinaryData: [true]
            }
          }
        }
      ]
    }
  end

  defp execute_http_request(method, url, item, parameters, response_format) do
    headers = build_headers(parameters)
    
    case method do
      "GET" ->
        execute_get_request(url, headers, response_format)
      
      "POST" ->
        execute_post_request(url, headers, item, parameters, response_format)
      
      "PUT" ->
        execute_put_request(url, headers, item, parameters, response_format)
      
      "DELETE" ->
        execute_delete_request(url, headers, response_format)
      
      _ ->
        create_error_response("Unsupported HTTP method: #{method}")
    end
  end

  defp execute_get_request(url, headers, response_format) do
    case HTTPoison.get(url, headers, timeout: 30_000, recv_timeout: 30_000) do
      {:ok, %HTTPoison.Response{status_code: status_code, body: body, headers: response_headers}} ->
        process_response(status_code, body, response_headers, response_format)
      
      {:error, %HTTPoison.Error{reason: reason}} ->
        create_error_response("HTTP request failed: #{reason}")
    end
  end

  defp execute_post_request(url, headers, item, parameters, response_format) do
    {body, content_headers} = prepare_request_body(item, parameters)
    final_headers = Keyword.merge(headers, content_headers)
    
    case HTTPoison.post(url, body, final_headers, timeout: 30_000, recv_timeout: 30_000) do
      {:ok, %HTTPoison.Response{status_code: status_code, body: response_body, headers: response_headers}} ->
        process_response(status_code, response_body, response_headers, response_format)
      
      {:error, %HTTPoison.Error{reason: reason}} ->
        create_error_response("HTTP request failed: #{reason}")
    end
  end

  defp execute_put_request(url, headers, item, parameters, response_format) do
    {body, content_headers} = prepare_request_body(item, parameters)
    final_headers = Keyword.merge(headers, content_headers)
    
    case HTTPoison.put(url, body, final_headers, timeout: 30_000, recv_timeout: 30_000) do
      {:ok, %HTTPoison.Response{status_code: status_code, body: response_body, headers: response_headers}} ->
        process_response(status_code, response_body, response_headers, response_format)
      
      {:error, %HTTPoison.Error{reason: reason}} ->
        create_error_response("HTTP request failed: #{reason}")
    end
  end

  defp execute_delete_request(url, headers, response_format) do
    case HTTPoison.delete(url, headers, timeout: 30_000, recv_timeout: 30_000) do
      {:ok, %HTTPoison.Response{status_code: status_code, body: body, headers: response_headers}} ->
        process_response(status_code, body, response_headers, response_format)
      
      {:error, %HTTPoison.Error{reason: reason}} ->
        create_error_response("HTTP request failed: #{reason}")
    end
  end

  defp prepare_request_body(item, parameters) do
    send_binary = Map.get(parameters, "sendBinaryData", false)
    
    if send_binary do
      prepare_binary_body(item, parameters)
    else
      prepare_json_body(item)
    end
  end

  defp prepare_binary_body(item, parameters) do
    binary_field = Map.get(parameters, "binaryDataField", "data")
    
    case extract_binary_data_from_item(item, binary_field) do
      {:ok, binary_data, mime_type, filename} ->
        # 创建multipart表单数据
        multipart_data = create_multipart_form(binary_data, filename, mime_type)
        content_type = "multipart/form-data; boundary=#{multipart_data.boundary}"
        
        {multipart_data.body, [{"Content-Type", content_type}]}
      
      {:error, _reason} ->
        # 回退到JSON
        prepare_json_body(item)
    end
  end

  defp prepare_json_body(item) do
    json_data = Map.get(item, "json", %{})
    body = Jason.encode!(json_data)
    
    {body, [{"Content-Type", "application/json"}]}
  end

  defp extract_binary_data_from_item(item, field) do
    case get_in(item, ["binary", field, "data"]) do
      nil ->
        {:error, "No binary data found"}
      
      binary_data_id ->
        case N8nElixir.BinaryData.get_binary_data(binary_data_id) do
          {:ok, data, info} ->
            {:ok, data, info.mime_type, info.file_name}
          
          {:error, reason} ->
            {:error, reason}
        end
    end
  end

  defp create_multipart_form(binary_data, filename, mime_type) do
    boundary = "----formdata-n8n-#{:crypto.strong_rand_bytes(16) |> Base.encode16()}"
    
    form_data = [
      "--#{boundary}\r\n",
      "Content-Disposition: form-data; name=\"file\"; filename=\"#{filename}\"\r\n",
      "Content-Type: #{mime_type}\r\n",
      "\r\n",
      binary_data,
      "\r\n--#{boundary}--\r\n"
    ]
    
    %{
      boundary: boundary,
      body: IO.iodata_to_binary(form_data)
    }
  end

  defp process_response(status_code, body, headers, response_format) do
    case response_format do
      "binary" ->
        process_binary_response(status_code, body, headers)
      
      "json" ->
        process_json_response(status_code, body, headers)
      
      "text" ->
        process_text_response(status_code, body, headers)
      
      _ ->
        process_auto_response(status_code, body, headers)
    end
  end

  defp process_binary_response(status_code, body, headers) do
    content_type = get_content_type(headers)
    filename = get_filename_from_headers(headers) || "download.bin"
    
    case N8nElixir.BinaryData.store_binary_data(body, filename) do
      {:ok, binary_data_id} ->
        %{
          "json" => %{
            "statusCode" => status_code,
            "headers" => headers_to_map(headers),
            "fileName" => filename,
            "fileSize" => byte_size(body),
            "mimeType" => content_type
          },
          "binary" => %{
            "data" => %{
              "data" => binary_data_id,
              "mimeType" => content_type,
              "fileName" => filename,
              "fileSize" => byte_size(body)
            }
          }
        }
      
      {:error, reason} ->
        create_error_response("Failed to store binary response: #{reason}")
    end
  end

  defp process_json_response(status_code, body, headers) do
    case Jason.decode(body) do
      {:ok, json_data} ->
        %{
          "json" => %{
            "statusCode" => status_code,
            "headers" => headers_to_map(headers),
            "data" => json_data
          }
        }
      
      {:error, _reason} ->
        %{
          "json" => %{
            "statusCode" => status_code,
            "headers" => headers_to_map(headers),
            "body" => body,
            "parseError" => "Failed to parse JSON response"
          }
        }
    end
  end

  defp process_text_response(status_code, body, headers) do
    %{
      "json" => %{
        "statusCode" => status_code,
        "headers" => headers_to_map(headers),
        "body" => body
      }
    }
  end

  defp process_auto_response(status_code, body, headers) do
    content_type = get_content_type(headers)
    
    cond do
      String.contains?(content_type, "json") ->
        process_json_response(status_code, body, headers)
      
      String.starts_with?(content_type, "text/") ->
        process_text_response(status_code, body, headers)
      
      is_binary_content_type?(content_type) ->
        process_binary_response(status_code, body, headers)
      
      true ->
        process_text_response(status_code, body, headers)
    end
  end

  defp build_headers(parameters) do
    base_headers = [
      {"User-Agent", "n8n-elixir/1.0"}
    ]
    
    # 可以根据参数添加自定义头部
    custom_headers = Map.get(parameters, "headers", %{})
    
    custom_header_list = Enum.map(custom_headers, fn {key, value} ->
      {to_string(key), to_string(value)}
    end)
    
    base_headers ++ custom_header_list
  end

  defp get_content_type(headers) do
    headers
    |> Enum.find_value("application/octet-stream", fn
      {"content-type", value} -> value
      {"Content-Type", value} -> value
      _ -> nil
    end)
    |> String.split(";")
    |> hd()
    |> String.trim()
  end

  defp get_filename_from_headers(headers) do
    headers
    |> Enum.find_value(nil, fn
      {"content-disposition", value} -> extract_filename_from_disposition(value)
      {"Content-Disposition", value} -> extract_filename_from_disposition(value)
      _ -> nil
    end)
  end

  defp extract_filename_from_disposition(disposition) do
    case Regex.run(~r/filename[*]?=['"]?([^'";\s]+)['"]?/, disposition) do
      [_, filename] -> filename
      _ -> nil
    end
  end

  defp headers_to_map(headers) do
    Enum.into(headers, %{})
  end

  defp is_binary_content_type?(content_type) do
    binary_types = [
      "image/", "video/", "audio/", "application/pdf",
      "application/zip", "application/octet-stream"
    ]
    
    Enum.any?(binary_types, &String.starts_with?(content_type, &1))
  end

  defp create_error_response(error_message) do
    %{
      "json" => %{
        "error" => error_message,
        "success" => false
      }
    }
  end
end
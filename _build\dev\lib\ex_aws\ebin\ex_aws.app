{application,ex_aws,
             [{modules,['Elixir.ExAws','Elixir.ExAws.Auth',
                        'Elixir.ExAws.Auth.Credentials',
                        'Elixir.ExAws.Auth.Signatures',
                        'Elixir.ExAws.Auth.Utils','Elixir.ExAws.Behaviour',
                        'Elixir.ExAws.Config','Elixir.ExAws.Config.AuthCache',
                        'Elixir.ExAws.Config.AuthCache.AuthConfigAdapter',
                        'Elixir.ExAws.Config.Defaults',
                        'Elixir.ExAws.CredentialsIni.File',
                        'Elixir.ExAws.CredentialsIni.Provider',
                        'Elixir.ExAws.Error','Elixir.ExAws.InstanceMeta',
                        'Elixir.ExAws.InstanceMetaTokenProvider',
                        'Elixir.ExAws.JSON.Codec','Elixir.ExAws.JSON.JSX',
                        'Elixir.ExAws.Operation',
                        'Elixir.ExAws.Operation.ExAws.Operation.JSON',
                        'Elixir.ExAws.Operation.ExAws.Operation.Query',
                        'Elixir.ExAws.Operation.ExAws.Operation.RestQuery',
                        'Elixir.ExAws.Operation.ExAws.Operation.S3',
                        'Elixir.ExAws.Operation.JSON',
                        'Elixir.ExAws.Operation.Query',
                        'Elixir.ExAws.Operation.Query.Parser',
                        'Elixir.ExAws.Operation.RestQuery',
                        'Elixir.ExAws.Operation.S3','Elixir.ExAws.Request',
                        'Elixir.ExAws.Request.Hackney',
                        'Elixir.ExAws.Request.HttpClient',
                        'Elixir.ExAws.Request.Req','Elixir.ExAws.Request.Url',
                        'Elixir.ExAws.Utils']},
              {optional_applications,[configparser_ex,hackney,req,jason,jsx,
                                      sweet_xml]},
              {applications,[kernel,stdlib,elixir,logger,crypto,telemetry,
                             mime,configparser_ex,hackney,req,jason,jsx,
                             sweet_xml]},
              {description,"Generic AWS client"},
              {registered,[]},
              {vsn,"2.5.10"},
              {mod,{'Elixir.ExAws',[]}}]}.

{application,ex_json_schema,
             [{modules,['Elixir.ExJsonSchema','Elixir.ExJsonSchema.Schema',
                        'Elixir.ExJsonSchema.Schema.Draft4',
                        'Elixir.ExJsonSchema.Schema.Draft6',
                        'Elixir.ExJsonSchema.Schema.Draft7',
                        'Elixir.ExJsonSchema.Schema.InvalidReferenceError',
                        'Elixir.ExJsonSchema.Schema.InvalidSchemaError',
                        'Elixir.ExJsonSchema.Schema.MissingJsonDecoderError',
                        'Elixir.ExJsonSchema.Schema.Ref',
                        'Elixir.ExJsonSchema.Schema.Root',
                        'Elixir.ExJsonSchema.Schema.UndefinedRemoteSchemaResolverError',
                        'Elixir.ExJsonSchema.Schema.UnsupportedSchemaVersionError',
                        'Elixir.ExJsonSchema.Validator',
                        'Elixir.ExJsonSchema.Validator.AllOf',
                        'Elixir.ExJsonSchema.Validator.AnyOf',
                        'Elixir.ExJsonSchema.Validator.Const',
                        'Elixir.ExJsonSchema.Validator.Contains',
                        'Elixir.ExJsonSchema.Validator.ContentEncodingContentMediaType',
                        'Elixir.ExJsonSchema.Validator.Dependencies',
                        'Elixir.ExJsonSchema.Validator.Enum',
                        'Elixir.ExJsonSchema.Validator.Error',
                        'Elixir.ExJsonSchema.Validator.Error.AdditionalItems',
                        'Elixir.ExJsonSchema.Validator.Error.AdditionalProperties',
                        'Elixir.ExJsonSchema.Validator.Error.AllOf',
                        'Elixir.ExJsonSchema.Validator.Error.AnyOf',
                        'Elixir.ExJsonSchema.Validator.Error.Const',
                        'Elixir.ExJsonSchema.Validator.Error.Contains',
                        'Elixir.ExJsonSchema.Validator.Error.ContentEncoding',
                        'Elixir.ExJsonSchema.Validator.Error.ContentMediaType',
                        'Elixir.ExJsonSchema.Validator.Error.Dependencies',
                        'Elixir.ExJsonSchema.Validator.Error.Enum',
                        'Elixir.ExJsonSchema.Validator.Error.False',
                        'Elixir.ExJsonSchema.Validator.Error.Format',
                        'Elixir.ExJsonSchema.Validator.Error.IfThenElse',
                        'Elixir.ExJsonSchema.Validator.Error.InvalidAtIndex',
                        'Elixir.ExJsonSchema.Validator.Error.ItemsNotAllowed',
                        'Elixir.ExJsonSchema.Validator.Error.MaxItems',
                        'Elixir.ExJsonSchema.Validator.Error.MaxLength',
                        'Elixir.ExJsonSchema.Validator.Error.MaxProperties',
                        'Elixir.ExJsonSchema.Validator.Error.Maximum',
                        'Elixir.ExJsonSchema.Validator.Error.MinItems',
                        'Elixir.ExJsonSchema.Validator.Error.MinLength',
                        'Elixir.ExJsonSchema.Validator.Error.MinProperties',
                        'Elixir.ExJsonSchema.Validator.Error.Minimum',
                        'Elixir.ExJsonSchema.Validator.Error.MultipleOf',
                        'Elixir.ExJsonSchema.Validator.Error.Not',
                        'Elixir.ExJsonSchema.Validator.Error.OneOf',
                        'Elixir.ExJsonSchema.Validator.Error.Pattern',
                        'Elixir.ExJsonSchema.Validator.Error.PropertyNames',
                        'Elixir.ExJsonSchema.Validator.Error.Required',
                        'Elixir.ExJsonSchema.Validator.Error.StringFormatter',
                        'Elixir.ExJsonSchema.Validator.Error.Type',
                        'Elixir.ExJsonSchema.Validator.Error.UniqueItems',
                        'Elixir.ExJsonSchema.Validator.ExclusiveMaximum',
                        'Elixir.ExJsonSchema.Validator.ExclusiveMinimum',
                        'Elixir.ExJsonSchema.Validator.Format',
                        'Elixir.ExJsonSchema.Validator.IfThenElse',
                        'Elixir.ExJsonSchema.Validator.Items',
                        'Elixir.ExJsonSchema.Validator.MaxItems',
                        'Elixir.ExJsonSchema.Validator.MaxLength',
                        'Elixir.ExJsonSchema.Validator.MaxProperties',
                        'Elixir.ExJsonSchema.Validator.Maximum',
                        'Elixir.ExJsonSchema.Validator.MinItems',
                        'Elixir.ExJsonSchema.Validator.MinLength',
                        'Elixir.ExJsonSchema.Validator.MinProperties',
                        'Elixir.ExJsonSchema.Validator.Minimum',
                        'Elixir.ExJsonSchema.Validator.MultipleOf',
                        'Elixir.ExJsonSchema.Validator.Not',
                        'Elixir.ExJsonSchema.Validator.OneOf',
                        'Elixir.ExJsonSchema.Validator.Pattern',
                        'Elixir.ExJsonSchema.Validator.Properties',
                        'Elixir.ExJsonSchema.Validator.PropertyNames',
                        'Elixir.ExJsonSchema.Validator.Ref',
                        'Elixir.ExJsonSchema.Validator.Required',
                        'Elixir.ExJsonSchema.Validator.Type',
                        'Elixir.ExJsonSchema.Validator.UniqueItems',
                        'Elixir.String.Chars.ExJsonSchema.Schema.Ref',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.AdditionalItems',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.AdditionalProperties',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.AllOf',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.AnyOf',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.Const',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.Contains',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.ContentEncoding',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.ContentMediaType',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.Dependencies',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.Enum',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.False',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.Format',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.IfThenElse',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.ItemsNotAllowed',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.MaxItems',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.MaxLength',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.MaxProperties',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.Maximum',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.MinItems',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.MinLength',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.MinProperties',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.Minimum',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.MultipleOf',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.Not',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.OneOf',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.Pattern',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.PropertyNames',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.Required',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.Type',
                        'Elixir.String.Chars.ExJsonSchema.Validator.Error.UniqueItems']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,decimal]},
              {description,"  A JSON Schema validator with full support for the draft 4 specification\n  and zero dependencies.\n"},
              {registered,[]},
              {vsn,"0.11.1"}]}.

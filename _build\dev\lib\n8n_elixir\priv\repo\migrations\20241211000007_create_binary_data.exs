defmodule N8nElixir.Repo.Migrations.CreateBinaryData do
  use Ecto.Migration

  def change do
    create table(:binary_data, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :data, :binary, null: false
      add :mime_type, :string, null: false
      add :file_type, :string, default: "other"
      add :file_name, :string
      add :file_extension, :string
      add :file_size, :integer
      add :directory, :string
      add :checksum, :string
      add :execution_id, references(:executions, type: :binary_id)
      add :node_name, :string
      
      timestamps()
    end

    create index(:binary_data, [:execution_id])
    create index(:binary_data, [:node_name])
    create index(:binary_data, [:mime_type])
    create index(:binary_data, [:file_type])
    create index(:binary_data, [:checksum])
  end
end
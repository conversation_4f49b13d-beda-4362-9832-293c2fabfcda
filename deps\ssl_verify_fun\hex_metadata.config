{<<"app">>,<<"ssl_verify_fun">>}.
{<<"build_tools">>,[<<"mix">>,<<"rebar3">>,<<"make">>]}.
{<<"description">>,<<"SSL verification library">>}.
{<<"files">>,
 [<<"src">>,<<"src/ssl_verify_pk.erl">>,<<"src/ssl_verify_util.erl">>,
  <<"src/ssl_verify_string.erl">>,<<"src/ssl_verify_fun_cert_helpers.erl">>,
  <<"src/ssl_verify_hostname.erl">>,<<"src/ssl_verify_fun.app.src">>,
  <<"src/ssl_verify_fun_encodings.erl">>,<<"src/ssl_verify_fingerprint.erl">>,
  <<"README.md">>,<<"LICENSE">>,<<"Makefile">>,<<"rebar.config">>,
  <<"mix.exs">>]}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"links">>,
 [{<<"GitHub">>,<<"https://github.com/deadtrickster/ssl_verify_fun.erl">>}]}.
{<<"name">>,<<"ssl_verify_fun">>}.
{<<"requirements">>,[]}.
{<<"version">>,<<"1.1.7">>}.

defmodule N8nElixir.NodeBehaviour do
  @moduledoc """
  节点行为定义

  简化版的节点行为接口，支持二进制数据处理
  """

  @doc """
  执行节点逻辑
  """
  @callback execute(input_data :: list(map()), credentials :: map(), parameters :: map()) :: 
    {:ok, list(map())} | {:error, String.t()}

  @doc """
  获取节点属性定义
  """
  @callback get_properties() :: map()

  @doc """
  测试节点连接（可选）
  """
  @callback test_connection(credentials :: map(), parameters :: map()) :: 
    :ok | {:error, String.t()}

  @optional_callbacks [test_connection: 2]
end
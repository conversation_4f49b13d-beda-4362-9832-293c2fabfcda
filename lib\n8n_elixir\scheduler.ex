defmodule N8nElixir.Scheduler do
  @moduledoc """
  任务调度器

  处理定时任务和调度
  """

  use GenServer
  require Logger

  @registry_name __MODULE__

  def start_link(init_arg) do
    GenServer.start_link(__MODULE__, init_arg, name: @registry_name)
  end

  @impl true
  def init(_init_arg) do
    Logger.info("Starting Scheduler")
    {:ok, %{}}
  end

  @impl true
  def handle_call(_msg, _from, state) do
    {:reply, :ok, state}
  end

  @impl true
  def handle_cast(_msg, state) do
    {:noreply, state}
  end
end
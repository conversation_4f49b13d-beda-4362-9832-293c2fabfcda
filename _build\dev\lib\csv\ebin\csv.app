{application,csv,
             [{modules,['Elixir.CSV','Elixir.CSV.Decoding.Decoder',
                        'Elixir.CSV.Decoding.Parser','Elixir.CSV.Defaults',
                        'Elixir.CSV.Encode','Elixir.CSV.Encode.Any',
                        'Elixir.CSV.Encode.BitString',
                        'Elixir.CSV.Encoding.Encoder',
                        'Elixir.CSV.EscapeSequenceError',
                        'Elixir.CSV.RowLengthError',
                        'Elixir.CSV.StrayEscapeCharacterError']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir]},
              {description,"CSV Decoding and Encoding for Elixir"},
              {registered,[]},
              {vsn,"3.2.2"}]}.

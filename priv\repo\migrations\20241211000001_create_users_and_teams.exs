defmodule N8nElixir.Repo.Migrations.CreateUsersAndTeams do
  use Ecto.Migration

  def change do
    create table(:teams, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :name, :string, null: false
      add :description, :text
      add :status, :string, default: "active"
      
      timestamps()
    end

    create unique_index(:teams, [:name])

    create table(:users, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :email, :string, null: false
      add :password_hash, :string
      add :first_name, :string
      add :last_name, :string
      add :role, :string, default: "user"
      add :status, :string, default: "pending"
      add :last_login_at, :utc_datetime
      add :team_id, references(:teams, type: :binary_id)
      
      timestamps()
    end

    create unique_index(:users, [:email])
    create index(:users, [:team_id])
  end
end
defmodule N8nElixir.BinaryDataManager do
  @moduledoc """
  二进制数据管理器

  提供高级的二进制数据操作接口，集成工作流执行上下文
  管理二进制数据的生命周期和与工作流的关联
  """

  use GenServer
  require Logger

  alias N8nElixir.BinaryData

  @registry_name __MODULE__

  # 客户端API

  def start_link(init_arg) do
    GenServer.start_link(__MODULE__, init_arg, name: @registry_name)
  end

  @doc """
  处理文件上传
  """
  def handle_file_upload(file_data, filename, execution_id, opts \\ []) do
    GenServer.call(@registry_name, {:handle_upload, file_data, filename, execution_id, opts})
  end

  @doc """
  处理文件下载
  """
  def handle_file_download(binary_data_id, opts \\ []) do
    GenServer.call(@registry_name, {:handle_download, binary_data_id, opts})
  end

  @doc """
  处理批量文件操作
  """
  def handle_batch_operation(operation, files, execution_id, opts \\ []) do
    GenServer.call(@registry_name, {:batch_operation, operation, files, execution_id, opts}, 30_000)
  end

  @doc """
  为节点输出准备二进制数据
  """
  def prepare_binary_output(binary_data_ids, execution_id) do
    GenServer.call(@registry_name, {:prepare_output, binary_data_ids, execution_id})
  end

  @doc """
  从节点输入提取二进制数据
  """
  def extract_binary_input(input_data, execution_id) do
    GenServer.call(@registry_name, {:extract_input, input_data, execution_id})
  end

  @doc """
  清理执行相关的二进制数据
  """
  def cleanup_execution_data(execution_id) do
    GenServer.cast(@registry_name, {:cleanup_execution, execution_id})
  end

  @doc """
  获取执行的二进制数据统计
  """
  def get_execution_stats(execution_id) do
    GenServer.call(@registry_name, {:get_stats, execution_id})
  end

  # 服务器回调

  @impl true
  def init(_init_arg) do
    # 初始化二进制数据存储
    BinaryData.init_storage()
    
    # 启动清理定时器
    schedule_cleanup()
    
    state = %{
      execution_binaries: %{},
      upload_progress: %{},
      download_cache: %{}
    }

    {:ok, state}
  end

  @impl true
  def handle_call({:handle_upload, file_data, filename, execution_id, opts}, _from, state) do
    case validate_upload(file_data, filename, opts) do
      :ok ->
        upload_id = generate_upload_id()
        
        # 记录上传进度
        progress = %{
          status: :uploading,
          started_at: DateTime.utc_now(),
          file_size: byte_size(file_data),
          filename: filename
        }
        
        updated_progress = Map.put(state.upload_progress, upload_id, progress)
        
        # 异步处理上传
        Task.start(fn ->
          process_upload(upload_id, file_data, filename, execution_id, opts)
        end)
        
        new_state = %{state | upload_progress: updated_progress}
        {:reply, {:ok, upload_id}, new_state}
      
      {:error, reason} ->
        {:reply, {:error, reason}, state}
    end
  end

  @impl true
  def handle_call({:handle_download, binary_data_id, opts}, _from, state) do
    case get_or_cache_binary_data(binary_data_id, state) do
      {:ok, binary_data, data} ->
        # 准备下载响应
        download_info = %{
          id: binary_data_id,
          filename: binary_data.file_name,
          mime_type: binary_data.mime_type,
          file_size: binary_data.file_size,
          data: data,
          download_url: generate_download_url(binary_data_id, opts)
        }
        
        {:reply, {:ok, download_info}, state}
      
      {:error, reason} ->
        {:reply, {:error, reason}, state}
    end
  end

  @impl true
  def handle_call({:batch_operation, operation, files, execution_id, opts}, _from, state) do
    Logger.info("Processing batch operation: #{operation} for #{length(files)} files")
    
    results = process_batch_operation(operation, files, execution_id, opts)
    
    # 更新执行的二进制数据记录
    updated_binaries = update_execution_binaries(state.execution_binaries, execution_id, results)
    new_state = %{state | execution_binaries: updated_binaries}
    
    {:reply, results, new_state}
  end

  @impl true
  def handle_call({:prepare_output, binary_data_ids, execution_id}, _from, state) do
    output_data = Enum.map(binary_data_ids, fn binary_id ->
      case BinaryData.get_binary_data_info(binary_id) do
        {:ok, info} ->
          %{
            "data" => binary_id,
            "mimeType" => info.mime_type,
            "fileName" => info.file_name,
            "fileSize" => info.file_size,
            "id" => binary_id
          }
        {:error, _reason} ->
          nil
      end
    end)
    |> Enum.reject(&is_nil/1)
    
    # 记录执行关联
    updated_binaries = Map.update(state.execution_binaries, execution_id, 
      binary_data_ids, fn existing -> existing ++ binary_data_ids end)
    
    new_state = %{state | execution_binaries: updated_binaries}
    {:reply, {:ok, output_data}, new_state}
  end

  @impl true
  def handle_call({:extract_input, input_data, execution_id}, _from, state) do
    binary_refs = extract_binary_references(input_data)
    
    binary_data = Enum.map(binary_refs, fn binary_ref ->
      case BinaryData.get_binary_data(binary_ref) do
        {:ok, data, info} ->
          %{
            id: binary_ref,
            data: data,
            info: info
          }
        {:error, _reason} ->
          nil
      end
    end)
    |> Enum.reject(&is_nil/1)
    
    # 记录执行关联
    binary_ids = Enum.map(binary_data, & &1.id)
    updated_binaries = Map.update(state.execution_binaries, execution_id, 
      binary_ids, fn existing -> existing ++ binary_ids end)
    
    new_state = %{state | execution_binaries: updated_binaries}
    {:reply, {:ok, binary_data}, new_state}
  end

  @impl true
  def handle_call({:get_stats, execution_id}, _from, state) do
    binary_ids = Map.get(state.execution_binaries, execution_id, [])
    
    stats = calculate_execution_stats(binary_ids)
    
    {:reply, {:ok, stats}, state}
  end

  @impl true
  def handle_cast({:cleanup_execution, execution_id}, state) do
    binary_ids = Map.get(state.execution_binaries, execution_id, [])
    
    # 异步清理二进制数据
    Task.start(fn ->
      cleanup_binary_data_list(binary_ids, execution_id)
    end)
    
    # 移除执行记录
    updated_binaries = Map.delete(state.execution_binaries, execution_id)
    new_state = %{state | execution_binaries: updated_binaries}
    
    {:noreply, new_state}
  end

  @impl true
  def handle_info({:upload_completed, upload_id, result}, state) do
    case Map.get(state.upload_progress, upload_id) do
      nil ->
        {:noreply, state}
      
      progress ->
        updated_progress = %{progress | 
          status: :completed,
          completed_at: DateTime.utc_now(),
          result: result
        }
        
        new_progress = Map.put(state.upload_progress, upload_id, updated_progress)
        new_state = %{state | upload_progress: new_progress}
        
        # 广播上传完成事件
        Phoenix.PubSub.broadcast(
          N8nElixir.PubSub,
          "binary_uploads",
          {:upload_completed, upload_id, result}
        )
        
        {:noreply, new_state}
    end
  end

  @impl true
  def handle_info({:upload_failed, upload_id, error}, state) do
    case Map.get(state.upload_progress, upload_id) do
      nil ->
        {:noreply, state}
      
      progress ->
        updated_progress = %{progress | 
          status: :failed,
          failed_at: DateTime.utc_now(),
          error: error
        }
        
        new_progress = Map.put(state.upload_progress, upload_id, updated_progress)
        new_state = %{state | upload_progress: new_progress}
        
        # 广播上传失败事件
        Phoenix.PubSub.broadcast(
          N8nElixir.PubSub,
          "binary_uploads",
          {:upload_failed, upload_id, error}
        )
        
        {:noreply, new_state}
    end
  end

  @impl true
  def handle_info(:cleanup_expired_data, state) do
    # 清理过期数据
    Task.start(fn ->
      BinaryData.cleanup_expired_binary_data(24) # 24小时过期
    end)
    
    # 重新调度清理
    schedule_cleanup()
    
    {:noreply, state}
  end

  @impl true
  def handle_info(_msg, state) do
    {:noreply, state}
  end

  # 私有实现函数

  defp validate_upload(file_data, filename, opts) do
    max_size = Keyword.get(opts, :max_size, 50 * 1024 * 1024) # 50MB
    
    cond do
      byte_size(file_data) > max_size ->
        {:error, "File size exceeds maximum allowed size"}
      
      String.trim(filename) == "" ->
        {:error, "Filename cannot be empty"}
      
      not valid_filename?(filename) ->
        {:error, "Invalid filename"}
      
      true ->
        :ok
    end
  end

  defp valid_filename?(filename) do
    # 检查文件名是否包含危险字符
    not String.contains?(filename, ["../", "..\\", "~", "|", "&", ";", "$", "%", "@", "'", "\"", "<", ">", "(", ")", "+", ",", "=", "[", "]", "{", "}"])
  end

  defp process_upload(upload_id, file_data, filename, execution_id, opts) do
    try do
      case BinaryData.store_binary_data(file_data, filename, [execution_id: execution_id] ++ opts) do
        {:ok, binary_data_id} ->
          send(self(), {:upload_completed, upload_id, {:ok, binary_data_id}})
        {:error, reason} ->
          send(self(), {:upload_failed, upload_id, reason})
      end
    rescue
      error ->
        send(self(), {:upload_failed, upload_id, "Upload processing failed: #{inspect(error)}"})
    end
  end

  defp process_batch_operation(operation, files, execution_id, opts) do
    case operation do
      :upload_multiple ->
        process_multiple_uploads(files, execution_id, opts)
      
      :convert_format ->
        target_format = Keyword.get(opts, :target_format, "json")
        process_format_conversions(files, target_format, opts)
      
      :create_thumbnails ->
        process_thumbnail_creation(files, opts)
      
      :compress ->
        process_compression(files, opts)
      
      _ ->
        {:error, "Unknown batch operation: #{operation}"}
    end
  end

  defp process_multiple_uploads(files, execution_id, opts) do
    results = Enum.map(files, fn file ->
      case file do
        %{data: data, filename: filename} ->
          BinaryData.store_binary_data(data, filename, [execution_id: execution_id] ++ opts)
        
        %{path: path, filename: filename} ->
          BinaryData.store_binary_data_from_file(path, [execution_id: execution_id] ++ opts)
        
        _ ->
          {:error, "Invalid file format"}
      end
    end)
    
    successes = Enum.count(results, fn result -> match?({:ok, _}, result) end)
    failures = length(results) - successes
    
    {:ok, %{
      total: length(results),
      successes: successes,
      failures: failures,
      results: results
    }}
  end

  defp process_format_conversions(files, target_format, opts) do
    results = Enum.map(files, fn binary_id ->
      BinaryData.convert_binary_data(binary_id, target_format, opts)
    end)
    
    {:ok, %{results: results}}
  end

  defp process_thumbnail_creation(files, opts) do
    results = Enum.map(files, fn binary_id ->
      BinaryData.create_thumbnail(binary_id, opts)
    end)
    
    {:ok, %{results: results}}
  end

  defp process_compression(files, opts) do
    # 创建ZIP压缩包
    case create_zip_archive(files, opts) do
      {:ok, zip_binary_id} ->
        {:ok, %{archive_id: zip_binary_id}}
      {:error, reason} ->
        {:error, reason}
    end
  end

  defp create_zip_archive(binary_ids, opts) do
    try do
      # 收集所有文件数据
      files_data = Enum.reduce(binary_ids, [], fn binary_id, acc ->
        case BinaryData.get_binary_data(binary_id) do
          {:ok, data, info} ->
            [{info.file_name, data} | acc]
          {:error, _reason} ->
            acc
        end
      end)
      
      # 创建ZIP文件
      zip_data = :zip.create("archive.zip", files_data, [:memory])
      
      case zip_data do
        {:ok, {"archive.zip", zip_binary}} ->
          # 存储ZIP文件
          execution_id = Keyword.get(opts, :execution_id)
          BinaryData.store_binary_data(zip_binary, "archive.zip", 
            execution_id: execution_id,
            created_by: "compression"
          )
        
        {:error, reason} ->
          {:error, "ZIP creation failed: #{reason}"}
      end
    rescue
      error ->
        {:error, "Compression failed: #{inspect(error)}"}
    end
  end

  defp get_or_cache_binary_data(binary_data_id, state) do
    case Map.get(state.download_cache, binary_data_id) do
      nil ->
        # 从存储加载
        case BinaryData.get_binary_data(binary_data_id) do
          {:ok, data, info} ->
            {:ok, info, data}
          {:error, reason} ->
            {:error, reason}
        end
      
      cached_data ->
        {:ok, cached_data.info, cached_data.data}
    end
  end

  defp extract_binary_references(input_data) when is_list(input_data) do
    Enum.flat_map(input_data, &extract_binary_references/1)
  end

  defp extract_binary_references(input_data) when is_map(input_data) do
    binary_refs = case Map.get(input_data, "binary") do
      nil -> []
      binary_map when is_map(binary_map) ->
        Map.values(binary_map)
        |> Enum.map(fn binary_info ->
          Map.get(binary_info, "data")
        end)
        |> Enum.reject(&is_nil/1)
    end
    
    # 递归检查嵌套的map
    nested_refs = input_data
    |> Map.values()
    |> Enum.flat_map(fn value ->
      case value do
        nested when is_map(nested) -> extract_binary_references(nested)
        nested when is_list(nested) -> extract_binary_references(nested)
        _ -> []
      end
    end)
    
    binary_refs ++ nested_refs
  end

  defp extract_binary_references(_input_data), do: []

  defp calculate_execution_stats(binary_ids) do
    stats = Enum.reduce(binary_ids, %{count: 0, total_size: 0, types: %{}}, fn binary_id, acc ->
      case BinaryData.get_binary_data_info(binary_id) do
        {:ok, info} ->
          mime_category = String.split(info.mime_type, "/") |> hd()
          
          %{acc |
            count: acc.count + 1,
            total_size: acc.total_size + info.file_size,
            types: Map.update(acc.types, mime_category, 1, &(&1 + 1))
          }
        {:error, _reason} ->
          acc
      end
    end)
    
    %{
      file_count: stats.count,
      total_size: stats.total_size,
      average_size: if(stats.count > 0, do: div(stats.total_size, stats.count), else: 0),
      file_types: stats.types
    }
  end

  defp cleanup_binary_data_list(binary_ids, execution_id) do
    Logger.info("Cleaning up #{length(binary_ids)} binary data files for execution #{execution_id}")
    
    Enum.each(binary_ids, fn binary_id ->
      case BinaryData.delete_binary_data(binary_id) do
        :ok ->
          Logger.debug("Deleted binary data: #{binary_id}")
        {:error, reason} ->
          Logger.warn("Failed to delete binary data #{binary_id}: #{reason}")
      end
    end)
  end

  defp update_execution_binaries(execution_binaries, execution_id, results) do
    case results do
      {:ok, %{results: result_list}} ->
        new_binary_ids = result_list
        |> Enum.filter(fn result -> match?({:ok, _}, result) end)
        |> Enum.map(fn {:ok, binary_id} -> binary_id end)
        
        Map.update(execution_binaries, execution_id, new_binary_ids, fn existing ->
          existing ++ new_binary_ids
        end)
      
      _ ->
        execution_binaries
    end
  end

  defp generate_upload_id() do
    "upload_" <> UUID.uuid4()
  end

  defp generate_download_url(binary_data_id, opts) do
    base_url = Keyword.get(opts, :base_url, "")
    "#{base_url}/api/binary-data/#{binary_data_id}/download"
  end

  defp schedule_cleanup() do
    # 每小时清理一次过期数据
    Process.send_after(self(), :cleanup_expired_data, 60 * 60 * 1000)
  end
end
defmodule N8nElixirWeb.Router do
  use N8nElixirWeb, :router

  pipeline :browser do
    plug :accepts, ["html"]
    plug :fetch_session
    plug :fetch_live_flash
    plug :put_root_layout, html: {N8nElixirWeb.Layouts, :root}
    plug :protect_from_forgery
    plug :put_secure_browser_headers
  end

  pipeline :api do
    plug :accepts, ["json"]
  end

  scope "/", N8nElixirWeb do
    pipe_through :browser

    get "/", PageController, :home
    
    # 工作流路由
    live "/workflows", WorkflowIndexLive, :index
    live "/workflows/new", WorkflowLive, :new
    live "/workflows/:id", WorkflowShowLive, :show
    live "/workflows/:id/edit", WorkflowLive, :edit
    live "/workflows/:id/executions", ExecutionIndexLive, :index
    live "/workflows/:id/executions/:execution_id", ExecutionShowLive, :show
    
    # 凭证管理路由
    live "/credentials", CredentialIndexLive, :index
    live "/credentials/new", CredentialLive, :new
    live "/credentials/:id/edit", CredentialLive, :edit
  end

  # API路由
  scope "/api", N8nElixirWeb do
    pipe_through :api

    # Webhook路由
    post "/webhook/:id", WebhookController, :receive
    get "/webhook/:id", WebhookController, :receive
    
    # 工作流执行API
    post "/workflows/:id/execute", WorkflowController, :execute
    get "/workflows/:id/status", WorkflowController, :status
    post "/workflows/:id/stop", WorkflowController, :stop
    
    # 节点类型API
    get "/node-types", NodeTypeController, :index
    get "/node-types/:type", NodeTypeController, :show
    
    # 二进制数据API
    post "/binary-data/upload", BinaryDataController, :upload
    post "/binary-data/batch-upload", BinaryDataController, :batch_upload
    get "/binary-data/:id", BinaryDataController, :show
    get "/binary-data/:id/download", BinaryDataController, :download
    get "/binary-data/:id/stream", BinaryDataController, :stream_download
    delete "/binary-data/:id", BinaryDataController, :delete
    post "/binary-data/:id/convert", BinaryDataController, :convert
    post "/binary-data/:id/thumbnail", BinaryDataController, :create_thumbnail
    post "/binary-data/compress", BinaryDataController, :compress
    get "/binary-data/execution/:execution_id/stats", BinaryDataController, :execution_stats
    delete "/binary-data/execution/:execution_id", BinaryDataController, :cleanup_execution
  end

  # 开发环境路由
  if Application.compile_env(:n8n_elixir, :dev_routes) do
    import Phoenix.LiveDashboard.Router

    scope "/dev" do
      pipe_through :browser

      live_dashboard "/dashboard", metrics: N8nElixirWeb.Telemetry
      forward "/mailbox", Plug.Swoosh.MailboxPreview
    end
  end
end
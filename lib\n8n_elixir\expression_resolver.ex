defmodule N8nElixir.ExpressionResolver do
  @moduledoc """
  表达式解析器

  参考n8n的表达式系统，支持动态表达式求值和数据引用
  支持JavaScript风格的表达式语法和内置函数
  """

  @doc """
  解析和执行表达式

  支持的语法：
  - 变量引用：$json.field, $node["NodeName"].json.field
  - 函数调用：$now(), $randomInt(1, 100)
  - 数学运算：$json.count + 1
  - 字符串操作：$json.name.toUpperCase()
  - 条件表达式：$json.status === 'active' ? 'yes' : 'no'
  """
  def resolve_expression(expression, context \\ %{}) do
    # 预处理表达式
    preprocessed = preprocess_expression(expression)
    
    # 检查是否是简单的表达式格式 {{ }}
    case extract_expression_content(preprocessed) do
      nil ->
        # 不是表达式，直接返回原值
        {:ok, expression}
      
      expr_content ->
        # 执行表达式解析
        execute_expression(expr_content, context)
    end
  end

  @doc """
  批量解析对象中的所有表达式
  """
  def resolve_expressions_in_object(object, context \\ %{}) do
    case object do
      map when is_map(map) ->
        resolved_map = 
          Enum.reduce(map, %{}, fn {key, value}, acc ->
            case resolve_expressions_in_object(value, context) do
              {:ok, resolved_value} -> Map.put(acc, key, resolved_value)
              {:error, _} -> Map.put(acc, key, value)
            end
          end)
        {:ok, resolved_map}
      
      list when is_list(list) ->
        resolved_list = 
          Enum.map(list, fn item ->
            case resolve_expressions_in_object(item, context) do
              {:ok, resolved_item} -> resolved_item
              {:error, _} -> item
            end
          end)
        {:ok, resolved_list}
      
      string when is_binary(string) ->
        resolve_expression(string, context)
      
      other ->
        {:ok, other}
    end
  end

  @doc """
  检查字符串是否包含表达式
  """
  def has_expression?(string) when is_binary(string) do
    String.contains?(string, "{{") and String.contains?(string, "}}")
  end
  def has_expression?(_), do: false

  @doc """
  获取表达式中引用的变量
  """
  def get_referenced_variables(expression) do
    case extract_expression_content(expression) do
      nil -> []
      expr_content -> extract_variable_references(expr_content)
    end
  end

  # 私有实现函数

  defp preprocess_expression(expression) when is_binary(expression) do
    expression
    |> String.trim()
  end
  defp preprocess_expression(expression), do: expression

  defp extract_expression_content(string) when is_binary(string) do
    # 匹配 {{ expression }} 格式
    case Regex.run(~r/^\{\{(.+)\}\}$/s, String.trim(string)) do
      [_, content] -> String.trim(content)
      nil -> 
        # 检查是否包含内联表达式
        if String.contains?(string, "{{") do
          string  # 返回整个字符串进行处理
        else
          nil
        end
    end
  end
  defp extract_expression_content(_), do: nil

  defp execute_expression(expr_content, context) do
    try do
      # 替换变量引用
      substituted = substitute_variables(expr_content, context)
      
      # 执行内置函数
      with_functions = apply_builtin_functions(substituted, context)
      
      # 简单的表达式求值
      result = evaluate_expression(with_functions, context)
      
      {:ok, result}
    rescue
      error ->
        {:error, "Expression evaluation failed: #{inspect(error)}"}
    end
  end

  defp substitute_variables(expression, context) do
    # 替换 $json 引用
    expression = substitute_json_references(expression, context)
    
    # 替换 $node 引用
    expression = substitute_node_references(expression, context)
    
    # 替换 $item 引用
    expression = substitute_item_references(expression, context)
    
    # 替换其他上下文变量
    substitute_context_variables(expression, context)
  end

  defp substitute_json_references(expression, context) do
    json_data = Map.get(context, "$json", %{})
    
    # 匹配 $json.field.subfield 格式
    Regex.replace(~r/\$json\.([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)/, expression, fn _, path ->
      value = get_nested_value(json_data, path)
      format_value_for_expression(value)
    end)
  end

  defp substitute_node_references(expression, context) do
    # 匹配 $node["NodeName"].json.field 格式
    Regex.replace(~r/\$node\["([^"]+)"\]\.json\.([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)/, expression, fn _, node_name, path ->
      node_data = get_node_data(node_name, context)
      json_data = Map.get(node_data, "json", %{})
      value = get_nested_value(json_data, path)
      format_value_for_expression(value)
    end)
  end

  defp substitute_item_references(expression, context) do
    item_data = Map.get(context, "$item", %{})
    
    # 匹配 $item.field 格式
    Regex.replace(~r/\$item\.([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)/, expression, fn _, path ->
      value = get_nested_value(item_data, path)
      format_value_for_expression(value)
    end)
  end

  defp substitute_context_variables(expression, context) do
    # 替换其他上下文变量
    Enum.reduce(context, expression, fn {key, value}, expr ->
      if is_binary(key) and String.starts_with?(key, "$") do
        variable_pattern = Regex.escape(key)
        Regex.replace(~r/\b#{variable_pattern}\b/, expr, fn _ ->
          format_value_for_expression(value)
        end)
      else
        expr
      end
    end)
  end

  defp apply_builtin_functions(expression, context) do
    # 应用内置函数
    expression
    |> apply_date_functions(context)
    |> apply_math_functions(context)
    |> apply_string_functions(context)
    |> apply_utility_functions(context)
  end

  defp apply_date_functions(expression, _context) do
    # $now() - 当前时间戳
    expression = Regex.replace(~r/\$now\(\)/, expression, fn _ ->
      DateTime.utc_now() |> DateTime.to_unix(:millisecond) |> to_string()
    end)
    
    # $today() - 今天的日期字符串
    expression = Regex.replace(~r/\$today\(\)/, expression, fn _ ->
      Date.utc_today() |> Date.to_string() |> format_value_for_expression()
    end)
    
    expression
  end

  defp apply_math_functions(expression, _context) do
    # $randomInt(min, max) - 随机整数
    expression = Regex.replace(~r/\$randomInt\((\d+),\s*(\d+)\)/, expression, fn _, min_str, max_str ->
      min_val = String.to_integer(min_str)
      max_val = String.to_integer(max_str)
      Enum.random(min_val..max_val) |> to_string()
    end)
    
    # $round(number, decimals) - 四舍五入
    expression = Regex.replace(~r/\$round\(([^,]+),\s*(\d+)\)/, expression, fn _, number_str, decimals_str ->
      try do
        number = String.to_float(number_str)
        decimals = String.to_integer(decimals_str)
        Float.round(number, decimals) |> to_string()
      rescue
        _ -> number_str
      end
    end)
    
    expression
  end

  defp apply_string_functions(expression, _context) do
    # $uuid() - 生成UUID
    expression = Regex.replace(~r/\$uuid\(\)/, expression, fn _ ->
      UUID.uuid4() |> format_value_for_expression()
    end)
    
    expression
  end

  defp apply_utility_functions(expression, _context) do
    # $json() - 将字符串解析为JSON
    expression = Regex.replace(~r/\$json\(([^)]+)\)/, expression, fn _, json_str ->
      try do
        # 移除引号并解析JSON
        cleaned = String.trim(json_str, "\"'")
        case Jason.decode(cleaned) do
          {:ok, parsed} -> format_value_for_expression(parsed)
          {:error, _} -> json_str
        end
      rescue
        _ -> json_str
      end
    end)
    
    expression
  end

  defp evaluate_expression(expression, context) do
    # 简单的表达式求值
    try do
      # 处理字符串模板
      if String.contains?(expression, "{{") do
        evaluate_template_expression(expression, context)
      else
        # 尝试解析为JSON值
        case Jason.decode(expression) do
          {:ok, value} -> value
          {:error, _} ->
            # 如果不是有效的JSON，尝试作为字符串处理
            if String.starts_with?(expression, "\"") and String.ends_with?(expression, "\"") do
              String.slice(expression, 1..-2)
            else
              # 尝试作为数字处理
              case Float.parse(expression) do
                {float_val, ""} -> float_val
                _ ->
                  case Integer.parse(expression) do
                    {int_val, ""} -> int_val
                    _ -> expression
                  end
              end
            end
        end
      end
    rescue
      _ -> expression
    end
  end

  defp evaluate_template_expression(expression, context) do
    # 处理包含多个表达式的模板字符串
    Regex.replace(~r/\{\{([^}]+)\}\}/, expression, fn _, expr_content ->
      case execute_expression(String.trim(expr_content), context) do
        {:ok, result} -> to_string(result)
        {:error, _} -> expr_content
      end
    end)
  end

  defp extract_variable_references(expression) do
    variables = []
    
    # 提取 $json 引用
    json_refs = Regex.scan(~r/\$json\.([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)/, expression)
    |> Enum.map(fn [_, path] -> "$json.#{path}" end)
    
    # 提取 $node 引用
    node_refs = Regex.scan(~r/\$node\["([^"]+)"\]/, expression)
    |> Enum.map(fn [match, _] -> match end)
    
    # 提取 $item 引用
    item_refs = Regex.scan(~r/\$item\.([a-zA-Z_][a-zA-Z0-9_]*(?:\.[a-zA-Z_][a-zA-Z0-9_]*)*)/, expression)
    |> Enum.map(fn [_, path] -> "$item.#{path}" end)
    
    variables ++ json_refs ++ node_refs ++ item_refs
  end

  defp get_node_data(node_name, context) do
    node_data_map = Map.get(context, "$nodes", %{})
    Map.get(node_data_map, node_name, %{})
  end

  defp get_nested_value(data, path) when is_binary(path) do
    path_parts = String.split(path, ".")
    get_nested_value(data, path_parts)
  end
  defp get_nested_value(data, []), do: data
  defp get_nested_value(data, [key | rest]) when is_map(data) do
    case Map.get(data, key) do
      nil -> nil
      value -> get_nested_value(value, rest)
    end
  end
  defp get_nested_value(_, _), do: nil

  defp format_value_for_expression(nil), do: "null"
  defp format_value_for_expression(true), do: "true"
  defp format_value_for_expression(false), do: "false"
  defp format_value_for_expression(value) when is_binary(value) do
    "\"#{String.replace(value, "\"", "\\\"")}\""
  end
  defp format_value_for_expression(value) when is_number(value), do: to_string(value)
  defp format_value_for_expression(value) when is_list(value) or is_map(value) do
    case Jason.encode(value) do
      {:ok, json} -> json
      {:error, _} -> "null"
    end
  end
  defp format_value_for_expression(_), do: "null"
end
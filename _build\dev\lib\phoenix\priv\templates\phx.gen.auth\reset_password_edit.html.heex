<div class="mx-auto max-w-sm">
  <.header class="text-center">
    Reset Password
  </.header>

  <.simple_form :let={f} for={@changeset} action={~p"<%= schema.route_prefix %>/reset_password/#{@token}"}>
    <.error :if={@changeset.action}>
      Oops, something went wrong! Please check the errors below.
    </.error>

    <.input field={f[:password]} type="password" label="New Password" required />
    <.input
      field={f[:password_confirmation]}
      type="password"
      label="Confirm new password"
      required
    />
    <:actions>
      <.button phx-disable-with="Resetting..." class="w-full">
        Reset password
      </.button>
    </:actions>
  </.simple_form>

  <p class="text-center text-sm mt-4">
    <.link href={~p"<%= schema.route_prefix %>/register"}>Register</.link>
    | <.link href={~p"<%= schema.route_prefix %>/log_in"}>Log in</.link>
  </p>
</div>

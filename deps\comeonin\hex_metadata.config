{<<"links">>,
 [{<<"Changelog">>,
   <<"https://github.com/riverrun/comeonin/blob/master/CHANGELOG.md">>},
  {<<"GitHub">>,<<"https://github.com/riverrun/comeonin">>}]}.
{<<"name">>,<<"comeonin">>}.
{<<"version">>,<<"5.5.1">>}.
{<<"description">>,<<"A specification for password hashing libraries">>}.
{<<"elixir">>,<<"~> 1.7">>}.
{<<"app">>,<<"comeonin">>}.
{<<"files">>,
 [<<"lib">>,<<"lib/comeonin.ex">>,<<"lib/comeonin">>,
  <<"lib/comeonin/behaviour_test_helper.ex">>,
  <<"lib/comeonin/password_hash.ex">>,<<"mix.exs">>,<<"CHANGELOG.md">>,
  <<"README.md">>,<<"LICENSE">>]}.
{<<"licenses">>,[<<"BSD-3-Clause">>]}.
{<<"requirements">>,[]}.
{<<"build_tools">>,[<<"mix">>]}.

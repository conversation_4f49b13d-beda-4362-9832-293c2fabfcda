defmodule N8nElixir.TriggerManager do
  @moduledoc """
  触发器管理器

  管理所有工作流的触发器，包括定时触发器、Webhook触发器等
  """

  use GenServer
  require Logger

  @registry_name __MODULE__

  def start_link(init_arg) do
    GenServer.start_link(__MODULE__, init_arg, name: @registry_name)
  end

  @impl true
  def init(_init_arg) do
    Logger.info("Starting TriggerManager")
    {:ok, %{}}
  end

  @impl true
  def handle_call(_msg, _from, state) do
    {:reply, :ok, state}
  end

  @impl true
  def handle_cast(_msg, state) do
    {:noreply, state}
  end
end
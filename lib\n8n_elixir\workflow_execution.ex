defmodule N8nElixir.Workflow do
  @moduledoc """
  工作流模型 - 参考n8n的Workflow类
  
  包含工作流的执行逻辑，节点连接关系管理等核心功能
  这是整个执行引擎的核心，管理节点之间的数据流和执行顺序
  """
  
  defstruct [
    :id,
    :name,
    :nodes,
    :connections,
    :node_types,
    :static_data,
    :settings,
    :pin_data,
    :active
  ]
  
  alias N8nElixir.NodeTypes
  
  @type t :: %__MODULE__{
    id: String.t(),
    name: String.t(),
    nodes: map(),
    connections: map(),
    node_types: pid(),
    static_data: map(),
    settings: map(),
    pin_data: map(),
    active: boolean()
  }

  def new(workflow_data) do
    %__MODULE__{
      id: Map.get(workflow_data, "id"),
      name: Map.get(workflow_data, "name"),
      nodes: Map.get(workflow_data, "nodes", %{}),
      connections: Map.get(workflow_data, "connections", %{}),
      node_types: NodeTypes,
      static_data: Map.get(workflow_data, "static_data", %{}),
      settings: Map.get(workflow_data, "settings", %{}),
      pin_data: Map.get(workflow_data, "pin_data", %{}),
      active: Map.get(workflow_data, "active", false)
    }
  end

  @doc """
  获取工作流的起始节点
  类似n8n的getStartNode功能
  """
  def get_start_nodes(%__MODULE__{} = workflow) do
    # 查找触发器节点或手动触发节点
    Enum.filter(workflow.nodes, fn {_name, node} ->
      node_type = Map.get(node, "type", "")
      is_trigger_node?(node_type) or is_start_node?(node_type)
    end)
    |> Enum.map(fn {_name, node} -> node end)
  end

  @doc """
  获取指定节点的父节点（输入连接）
  """
  def get_parent_nodes(%__MODULE__{} = workflow, node_name) do
    Enum.reduce(workflow.connections, [], fn {source_node, connections}, acc ->
      # 检查是否有连接到目标节点
      has_connection = Enum.any?(connections, fn {_output_type, output_connections} ->
        Enum.any?(output_connections, fn connection ->
          Map.get(connection, "node") == node_name
        end)
      end)
      
      if has_connection do
        [source_node | acc]
      else
        acc
      end
    end)
  end

  @doc """
  获取指定节点的子节点（输出连接）
  """
  def get_child_nodes(%__MODULE__{} = workflow, node_name) do
    case Map.get(workflow.connections, node_name) do
      nil -> []
      connections ->
        Enum.reduce(connections, [], fn {_output_type, output_connections}, acc ->
          child_nodes = Enum.map(output_connections, fn connection ->
            Map.get(connection, "node")
          end)
          acc ++ child_nodes
        end)
    end
  end

  @doc """
  检查工作流是否有问题
  """
  def validate(%__MODULE__{} = workflow) do
    errors = []
    
    # 检查节点是否存在
    errors = validate_nodes_exist(workflow, errors)
    
    # 检查连接是否有效
    errors = validate_connections(workflow, errors)
    
    # 检查是否有起始节点
    errors = validate_start_nodes(workflow, errors)
    
    case errors do
      [] -> :ok
      _ -> {:error, errors}
    end
  end

  @doc """
  获取工作流执行顺序
  参考n8n的getExecutionOrder方法
  """
  def get_execution_order(%__MODULE__{} = workflow, start_node \\ nil) do
    start_nodes = if start_node do
      [start_node]
    else
      get_start_nodes(workflow)
    end
    
    case start_nodes do
      [] -> {:error, "No start nodes found"}
      _ -> 
        execution_order = build_execution_order(workflow, start_nodes)
        {:ok, execution_order}
    end
  end

  # 私有函数
  defp is_trigger_node?(node_type) do
    node_type in ["webhook", "cron", "manualTrigger", "scheduleTrigger"]
  end

  defp is_start_node?(node_type) do
    node_type in ["start", "manualTrigger", "scheduleTrigger"]
  end

  defp validate_nodes_exist(%__MODULE__{} = workflow, errors) do
    # 检查连接中引用的节点是否都存在
    Enum.reduce(workflow.connections, errors, fn {source_node, connections}, acc ->
      if not Map.has_key?(workflow.nodes, source_node) do
        ["Node '#{source_node}' referenced in connections but does not exist" | acc]
      else
        # 检查目标节点
        Enum.reduce(connections, acc, fn {_output_type, output_connections}, inner_acc ->
          Enum.reduce(output_connections, inner_acc, fn connection, final_acc ->
            target_node = Map.get(connection, "node")
            if target_node && not Map.has_key?(workflow.nodes, target_node) do
              ["Node '#{target_node}' referenced in connections but does not exist" | final_acc]
            else
              final_acc
            end
          end)
        end)
      end
    end)
  end

  defp validate_connections(%__MODULE__{} = workflow, errors) do
    # 检查连接的有效性
    # 这里可以添加更复杂的连接验证逻辑
    Enum.reduce(workflow.connections, errors, fn {source_node, connections}, acc ->
      source_node_data = Map.get(workflow.nodes, source_node)
      
      if source_node_data do
        # 验证输出连接
        validate_node_connections(source_node_data, connections, acc)
      else
        acc
      end
    end)
  end

  defp validate_node_connections(_node_data, _connections, errors) do
    # 这里可以添加节点特定的连接验证
    # 比如检查输出类型是否匹配等
    errors
  end

  defp validate_start_nodes(%__MODULE__{} = workflow, errors) do
    start_nodes = get_start_nodes(workflow)
    
    if start_nodes == [] do
      ["Workflow has no start nodes (trigger or manual start)" | errors]
    else
      errors
    end
  end

  defp build_execution_order(%__MODULE__{} = workflow, start_nodes) do
    # 使用深度优先搜索构建执行顺序
    visited = MapSet.new()
    execution_order = []
    
    Enum.reduce(start_nodes, {execution_order, visited}, fn start_node, {order, visited_set} ->
      build_order_dfs(workflow, start_node, order, visited_set)
    end)
    |> elem(0)
    |> Enum.reverse()
  end

  defp build_order_dfs(%__MODULE__{} = workflow, node, order, visited) do
    node_name = if is_map(node), do: Map.get(node, "name"), else: node
    
    if MapSet.member?(visited, node_name) do
      {order, visited}
    else
      visited = MapSet.put(visited, node_name)
      order = [node_name | order]
      
      # 递归处理子节点
      child_nodes = get_child_nodes(workflow, node_name)
      
      Enum.reduce(child_nodes, {order, visited}, fn child_node, {current_order, current_visited} ->
        build_order_dfs(workflow, child_node, current_order, current_visited)
      end)
    end
  end
end

defmodule N8nElixir.WorkflowExecution.Server do
  @moduledoc """
  工作流执行服务器
  
  参考n8n的WorkflowExecute类，每个正在运行的工作流实例都是一个独立的GenServer进程
  这个进程负责管理单个工作流的完整生命周期
  """
  use GenServer, restart: :temporary
  
  alias N8nElixir.Workflow
  alias N8nElixir.NodeTypes
  alias N8nElixir.Executions
  alias N8nElixir.BinaryData

  @type workflow_execution_state :: %{
    workflow_id: String.t(),
    execution_id: String.t(),
    workflow: Workflow.t(),
    execution_data: map(),
    current_node: String.t() | nil,
    status: atom(),
    start_time: DateTime.t(),
    end_time: DateTime.t() | nil,
    error: any() | nil,
    mode: atom(),
    static_data: map(),
    pin_data: map()
  }

  # 客户端API
  def start_link(opts) do
    execution_id = Keyword.get(opts, :execution_id)
    GenServer.start_link(__MODULE__, opts, name: via_tuple(execution_id))
  end

  def execute_workflow(execution_id) do
    GenServer.call(via_tuple(execution_id), :execute_workflow, 60_000)
  end

  def get_status(execution_id) do
    GenServer.call(via_tuple(execution_id), :get_status)
  end

  def pause_execution(execution_id) do
    GenServer.call(via_tuple(execution_id), :pause_execution)
  end

  def resume_execution(execution_id) do
    GenServer.call(via_tuple(execution_id), :resume_execution)
  end

  def cancel_execution(execution_id) do
    GenServer.call(via_tuple(execution_id), :cancel_execution)
  end

  def stop_execution(execution_id) do
    GenServer.call(via_tuple(execution_id), :stop_execution)
  end

  # 服务器回调
  @impl true
  def init(opts) do
    workflow_id = Keyword.get(opts, :workflow_id)
    execution_id = Keyword.get(opts, :execution_id)
    mode = Keyword.get(opts, :mode, :manual)
    trigger_data = Keyword.get(opts, :trigger_data, %{})
    
    # 从数据库加载工作流定义
    case load_workflow(workflow_id) do
      {:ok, workflow} ->
        state = %{
          workflow_id: workflow_id,
          execution_id: execution_id,
          workflow: workflow,
          execution_data: %{},
          current_node: nil,
          status: :new,
          start_time: DateTime.utc_now(),
          end_time: nil,
          error: nil,
          mode: mode,
          static_data: workflow.static_data,
          pin_data: workflow.pin_data
        }

        # 创建数据库执行记录
        create_execution_record(state, trigger_data)
        
        # 发布执行开始事件
        broadcast_execution_event(state, :execution_created)

        {:ok, state}
      
      {:error, reason} ->
        {:stop, {:shutdown, reason}}
    end
  end

  @impl true
  def handle_call(:execute_workflow, _from, state) do
    case Workflow.validate(state.workflow) do
      :ok ->
        updated_state = %{state | status: :running}
        broadcast_execution_event(updated_state, :execution_started)
        
        case execute_workflow_steps(updated_state) do
          {:ok, final_state} ->
            completed_state = %{final_state | 
              status: :success, 
              end_time: DateTime.utc_now()
            }
            
            # 更新数据库记录
            update_execution_record(completed_state)
            broadcast_execution_event(completed_state, :execution_completed)
            
            {:reply, {:ok, completed_state}, completed_state}
          
          {:error, reason} ->
            error_state = %{updated_state | 
              status: :error, 
              error: reason, 
              end_time: DateTime.utc_now()
            }
            
            update_execution_record(error_state)
            broadcast_execution_event(error_state, :execution_failed)
            
            {:reply, {:error, reason}, error_state}
        end
      
      {:error, validation_errors} ->
        error_state = %{state | 
          status: :error, 
          error: validation_errors, 
          end_time: DateTime.utc_now()
        }
        
        {:reply, {:error, validation_errors}, error_state}
    end
  end

  @impl true
  def handle_call(:get_status, _from, state) do
    status_info = %{
      execution_id: state.execution_id,
      workflow_id: state.workflow_id,
      status: state.status,
      current_node: state.current_node,
      start_time: state.start_time,
      end_time: state.end_time,
      error: state.error,
      mode: state.mode
    }
    
    {:reply, status_info, state}
  end

  @impl true
  def handle_call(:pause_execution, _from, state) do
    if state.status == :running do
      paused_state = %{state | status: :waiting}
      broadcast_execution_event(paused_state, :execution_paused)
      {:reply, :ok, paused_state}
    else
      {:reply, {:error, "Cannot pause execution in #{state.status} state"}, state}
    end
  end

  @impl true
  def handle_call(:resume_execution, _from, state) do
    if state.status == :waiting do
      resumed_state = %{state | status: :running}
      broadcast_execution_event(resumed_state, :execution_resumed)
      
      # 继续执行
      case continue_execution(resumed_state) do
        {:ok, final_state} ->
          {:reply, :ok, final_state}
        {:error, reason} ->
          error_state = %{resumed_state | status: :error, error: reason}
          {:reply, {:error, reason}, error_state}
      end
    else
      {:reply, {:error, "Cannot resume execution in #{state.status} state"}, state}
    end
  end

  @impl true
  def handle_call(:cancel_execution, _from, state) do
    cancelled_state = %{state | 
      status: :canceled, 
      end_time: DateTime.utc_now()
    }
    
    update_execution_record(cancelled_state)
    broadcast_execution_event(cancelled_state, :execution_cancelled)
    
    {:reply, :ok, cancelled_state}
  end

  @impl true
  def handle_call(:stop_execution, _from, state) do
    {:stop, :normal, :ok, state}
  end

  # 执行工作流的核心逻辑
  defp execute_workflow_steps(state) do
    case Workflow.get_execution_order(state.workflow) do
      {:ok, execution_order} ->
        execute_nodes_in_order(state, execution_order)
      {:error, reason} ->
        {:error, reason}
    end
  end

  defp execute_nodes_in_order(state, node_order) do
    # 初始化执行数据
    initial_data = %{}
    
    Enum.reduce_while(node_order, {:ok, state, initial_data}, fn node_name, {:ok, current_state, execution_data} ->
      case execute_single_node(current_state, node_name, execution_data) do
        {:ok, node_output, updated_state} ->
          updated_execution_data = Map.put(execution_data, node_name, node_output)
          updated_state = %{updated_state | 
            current_node: node_name,
            execution_data: updated_execution_data
          }
          
          # 广播节点执行完成事件
          broadcast_node_event(updated_state, node_name, :node_executed)
          
          {:cont, {:ok, updated_state, updated_execution_data}}
        
        {:error, reason} ->
          error_state = %{current_state | 
            current_node: node_name,
            error: reason
          }
          
          broadcast_node_event(error_state, node_name, :node_failed)
          {:halt, {:error, reason}}
      end
    end)
    |> case do
      {:ok, final_state, _execution_data} -> {:ok, final_state}
      {:error, reason} -> {:error, reason}
    end
  end

  defp execute_single_node(state, node_name, execution_data) do
    node_data = Map.get(state.workflow.nodes, node_name)
    
    if node_data do
      node_type = Map.get(node_data, "type")
      node_parameters = Map.get(node_data, "parameters", %{})
      
      # 获取节点类型模块
      case NodeTypes.get_node_type(node_type) do
        {:ok, node_module} ->
          # 准备输入数据
          input_data = prepare_node_input(state, node_name, execution_data)
          
          # 应用数据处理器进行预处理
          processed_input = case N8nElixir.DataProcessor.process_input_data(input_data) do
            {:ok, processed} -> processed
            {:error, _reason} -> input_data
          end
          
          # 获取凭证
          credentials = get_node_credentials(state, node_name)
          
          # 记录节点开始执行
          start_time = DateTime.utc_now()
          
          # 执行节点，包含错误处理
          case execute_node_with_error_handling(node_module, processed_input, credentials, node_parameters, state, node_name) do
            {:ok, output_data} ->
              # 格式化输出数据
              formatted_output = N8nElixir.DataProcessor.format_output_data(output_data)
              
              # 记录执行数据
              create_execution_data_record(state, node_name, node_type, start_time, processed_input, formatted_output, nil)
              
              {:ok, formatted_output, state}
            
            {:error, reason} ->
              # 记录错误
              create_execution_data_record(state, node_name, node_type, start_time, processed_input, nil, reason)
              
              {:error, reason}
            
            {:retry, retry_info} ->
              # 需要重试
              {:retry, retry_info}
          end
        
        {:error, reason} ->
          {:error, "Node type '#{node_type}' not found: #{reason}"}
      end
    else
      {:error, "Node '#{node_name}' not found in workflow"}
    end
  end

  defp execute_node_with_error_handling(node_module, input_data, credentials, parameters, state, node_name) do
    try do
      case node_module.execute(input_data, credentials, parameters) do
        {:ok, output_data} ->
          {:ok, output_data}
        
        {:error, reason} = error ->
          # 处理节点执行错误
          error_context = %{
            node_name: node_name,
            node_type: Map.get(state.workflow.nodes[node_name], "type"),
            workflow_id: state.workflow_id,
            execution_id: state.execution_id,
            input_data: input_data,
            parameters: parameters
          }
          
          case N8nElixir.ErrorHandler.handle_node_error(error, error_context) do
            {:continue, error_data} ->
              # 继续执行，返回错误数据
              {:ok, [error_data]}
            
            {:retry, error_info} ->
              # 需要重试
              {:retry, error_info}
            
            {:fail, _error_info} ->
              # 失败，停止执行
              {:error, reason}
            
            {:skip, _error_info} ->
              # 跳过此节点，返回空数据
              {:ok, []}
          end
      end
    rescue
      error ->
        # 捕获所有异常
        error_context = %{
          node_name: node_name,
          workflow_id: state.workflow_id,
          execution_id: state.execution_id,
          exception: error
        }
        
        case N8nElixir.ErrorHandler.handle_node_error(error, error_context) do
          {:continue, error_data} -> {:ok, [error_data]}
          {:retry, error_info} -> {:retry, error_info}
          {:fail, _error_info} -> {:error, "Node execution failed: #{inspect(error)}"}
          {:skip, _error_info} -> {:ok, []}
        end
    end
  end

  defp prepare_node_input(state, node_name, execution_data) do
    # 获取父节点的输出作为输入
    parent_nodes = Workflow.get_parent_nodes(state.workflow, node_name)
    
    case parent_nodes do
      [] ->
        # 起始节点，使用空输入或pin数据
        case Map.get(state.pin_data, node_name) do
          nil -> [%{"json" => %{}}]
          pin_data -> pin_data
        end
      
      parents ->
        # 合并父节点的输出
        Enum.reduce(parents, [], fn parent_name, acc ->
          case Map.get(execution_data, parent_name) do
            nil -> acc
            output_data when is_list(output_data) -> acc ++ output_data
            output_data -> [output_data | acc]
          end
        end)
    end
  end

  defp get_node_credentials(state, node_name) do
    # 从数据库或缓存获取节点凭证
    # 这里需要实现凭证解密和获取逻辑
    %{}
  end

  defp continue_execution(state) do
    # 从当前节点继续执行
    # 这里需要实现暂停/恢复的逻辑
    {:ok, state}
  end

  # 数据库操作
  defp load_workflow(workflow_id) do
    case N8nElixir.Workflows.get_workflow(workflow_id) do
      nil -> {:error, "Workflow not found"}
      workflow -> 
        workflow_data = %{
          "id" => workflow.id,
          "name" => workflow.name,
          "nodes" => workflow.nodes,
          "connections" => workflow.connections,
          "static_data" => workflow.static_data,
          "settings" => workflow.settings,
          "pin_data" => workflow.pin_data,
          "active" => workflow.status == :active
        }
        {:ok, Workflow.new(workflow_data)}
    end
  end

  defp create_execution_record(state, trigger_data) do
    execution_attrs = %{
      id: state.execution_id,
      workflow_id: state.workflow_id,
      status: state.status,
      mode: state.mode,
      started_at: state.start_time,
      data: %{
        "trigger_data" => trigger_data
      },
      workflow_data: %{
        "nodes" => state.workflow.nodes,
        "connections" => state.workflow.connections
      }
    }

    Executions.create_execution(execution_attrs)
  end

  defp update_execution_record(state) do
    execution_attrs = %{
      status: state.status,
      stopped_at: state.end_time,
      finished: state.status in [:success, :error, :canceled],
      error: if(state.error, do: %{"message" => to_string(state.error)}, else: nil),
      data: state.execution_data
    }

    Executions.update_execution(state.execution_id, execution_attrs)
  end

  defp create_execution_data_record(state, node_name, node_type, start_time, input_data, output_data, error) do
    execution_time = DateTime.diff(DateTime.utc_now(), start_time, :millisecond)
    
    data_attrs = %{
      execution_id: state.execution_id,
      node_name: node_name,
      node_type: node_type,
      start_time: start_time,
      execution_time: execution_time,
      source: input_data,
      data: output_data,
      error: if(error, do: %{"message" => to_string(error)}, else: nil)
    }

    Executions.create_execution_data(data_attrs)
  end

  # 事件广播
  defp broadcast_execution_event(state, event_type) do
    Phoenix.PubSub.broadcast(
      N8nElixir.PubSub,
      "workflow_executions:#{state.workflow_id}",
      {event_type, state.execution_id, state}
    )
  end

  defp broadcast_node_event(state, node_name, event_type) do
    Phoenix.PubSub.broadcast(
      N8nElixir.PubSub,
      "node_executions:#{state.execution_id}",
      {event_type, node_name, state}
    )
  end

  # 辅助函数
  defp via_tuple(execution_id) do
    {:via, Registry, {N8nElixir.WorkflowRegistry, execution_id}}
  end
end

defmodule N8nElixir.WorkflowRunner do
  @moduledoc """
  工作流运行器
  
  参考n8n的WorkflowRunner，负责启动和管理工作流执行
  """
  
  alias N8nElixir.WorkflowExecution

  def start_execution(workflow_id, opts \\ []) do
    execution_id = Keyword.get(opts, :execution_id, UUID.uuid4())
    mode = Keyword.get(opts, :mode, :manual)
    trigger_data = Keyword.get(opts, :trigger_data, %{})
    
    execution_spec = %{
      id: execution_id,
      start: {
        WorkflowExecution.Server,
        :start_link,
        [
          workflow_id: workflow_id,
          execution_id: execution_id,
          mode: mode,
          trigger_data: trigger_data
        ]
      },
      restart: :temporary
    }

    case DynamicSupervisor.start_child(N8nElixir.ExecutionSupervisor, execution_spec) do
      {:ok, _pid} ->
        {:ok, execution_id}
      {:error, reason} ->
        {:error, reason}
    end
  end

  def execute_workflow(workflow_id, opts \\ []) do
    case start_execution(workflow_id, opts) do
      {:ok, execution_id} ->
        WorkflowExecution.Server.execute_workflow(execution_id)
      {:error, reason} ->
        {:error, reason}
    end
  end

  def get_execution_status(execution_id) do
    WorkflowExecution.Server.get_status(execution_id)
  end

  def pause_execution(execution_id) do
    WorkflowExecution.Server.pause_execution(execution_id)
  end

  def resume_execution(execution_id) do
    WorkflowExecution.Server.resume_execution(execution_id)
  end

  def cancel_execution(execution_id) do
    WorkflowExecution.Server.cancel_execution(execution_id)
  end

  def stop_execution(execution_id) do
    WorkflowExecution.Server.stop_execution(execution_id)
  end
end
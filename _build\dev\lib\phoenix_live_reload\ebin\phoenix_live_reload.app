{application,phoenix_live_reload,
             [{modules,['Elixir.Phoenix.LiveReloader',
                        'Elixir.Phoenix.LiveReloader.Application',
                        'Elixir.Phoenix.LiveReloader.Channel',
                        'Elixir.Phoenix.LiveReloader.Socket',
                        'Elixir.Phoenix.LiveReloader.WebConsoleLogger']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger,phoenix,file_system]},
              {description,"Provides live-reload functionality for Phoenix"},
              {registered,[]},
              {vsn,"1.6.0"},
              {mod,{'Elixir.Phoenix.LiveReloader.Application',[]}}]}.

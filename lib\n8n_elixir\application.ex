defmodule N8nElixir.Application do
  @moduledoc """
  N8nElixir 应用程序的主监督树
  
  这是整个N8nElixir系统的根监督器，负责启动和监督所有核心服务
  参考n8n的模块化架构，使用Elixir/OTP的优势
  """
  use Application

  def start(_type, _args) do
    children = [
      # 数据库连接池
      N8nElixir.Repo,
      
      # Phoenix 端点监督器
      N8nElixirWeb.Endpoint,
      
      # PubSub 用于实时通信
      {Phoenix.PubSub, name: N8nElixir.PubSub},
      
      # 核心业务逻辑监督器
      N8nElixir.Core.Supervisor,
      
      # 定时任务调度器
      N8nElixir.Scheduler
    ]

    opts = [strategy: :one_for_one, name: N8nElixir.Supervisor]
    Supervisor.start_link(children, opts)
  end

  def config_change(changed, _new, removed) do
    N8nElixirWeb.Endpoint.config_change(changed, removed)
    :ok
  end
end

defmodule N8nElixir.Core.Supervisor do
  @moduledoc """
  核心业务逻辑的监督器
  
  负责管理所有与工作流执行相关的核心服务
  参考n8n的WorkflowRunner和ExecutionService架构
  """
  use Supervisor

  def start_link(init_arg) do
    Supervisor.start_link(__MODULE__, init_arg, name: __MODULE__)
  end

  @impl true
  def init(_init_arg) do
    children = [
      # 工作流注册表 - 管理所有活跃工作流的元数据
      {Registry, keys: :unique, name: N8nElixir.WorkflowRegistry},
      
      # 节点注册表 - 管理所有可用的节点类型
      {Registry, keys: :unique, name: N8nElixir.NodeRegistry},
      
      # 执行监督器 - 动态管理工作流执行进程
      {DynamicSupervisor, name: N8nElixir.ExecutionSupervisor, strategy: :one_for_one},
      
      # 触发器管理器 - 管理所有触发器和调度
      N8nElixir.TriggerManager,
      
      # 凭证管理器 - 安全管理加密凭证
      N8nElixir.CredentialManager,
      
      # 执行追踪器 - 追踪和监控执行状态
      N8nElixir.ExecutionTracker,
      
      # 节点类型管理器 - 类似n8n的NodeTypes
      N8nElixir.NodeTypes,
      
      # 工作流活动管理器 - 管理工作流的激活状态
      N8nElixir.ActiveWorkflows,
      
      # 执行日志管理器 - 记录工作流执行日志
      N8nElixir.ExecutionLogger,
      
      # 重试管理器 - 管理失败任务的重试
      N8nElixir.RetryManager,
      
      # 二进制数据管理器 - 管理文件上传下载和二进制数据处理
      N8nElixir.BinaryDataManager
    ]

    Supervisor.init(children, strategy: :one_for_one)
  end
end
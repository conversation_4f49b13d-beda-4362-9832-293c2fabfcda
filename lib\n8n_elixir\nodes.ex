defmodule N8nElixir.Nodes.Code do
  @moduledoc """
  代码执行节点
  
  参考n8n的Code节点，支持执行JavaScript和Python代码
  在沙盒环境中安全执行用户代码
  """
  @behaviour N8nElixir.NodeType

  @impl true
  def description() do
    %{
      displayName: "Code",
      name: "code",
      icon: "fa:code",
      group: ["transform"],
      version: 1,
      description: "Execute custom JavaScript or Python code",
      defaults: %{
        name: "Code",
        color: "#FF6D5A"
      },
      inputs: ["main"],
      outputs: ["main"],
      credentials: [],
      properties: [
        %{
          displayName: "Mode",
          name: "mode",
          type: "options",
          noDataExpression: true,
          options: [
            %{
              name: "Run Once for All Items",
              value: "runOnceForAllItems",
              description: "The code is executed only once with all items at once"
            },
            %{
              name: "Run Once for Each Item",
              value: "runOnceForEachItem", 
              description: "The code is executed separately for each item"
            }
          ],
          default: "runOnceForAllItems"
        },
        %{
          displayName: "Language",
          name: "language",
          type: "options",
          noDataExpression: true,
          options: [
            %{name: "JavaScript", value: "javascript"},
            %{name: "Python", value: "python"}
          ],
          default: "javascript",
          description: "Programming language to use"
        },
        %{
          displayName: "JavaScript Code",
          name: "jsCode",
          type: "string",
          typeOptions: %{
            editor: "codeNodeEditor",
            editorLanguage: "javascript"
          },
          displayOptions: %{
            show: %{
              language: ["javascript"]
            }
          },
          default: "// Add your JavaScript code here\n// The input data is available as 'items'\n// Return an array of items\nreturn items.map(item => {\n  return {\n    json: {\n      ...item.json,\n      // Add your transformations here\n    }\n  };\n});",
          description: "JavaScript code to execute",
          required: true
        },
        %{
          displayName: "Python Code", 
          name: "pythonCode",
          type: "string",
          typeOptions: %{
            editor: "codeNodeEditor",
            editorLanguage: "python"
          },
          displayOptions: %{
            show: %{
              language: ["python"]
            }
          },
          default: "# Add your Python code here\n# The input data is available as 'items'\n# Return a list of items\nfor item in items:\n    # Add your transformations here\n    pass\n\nreturn items",
          description: "Python code to execute",
          required: true
        }
      ]
    }
  end

  @impl true
  def execute(input_data, _credentials, parameters) do
    language = Map.get(parameters, "language", "javascript")
    mode = Map.get(parameters, "mode", "runOnceForAllItems")
    
    case language do
      "javascript" ->
        code = Map.get(parameters, "jsCode", "")
        execute_javascript(input_data, code, mode)
      "python" ->
        code = Map.get(parameters, "pythonCode", "")
        execute_python(input_data, code, mode)
      _ ->
        {:error, "Unsupported language: #{language}"}
    end
  end

  @impl true
  def credential_types() do
    []
  end

  defp execute_javascript(input_data, code, mode) do
    case mode do
      "runOnceForAllItems" ->
        execute_js_for_all_items(input_data, code)
      "runOnceForEachItem" ->
        execute_js_for_each_item(input_data, code)
      _ ->
        {:error, "Unknown execution mode: #{mode}"}
    end
  end

  defp execute_js_for_all_items(input_data, code) do
    # 在实际实现中，这里需要使用安全的JavaScript执行环境
    # 比如使用 Node.js 的 vm 模块或者 QuickJS
    try do
      # 构建JavaScript执行上下文
      context = %{
        "items" => input_data,
        "console" => %{"log" => &IO.puts/1}
      }
      
      # 这里是伪代码，实际需要实现安全的JS执行
      result = execute_js_in_sandbox(code, context)
      
      case result do
        items when is_list(items) ->
          {:ok, items}
        item when is_map(item) ->
          {:ok, [item]}
        _ ->
          {:error, "JavaScript code must return an array or object"}
      end
    rescue
      error ->
        {:error, "JavaScript execution failed: #{inspect(error)}"}
    end
  end

  defp execute_js_for_each_item(input_data, code) do
    try do
      results = Enum.map(input_data, fn item ->
        context = %{
          "item" => item,
          "console" => %{"log" => &IO.puts/1}
        }
        
        # 为每个项目执行代码
        execute_js_in_sandbox(code, context)
      end)
      
      {:ok, List.flatten(results)}
    rescue
      error ->
        {:error, "JavaScript execution failed: #{inspect(error)}"}
    end
  end

  defp execute_python(input_data, code, mode) do
    case mode do
      "runOnceForAllItems" ->
        execute_python_for_all_items(input_data, code)
      "runOnceForEachItem" ->
        execute_python_for_each_item(input_data, code)
      _ ->
        {:error, "Unknown execution mode: #{mode}"}
    end
  end

  defp execute_python_for_all_items(input_data, code) do
    try do
      # 在实际实现中，这里需要使用安全的Python执行环境
      # 比如使用 Python 的 RestrictedPython 或者容器化执行
      python_code = """
      import json
      import sys
      
      # 输入数据
      items = #{Jason.encode!(input_data)}
      
      # 用户代码
      #{code}
      
      # 确保返回结果
      if 'result' in locals():
          print(json.dumps(result))
      else:
          print(json.dumps(items))
      """
      
      # 执行Python代码
      result = execute_python_in_sandbox(python_code)
      {:ok, result}
    rescue
      error ->
        {:error, "Python execution failed: #{inspect(error)}"}
    end
  end

  defp execute_python_for_each_item(input_data, code) do
    try do
      results = Enum.map(input_data, fn item ->
        python_code = """
        import json
        import sys
        
        # 当前项目数据
        item = #{Jason.encode!(item)}
        
        # 用户代码
        #{code}
        
        # 确保返回结果
        if 'result' in locals():
            print(json.dumps(result))
        else:
            print(json.dumps(item))
        """
        
        execute_python_in_sandbox(python_code)
      end)
      
      {:ok, List.flatten(results)}
    rescue
      error ->
        {:error, "Python execution failed: #{inspect(error)}"}
    end
  end

  # 这些是占位符函数，实际实现需要集成安全的代码执行引擎
  defp execute_js_in_sandbox(_code, _context) do
    # 实际实现需要集成JavaScript执行引擎
    # 比如通过Port与Node.js通信，或者使用Rust的QuickJS绑定
    %{"result" => "JavaScript execution placeholder"}
  end

  defp execute_python_in_sandbox(_code) do
    # 实际实现需要集成Python执行引擎
    # 比如通过Port与Python进程通信，或者使用ErlPort
    [%{"result" => "Python execution placeholder"}]
  end
end

defmodule N8nElixir.Nodes.If do
  @moduledoc """
  条件判断节点 - If
  
  参考n8n的If节点，根据条件表达式决定工作流的分支
  """
  @behaviour N8nElixir.NodeType

  @impl true
  def description() do
    %{
      displayName: "IF",
      name: "if",
      icon: "fa:map-signs",
      group: ["transform"],
      version: 1,
      description: "Route items based on conditional logic",
      defaults: %{
        name: "IF",
        color: "#408000"
      },
      inputs: ["main"],
      outputs: ["main", "main"],
      outputNames: ["true", "false"],
      properties: [
        %{
          displayName: "Conditions",
          name: "conditions",
          placeholder: "Add Condition",
          type: "fixedCollection",
          typeOptions: %{
            multipleValues: true
          },
          description: "The conditions to check",
          default: %{},
          options: [
            %{
              name: "boolean",
              displayName: "Boolean",
              values: [
                %{
                  displayName: "Value 1",
                  name: "value1",
                  type: "string",
                  default: "",
                  description: "The first value to compare"
                },
                %{
                  displayName: "Operation",
                  name: "operation",
                  type: "options",
                  options: [
                    %{name: "Equal", value: "equal"},
                    %{name: "Not Equal", value: "notEqual"},
                    %{name: "Smaller", value: "smaller"},
                    %{name: "Smaller Equal", value: "smallerEqual"},
                    %{name: "Larger", value: "larger"},
                    %{name: "Larger Equal", value: "largerEqual"},
                    %{name: "Contains", value: "contains"},
                    %{name: "Not Contains", value: "notContains"},
                    %{name: "Starts With", value: "startsWith"},
                    %{name: "Not Starts With", value: "notStartsWith"},
                    %{name: "Ends With", value: "endsWith"},
                    %{name: "Not Ends With", value: "notEndsWith"},
                    %{name: "Regex", value: "regex"}
                  ],
                  default: "equal",
                  description: "Operation to decide where the the data should be mapped to"
                },
                %{
                  displayName: "Value 2",
                  name: "value2",
                  type: "string",
                  default: "",
                  description: "The second value to compare"
                }
              ]
            }
          ]
        },
        %{
          displayName: "Combine",
          name: "combineOperation",
          type: "options",
          options: [
            %{name: "ALL", value: "all"},
            %{name: "ANY", value: "any"}
          ],
          default: "all",
          description: "If multiple conditions are defined how to combine them"
        }
      ]
    }
  end

  @impl true
  def execute(input_data, _credentials, parameters) do
    conditions = Map.get(parameters, "conditions", %{})
    combine_operation = Map.get(parameters, "combineOperation", "all")
    
    # 分别处理每个输入项
    {true_items, false_items} = Enum.reduce(input_data, {[], []}, fn item, {true_acc, false_acc} ->
      if evaluate_conditions(item, conditions, combine_operation) do
        {[item | true_acc], false_acc}
      else
        {true_acc, [item | false_acc]}
      end
    end)

    # 返回两个输出：true分支和false分支
    {:ok, [Enum.reverse(true_items), Enum.reverse(false_items)]}
  end

  @impl true
  def credential_types() do
    []
  end

  defp evaluate_conditions(item, conditions, combine_operation) do
    boolean_conditions = Map.get(conditions, "boolean", [])
    
    if boolean_conditions == [] do
      # 没有条件时默认为true
      true
    else
      results = Enum.map(boolean_conditions, fn condition ->
        evaluate_single_condition(item, condition)
      end)
      
      case combine_operation do
        "all" -> Enum.all?(results)
        "any" -> Enum.any?(results)
        _ -> false
      end
    end
  end

  defp evaluate_single_condition(item, condition) do
    value1 = resolve_value(item, Map.get(condition, "value1", ""))
    value2 = resolve_value(item, Map.get(condition, "value2", ""))
    operation = Map.get(condition, "operation", "equal")
    
    case operation do
      "equal" -> value1 == value2
      "notEqual" -> value1 != value2
      "smaller" -> compare_numeric(value1, value2, :lt)
      "smallerEqual" -> compare_numeric(value1, value2, :le)
      "larger" -> compare_numeric(value1, value2, :gt)
      "largerEqual" -> compare_numeric(value1, value2, :ge)
      "contains" -> String.contains?(to_string(value1), to_string(value2))
      "notContains" -> not String.contains?(to_string(value1), to_string(value2))
      "startsWith" -> String.starts_with?(to_string(value1), to_string(value2))
      "notStartsWith" -> not String.starts_with?(to_string(value1), to_string(value2))
      "endsWith" -> String.ends_with?(to_string(value1), to_string(value2))
      "notEndsWith" -> not String.ends_with?(to_string(value1), to_string(value2))
      "regex" -> match_regex(to_string(value1), to_string(value2))
      _ -> false
    end
  end

  defp resolve_value(item, value_string) do
    # 简单的值解析，支持JSON路径访问
    # 在实际实现中，这里需要实现完整的表达式解析器
    cond do
      String.starts_with?(value_string, "{{") and String.ends_with?(value_string, "}}") ->
        # 表达式解析
        path = String.slice(value_string, 2..-3) |> String.trim()
        get_nested_value(item, path)
      true ->
        value_string
    end
  end

  defp get_nested_value(data, path) do
    # 支持 "json.field.subfield" 这样的路径
    path_parts = String.split(path, ".")
    
    Enum.reduce(path_parts, data, fn key, acc ->
      case acc do
        %{} -> Map.get(acc, key)
        _ -> nil
      end
    end)
  end

  defp compare_numeric(value1, value2, operator) do
    try do
      num1 = parse_number(value1)
      num2 = parse_number(value2)
      
      case operator do
        :lt -> num1 < num2
        :le -> num1 <= num2
        :gt -> num1 > num2
        :ge -> num1 >= num2
      end
    rescue
      _ -> false
    end
  end

  defp parse_number(value) when is_number(value), do: value
  defp parse_number(value) when is_binary(value) do
    case Float.parse(value) do
      {num, ""} -> num
      _ ->
        case Integer.parse(value) do
          {num, ""} -> num
          _ -> raise "Not a number"
        end
    end
  end
  defp parse_number(_), do: raise "Not a number"

  defp match_regex(text, pattern) do
    case Regex.compile(pattern) do
      {:ok, regex} -> Regex.match?(regex, text)
      {:error, _} -> false
    end
  end
end

defmodule N8nElixir.Nodes.Set do
  @moduledoc """
  Set节点 - 数据设置和转换
  
  参考n8n的Set节点，用于设置、修改和删除JSON数据中的字段
  """
  @behaviour N8nElixir.NodeType

  @impl true
  def description() do
    %{
      displayName: "Set",
      name: "set",
      icon: "fa:pen",
      group: ["transform"],
      version: 1,
      description: "Sets values for JSON object properties",
      defaults: %{
        name: "Set",
        color: "#0000FF"
      },
      inputs: ["main"],
      outputs: ["main"],
      properties: [
        %{
          displayName: "Keep Only Set",
          name: "keepOnlySet",
          type: "boolean",
          default: false,
          description: "If only the values set on this node should be kept and all others removed"
        },
        %{
          displayName: "Values to Set",
          name: "values",
          placeholder: "Add Value",
          type: "fixedCollection",
          typeOptions: %{
            multipleValues: true,
            sortable: true
          },
          description: "The value to set",
          default: %{},
          options: [
            %{
              name: "boolean",
              displayName: "Boolean",
              values: [
                %{
                  displayName: "Name",
                  name: "name",
                  type: "string",
                  default: "propertyName",
                  description: "Name of the property to write data to"
                },
                %{
                  displayName: "Value",
                  name: "value",
                  type: "boolean",
                  default: false,
                  description: "The boolean value to write in the property"
                }
              ]
            },
            %{
              name: "number",
              displayName: "Number",
              values: [
                %{
                  displayName: "Name",
                  name: "name",
                  type: "string",
                  default: "propertyName",
                  description: "Name of the property to write data to"
                },
                %{
                  displayName: "Value",
                  name: "value",
                  type: "number",
                  default: 0,
                  description: "The number value to write in the property"
                }
              ]
            },
            %{
              name: "string",
              displayName: "String",
              values: [
                %{
                  displayName: "Name",
                  name: "name",
                  type: "string",
                  default: "propertyName",
                  description: "Name of the property to write data to"
                },
                %{
                  displayName: "Value",
                  name: "value",
                  type: "string",
                  default: "",
                  description: "The string value to write in the property"
                }
              ]
            }
          ]
        }
      ]
    }
  end

  @impl true
  def execute(input_data, _credentials, parameters) do
    keep_only_set = Map.get(parameters, "keepOnlySet", false)
    values = Map.get(parameters, "values", %{})
    
    result = Enum.map(input_data, fn item ->
      process_item(item, values, keep_only_set)
    end)
    
    {:ok, result}
  end

  @impl true
  def credential_types() do
    []
  end

  defp process_item(item, values, keep_only_set) do
    base_item = if keep_only_set do
      %{"json" => %{}}
    else
      item
    end
    
    # 处理不同类型的值设置
    updated_json = process_value_types(base_item["json"] || %{}, values)
    
    %{item | "json" => updated_json}
  end

  defp process_value_types(json_data, values) do
    # 处理布尔值
    boolean_values = Map.get(values, "boolean", [])
    json_after_boolean = Enum.reduce(boolean_values, json_data, fn value_def, acc ->
      name = Map.get(value_def, "name", "")
      value = Map.get(value_def, "value", false)
      if name != "" do
        set_nested_value(acc, name, value)
      else
        acc
      end
    end)
    
    # 处理数字值
    number_values = Map.get(values, "number", [])
    json_after_number = Enum.reduce(number_values, json_after_boolean, fn value_def, acc ->
      name = Map.get(value_def, "name", "")
      value = Map.get(value_def, "value", 0)
      if name != "" do
        set_nested_value(acc, name, value)
      else
        acc
      end
    end)
    
    # 处理字符串值
    string_values = Map.get(values, "string", [])
    Enum.reduce(string_values, json_after_number, fn value_def, acc ->
      name = Map.get(value_def, "name", "")
      value = Map.get(value_def, "value", "")
      if name != "" do
        set_nested_value(acc, name, value)
      else
        acc
      end
    end)
  end

  defp set_nested_value(data, path, value) when is_map(data) do
    case String.split(path, ".", parts: 2) do
      [key] ->
        Map.put(data, key, value)
      [key, rest] ->
        nested_value = Map.get(data, key, %{})
        updated_nested = set_nested_value(nested_value, rest, value)
        Map.put(data, key, updated_nested)
    end
  end
  defp set_nested_value(data, _path, _value), do: data
end
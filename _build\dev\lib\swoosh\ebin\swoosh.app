{application,swoosh,
             [{modules,['Elixir.Mix.Tasks.Swoosh.Mailbox.Server',
                        'Elixir.Plug.Swoosh.MailboxPreview','Elixir.Swoosh',
                        'Elixir.Swoosh.Adapter',
                        'Elixir.Swoosh.Adapters.AmazonSES',
                        'Elixir.Swoosh.Adapters.Brevo',
                        'Elixir.Swoosh.Adapters.CustomerIO',
                        'Elixir.Swoosh.Adapters.Dyn',
                        'Elixir.Swoosh.Adapters.ExAwsAmazonSES',
                        'Elixir.Swoosh.Adapters.Gmail',
                        'Elixir.Swoosh.Adapters.Local',
                        'Elixir.Swoosh.Adapters.Local.Storage.Manager',
                        'Elixir.Swoosh.Adapters.Local.Storage.Memory',
                        'Elixir.Swoosh.Adapters.Logger',
                        'Elixir.Swoosh.Adapters.Loops',
                        'Elixir.Swoosh.Adapters.MailPace',
                        'Elixir.Swoosh.Adapters.Mailgun',
                        'Elixir.Swoosh.Adapters.Mailjet',
                        'Elixir.Swoosh.Adapters.Mailtrap',
                        'Elixir.Swoosh.Adapters.Mandrill',
                        'Elixir.Swoosh.Adapters.MsGraph',
                        'Elixir.Swoosh.Adapters.Mua',
                        'Elixir.Swoosh.Adapters.Mua.MultihostError',
                        'Elixir.Swoosh.Adapters.OhMySmtp',
                        'Elixir.Swoosh.Adapters.PostUp',
                        'Elixir.Swoosh.Adapters.Postal',
                        'Elixir.Swoosh.Adapters.Postmark',
                        'Elixir.Swoosh.Adapters.ProtonBridge',
                        'Elixir.Swoosh.Adapters.SMTP',
                        'Elixir.Swoosh.Adapters.SMTP.Helpers',
                        'Elixir.Swoosh.Adapters.SMTP2GO',
                        'Elixir.Swoosh.Adapters.Scaleway',
                        'Elixir.Swoosh.Adapters.Sendgrid',
                        'Elixir.Swoosh.Adapters.Sendinblue',
                        'Elixir.Swoosh.Adapters.Sendmail',
                        'Elixir.Swoosh.Adapters.SocketLabs',
                        'Elixir.Swoosh.Adapters.SparkPost',
                        'Elixir.Swoosh.Adapters.Test',
                        'Elixir.Swoosh.Adapters.XML.Helpers',
                        'Elixir.Swoosh.Adapters.ZeptoMail',
                        'Elixir.Swoosh.ApiClient',
                        'Elixir.Swoosh.ApiClient.Finch',
                        'Elixir.Swoosh.ApiClient.Hackney',
                        'Elixir.Swoosh.ApiClient.Req',
                        'Elixir.Swoosh.Application',
                        'Elixir.Swoosh.Attachment',
                        'Elixir.Swoosh.AttachmentContentError',
                        'Elixir.Swoosh.DeliveryError','Elixir.Swoosh.Email',
                        'Elixir.Swoosh.Email.Recipient',
                        'Elixir.Swoosh.Email.Recipient.Any',
                        'Elixir.Swoosh.Email.Recipient.BitString',
                        'Elixir.Swoosh.Email.Recipient.Tuple',
                        'Elixir.Swoosh.Email.Render','Elixir.Swoosh.Mailer',
                        'Elixir.Swoosh.TestAssertions',
                        'Elixir.Swoosh.X.TestAssertions']},
              {optional_applications,[hackney,finch,req,mail,gen_smtp,mua,
                                      cowboy,plug,plug_cowboy,bandit,
                                      multipart,ex_aws]},
              {applications,[kernel,stdlib,elixir,logger,xmerl,mime,jason,
                             telemetry,hackney,finch,req,mail,gen_smtp,mua,
                             cowboy,plug,plug_cowboy,bandit,multipart,ex_aws]},
              {description,"Compose, deliver and test your emails easily in Elixir. Supports SMTP,\nSendgrid, Mandrill, Postmark, Mailgun and many more out of the box.\nPreview your emails in the browser. Test your email sending code.\n"},
              {registered,[]},
              {vsn,"1.19.3"},
              {mod,{'Elixir.Swoosh.Application',[]}},
              {env,[{json_library,'Elixir.Jason'},
                    {api_client,'Elixir.Swoosh.ApiClient.Hackney'}]}]}.

{<<"links">>,
 [{<<"GitHub">>,<<"https://github.com/elixir-makeup/makeup_elixir">>}]}.
{<<"name">>,<<"makeup_elixir">>}.
{<<"version">>,<<"1.0.1">>}.
{<<"description">>,<<"Elixir lexer for the Makeup syntax highlighter.">>}.
{<<"elixir">>,<<"~> 1.12">>}.
{<<"app">>,<<"makeup_elixir">>}.
{<<"licenses">>,[<<"BSD-2-Clause">>]}.
{<<"requirements">>,
 [[{<<"name">>,<<"makeup">>},
   {<<"app">>,<<"makeup">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 1.0">>},
   {<<"repository">>,<<"hexpm">>}],
  [{<<"name">>,<<"nimble_parsec">>},
   {<<"app">>,<<"nimble_parsec">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 1.2.3 or ~> 1.3">>},
   {<<"repository">>,<<"hexpm">>}]]}.
{<<"files">>,
 [<<"lib">>,<<"lib/makeup">>,<<"lib/makeup/lexers">>,
  <<"lib/makeup/lexers/elixir_lexer.ex">>,
  <<"lib/makeup/lexers/elixir_lexer">>,
  <<"lib/makeup/lexers/elixir_lexer/atoms.ex.exs">>,
  <<"lib/makeup/lexers/elixir_lexer/variables.ex">>,
  <<"lib/makeup/lexers/elixir_lexer/atoms.ex">>,
  <<"lib/makeup/lexers/elixir_lexer/testing.ex">>,
  <<"lib/makeup/lexers/elixir_lexer/variables.ex.exs">>,
  <<"lib/makeup/lexers/elixir_lexer/application.ex">>,
  <<"lib/makeup/lexers/elixir_lexer/helper.ex">>,<<".formatter.exs">>,
  <<"mix.exs">>,<<"README.md">>,<<"LICENSE">>,<<"CHANGELOG.md">>]}.
{<<"build_tools">>,[<<"mix">>]}.

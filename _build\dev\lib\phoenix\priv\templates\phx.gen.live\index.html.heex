<.header>
  Listing <%= schema.human_plural %>
  <:actions>
    <.link patch={~p"<%= schema.route_prefix %>/new"}>
      <.button>New <%= schema.human_singular %></.button>
    </.link>
  </:actions>
</.header>

<.table
  id="<%= schema.plural %>"
  rows={@streams.<%= schema.collection %>}
  row_click={fn {_id, <%= schema.singular %>} -> JS.navigate(~p"<%= schema.route_prefix %>/#{<%= schema.singular %>}") end}
><%= for {k, _} <- schema.attrs do %>
  <:col :let={{_id, <%= schema.singular %>}} label="<%= Phoenix.Naming.humanize(Atom.to_string(k)) %>">{<%= schema.singular %>.<%= k %>}</:col><% end %>
  <:action :let={{_id, <%= schema.singular %>}}>
    <div class="sr-only">
      <.link navigate={~p"<%= schema.route_prefix %>/#{<%= schema.singular %>}"}>Show</.link>
    </div>
    <.link patch={~p"<%= schema.route_prefix %>/#{<%= schema.singular %>}/edit"}>Edit</.link>
  </:action>
  <:action :let={{id, <%= schema.singular %>}}>
    <.link
      phx-click={JS.push("delete", value: %{id: <%= schema.singular %>.id}) |> hide("##{id}")}
      data-confirm="Are you sure?"
    >
      Delete
    </.link>
  </:action>
</.table>

<.modal :if={@live_action in [:new, :edit]} id="<%= schema.singular %>-modal" show on_cancel={JS.patch(~p"<%= schema.route_prefix %>")}>
  <.live_component
    module={<%= inspect context.web_module %>.<%= inspect Module.concat(schema.web_namespace, schema.alias) %>Live.FormComponent}
    id={@<%= schema.singular %>.id || :new}
    title={@page_title}
    action={@live_action}
    <%= schema.singular %>={@<%= schema.singular %>}
    patch={~p"<%= schema.route_prefix %>"}
  />
</.modal>

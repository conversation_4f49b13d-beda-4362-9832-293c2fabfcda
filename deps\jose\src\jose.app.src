%% -*- mode: erlang; tab-width: 4; indent-tabs-mode: 1; st-rulers: [70] -*-
%% vim: ts=4 sw=4 ft=erlang noet
{application, jose, [
	{description, "JSON Object Signing and Encryption (JOSE) for Erlang and Elixir."},
	{vsn, "1.11.10"},
	{id, "git"},
	{mod, {'jose_app', []}},
	{registered, []},
	{applications, [
		kernel,
		stdlib,
		crypto,
		asn1,
		public_key
	]},
	{modules, []},
	{maintainers, ["<PERSON> Bennett"]},
	{licenses, ["MIT"]},
	{links, [{"Github", "https://github.com/potatosalad/erlang-jose"}]},
	{env, [
		{crypto_fallback, true},
		{pbes2_count_maximum, 10000}
	]}
]}.


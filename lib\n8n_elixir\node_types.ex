defmodule N8nElixir.NodeType do
  @moduledoc """
  节点类型行为定义
  
  参考n8n的INodeType接口，所有节点模块都必须实现这个 behaviour
  这个 behaviour 定义了节点的标准接口
  """
  
  @type node_config :: map()
  @type input_data :: map()
  @type output_data :: map()
  @type credentials :: map()
  @type node_state :: any()
  @type execute_result :: {:ok, output_data} | {:error, String.t()}
  @type parameter_definition :: %{
    name: String.t(),
    type: String.t(),
    required: boolean(),
    default: any(),
    description: String.t(),
    options: list() | nil
  }

  @doc """
  返回节点的描述信息 - 类似n8n的INodeTypeDescription
  """
  @callback description() :: map()

  @doc """
  执行节点的核心逻辑 - 类似n8n的execute方法
  
  这是节点的主要执行函数，处理输入数据并返回输出数据
  """
  @callback execute(input_data :: list(map()), credentials :: map(), parameters :: map()) :: 
    {:ok, list(map())} | {:error, String.t()}

  @doc """
  返回节点支持的凭证类型
  """
  @callback credential_types() :: [String.t()]

  @doc """
  测试节点连接 - 用于验证配置和凭证
  """
  @callback test_connection(credentials :: map(), parameters :: map()) :: 
    :ok | {:error, String.t()}

  @optional_callbacks [test_connection: 2]
end

defmodule N8nElixir.NodeTypes do
  @moduledoc """
  节点类型管理器
  
  参考n8n的NodeTypes类，管理所有可用的节点类型，提供节点发现和实例化功能
  """
  use GenServer

  @registry_name __MODULE__

  # 客户端API
  def start_link(init_arg) do
    GenServer.start_link(__MODULE__, init_arg, name: @registry_name)
  end

  def register_node(node_type, node_module) do
    GenServer.call(@registry_name, {:register_node, node_type, node_module})
  end

  def get_node_type(node_type) do
    GenServer.call(@registry_name, {:get_node_type, node_type})
  end

  def get_node_by_name_and_version(node_type, version \\ 1) do
    GenServer.call(@registry_name, {:get_node_by_name_and_version, node_type, version})
  end

  def get_all_node_types() do
    GenServer.call(@registry_name, :get_all_node_types)
  end

  def get_node_description(node_type) do
    GenServer.call(@registry_name, {:get_node_description, node_type})
  end

  def get_nodes_by_category(category) do
    GenServer.call(@registry_name, {:get_nodes_by_category, category})
  end

  def reload_node_types() do
    GenServer.call(@registry_name, :reload_node_types)
  end

  # 服务器回调
  @impl true
  def init(_init_arg) do
    # 启动时自动注册所有内置节点
    nodes = discover_and_register_nodes()
    
    state = %{
      nodes: nodes,
      node_descriptions: %{},
      categories: %{}
    }

    # 缓存节点描述信息
    updated_state = cache_node_descriptions(state)

    {:ok, updated_state}
  end

  @impl true
  def handle_call({:register_node, node_type, node_module}, _from, state) do
    case Code.ensure_loaded(node_module) do
      {:module, _} ->
        if function_exported?(node_module, :execute, 3) do
          updated_nodes = Map.put(state.nodes, node_type, node_module)
          # 缓存节点描述
          description = safe_get_description(node_module)
          updated_descriptions = Map.put(state.node_descriptions, node_type, description)
          
          new_state = %{state | 
            nodes: updated_nodes, 
            node_descriptions: updated_descriptions
          }
          {:reply, :ok, new_state}
        else
          {:reply, {:error, "Module must implement execute/3"}, state}
        end
      {:error, reason} ->
        {:reply, {:error, "Failed to load module: #{reason}"}, state}
    end
  end

  @impl true
  def handle_call({:get_node_type, node_type}, _from, state) do
    case Map.get(state.nodes, node_type) do
      nil -> {:reply, {:error, "Node type not found: #{node_type}"}, state}
      module -> {:reply, {:ok, module}, state}
    end
  end

  @impl true
  def handle_call({:get_node_by_name_and_version, node_type, _version}, _from, state) do
    # 目前简化版本处理，后续可以扩展版本支持
    case Map.get(state.nodes, node_type) do
      nil -> {:reply, nil, state}
      module -> {:reply, module, state}
    end
  end

  @impl true
  def handle_call(:get_all_node_types, _from, state) do
    node_list = Enum.map(state.nodes, fn {type, module} ->
      description = Map.get(state.node_descriptions, type, %{})
      Map.put(description, :type, type)
    end)
    
    {:reply, node_list, state}
  end

  @impl true
  def handle_call({:get_node_description, node_type}, _from, state) do
    case Map.get(state.node_descriptions, node_type) do
      nil -> {:reply, {:error, "Node type not found: #{node_type}"}, state}
      description -> {:reply, {:ok, description}, state}
    end
  end

  @impl true
  def handle_call({:get_nodes_by_category, category}, _from, state) do
    nodes = Enum.filter(state.node_descriptions, fn {_type, description} ->
      Map.get(description, :group, []) |> Enum.any?(fn group -> group == category end)
    end)
    
    {:reply, nodes, state}
  end

  @impl true
  def handle_call(:reload_node_types, _from, _state) do
    # 重新发现和注册节点
    nodes = discover_and_register_nodes()
    
    new_state = %{
      nodes: nodes,
      node_descriptions: %{},
      categories: %{}
    }

    updated_state = cache_node_descriptions(new_state)
    
    {:reply, :ok, updated_state}
  end

  # 私有辅助函数
  defp discover_and_register_nodes() do
    # 扫描并注册所有节点模块
    base_modules = [
      {"HttpRequest", N8nElixir.Nodes.HttpRequest},
      {"Code", N8nElixir.Nodes.Code},
      {"Webhook", N8nElixir.Nodes.Webhook},
      {"If", N8nElixir.Nodes.If},
      {"Switch", N8nElixir.Nodes.Switch},
      {"Merge", N8nElixir.Nodes.Merge},
      {"Split", N8nElixir.Nodes.Split},
      {"Set", N8nElixir.Nodes.Set},
      {"Function", N8nElixir.Nodes.Function},
      {"FunctionItem", N8nElixir.Nodes.FunctionItem},
      {"Schedule Trigger", N8nElixir.Nodes.ScheduleTrigger},
      {"Manual Trigger", N8nElixir.Nodes.ManualTrigger},
      {"Wait", N8nElixir.Nodes.Wait},
      # 二进制数据处理节点
      {"ReadBinaryFile", N8nElixir.Nodes.ReadBinaryFile},
      {"WriteBinaryFile", N8nElixir.Nodes.WriteBinaryFile},
      {"ConvertBinaryData", N8nElixir.Nodes.ConvertBinaryData},
      {"HttpBinary", N8nElixir.Nodes.HttpBinary}
    ]

    Enum.reduce(base_modules, %{}, fn {type, module}, acc ->
      case Code.ensure_loaded(module) do
        {:module, _} -> Map.put(acc, type, module)
        _ -> acc
      end
    end)
  end

  defp cache_node_descriptions(state) do
    descriptions = Enum.reduce(state.nodes, %{}, fn {type, module}, acc ->
      description = safe_get_description(module)
      Map.put(acc, type, description)
    end)

    %{state | node_descriptions: descriptions}
  end

  defp safe_get_description(module) do
    if function_exported?(module, :description, 0) do
      try do
        module.description()
      rescue
        _ -> %{displayName: "Unknown", name: "unknown", description: "Node description not available"}
      end
    else
      %{displayName: "Unknown", name: "unknown", description: "Node description not available"}
    end
  end
end

defmodule N8nElixir.Nodes.HttpRequest do
  @moduledoc """
  HTTP请求节点
  
  参考n8n的HttpRequest节点实现，支持GET、POST、PUT、DELETE等HTTP方法
  可以发送JSON、表单数据等格式的请求
  """
  @behaviour N8nElixir.NodeType

  @impl true
  def description() do
    %{
      displayName: "HTTP Request",
      name: "httpRequest",
      icon: "fa:globe",
      group: ["output"],
      version: 3,
      subtitle: "={{$parameter.method + \": \" + $parameter.url}}",
      description: "Makes an HTTP request and returns the response data",
      defaults: %{
        name: "HTTP Request",
        color: "#0004F5"
      },
      inputs: ["main"],
      outputs: ["main"],
      credentials: [
        %{
          name: "httpBasicAuth",
          required: false
        },
        %{
          name: "httpDigestAuth", 
          required: false
        },
        %{
          name: "httpHeaderAuth",
          required: false
        },
        %{
          name: "oAuth1Api",
          required: false
        },
        %{
          name: "oAuth2Api",
          required: false
        }
      ],
      properties: [
        %{
          displayName: "Method",
          name: "method",
          type: "options",
          options: [
            %{name: "GET", value: "GET"},
            %{name: "POST", value: "POST"},
            %{name: "PUT", value: "PUT"},
            %{name: "PATCH", value: "PATCH"},
            %{name: "DELETE", value: "DELETE"},
            %{name: "HEAD", value: "HEAD"},
            %{name: "OPTIONS", value: "OPTIONS"}
          ],
          default: "GET",
          description: "The request method to use"
        },
        %{
          displayName: "URL",
          name: "url",
          type: "string",
          default: "",
          placeholder: "http://example.com/index.html",
          description: "The URL to make the request to",
          required: true
        },
        %{
          displayName: "Response Format",
          name: "responseFormat",
          type: "options",
          options: [
            %{name: "Autodetect", value: "autodetect"},
            %{name: "JSON", value: "json"},
            %{name: "String", value: "string"},
            %{name: "Binary", value: "binary"}
          ],
          default: "autodetect",
          description: "The format in which the response should be returned"
        },
        %{
          displayName: "Parameters",
          name: "parameters",
          placeholder: "Add Parameter",
          type: "fixedCollection",
          typeOptions: %{
            multipleValues: true
          },
          description: "The query parameters to send",
          default: %{},
          options: [
            %{
              name: "parameter",
              displayName: "Parameter",
              values: [
                %{
                  displayName: "Name",
                  name: "name", 
                  type: "string",
                  default: "",
                  description: "Name of the parameter"
                },
                %{
                  displayName: "Value",
                  name: "value",
                  type: "string", 
                  default: "",
                  description: "Value to set for the parameter"
                }
              ]
            }
          ]
        },
        %{
          displayName: "Headers",
          name: "headers", 
          placeholder: "Add Header",
          type: "fixedCollection",
          typeOptions: %{
            multipleValues: true
          },
          description: "The headers to send",
          default: %{},
          options: [
            %{
              name: "parameter",
              displayName: "Header",
              values: [
                %{
                  displayName: "Name",
                  name: "name",
                  type: "string", 
                  default: "",
                  description: "Name of the header"
                },
                %{
                  displayName: "Value", 
                  name: "value",
                  type: "string",
                  default: "",
                  description: "Value to set for the header"
                }
              ]
            }
          ]
        },
        %{
          displayName: "Body",
          name: "body",
          type: "json",
          displayOptions: %{
            show: %{
              method: ["POST", "PUT", "PATCH"]
            }
          },
          default: "",
          description: "The body to send with the request"
        }
      ]
    }
  end

  @impl true
  def execute(input_data, credentials, parameters) do
    method = Map.get(parameters, "method", "GET")
    url = Map.get(parameters, "url", "")
    headers = build_headers(parameters, credentials)
    body = build_body(input_data, parameters)
    
    if url == "" do
      {:error, "URL parameter is required"}
    else
      case make_http_request(method, url, headers, body, parameters) do
        {:ok, response} ->
          formatted_response = format_response(response, parameters)
          {:ok, [formatted_response]}
        {:error, reason} ->
          {:error, "HTTP request failed: #{inspect(reason)}"}
      end
    end
  end

  @impl true
  def credential_types() do
    ["httpBasicAuth", "httpDigestAuth", "httpHeaderAuth", "oAuth1Api", "oAuth2Api"]
  end

  @impl true
  def test_connection(credentials, parameters) do
    url = Map.get(parameters, "url", "")
    
    if url == "" do
      {:error, "URL is required for connection test"}
    else
      case make_http_request("HEAD", url, %{}, "", %{}) do
        {:ok, _response} -> :ok
        {:error, reason} -> {:error, "Connection test failed: #{inspect(reason)}"}
      end
    end
  end

  # 私有辅助函数
  defp build_headers(parameters, credentials) do
    base_headers = %{"User-Agent" => "n8n-elixir/1.0"}
    
    # 添加用户定义的头部
    parameter_headers = case Map.get(parameters, "headers") do
      %{"parameter" => header_list} when is_list(header_list) ->
        Enum.reduce(header_list, %{}, fn header, acc ->
          name = Map.get(header, "name", "")
          value = Map.get(header, "value", "")
          if name != "" do
            Map.put(acc, name, value)
          else
            acc
          end
        end)
      _ -> %{}
    end
    
    # 添加认证头部
    auth_headers = add_authentication_headers(credentials)
    
    Map.merge(base_headers, parameter_headers) |> Map.merge(auth_headers)
  end

  defp add_authentication_headers(credentials) do
    case Map.get(credentials, "type") do
      "httpBasicAuth" ->
        username = Map.get(credentials, "username", "")
        password = Map.get(credentials, "password", "")
        auth_string = Base.encode64("#{username}:#{password}")
        %{"Authorization" => "Basic #{auth_string}"}
      
      "httpHeaderAuth" ->
        header_name = Map.get(credentials, "name", "Authorization")
        header_value = Map.get(credentials, "value", "")
        %{header_name => header_value}
      
      _ -> %{}
    end
  end

  defp build_body(input_data, parameters) do
    method = Map.get(parameters, "method", "GET")
    
    if method in ["POST", "PUT", "PATCH"] do
      case Map.get(parameters, "body") do
        nil -> 
          # 如果没有指定body，使用输入数据
          if length(input_data) > 0 do
            Jason.encode!(List.first(input_data))
          else
            ""
          end
        body_content when is_binary(body_content) ->
          body_content
        body_content ->
          Jason.encode!(body_content)
      end
    else
      ""
    end
  end

  defp make_http_request(method, url, headers, body, parameters) do
    # 构建查询参数
    query_params = build_query_params(parameters)
    final_url = if query_params != "", do: "#{url}?#{query_params}", else: url
    
    method_atom = String.downcase(method) |> String.to_atom()
    
    request_options = [
      timeout: 30_000,
      recv_timeout: 30_000
    ]

    case HTTPoison.request(method_atom, final_url, body, headers, request_options) do
      {:ok, response} ->
        {:ok, %{
          status_code: response.status_code,
          headers: response.headers,
          body: response.body
        }}
      {:error, reason} ->
        {:error, reason}
    end
  end

  defp build_query_params(parameters) do
    case Map.get(parameters, "parameters") do
      %{"parameter" => param_list} when is_list(param_list) ->
        params = Enum.reduce(param_list, %{}, fn param, acc ->
          name = Map.get(param, "name", "")
          value = Map.get(param, "value", "")
          if name != "" do
            Map.put(acc, name, value)
          else
            acc
          end
        end)
        URI.encode_query(params)
      _ -> ""
    end
  end

  defp format_response(response, parameters) do
    response_format = Map.get(parameters, "responseFormat", "autodetect")
    
    formatted_body = case response_format do
      "json" ->
        try do
          Jason.decode!(response.body)
        rescue
          _ -> response.body
        end
      "string" ->
        response.body
      "binary" ->
        %{
          data: Base.encode64(response.body),
          mimeType: get_content_type(response.headers),
          fileName: "response"
        }
      "autodetect" ->
        autodetect_format(response.body, response.headers)
    end

    %{
      json: formatted_body,
      headers: Map.new(response.headers),
      statusCode: response.status_code
    }
  end

  defp get_content_type(headers) do
    headers
    |> Enum.find(fn {key, _value} -> String.downcase(key) == "content-type" end)
    |> case do
      {_key, value} -> value
      nil -> "application/octet-stream"
    end
  end

  defp autodetect_format(body, headers) do
    content_type = get_content_type(headers)
    
    cond do
      String.contains?(content_type, "application/json") ->
        try do
          Jason.decode!(body)
        rescue
          _ -> body
        end
      String.contains?(content_type, "text/") ->
        body
      true ->
        # 尝试解析为JSON，失败则返回原始字符串
        try do
          Jason.decode!(body)
        rescue
          _ -> body
        end
    end
  end
end
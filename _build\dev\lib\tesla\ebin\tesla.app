{application,tesla,
             [{modules,['Elixir.Tesla','Elixir.Tesla.Adapter',
                        'Elixir.Tesla.Adapter.Finch',
                        'Elixir.Tesla.Adapter.Hackney',
                        'Elixir.Tesla.Adapter.Httpc',
                        'Elixir.Tesla.Adapter.Mint',
                        'Elixir.Tesla.Adapter.Shared','Elixir.Tesla.Builder',
                        'Elixir.Tesla.Client','Elixir.Tesla.Env',
                        'Elixir.Tesla.Error','Elixir.Tesla.Middleware',
                        'Elixir.Tesla.Middleware.BaseUrl',
                        'Elixir.Tesla.Middleware.BasicAuth',
                        'Elixir.Tesla.Middleware.BearerAuth',
                        'Elixir.Tesla.Middleware.CompressRequest',
                        'Elixir.Tesla.Middleware.Compression',
                        'Elixir.Tesla.Middleware.DecodeFormUrlencoded',
                        'Elixir.Tesla.Middleware.DecodeJson',
                        'Elixir.Tesla.Middleware.DecodeRels',
                        'Elixir.Tesla.Middleware.DecompressResponse',
                        'Elixir.Tesla.Middleware.DigestAuth',
                        'Elixir.Tesla.Middleware.EncodeFormUrlencoded',
                        'Elixir.Tesla.Middleware.EncodeJson',
                        'Elixir.Tesla.Middleware.FollowRedirects',
                        'Elixir.Tesla.Middleware.FormUrlencoded',
                        'Elixir.Tesla.Middleware.Headers',
                        'Elixir.Tesla.Middleware.JSON',
                        'Elixir.Tesla.Middleware.JSON.JSONAdapter',
                        'Elixir.Tesla.Middleware.KeepRequest',
                        'Elixir.Tesla.Middleware.Logger',
                        'Elixir.Tesla.Middleware.Logger.Formatter',
                        'Elixir.Tesla.Middleware.MethodOverride',
                        'Elixir.Tesla.Middleware.Opts',
                        'Elixir.Tesla.Middleware.PathParams',
                        'Elixir.Tesla.Middleware.Query',
                        'Elixir.Tesla.Middleware.Retry',
                        'Elixir.Tesla.Middleware.SSE',
                        'Elixir.Tesla.Middleware.Telemetry',
                        'Elixir.Tesla.Middleware.Timeout','Elixir.Tesla.Mock',
                        'Elixir.Tesla.Mock.Error','Elixir.Tesla.Multipart',
                        'Elixir.Tesla.Multipart.Part','Elixir.Tesla.Test']},
              {compile_env,[{tesla,['Elixir.Tesla.Middleware.Logger'],error},
                            {tesla,['Elixir.Tesla.Middleware.Telemetry',
                                    disable_legacy_event],
                                   error}]},
              {optional_applications,[ibrowse,hackney,gun,finch,castore,mint,
                                      jason,poison,exjsx,msgpax,fuse,
                                      telemetry,mox]},
              {applications,[kernel,stdlib,elixir,logger,ssl,inets,mime,
                             ibrowse,hackney,gun,finch,castore,mint,jason,
                             poison,exjsx,msgpax,fuse,telemetry,mox]},
              {description,"HTTP client library, with support for middleware and multiple adapters."},
              {registered,[]},
              {vsn,"1.14.3"}]}.

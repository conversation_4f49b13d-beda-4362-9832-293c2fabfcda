defmodule N8nElixir.CredentialManager do
  @moduledoc """
  凭证管理器

  安全管理和存储工作流凭证
  """

  use GenServer
  require Logger

  @registry_name __MODULE__

  def start_link(init_arg) do
    GenServer.start_link(__MODULE__, init_arg, name: @registry_name)
  end

  @impl true
  def init(_init_arg) do
    Logger.info("Starting CredentialManager")
    {:ok, %{}}
  end

  @impl true
  def handle_call(_msg, _from, state) do
    {:reply, :ok, state}
  end

  @impl true
  def handle_cast(_msg, state) do
    {:noreply, state}
  end
end
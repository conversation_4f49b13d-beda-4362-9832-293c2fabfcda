defmodule N8nElixir.ErrorHandler do
  @moduledoc """
  错误处理器

  参考n8n的错误处理机制，提供全面的错误捕获、分类和处理功能
  支持错误恢复、重试策略和错误报告
  """

  require Logger

  @type error_type :: :node_error | :workflow_error | :system_error | :validation_error | :timeout_error
  @type error_severity :: :low | :medium | :high | :critical
  @type retry_strategy :: :none | :fixed_delay | :exponential_backoff | :linear_backoff

  @doc """
  处理节点执行错误

  捕获和处理节点执行过程中的错误
  """
  def handle_node_error(error, context) do
    error_info = classify_error(error, context)
    
    # 记录错误
    log_error(error_info)
    
    # 根据错误类型和配置决定处理策略
    case determine_error_strategy(error_info, context) do
      :continue ->
        {:continue, format_error_for_continuation(error_info)}
      
      :retry ->
        {:retry, error_info}
      
      :fail ->
        {:fail, error_info}
      
      :skip ->
        {:skip, error_info}
    end
  end

  @doc """
  处理工作流级别错误

  处理影响整个工作流的错误
  """
  def handle_workflow_error(error, workflow_state) do
    error_info = %{
      type: :workflow_error,
      message: extract_error_message(error),
      original_error: error,
      workflow_id: workflow_state.workflow_id,
      execution_id: workflow_state.execution_id,
      timestamp: DateTime.utc_now(),
      context: sanitize_context(workflow_state)
    }
    
    # 记录错误
    log_error(error_info)
    
    # 尝试错误恢复
    case attempt_error_recovery(error_info, workflow_state) do
      {:ok, recovered_state} ->
        {:recovered, recovered_state}
      
      {:error, _reason} ->
        {:failed, error_info}
    end
  end

  @doc """
  实施重试策略

  根据配置的重试策略执行重试逻辑
  """
  def execute_retry_strategy(retry_config, error_context, attempt_count \\ 1) do
    strategy = Map.get(retry_config, :strategy, :none)
    max_attempts = Map.get(retry_config, :max_attempts, 3)
    
    if attempt_count > max_attempts do
      {:max_attempts_reached, error_context}
    else
      case strategy do
        :none ->
          {:no_retry, error_context}
        
        :fixed_delay ->
          execute_fixed_delay_retry(retry_config, error_context, attempt_count)
        
        :exponential_backoff ->
          execute_exponential_backoff_retry(retry_config, error_context, attempt_count)
        
        :linear_backoff ->
          execute_linear_backoff_retry(retry_config, error_context, attempt_count)
        
        _ ->
          {:unknown_strategy, error_context}
      end
    end
  end

  @doc """
  创建错误报告

  生成详细的错误报告用于调试和监控
  """
  def create_error_report(error_info, additional_context \\ %{}) do
    %{
      error_id: UUID.uuid4(),
      timestamp: DateTime.utc_now(),
      error_type: Map.get(error_info, :type, :unknown),
      severity: Map.get(error_info, :severity, :medium),
      message: Map.get(error_info, :message, "Unknown error"),
      stack_trace: Map.get(error_info, :stack_trace),
      context: Map.merge(Map.get(error_info, :context, %{}), additional_context),
      workflow_info: extract_workflow_info(error_info),
      node_info: extract_node_info(error_info),
      execution_info: extract_execution_info(error_info),
      system_info: get_system_info(),
      retry_attempts: Map.get(error_info, :retry_attempts, 0),
      recovery_attempted: Map.get(error_info, :recovery_attempted, false)
    }
  end

  @doc """
  检查错误恢复可能性

  评估是否可以从错误中恢复
  """
  def can_recover_from_error?(error_info) do
    case error_info.type do
      :node_error ->
        check_node_error_recovery(error_info)
      
      :workflow_error ->
        check_workflow_error_recovery(error_info)
      
      :system_error ->
        check_system_error_recovery(error_info)
      
      :validation_error ->
        false  # 验证错误通常无法自动恢复
      
      :timeout_error ->
        true   # 超时错误可以通过重试恢复
      
      _ ->
        false
    end
  end

  # 私有实现函数

  defp classify_error(error, context) do
    {error_type, severity} = 
      case error do
        %{__struct__: struct_name} = structured_error ->
          classify_structured_error(structured_error, struct_name)
        
        {:timeout, _} ->
          {:timeout_error, :medium}
        
        {:error, {:validation, _}} ->
          {:validation_error, :low}
        
        {:error, reason} when is_binary(reason) ->
          classify_text_error(reason)
        
        _ ->
          {:system_error, :high}
      end
    
    %{
      type: error_type,
      severity: severity,
      message: extract_error_message(error),
      original_error: error,
      timestamp: DateTime.utc_now(),
      context: context,
      stack_trace: get_stack_trace(error),
      node_name: Map.get(context, :node_name),
      workflow_id: Map.get(context, :workflow_id),
      execution_id: Map.get(context, :execution_id)
    }
  end

  defp classify_structured_error(error, struct_name) do
    case struct_name do
      Ecto.ChangesetError -> {:validation_error, :low}
      Jason.DecodeError -> {:node_error, :medium}
      HTTPoison.Error -> {:node_error, :medium}
      RuntimeError -> {:system_error, :high}
      _ -> {:system_error, :medium}
    end
  end

  defp classify_text_error(reason) do
    cond do
      String.contains?(reason, "timeout") -> {:timeout_error, :medium}
      String.contains?(reason, "connection") -> {:node_error, :medium}
      String.contains?(reason, "validation") -> {:validation_error, :low}
      String.contains?(reason, "permission") -> {:system_error, :high}
      true -> {:node_error, :medium}
    end
  end

  defp determine_error_strategy(error_info, context) do
    # 获取节点或工作流的错误处理配置
    error_config = get_error_config(context)
    
    case error_info.severity do
      :critical ->
        :fail
      
      :high ->
        if Map.get(error_config, :continue_on_fail, false) do
          :continue
        else
          :fail
        end
      
      :medium ->
        cond do
          Map.get(error_config, :retry_on_fail, false) -> :retry
          Map.get(error_config, :continue_on_fail, false) -> :continue
          true -> :fail
        end
      
      :low ->
        if Map.get(error_config, :skip_on_minor_error, true) do
          :skip
        else
          :continue
        end
    end
  end

  defp execute_fixed_delay_retry(retry_config, error_context, attempt_count) do
    delay = Map.get(retry_config, :delay, 1000)
    
    Logger.info("Retrying with fixed delay: #{delay}ms (attempt #{attempt_count})")
    :timer.sleep(delay)
    
    {:retry_now, %{error_context | retry_attempts: attempt_count}}
  end

  defp execute_exponential_backoff_retry(retry_config, error_context, attempt_count) do
    base_delay = Map.get(retry_config, :base_delay, 1000)
    max_delay = Map.get(retry_config, :max_delay, 30_000)
    
    delay = min(base_delay * :math.pow(2, attempt_count - 1), max_delay) |> trunc()
    
    Logger.info("Retrying with exponential backoff: #{delay}ms (attempt #{attempt_count})")
    :timer.sleep(delay)
    
    {:retry_now, %{error_context | retry_attempts: attempt_count}}
  end

  defp execute_linear_backoff_retry(retry_config, error_context, attempt_count) do
    base_delay = Map.get(retry_config, :base_delay, 1000)
    increment = Map.get(retry_config, :increment, 1000)
    
    delay = base_delay + (attempt_count - 1) * increment
    
    Logger.info("Retrying with linear backoff: #{delay}ms (attempt #{attempt_count})")
    :timer.sleep(delay)
    
    {:retry_now, %{error_context | retry_attempts: attempt_count}}
  end

  defp attempt_error_recovery(error_info, workflow_state) do
    case error_info.type do
      :workflow_error ->
        attempt_workflow_recovery(error_info, workflow_state)
      
      :node_error ->
        attempt_node_recovery(error_info, workflow_state)
      
      :system_error ->
        attempt_system_recovery(error_info, workflow_state)
      
      _ ->
        {:error, "Recovery not supported for error type: #{error_info.type}"}
    end
  end

  defp attempt_workflow_recovery(error_info, workflow_state) do
    try do
      # 尝试重置工作流状态到上一个稳定点
      case find_last_stable_state(workflow_state) do
        {:ok, stable_state} ->
          Logger.info("Recovering workflow to stable state")
          {:ok, stable_state}
        
        {:error, _reason} ->
          # 尝试重新加载工作流定义
          case reload_workflow_definition(workflow_state.workflow_id) do
            {:ok, new_workflow} ->
              updated_state = %{workflow_state | workflow: new_workflow}
              Logger.info("Recovered workflow by reloading definition")
              {:ok, updated_state}
            
            {:error, reason} ->
              {:error, reason}
          end
      end
    rescue
      recovery_error ->
        Logger.error("Workflow recovery failed: #{inspect(recovery_error)}")
        {:error, "Recovery failed"}
    end
  end

  defp attempt_node_recovery(error_info, workflow_state) do
    node_name = error_info.node_name
    
    if node_name do
      try do
        # 尝试重置节点状态
        case reset_node_state(workflow_state, node_name) do
          {:ok, updated_state} ->
            Logger.info("Recovered node: #{node_name}")
            {:ok, updated_state}
          
          {:error, reason} ->
            {:error, reason}
        end
      rescue
        recovery_error ->
        Logger.error("Node recovery failed: #{inspect(recovery_error)}")
        {:error, "Node recovery failed"}
      end
    else
      {:error, "Cannot recover: node name not specified"}
    end
  end

  defp attempt_system_recovery(_error_info, workflow_state) do
    # 系统级错误恢复通常比较复杂
    # 这里实现基本的系统状态检查和重置
    
    try do
      # 检查系统资源
      case check_system_resources() do
        :ok ->
          Logger.info("System resources OK, attempting workflow restart")
          {:ok, workflow_state}
        
        {:error, reason} ->
          Logger.error("System resource check failed: #{reason}")
          {:error, "System resources unavailable"}
      end
    rescue
      error ->
        Logger.error("System recovery failed: #{inspect(error)}")
        {:error, "System recovery failed"}
    end
  end

  defp check_node_error_recovery(error_info) do
    # 检查节点错误是否可以恢复
    case error_info.message do
      msg when is_binary(msg) ->
        not String.contains?(msg, ["fatal", "permanent", "invalid_config"])
      _ -> false
    end
  end

  defp check_workflow_error_recovery(error_info) do
    # 工作流错误通常可以通过重新加载或重置状态来恢复
    not (error_info.severity == :critical)
  end

  defp check_system_error_recovery(error_info) do
    # 系统错误的恢复能力取决于具体的错误类型
    case error_info.message do
      msg when is_binary(msg) ->
        not String.contains?(msg, ["out_of_memory", "disk_full", "permission_denied"])
      _ -> false
    end
  end

  defp format_error_for_continuation(error_info) do
    %{
      "json" => %{
        "error" => %{
          "type" => error_info.type,
          "message" => error_info.message,
          "timestamp" => error_info.timestamp,
          "recoverable" => can_recover_from_error?(error_info)
        }
      },
      "binary" => %{},
      "error" => error_info
    }
  end

  defp log_error(error_info) do
    log_level = case error_info.severity do
      :critical -> :error
      :high -> :error
      :medium -> :warn
      :low -> :info
    end
    
    Logger.log(log_level, """
    Error occurred in workflow execution:
    Type: #{error_info.type}
    Severity: #{error_info.severity}
    Message: #{error_info.message}
    Node: #{error_info.node_name || "N/A"}
    Workflow: #{error_info.workflow_id || "N/A"}
    Execution: #{error_info.execution_id || "N/A"}
    """)
  end

  defp get_error_config(context) do
    # 从上下文中获取错误处理配置
    Map.get(context, :error_config, %{
      continue_on_fail: false,
      retry_on_fail: false,
      skip_on_minor_error: true,
      max_retry_attempts: 3,
      retry_strategy: :exponential_backoff
    })
  end

  defp extract_error_message(error) do
    case error do
      %{message: msg} when is_binary(msg) -> msg
      {:error, msg} when is_binary(msg) -> msg
      {:error, {_type, msg}} when is_binary(msg) -> msg
      error when is_binary(error) -> error
      error when is_atom(error) -> Atom.to_string(error)
      _ -> "Unknown error: #{inspect(error)}"
    end
  end

  defp get_stack_trace(error) do
    case error do
      %{__stacktrace__: stack} -> stack
      _ -> Process.info(self(), :current_stacktrace) |> elem(1)
    end
  end

  defp sanitize_context(context) do
    # 移除敏感信息，保留调试需要的信息
    context
    |> Map.drop([:credentials, :secrets, :password])
    |> Map.take([:workflow_id, :execution_id, :node_name, :current_node, :status])
  end

  defp extract_workflow_info(error_info) do
    %{
      workflow_id: error_info.workflow_id,
      execution_id: error_info.execution_id
    }
  end

  defp extract_node_info(error_info) do
    %{
      node_name: error_info.node_name,
      node_type: Map.get(error_info.context, :node_type)
    }
  end

  defp extract_execution_info(error_info) do
    %{
      execution_id: error_info.execution_id,
      timestamp: error_info.timestamp,
      retry_attempts: Map.get(error_info, :retry_attempts, 0)
    }
  end

  defp get_system_info() do
    %{
      node: Node.self(),
      memory_usage: :erlang.memory(),
      process_count: :erlang.system_info(:process_count),
      timestamp: DateTime.utc_now()
    }
  end

  defp find_last_stable_state(workflow_state) do
    # 在实际实现中，这里会查找最后一个稳定的执行状态
    # 现在返回当前状态作为示例
    {:ok, workflow_state}
  end

  defp reload_workflow_definition(workflow_id) do
    # 重新从数据库加载工作流定义
    case N8nElixir.Workflows.get_workflow(workflow_id) do
      nil -> {:error, "Workflow not found"}
      workflow -> 
        workflow_data = %{
          "id" => workflow.id,
          "name" => workflow.name,
          "nodes" => workflow.nodes,
          "connections" => workflow.connections,
          "static_data" => workflow.static_data,
          "settings" => workflow.settings,
          "pin_data" => workflow.pin_data,
          "active" => workflow.status == :active
        }
        {:ok, N8nElixir.Workflow.new(workflow_data)}
    end
  end

  defp reset_node_state(workflow_state, node_name) do
    # 重置特定节点的状态
    updated_execution_data = Map.delete(workflow_state.execution_data, node_name)
    updated_state = %{workflow_state | execution_data: updated_execution_data}
    {:ok, updated_state}
  end

  defp check_system_resources() do
    # 简单的系统资源检查
    memory_usage = :erlang.memory(:total)
    max_memory = 1024 * 1024 * 1024  # 1GB 示例限制
    
    if memory_usage < max_memory do
      :ok
    else
      {:error, "Memory usage too high"}
    end
  end
end
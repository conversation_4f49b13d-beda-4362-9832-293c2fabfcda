%% -*- mode: erlang; tab-width: 4; indent-tabs-mode: 1; st-rulers: [70] -*-
%% vim: ts=4 sw=4 ft=erlang noet
%%%-------------------------------------------------------------------
%%% <AUTHOR> <<EMAIL>>
%%% @copyright 2014-2022, <PERSON> Bennett
%%% @doc
%%%
%%% @end
%%% Created :  14 Aug 2015 by <PERSON> <<EMAIL>>
%%%-------------------------------------------------------------------
-module(jose_json_unsupported).
-behaviour(jose_json).

%% jose_json callbacks
-export([decode/1]).
-export([encode/1]).

%%====================================================================
%% jose_json callbacks
%%====================================================================

decode(_Binary) ->
	erlang:error(unsupported_json_module).

encode(_Term) ->
	erlang:error(unsupported_json_module).

%%%-------------------------------------------------------------------
%%% Internal functions
%%%-------------------------------------------------------------------

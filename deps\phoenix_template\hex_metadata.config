{<<"links">>,
 [{<<"GitHub">>,<<"https://github.com/phoenixframework/phoenix_template">>}]}.
{<<"name">>,<<"phoenix_template">>}.
{<<"version">>,<<"1.0.4">>}.
{<<"description">>,<<"Template rendering for Phoenix">>}.
{<<"elixir">>,<<"~> 1.9">>}.
{<<"app">>,<<"phoenix_template">>}.
{<<"licenses">>,[<<"MIT">>]}.
{<<"requirements">>,
 [[{<<"name">>,<<"phoenix_html">>},
   {<<"app">>,<<"phoenix_html">>},
   {<<"optional">>,true},
   {<<"requirement">>,<<"~> 2.14.2 or ~> 3.0 or ~> 4.0">>},
   {<<"repository">>,<<"hexpm">>}]]}.
{<<"files">>,
 [<<"lib">>,<<"lib/phoenix">>,<<"lib/phoenix/template">>,
  <<"lib/phoenix/template/exs_engine.ex">>,
  <<"lib/phoenix/template/eex_engine.ex">>,
  <<"lib/phoenix/template/engine.ex">>,<<"lib/phoenix/template.ex">>,
  <<".formatter.exs">>,<<"mix.exs">>,<<"README.md">>,<<"LICENSE.md">>,
  <<"CHANGELOG.md">>]}.
{<<"build_tools">>,[<<"mix">>]}.

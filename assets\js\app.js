// App.js - 主应用JavaScript入口
import "phoenix_html"
import {Socket} from "phoenix"
import {LiveSocket} from "phoenix_live_view"
import topbar from "../vendor/topbar"

// 导入自定义 hooks
import WorkflowCanvas from "./workflow_canvas"

let csrfToken = document.querySelector("meta[name='csrf-token']").getAttribute("content")

// LiveView hooks
let Hooks = {
  WorkflowCanvas: WorkflowCanvas
}

let liveSocket = new LiveSocket("/live", Socket, {
  longPollFallbackMs: 2500,
  params: {_csrf_token: csrfToken},
  hooks: Hooks
})

// 进度条
topbar.config({barColors: {0: "#29d"}, shadowColor: "rgba(0, 0, 0, .3)"})
window.addEventListener("phx:page-loading-start", _info => topbar.show(300))
window.addEventListener("phx:page-loading-stop", _info => topbar.hide())

// 连接LiveSocket
liveSocket.connect()

// 在开发环境下暴露liveSocket
window.liveSocket = liveSocket
{application,guardian,
             [{modules,['Elixir.Guardian','Elixir.Guardian.Config',
                        'Elixir.Guardian.MalformedReturnValueError',
                        'Elixir.Guardian.Permissions',
                        'Elixir.Guardian.Permissions.AtomEncoding',
                        'Elixir.Guardian.Permissions.BitwiseEncoding',
                        'Elixir.Guardian.Permissions.PermissionEncoding',
                        'Elixir.Guardian.Permissions.PermissionNotFoundError',
                        'Elixir.Guardian.Permissions.Plug',
                        'Elixir.Guardian.Permissions.TextEncoding',
                        'Elixir.Guardian.Plug',
                        'Elixir.Guardian.Plug.EnsureAuthenticated',
                        'Elixir.Guardian.Plug.EnsureNotAuthenticated',
                        'Elixir.Guardian.Plug.ErrorHandler',
                        'Elixir.Guardian.Plug.Keys',
                        'Elixir.Guardian.Plug.LoadResource',
                        'Elixir.Guardian.Plug.Pipeline',
                        'Elixir.Guardian.Plug.SlidingCookie',
                        'Elixir.Guardian.Plug.UnauthenticatedError',
                        'Elixir.Guardian.Plug.VerifyCookie',
                        'Elixir.Guardian.Plug.VerifyHeader',
                        'Elixir.Guardian.Plug.VerifySession',
                        'Elixir.Guardian.Token','Elixir.Guardian.Token.Jwt',
                        'Elixir.Guardian.Token.Jwt.SecretFetcher',
                        'Elixir.Guardian.Token.Jwt.SecretFetcher.SecretFetcherDefaultImpl',
                        'Elixir.Guardian.Token.Jwt.Verify',
                        'Elixir.Guardian.Token.Verify','Elixir.Guardian.UUID',
                        'Elixir.Mix.Tasks.Guardian.Gen.Secret']},
              {optional_applications,[plug]},
              {applications,[kernel,stdlib,elixir,crypto,logger,jose,plug]},
              {description,"Elixir Authentication framework"},
              {registered,[]},
              {vsn,"2.3.2"}]}.

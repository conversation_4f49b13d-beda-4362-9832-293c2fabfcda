{application,gen_stage,
             [{modules,['Elixir.ConsumerSupervisor',
                        'Elixir.ConsumerSupervisor.Default','Elixir.GenStage',
                        'Elixir.GenStage.BroadcastDispatcher',
                        'Elixir.GenStage.Buffer',
                        'Elixir.GenStage.DemandDispatcher',
                        'Elixir.GenStage.Dispatcher',
                        'Elixir.GenStage.PartitionDispatcher',
                        'Elixir.GenStage.Stream','Elixir.GenStage.Streamer',
                        'Elixir.GenStage.Utils']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger]},
              {description,"Producer and consumer actors with back-pressure for Elixir"},
              {registered,[]},
              {vsn,"1.3.1"}]}.

{<<"links">>,
 [{<<"GitHub">>,<<"https://github.com/dashbitco/nimble_options">>}]}.
{<<"name">>,<<"nimble_options">>}.
{<<"version">>,<<"1.1.1">>}.
{<<"description">>,
 <<"A tiny library for validating and documenting high-level options">>}.
{<<"elixir">>,<<"~> 1.9">>}.
{<<"app">>,<<"nimble_options">>}.
{<<"licenses">>,[<<"Apache-2.0">>]}.
{<<"requirements">>,[]}.
{<<"files">>,
 [<<"lib">>,<<"lib/nimble_options">>,<<"lib/nimble_options/docs.ex">>,
  <<"lib/nimble_options/validation_error.ex">>,<<"lib/nimble_options.ex">>,
  <<".formatter.exs">>,<<"mix.exs">>,<<"README.md">>,<<"LICENSE.md">>,
  <<"CHANGELOG.md">>]}.
{<<"build_tools">>,[<<"mix">>]}.

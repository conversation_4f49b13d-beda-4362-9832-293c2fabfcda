import Config

# 数据库配置
config :n8n_elixir, N8nElixir.Repo,
  username: System.get_env("DB_USERNAME", "postgres"),
  password: System.get_env("DB_PASSWORD", "postgres"),
  hostname: System.get_env("DB_HOST", "localhost"),
  database: System.get_env("DB_NAME", "n8n_elixir_dev"),
  stacktrace: true,
  show_sensitive_data_on_connection_error: true,
  pool_size: String.to_integer(System.get_env("DB_POOL_SIZE", "10"))

# Phoenix 端点配置
config :n8n_elixir, N8nElixirWeb.Endpoint,
  url: [host: "localhost"],
  render_errors: [view: N8nElixirWeb.ErrorView, accepts: ~w(html json), layout: false],
  pubsub_server: N8nElixir.PubSub,
  live_view: [signing_salt: "n8n-elixir-salt"]

# 资源构建配置
config :esbuild,
  version: "0.17.11",
  default: [
    args: ~w(js/app.js --bundle --target=es2017 --outdir=../priv/static/assets --external:/fonts/* --external:/images/*),
    cd: Path.expand("../assets", __DIR__),
    env: %{"NODE_PATH" => Path.expand("../deps", __DIR__)}
  ]

config :tailwind,
  version: "3.3.0",
  default: [
    args: ~w(
      --config=tailwind.config.js
      --input=css/app.css
      --output=../priv/static/assets/app.css
    ),
    cd: Path.expand("../assets", __DIR__)
  ]

# 定时任务配置
config :n8n_elixir, N8nElixir.Scheduler,
  jobs: [
    # 清理旧的执行日志
    {"0 2 * * *", {N8nElixir.Maintenance, :cleanup_old_logs, []}},
    # 健康检查
    {"*/5 * * * *", {N8nElixir.HealthCheck, :check_system_health, []}}
  ]

# Quantum 配置
config :quantum, timezone: "UTC"

# Guardian 认证配置
config :n8n_elixir, N8nElixir.Guardian,
  issuer: "n8n_elixir",
  secret_key: System.get_env("GUARDIAN_SECRET_KEY", "your-secret-key")

# 加密配置
config :cloak, N8nElixir.Vault,
  ciphers: [
    default: {Cloak.Ciphers.AES.GCM, tag: "AES.GCM.V1", key: Base.decode64!(System.get_env("CLOAK_KEY", "your-base64-key"))}
  ]

# 日志配置
config :logger, :console,
  format: "$time $metadata[$level] $message\n",
  metadata: [:request_id]

# Phoenix 配置
config :phoenix, :json_library, Jason

# 国际化配置
config :n8n_elixir, N8nElixirWeb.Gettext,
  default_locale: "en",
  locales: ~w(en zh)

# 速率限制配置
config :hammer,
  backend: {Hammer.Backend.ETS, [expiry_ms: 60_000 * 60 * 4, cleanup_interval_ms: 60_000 * 10]}

# 后台任务配置
config :n8n_elixir, Oban,
  repo: N8nElixir.Repo,
  plugins: [
    Oban.Plugins.Pruner,
    {Oban.Plugins.Cron, crontab: [
      {"0 2 * * *", N8nElixir.Workers.CleanupWorker},
      {"*/15 * * * *", N8nElixir.Workers.HealthCheckWorker}
    ]}
  ],
  queues: [
    workflow_execution: 50,
    notification: 10,
    cleanup: 1
  ]

# 开发环境配置
import_config "#{config_env()}.exs"
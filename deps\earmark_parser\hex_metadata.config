{<<"links">>,
 [{<<"Changelog">>,
   <<"https://github.com/RobertDober/earmark_parser/blob/master/RELEASE.md">>},
  {<<"GitHub">>,<<"https://github.com/RobertDober/earmark_parser">>}]}.
{<<"name">>,<<"earmark_parser">>}.
{<<"version">>,<<"1.4.44">>}.
{<<"description">>,<<"AST parser and generator for Markdown">>}.
{<<"elixir">>,<<"~> 1.13">>}.
{<<"app">>,<<"earmark_parser">>}.
{<<"files">>,
 [<<"lib">>,<<"lib/earmark_parser">>,<<"lib/earmark_parser/line_scanner">>,
  <<"lib/earmark_parser/line_scanner/rgx.ex">>,
  <<"lib/earmark_parser/line.ex">>,<<"lib/earmark_parser/message.ex">>,
  <<"lib/earmark_parser/options.ex">>,<<"lib/earmark_parser/enum">>,
  <<"lib/earmark_parser/enum/ext.ex">>,<<"lib/earmark_parser/parser">>,
  <<"lib/earmark_parser/parser/footnote_parser.ex">>,
  <<"lib/earmark_parser/parser/list_info.ex">>,
  <<"lib/earmark_parser/parser/list_parser.ex">>,
  <<"lib/earmark_parser/parser/link_parser.ex">>,
  <<"lib/earmark_parser/helpers.ex">>,<<"lib/earmark_parser/ast">>,
  <<"lib/earmark_parser/ast/renderer">>,
  <<"lib/earmark_parser/ast/renderer/table_renderer.ex">>,
  <<"lib/earmark_parser/ast/renderer/html_renderer.ex">>,
  <<"lib/earmark_parser/ast/renderer/footnote_renderer.ex">>,
  <<"lib/earmark_parser/ast/renderer/ast_walker.ex">>,
  <<"lib/earmark_parser/ast/emitter.ex">>,
  <<"lib/earmark_parser/ast/inline.ex">>,<<"lib/earmark_parser/parser.ex">>,
  <<"lib/earmark_parser/context.ex">>,
  <<"lib/earmark_parser/line_scanner.ex">>,
  <<"lib/earmark_parser/ast_renderer.ex">>,<<"lib/earmark_parser/helpers">>,
  <<"lib/earmark_parser/helpers/yecc_helpers.ex">>,
  <<"lib/earmark_parser/helpers/line_helpers.ex">>,
  <<"lib/earmark_parser/helpers/ast_helpers.ex">>,
  <<"lib/earmark_parser/helpers/string_helpers.ex">>,
  <<"lib/earmark_parser/helpers/reparse_helpers.ex">>,
  <<"lib/earmark_parser/helpers/html_parser.ex">>,
  <<"lib/earmark_parser/helpers/leex_helpers.ex">>,
  <<"lib/earmark_parser/helpers/attr_parser.ex">>,
  <<"lib/earmark_parser/helpers/lookahead_helpers.ex">>,
  <<"lib/earmark_parser/helpers/pure_link_helpers.ex">>,
  <<"lib/earmark_parser/block">>,<<"lib/earmark_parser/block/list_item.ex">>,
  <<"lib/earmark_parser/block/html.ex">>,
  <<"lib/earmark_parser/block/ruler.ex">>,
  <<"lib/earmark_parser/block/html_oneline.ex">>,
  <<"lib/earmark_parser/block/code.ex">>,
  <<"lib/earmark_parser/block/id_def.ex">>,
  <<"lib/earmark_parser/block/para.ex">>,
  <<"lib/earmark_parser/block/fn_list.ex">>,
  <<"lib/earmark_parser/block/ial.ex">>,
  <<"lib/earmark_parser/block/list.ex">>,
  <<"lib/earmark_parser/block/table.ex">>,
  <<"lib/earmark_parser/block/heading.ex">>,
  <<"lib/earmark_parser/block/fn_def.ex">>,
  <<"lib/earmark_parser/block/html_comment.ex">>,
  <<"lib/earmark_parser/block/text.ex">>,
  <<"lib/earmark_parser/block/block_quote.ex">>,<<"lib/earmark_parser.ex">>,
  <<"src/earmark_parser_link_text_lexer.xrl">>,
  <<"src/earmark_parser_string_lexer.xrl">>,
  <<"src/earmark_parser_link_text_parser.yrl">>,<<"mix.exs">>,<<"README.md">>,
  <<"RELEASE.md">>,<<"LICENSE">>]}.
{<<"licenses">>,[<<"Apache-2.0">>]}.
{<<"requirements">>,[]}.
{<<"build_tools">>,[<<"mix">>]}.

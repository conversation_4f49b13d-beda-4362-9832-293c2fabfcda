{<<"links">>,
 [{<<"GitHub">>,<<"https://github.com/beam-telemetry/telemetry_registry">>}]}.
{<<"name">>,<<"telemetry_registry">>}.
{<<"version">>,<<"0.3.2">>}.
{<<"description">>,<<"Registry and helpers for Telemetry events">>}.
{<<"elixir">>,<<"~> 1.8">>}.
{<<"app">>,<<"telemetry_registry">>}.
{<<"build_tools">>,[<<"rebar3">>,<<"mix">>]}.
{<<"files">>,
 [<<"lib">>,<<"lib/telemetry_registry.ex">>,<<"mix.exs">>,<<"README.md">>,
  <<"LICENSE">>,<<"CODEOWNERS">>,<<"rebar.config">>,<<"rebar.lock">>,
  <<"VERSION">>,<<"src">>,<<"src/telemetry_registry.app.src">>,
  <<"src/telemetry_registry.erl">>,<<".formatter.exs">>]}.
{<<"licenses">>,[<<"Apache-2.0">>]}.
{<<"requirements">>,
 [[{<<"name">>,<<"telemetry">>},
   {<<"app">>,<<"telemetry">>},
   {<<"optional">>,false},
   {<<"requirement">>,<<"~> 1.0">>},
   {<<"repository">>,<<"hexpm">>}]]}.

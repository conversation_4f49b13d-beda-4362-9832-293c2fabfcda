defmodule N8nElixirWeb.WorkflowLiveHelpers do
  @moduledoc """
  工作流LiveView的辅助函数
  """

  def get_node_categories(node_types) do
    # 将节点按类别分组
    categories = %{
      "Core" => %{name: "Core Nodes", nodes: []},
      "Transform" => %{name: "Data Transform", nodes: []},
      "Trigger" => %{name: "Trigger Nodes", nodes: []},
      "Output" => %{name: "Output Nodes", nodes: []},
      "Logic" => %{name: "Logic & Control", nodes: []}
    }

    Enum.reduce(node_types, categories, fn node_type, acc ->
      group = get_node_group(node_type)
      category_key = case group do
        group when group in ["trigger"] -> "Trigger"
        group when group in ["transform"] -> "Transform" 
        group when group in ["output"] -> "Output"
        group when group in ["logic", "conditional"] -> "Logic"
        _ -> "Core"
      end
      
      update_in(acc[category_key][:nodes], fn nodes -> [node_type | nodes] end)
    end)
    |> Enum.map(fn {_key, category} -> category end)
    |> Enum.reject(fn category -> Enum.empty?(category.nodes) end)
  end

  def get_node_group(node_type) do
    groups = Map.get(node_type, :group, [])
    if Enum.empty?(groups), do: "core", else: List.first(groups)
  end

  def get_node_icon(node_type, node_types) do
    case Enum.find(node_types, fn nt -> nt.name == node_type end) do
      nil -> "fa fa-cog"
      node_description -> Map.get(node_description, :icon, "fa fa-cog")
    end
  end

  def get_node_class(node, selected_node, execution_data) do
    classes = ["workflow-node"]
    
    # 选中状态
    if Map.get(node, "id") == selected_node do
      classes = ["selected" | classes]
    end
    
    # 禁用状态
    if Map.get(node, "disabled", false) do
      classes = ["disabled" | classes]
    end
    
    # 执行状态
    node_id = Map.get(node, "id")
    cond do
      Map.has_key?(execution_data, node_id) ->
        case Map.get(execution_data[node_id], "status") do
          "success" -> ["executed" | classes]
          "error" -> ["error" | classes]
          "running" -> ["executing" | classes]
          _ -> classes
        end
      true -> classes
    end
    |> Enum.join(" ")
  end

  def get_node_inputs(node_type, node_types) do
    case Enum.find(node_types, fn nt -> nt.name == node_type end) do
      nil -> [%{name: "main", type: "main"}]
      node_description -> 
        inputs = Map.get(node_description, :inputs, ["main"])
        Enum.map(inputs, fn input ->
          if is_binary(input) do
            %{name: input, type: input}
          else
            input
          end
        end)
    end
  end

  def get_node_outputs(node_type, node_types) do
    case Enum.find(node_types, fn nt -> nt.name == node_type end) do
      nil -> [%{name: "main", type: "main"}]
      node_description -> 
        outputs = Map.get(node_description, :outputs, ["main"])
        output_names = Map.get(node_description, :outputNames, [])
        
        outputs
        |> Enum.with_index()
        |> Enum.map(fn {output, index} ->
          name = Enum.at(output_names, index, output)
          if is_binary(output) do
            %{name: name, type: output}
          else
            Map.put(output, :name, name)
          end
        end)
    end
  end

  def render_connection(source_id, target_id, output_index, input_index, nodes) do
    source_node = Map.get(nodes, source_id)
    target_node = Map.get(nodes, target_id)
    
    if source_node && target_node do
      source_pos = Map.get(source_node, "position", [0, 0])
      target_pos = Map.get(target_node, "position", [0, 0])
      
      # 计算连接点位置
      source_x = Enum.at(source_pos, 0, 0) + 150  # 节点宽度 + 输出点位置
      source_y = Enum.at(source_pos, 1, 0) + 40   # 节点高度的一半
      target_x = Enum.at(target_pos, 0, 0)        # 输入点在左侧
      target_y = Enum.at(target_pos, 1, 0) + 40
      
      # 创建贝塞尔曲线
      control_offset = abs(target_x - source_x) * 0.5
      cp1_x = source_x + control_offset
      cp1_y = source_y
      cp2_x = target_x - control_offset
      cp2_y = target_y
      
      path_data = "M #{source_x} #{source_y} C #{cp1_x} #{cp1_y}, #{cp2_x} #{cp2_y}, #{target_x} #{target_y}"
      
      Phoenix.HTML.raw("""
      <path d="#{path_data}" 
            stroke="#6c757d" 
            stroke-width="2" 
            fill="none" 
            class="connection-path"
            data-source="#{source_id}"
            data-target="#{target_id}" />
      """)
    else
      ""
    end
  end

  def format_node_position(node) do
    position = Map.get(node, "position", [0, 0])
    x = Enum.at(position, 0, 0)
    y = Enum.at(position, 1, 0)
    "left: #{x}px; top: #{y}px"
  end
end
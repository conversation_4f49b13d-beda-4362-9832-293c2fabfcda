defmodule N8nElixirWeb.WorkflowIndexLive do
  @moduledoc """
  工作流列表页面
  
  显示所有工作流，支持创建、编辑、删除和执行操作
  """
  use N8nElixirWeb, :live_view

  @impl true
  def mount(_params, _session, socket) do
    # 临时使用空列表，直到数据库完全设置好
    workflows = []
    
    socket = 
      socket
      |> assign(:workflows, workflows)
      |> assign(:page_title, "Workflows")
    
    {:ok, socket}
  end

  @impl true
  def handle_event("delete_workflow", %{"id" => _workflow_id}, socket) do
    {:noreply, put_flash(socket, :info, "Delete functionality coming soon")}
  end

  @impl true
  def handle_event("execute_workflow", %{"id" => _workflow_id}, socket) do
    {:noreply, put_flash(socket, :info, "Execute functionality coming soon")}
  end

  @impl true
  def handle_event("activate_workflow", %{"id" => _workflow_id}, socket) do
    {:noreply, put_flash(socket, :info, "Activate functionality coming soon")}
  end

  @impl true
  def handle_event("deactivate_workflow", %{"id" => _workflow_id}, socket) do
    {:noreply, put_flash(socket, :info, "Deactivate functionality coming soon")}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="workflows-index">
      <div class="page-header">
        <h1>Workflows</h1>
        <.link navigate="/workflows/new" class="btn btn-primary">
          <i class="fa fa-plus"></i> New Workflow
        </.link>
      </div>

      <div class="workflows-grid">
        <%= for workflow <- @workflows do %>
          <div class="workflow-card">
            <div class="workflow-header">
              <h3><%= workflow.name %></h3>
              <div class="workflow-status">
                <%= if workflow.status == :active do %>
                  <span class="status-badge active">Active</span>
                <% else %>
                  <span class="status-badge inactive">Inactive</span>
                <% end %>
              </div>
            </div>

            <div class="workflow-description">
              <%= workflow.description || "No description" %>
            </div>

            <div class="workflow-stats">
              <div class="stat">
                <span class="stat-label">Nodes:</span>
                <span class="stat-value"><%= map_size(workflow.nodes || %{}) %></span>
              </div>
              <div class="stat">
                <span class="stat-label">Last Modified:</span>
                <span class="stat-value"><%= format_date(workflow.updated_at) %></span>
              </div>
            </div>

            <div class="workflow-actions">
              <.link navigate={"/workflows/#{workflow.id}/edit"} class="btn btn-sm btn-secondary">
                <i class="fa fa-edit"></i> Edit
              </.link>
              
              <button class="btn btn-sm btn-primary" 
                      phx-click="execute_workflow" 
                      phx-value-id={workflow.id}>
                <i class="fa fa-play"></i> Execute
              </button>

              <%= if workflow.status == :active do %>
                <button class="btn btn-sm btn-warning" 
                        phx-click="deactivate_workflow" 
                        phx-value-id={workflow.id}>
                  <i class="fa fa-pause"></i> Deactivate
                </button>
              <% else %>
                <button class="btn btn-sm btn-success" 
                        phx-click="activate_workflow" 
                        phx-value-id={workflow.id}>
                  <i class="fa fa-play-circle"></i> Activate
                </button>
              <% end %>

              <button class="btn btn-sm btn-danger" 
                      phx-click="delete_workflow" 
                      phx-value-id={workflow.id}
                      data-confirm="Are you sure you want to delete this workflow?">
                <i class="fa fa-trash"></i> Delete
              </button>
            </div>
          </div>
        <% end %>

        <%= if Enum.empty?(@workflows) do %>
          <div class="empty-state">
            <div class="empty-icon">
              <i class="fa fa-sitemap"></i>
            </div>
            <h3>No workflows yet</h3>
            <p>Create your first workflow to get started with automation.</p>
            <.link navigate="/workflows/new" class="btn btn-primary">
              <i class="fa fa-plus"></i> Create First Workflow
            </.link>
          </div>
        <% end %>
      </div>
    </div>

    <style>
    .workflows-index {
      padding: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 1px solid #eee;
    }

    .page-header h1 {
      margin: 0;
      color: #333;
    }

    .workflows-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
      gap: 20px;
    }

    .workflow-card {
      background: white;
      border: 1px solid #ddd;
      border-radius: 8px;
      padding: 20px;
      transition: all 0.2s ease;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .workflow-card:hover {
      border-color: #007bff;
      box-shadow: 0 4px 12px rgba(0,123,255,0.15);
      transform: translateY(-2px);
    }

    .workflow-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 15px;
    }

    .workflow-header h3 {
      margin: 0;
      color: #333;
      font-size: 18px;
      flex: 1;
      margin-right: 15px;
    }

    .workflow-status {
      flex-shrink: 0;
    }

    .status-badge {
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .status-badge.active {
      background: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .status-badge.inactive {
      background: #f8d7da;
      color: #721c24;
      border: 1px solid #f1b0b7;
    }

    .workflow-description {
      color: #666;
      margin-bottom: 15px;
      line-height: 1.4;
      min-height: 40px;
    }

    .workflow-stats {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
      padding: 15px 0;
      border-top: 1px solid #f0f0f0;
      border-bottom: 1px solid #f0f0f0;
    }

    .stat {
      display: flex;
      flex-direction: column;
      gap: 4px;
    }

    .stat-label {
      font-size: 12px;
      color: #666;
      font-weight: 500;
    }

    .stat-value {
      font-size: 14px;
      color: #333;
      font-weight: 600;
    }

    .workflow-actions {
      display: flex;
      gap: 8px;
      flex-wrap: wrap;
    }

    .btn {
      padding: 6px 12px;
      border: none;
      border-radius: 4px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 6px;
      font-size: 13px;
    }

    .btn-primary {
      background: #007bff;
      color: white;
    }

    .btn-primary:hover {
      background: #0056b3;
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background: #545b62;
    }

    .btn-success {
      background: #28a745;
      color: white;
    }

    .btn-success:hover {
      background: #1e7e34;
    }

    .btn-warning {
      background: #ffc107;
      color: #212529;
    }

    .btn-warning:hover {
      background: #e0a800;
    }

    .btn-danger {
      background: #dc3545;
      color: white;
    }

    .btn-danger:hover {
      background: #c82333;
    }

    .btn-sm {
      padding: 4px 8px;
      font-size: 12px;
    }

    .empty-state {
      grid-column: 1 / -1;
      text-align: center;
      padding: 60px 20px;
      color: #666;
    }

    .empty-icon {
      font-size: 48px;
      color: #ddd;
      margin-bottom: 20px;
    }

    .empty-state h3 {
      margin: 0 0 10px 0;
      color: #333;
    }

    .empty-state p {
      margin: 0 0 30px 0;
      max-width: 400px;
      margin-left: auto;
      margin-right: auto;
      line-height: 1.5;
    }
    </style>
    """
  end

  defp format_date(datetime) do
    case datetime do
      nil -> "Never"
      %DateTime{} -> Calendar.strftime(datetime, "%Y-%m-%d %H:%M")
      %NaiveDateTime{} -> Calendar.strftime(datetime, "%Y-%m-%d %H:%M")
      _ -> "Unknown"
    end
  end
end
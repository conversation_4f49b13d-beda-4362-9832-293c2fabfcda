<div class="mx-auto max-w-sm">
  <.header class="text-center">Confirm account</.header>

  <.simple_form for={@conn.params["<%= schema.singular %>"]} as={:<%= schema.singular %>} action={~p"<%= schema.route_prefix %>/confirm/#{@token}"}>
    <:actions>
      <.button class="w-full">Confirm my account</.button>
    </:actions>
  </.simple_form>

  <p class="text-center mt-4">
    <.link href={~p"<%= schema.route_prefix %>/register"}>Register</.link>
    | <.link href={~p"<%= schema.route_prefix %>/log_in"}>Log in</.link>
  </p>
</div>

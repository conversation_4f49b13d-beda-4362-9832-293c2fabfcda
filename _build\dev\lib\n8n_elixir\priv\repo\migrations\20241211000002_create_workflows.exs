defmodule N8nElixir.Repo.Migrations.CreateWorkflows do
  use Ecto.Migration

  def change do
    create table(:workflows, primary_key: false) do
      add :id, :binary_id, primary_key: true
      add :name, :string, null: false
      add :description, :text
      add :version, :integer, default: 1
      add :status, :string, default: "draft"
      add :tags, {:array, :string}, default: []
      add :nodes, :map, null: false, default: %{}
      add :connections, :map, null: false, default: %{}
      add :settings, :map, default: %{}
      add :static_data, :map, default: %{}
      add :pin_data, :map, default: %{}
      add :execution_timeout, :integer, default: 3600
      add :max_execution_time, :integer, default: 300
      add :retry_on_failure, :boolean, default: false
      add :max_retries, :integer, default: 3
      add :parent_workflow_id, references(:workflows, type: :binary_id)
      add :owner_id, references(:users, type: :binary_id), null: false
      add :team_id, references(:teams, type: :binary_id)
      
      timestamps()
    end

    create unique_index(:workflows, [:name, :owner_id])
    create index(:workflows, [:owner_id])
    create index(:workflows, [:team_id])
    create index(:workflows, [:status])
    create index(:workflows, [:parent_workflow_id])
  end
end
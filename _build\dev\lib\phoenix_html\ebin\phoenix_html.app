{application,phoenix_html,
    [{modules,
         ['Elixir.Phoenix.HTML','Elixir.Phoenix.HTML.Engine',
          'Elixir.Phoenix.HTML.Form','Elixir.Phoenix.HTML.FormData',
          'Elixir.Phoenix.HTML.FormData.Atom',
          'Elixir.Phoenix.HTML.FormData.Map',
          'Elixir.Phoenix.HTML.FormData.Plug.Conn',
          'Elixir.Phoenix.HTML.FormField','Elixir.Phoenix.HTML.Format',
          'Elixir.Phoenix.HTML.Link','Elixir.Phoenix.HTML.Safe',
          'Elixir.Phoenix.HTML.Safe.Atom',
          'Elixir.Phoenix.HTML.Safe.BitString',
          'Elixir.Phoenix.HTML.Safe.Date','Elixir.Phoenix.HTML.Safe.DateTime',
          'Elixir.Phoenix.HTML.Safe.Float','Elixir.Phoenix.HTML.Safe.Integer',
          'Elixir.Phoenix.HTML.Safe.List',
          'Elixir.Phoenix.HTML.Safe.NaiveDateTime',
          'Elixir.Phoenix.HTML.Safe.Phoenix.HTML.Form',
          'Elixir.Phoenix.HTML.Safe.Time','Elixir.Phoenix.HTML.Safe.Tuple',
          'Elixir.Phoenix.HTML.Safe.URI','Elixir.Phoenix.HTML.Tag']},
     {optional_applications,[plug]},
     {applications,[kernel,stdlib,elixir,eex,logger,plug]},
     {description,"Phoenix view functions for working with HTML templates"},
     {registered,[]},
     {vsn,"3.3.4"},
     {env,
         [{csrf_token_reader,
              {'Elixir.Plug.CSRFProtection',get_csrf_token_for,[]}}]}]}.

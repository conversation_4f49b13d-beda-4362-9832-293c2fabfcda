defmodule N8nElixir.Nodes.Switch do
  @moduledoc """
  Switch节点 - 多条件分支

  参考n8n的Switch节点，根据多个条件将数据路由到不同的输出分支
  支持复杂的条件逻辑和多输出端口
  """
  @behaviour N8nElixir.NodeType

  alias N8nElixir.ExpressionResolver

  @impl true
  def description() do
    %{
      displayName: "Switch",
      name: "switch",
      icon: "fa:map-signs",
      group: ["transform"],
      version: 1,
      description: "Route items to different branches based on conditions",
      defaults: %{
        name: "Switch",
        color: "#506000"
      },
      inputs: ["main"],
      outputs: ["main", "main", "main", "main"],
      outputNames: ["0", "1", "2", "3"],
      properties: [
        %{
          displayName: "Mode",
          name: "mode",
          type: "options",
          options: [
            %{
              name: "Expression",
              value: "expression",
              description: "Expression decides the output"
            },
            %{
              name: "Rules",
              value: "rules",
              description: "Rules decide the output"
            }
          ],
          default: "rules"
        },
        %{
          displayName: "Output",
          name: "output",
          type: "string",
          displayOptions: %{
            show: %{
              mode: ["expression"]
            }
          },
          default: "",
          placeholder: "={{$json.category}}",
          description: "Expression that returns which output to use (0-3)",
          required: true
        },
        %{
          displayName: "Rules",
          name: "rules",
          placeholder: "Add Rule",
          type: "fixedCollection",
          typeOptions: %{
            multipleValues: true
          },
          displayOptions: %{
            show: %{
              mode: ["rules"]
            }
          },
          description: "Rules to decide the output",
          default: %{
            "rules" => [
              %{
                "conditions" => %{
                  "boolean" => [
                    %{
                      "value1" => "",
                      "operation" => "equal",
                      "value2" => ""
                    }
                  ]
                },
                "output" => 0
              }
            ]
          },
          options: [
            %{
              name: "rules",
              displayName: "Rules",
              values: [
                %{
                  displayName: "Conditions",
                  name: "conditions",
                  placeholder: "Add Condition",
                  type: "fixedCollection",
                  typeOptions: %{
                    multipleValues: true
                  },
                  default: %{},
                  options: [
                    %{
                      name: "boolean",
                      displayName: "Boolean",
                      values: [
                        %{
                          displayName: "Value 1",
                          name: "value1",
                          type: "string",
                          default: "",
                          description: "The first value to compare"
                        },
                        %{
                          displayName: "Operation",
                          name: "operation",
                          type: "options",
                          options: [
                            %{name: "Equal", value: "equal"},
                            %{name: "Not Equal", value: "notEqual"},
                            %{name: "Smaller", value: "smaller"},
                            %{name: "Smaller Equal", value: "smallerEqual"},
                            %{name: "Larger", value: "larger"},
                            %{name: "Larger Equal", value: "largerEqual"},
                            %{name: "Contains", value: "contains"},
                            %{name: "Not Contains", value: "notContains"},
                            %{name: "Starts With", value: "startsWith"},
                            %{name: "Ends With", value: "endsWith"},
                            %{name: "Regex", value: "regex"},
                            %{name: "Is Empty", value: "isEmpty"},
                            %{name: "Is Not Empty", value: "isNotEmpty"}
                          ],
                          default: "equal"
                        },
                        %{
                          displayName: "Value 2",
                          name: "value2",
                          type: "string",
                          default: "",
                          description: "The second value to compare"
                        }
                      ]
                    }
                  ]
                },
                %{
                  displayName: "Combine",
                  name: "combineOperation",
                  type: "options",
                  options: [
                    %{name: "ALL", value: "all"},
                    %{name: "ANY", value: "any"}
                  ],
                  default: "all",
                  description: "If multiple conditions are defined how to combine them"
                },
                %{
                  displayName: "Output",
                  name: "output",
                  type: "number",
                  typeOptions: %{
                    minValue: 0,
                    maxValue: 3
                  },
                  default: 0,
                  description: "The output to route the item to (0-3)"
                }
              ]
            }
          ]
        },
        %{
          displayName: "Fallback Output",
          name: "fallbackOutput",
          type: "number",
          typeOptions: %{
            minValue: -1,
            maxValue: 3
          },
          default: -1,
          description: "Output to use if no rules match (-1 = don't output item)"
        }
      ]
    }
  end

  @impl true
  def execute(input_data, _credentials, parameters) do
    mode = Map.get(parameters, "mode", "rules")
    fallback_output = Map.get(parameters, "fallbackOutput", -1)
    
    case mode do
      "expression" ->
        execute_expression_mode(input_data, parameters, fallback_output)
      
      "rules" ->
        execute_rules_mode(input_data, parameters, fallback_output)
      
      _ ->
        {:error, "Unknown switch mode: #{mode}"}
    end
  end

  @impl true
  def credential_types() do
    []
  end

  # 私有实现函数

  defp execute_expression_mode(input_data, parameters, fallback_output) do
    expression = Map.get(parameters, "output", "")
    
    if String.trim(expression) == "" do
      {:error, "Output expression is required"}
    else
      try do
        # 初始化4个输出端口
        outputs = [[], [], [], []]
        
        routed_data = 
          Enum.reduce(input_data, outputs, fn item, acc_outputs ->
            context = %{
              "$json" => Map.get(item, "json", %{}),
              "$binary" => Map.get(item, "binary", %{}),
              "$item" => item
            }
            
            case ExpressionResolver.resolve_expression(expression, context) do
              {:ok, output_index} when is_number(output_index) ->
                index = trunc(output_index)
                if index >= 0 and index <= 3 do
                  List.update_at(acc_outputs, index, fn output -> [item | output] end)
                else
                  route_to_fallback(acc_outputs, item, fallback_output)
                end
              
              {:ok, output_index} when is_binary(output_index) ->
                case Integer.parse(output_index) do
                  {index, ""} when index >= 0 and index <= 3 ->
                    List.update_at(acc_outputs, index, fn output -> [item | output] end)
                  _ ->
                    route_to_fallback(acc_outputs, item, fallback_output)
                end
              
              _ ->
                route_to_fallback(acc_outputs, item, fallback_output)
            end
          end)
        
        # 反转每个输出列表以保持顺序
        final_outputs = Enum.map(routed_data, &Enum.reverse/1)
        
        {:ok, final_outputs}
      rescue
        error ->
          {:error, "Expression evaluation failed: #{inspect(error)}"}
      end
    end
  end

  defp execute_rules_mode(input_data, parameters, fallback_output) do
    rules = get_rules_from_parameters(parameters)
    
    try do
      # 初始化4个输出端口
      outputs = [[], [], [], []]
      
      routed_data = 
        Enum.reduce(input_data, outputs, fn item, acc_outputs ->
          # 为每个项目找到匹配的规则
          case find_matching_rule(item, rules) do
            {:ok, output_index} ->
              List.update_at(acc_outputs, output_index, fn output -> [item | output] end)
            
            :no_match ->
              route_to_fallback(acc_outputs, item, fallback_output)
          end
        end)
      
      # 反转每个输出列表以保持顺序
      final_outputs = Enum.map(routed_data, &Enum.reverse/1)
      
      {:ok, final_outputs}
    rescue
      error ->
        {:error, "Rules evaluation failed: #{inspect(error)}"}
    end
  end

  defp get_rules_from_parameters(parameters) do
    rules_config = Map.get(parameters, "rules", %{})
    Map.get(rules_config, "rules", [])
  end

  defp find_matching_rule(item, rules) do
    # 遍历规则，找到第一个匹配的规则
    Enum.reduce_while(rules, :no_match, fn rule, _acc ->
      if evaluate_rule(item, rule) do
        output_index = Map.get(rule, "output", 0)
        {:halt, {:ok, output_index}}
      else
        {:cont, :no_match}
      end
    end)
  end

  defp evaluate_rule(item, rule) do
    conditions = Map.get(rule, "conditions", %{})
    combine_operation = Map.get(rule, "combineOperation", "all")
    
    boolean_conditions = Map.get(conditions, "boolean", [])
    
    if Enum.empty?(boolean_conditions) do
      false
    else
      condition_results = Enum.map(boolean_conditions, fn condition ->
        evaluate_single_condition(item, condition)
      end)
      
      case combine_operation do
        "all" -> Enum.all?(condition_results)
        "any" -> Enum.any?(condition_results)
        _ -> false
      end
    end
  end

  defp evaluate_single_condition(item, condition) do
    value1 = resolve_condition_value(item, Map.get(condition, "value1", ""))
    value2 = resolve_condition_value(item, Map.get(condition, "value2", ""))
    operation = Map.get(condition, "operation", "equal")
    
    case operation do
      "equal" -> 
        value1 == value2
      
      "notEqual" -> 
        value1 != value2
      
      "smaller" -> 
        compare_numeric(value1, value2, :lt)
      
      "smallerEqual" -> 
        compare_numeric(value1, value2, :le)
      
      "larger" -> 
        compare_numeric(value1, value2, :gt)
      
      "largerEqual" -> 
        compare_numeric(value1, value2, :ge)
      
      "contains" -> 
        String.contains?(to_string(value1), to_string(value2))
      
      "notContains" -> 
        not String.contains?(to_string(value1), to_string(value2))
      
      "startsWith" -> 
        String.starts_with?(to_string(value1), to_string(value2))
      
      "endsWith" -> 
        String.ends_with?(to_string(value1), to_string(value2))
      
      "regex" -> 
        match_regex(to_string(value1), to_string(value2))
      
      "isEmpty" -> 
        is_empty_value(value1)
      
      "isNotEmpty" -> 
        not is_empty_value(value1)
      
      _ -> 
        false
    end
  end

  defp resolve_condition_value(item, value_string) do
    # 解析条件值，支持表达式和静态值
    if String.starts_with?(value_string, "{{") and String.ends_with?(value_string, "}}") do
      # 表达式求值
      context = %{
        "$json" => Map.get(item, "json", %{}),
        "$binary" => Map.get(item, "binary", %{}),
        "$item" => item
      }
      
      case ExpressionResolver.resolve_expression(value_string, context) do
        {:ok, result} -> result
        {:error, _} -> value_string
      end
    else
      # 静态值或简单路径引用
      if String.starts_with?(value_string, "$json.") do
        path = String.slice(value_string, 6..-1)
        get_nested_value(item["json"] || %{}, path)
      else
        value_string
      end
    end
  end

  defp route_to_fallback(outputs, item, fallback_output) do
    if fallback_output >= 0 and fallback_output <= 3 do
      List.update_at(outputs, fallback_output, fn output -> [item | output] end)
    else
      # fallback_output == -1，不输出项目
      outputs
    end
  end

  defp compare_numeric(value1, value2, operator) do
    try do
      num1 = parse_number(value1)
      num2 = parse_number(value2)
      
      case operator do
        :lt -> num1 < num2
        :le -> num1 <= num2
        :gt -> num1 > num2
        :ge -> num1 >= num2
      end
    rescue
      _ -> false
    end
  end

  defp parse_number(value) when is_number(value), do: value
  defp parse_number(value) when is_binary(value) do
    case Float.parse(value) do
      {num, ""} -> num
      _ ->
        case Integer.parse(value) do
          {num, ""} -> num
          _ -> raise "Not a number"
        end
    end
  end
  defp parse_number(_), do: raise "Not a number"

  defp match_regex(text, pattern) do
    try do
      case Regex.compile(pattern) do
        {:ok, regex} -> Regex.match?(regex, text)
        {:error, _} -> false
      end
    rescue
      _ -> false
    end
  end

  defp is_empty_value(nil), do: true
  defp is_empty_value(""), do: true
  defp is_empty_value([]), do: true
  defp is_empty_value(%{} = map) when map_size(map) == 0, do: true
  defp is_empty_value(_), do: false

  defp get_nested_value(data, path) when is_binary(path) do
    path_parts = String.split(path, ".")
    get_nested_value(data, path_parts)
  end
  defp get_nested_value(data, []), do: data
  defp get_nested_value(data, [key | rest]) when is_map(data) do
    case Map.get(data, key) do
      nil -> nil
      value -> get_nested_value(value, rest)
    end
  end
  defp get_nested_value(_, _), do: nil
end
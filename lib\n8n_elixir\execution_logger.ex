defmodule N8nElixir.ExecutionLogger do
  @moduledoc """
  执行日志记录器

  参考n8n的执行日志系统，提供详细的执行追踪和审计功能
  记录工作流和节点的执行状态、性能指标和调试信息
  """

  use GenServer
  require Logger

  alias N8nElixir.{Executions}

  @registry_name __MODULE__

  # 客户端API
  def start_link(init_arg) do
    GenServer.start_link(__MODULE__, init_arg, name: @registry_name)
  end

  @doc """
  记录工作流执行开始
  """
  def log_workflow_start(execution_id, workflow_data) do
    GenServer.cast(@registry_name, {:log_workflow_start, execution_id, workflow_data})
  end

  @doc """
  记录工作流执行完成
  """
  def log_workflow_complete(execution_id, result) do
    GenServer.cast(@registry_name, {:log_workflow_complete, execution_id, result})
  end

  @doc """
  记录节点执行开始
  """
  def log_node_start(execution_id, node_name, input_data) do
    GenServer.cast(@registry_name, {:log_node_start, execution_id, node_name, input_data})
  end

  @doc """
  记录节点执行完成
  """
  def log_node_complete(execution_id, node_name, output_data, execution_time) do
    GenServer.cast(@registry_name, {:log_node_complete, execution_id, node_name, output_data, execution_time})
  end

  @doc """
  记录节点执行错误
  """
  def log_node_error(execution_id, node_name, error_info) do
    GenServer.cast(@registry_name, {:log_node_error, execution_id, node_name, error_info})
  end

  @doc """
  记录执行事件
  """
  def log_execution_event(execution_id, event_type, event_data) do
    GenServer.cast(@registry_name, {:log_execution_event, execution_id, event_type, event_data})
  end

  @doc """
  获取执行日志
  """
  def get_execution_logs(execution_id, opts \\ []) do
    GenServer.call(@registry_name, {:get_execution_logs, execution_id, opts})
  end

  @doc """
  获取节点日志
  """
  def get_node_logs(execution_id, node_name) do
    GenServer.call(@registry_name, {:get_node_logs, execution_id, node_name})
  end

  @doc """
  搜索执行日志
  """
  def search_logs(query, filters \\ %{}) do
    GenServer.call(@registry_name, {:search_logs, query, filters})
  end

  # 服务器回调
  @impl true
  def init(_init_arg) do
    # 启动日志清理定时器
    schedule_log_cleanup()
    
    state = %{
      execution_logs: %{},
      log_buffer: [],
      buffer_size: 0,
      max_buffer_size: 1000,
      cleanup_timer: nil
    }

    {:ok, state}
  end

  @impl true
  def handle_cast({:log_workflow_start, execution_id, workflow_data}, state) do
    log_entry = create_log_entry(:workflow_start, execution_id, nil, %{
      workflow_id: workflow_data.workflow_id,
      workflow_name: workflow_data.workflow_name,
      mode: workflow_data.mode,
      trigger_data: workflow_data.trigger_data
    })
    
    # 记录到系统日志
    Logger.info("Workflow execution started", [
      execution_id: execution_id,
      workflow_id: workflow_data.workflow_id
    ])
    
    updated_state = add_log_entry(state, log_entry)
    {:noreply, updated_state}
  end

  @impl true
  def handle_cast({:log_workflow_complete, execution_id, result}, state) do
    log_entry = create_log_entry(:workflow_complete, execution_id, nil, %{
      status: result.status,
      total_duration: result.total_duration,
      nodes_executed: result.nodes_executed,
      data_processed: result.data_processed
    })
    
    # 记录到系统日志
    Logger.info("Workflow execution completed", [
      execution_id: execution_id,
      status: result.status,
      duration: result.total_duration
    ])
    
    updated_state = add_log_entry(state, log_entry)
    {:noreply, updated_state}
  end

  @impl true
  def handle_cast({:log_node_start, execution_id, node_name, input_data}, state) do
    log_entry = create_log_entry(:node_start, execution_id, node_name, %{
      input_item_count: length(input_data),
      input_data_preview: preview_data(input_data)
    })
    
    updated_state = add_log_entry(state, log_entry)
    {:noreply, updated_state}
  end

  @impl true
  def handle_cast({:log_node_complete, execution_id, node_name, output_data, execution_time}, state) do
    log_entry = create_log_entry(:node_complete, execution_id, node_name, %{
      output_item_count: length(output_data),
      output_data_preview: preview_data(output_data),
      execution_time: execution_time,
      status: :success
    })
    
    updated_state = add_log_entry(state, log_entry)
    {:noreply, updated_state}
  end

  @impl true
  def handle_cast({:log_node_error, execution_id, node_name, error_info}, state) do
    log_entry = create_log_entry(:node_error, execution_id, node_name, %{
      error_type: error_info.type,
      error_message: error_info.message,
      error_severity: error_info.severity,
      retry_attempted: Map.get(error_info, :retry_attempted, false),
      status: :error
    })
    
    # 记录到系统日志
    Logger.error("Node execution failed", [
      execution_id: execution_id,
      node_name: node_name,
      error: error_info.message
    ])
    
    updated_state = add_log_entry(state, log_entry)
    {:noreply, updated_state}
  end

  @impl true
  def handle_cast({:log_execution_event, execution_id, event_type, event_data}, state) do
    log_entry = create_log_entry(event_type, execution_id, nil, event_data)
    
    updated_state = add_log_entry(state, log_entry)
    {:noreply, updated_state}
  end

  @impl true
  def handle_call({:get_execution_logs, execution_id, opts}, _from, state) do
    logs = get_logs_for_execution(state, execution_id, opts)
    {:reply, logs, state}
  end

  @impl true
  def handle_call({:get_node_logs, execution_id, node_name}, _from, state) do
    logs = get_logs_for_node(state, execution_id, node_name)
    {:reply, logs, state}
  end

  @impl true
  def handle_call({:search_logs, query, filters}, _from, state) do
    results = search_logs_in_state(state, query, filters)
    {:reply, results, state}
  end

  @impl true
  def handle_info(:flush_log_buffer, state) do
    updated_state = flush_log_buffer(state)
    {:noreply, updated_state}
  end

  @impl true
  def handle_info(:cleanup_old_logs, state) do
    updated_state = cleanup_old_logs(state)
    schedule_log_cleanup()
    {:noreply, updated_state}
  end

  @impl true
  def handle_info(_msg, state) do
    {:noreply, state}
  end

  # 私有实现函数

  defp create_log_entry(event_type, execution_id, node_name, data) do
    %{
      id: UUID.uuid4(),
      timestamp: DateTime.utc_now(),
      event_type: event_type,
      execution_id: execution_id,
      node_name: node_name,
      data: data,
      level: determine_log_level(event_type, data)
    }
  end

  defp add_log_entry(state, log_entry) do
    # 添加到缓冲区
    updated_buffer = [log_entry | state.log_buffer]
    updated_size = state.buffer_size + 1
    
    # 添加到执行日志映射
    execution_logs = Map.get(state.execution_logs, log_entry.execution_id, [])
    updated_execution_logs = [log_entry | execution_logs]
    updated_logs_map = Map.put(state.execution_logs, log_entry.execution_id, updated_execution_logs)
    
    new_state = %{state |
      log_buffer: updated_buffer,
      buffer_size: updated_size,
      execution_logs: updated_logs_map
    }
    
    # 检查是否需要刷新缓冲区
    if updated_size >= state.max_buffer_size do
      flush_log_buffer(new_state)
    else
      new_state
    end
  end

  defp flush_log_buffer(state) do
    if state.buffer_size > 0 do
      # 批量写入数据库
      case persist_log_entries(state.log_buffer) do
        :ok ->
          Logger.debug("Flushed #{state.buffer_size} log entries to database")
          
          %{state |
            log_buffer: [],
            buffer_size: 0
          }
        
        {:error, reason} ->
          Logger.error("Failed to flush log buffer: #{inspect(reason)}")
          state
      end
    else
      state
    end
  end

  defp persist_log_entries(log_entries) do
    try do
      # 将日志条目批量插入数据库
      Enum.each(log_entries, fn log_entry ->
        # 这里可以插入到专门的执行日志表
        # 或者使用已有的execution_data表
        create_execution_log_record(log_entry)
      end)
      
      :ok
    rescue
      error ->
        {:error, error}
    end
  end

  defp create_execution_log_record(log_entry) do
    # 创建执行日志记录
    log_attrs = %{
      id: log_entry.id,
      execution_id: log_entry.execution_id,
      event_type: to_string(log_entry.event_type),
      node_name: log_entry.node_name,
      timestamp: log_entry.timestamp,
      level: to_string(log_entry.level),
      data: log_entry.data
    }
    
    # 在实际实现中，这里会插入到数据库
    # 现在只记录到系统日志
    Logger.log(log_entry.level, "Execution log", log_attrs)
  end

  defp get_logs_for_execution(state, execution_id, opts) do
    logs = Map.get(state.execution_logs, execution_id, [])
    
    # 应用过滤器和排序
    filtered_logs = apply_log_filters(logs, opts)
    
    # 限制返回数量
    limit = Keyword.get(opts, :limit, 100)
    Enum.take(filtered_logs, limit)
  end

  defp get_logs_for_node(state, execution_id, node_name) do
    execution_logs = Map.get(state.execution_logs, execution_id, [])
    
    Enum.filter(execution_logs, fn log_entry ->
      log_entry.node_name == node_name
    end)
  end

  defp search_logs_in_state(state, query, filters) do
    all_logs = 
      state.execution_logs
      |> Map.values()
      |> List.flatten()
    
    # 应用搜索查询
    query_filtered = if query && String.trim(query) != "" do
      Enum.filter(all_logs, fn log_entry ->
        search_in_log_entry(log_entry, query)
      end)
    else
      all_logs
    end
    
    # 应用其他过滤器
    apply_log_filters(query_filtered, filters)
  end

  defp apply_log_filters(logs, opts) do
    logs
    |> filter_by_level(Keyword.get(opts, :level))
    |> filter_by_event_type(Keyword.get(opts, :event_type))
    |> filter_by_time_range(Keyword.get(opts, :start_time), Keyword.get(opts, :end_time))
    |> sort_logs(Keyword.get(opts, :sort, :desc))
  end

  defp filter_by_level(logs, nil), do: logs
  defp filter_by_level(logs, level) do
    Enum.filter(logs, fn log -> log.level == level end)
  end

  defp filter_by_event_type(logs, nil), do: logs
  defp filter_by_event_type(logs, event_type) do
    Enum.filter(logs, fn log -> log.event_type == event_type end)
  end

  defp filter_by_time_range(logs, nil, nil), do: logs
  defp filter_by_time_range(logs, start_time, end_time) do
    Enum.filter(logs, fn log ->
      after_start = if start_time, do: DateTime.compare(log.timestamp, start_time) != :lt, else: true
      before_end = if end_time, do: DateTime.compare(log.timestamp, end_time) != :gt, else: true
      after_start and before_end
    end)
  end

  defp sort_logs(logs, :asc) do
    Enum.sort(logs, fn a, b -> DateTime.compare(a.timestamp, b.timestamp) != :gt end)
  end
  defp sort_logs(logs, :desc) do
    Enum.sort(logs, fn a, b -> DateTime.compare(a.timestamp, b.timestamp) != :lt end)
  end

  defp search_in_log_entry(log_entry, query) do
    query_lower = String.downcase(query)
    
    # 搜索节点名称
    node_match = if log_entry.node_name do
      String.contains?(String.downcase(log_entry.node_name), query_lower)
    else
      false
    end
    
    # 搜索事件类型
    event_match = String.contains?(String.downcase(to_string(log_entry.event_type)), query_lower)
    
    # 搜索数据内容
    data_match = search_in_data(log_entry.data, query_lower)
    
    node_match or event_match or data_match
  end

  defp search_in_data(data, query) when is_map(data) do
    data
    |> Map.values()
    |> Enum.any?(fn value -> search_in_value(value, query) end)
  end
  defp search_in_data(data, query) do
    search_in_value(data, query)
  end

  defp search_in_value(value, query) when is_binary(value) do
    String.contains?(String.downcase(value), query)
  end
  defp search_in_value(value, query) when is_atom(value) do
    String.contains?(String.downcase(Atom.to_string(value)), query)
  end
  defp search_in_value(value, query) when is_list(value) do
    Enum.any?(value, fn item -> search_in_value(item, query) end)
  end
  defp search_in_value(value, query) when is_map(value) do
    search_in_data(value, query)
  end
  defp search_in_value(_value, _query), do: false

  defp determine_log_level(event_type, data) do
    case event_type do
      :workflow_start -> :info
      :workflow_complete -> :info
      :node_start -> :debug
      :node_complete -> :debug
      :node_error -> :error
      :retry_attempt -> :warn
      :execution_paused -> :info
      :execution_resumed -> :info
      :execution_cancelled -> :warn
      _ -> 
        # 根据数据内容判断级别
        if Map.get(data, :status) == :error do
          :error
        else
          :info
        end
    end
  end

  defp preview_data(data, max_items \\ 3) do
    case data do
      items when is_list(items) ->
        preview_items = Enum.take(items, max_items)
        %{
          total_count: length(items),
          preview_count: length(preview_items),
          items: Enum.map(preview_items, &preview_single_item/1)
        }
      
      item ->
        preview_single_item(item)
    end
  end

  defp preview_single_item(item) when is_map(item) do
    # 只保留JSON数据的预览，移除敏感信息
    json_data = Map.get(item, "json", %{})
    
    # 限制预览数据的大小
    preview_json = 
      json_data
      |> Enum.take(5)  # 只取前5个字段
      |> Enum.into(%{})
    
    %{
      json: preview_json,
      has_binary: Map.has_key?(item, "binary") and map_size(Map.get(item, "binary", %{})) > 0
    }
  end
  defp preview_single_item(item), do: %{data: item}

  defp cleanup_old_logs(state) do
    # 清理超过一定时间的日志（例如7天）
    cutoff_time = DateTime.add(DateTime.utc_now(), -7 * 24 * 60 * 60, :second)
    
    cleaned_logs = 
      state.execution_logs
      |> Enum.reduce(%{}, fn {execution_id, logs}, acc ->
        recent_logs = Enum.filter(logs, fn log ->
          DateTime.compare(log.timestamp, cutoff_time) != :lt
        end)
        
        if Enum.empty?(recent_logs) do
          acc
        else
          Map.put(acc, execution_id, recent_logs)
        end
      end)
    
    cleaned_count = map_size(state.execution_logs) - map_size(cleaned_logs)
    
    if cleaned_count > 0 do
      Logger.info("Cleaned up #{cleaned_count} old execution logs")
    end
    
    %{state | execution_logs: cleaned_logs}
  end

  defp schedule_log_cleanup() do
    # 每小时清理一次旧日志
    Process.send_after(self(), :cleanup_old_logs, 60 * 60 * 1000)
  end
end
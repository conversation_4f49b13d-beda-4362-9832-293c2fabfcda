{application,makeup_erlang,
             [{modules,['Elixir.Makeup.Lexers.ErlangLexer',
                        'Elixir.Makeup.Lexers.ErlangLexer.Application',
                        'Elixir.Makeup.Lexers.ErlangLexer.Helper',
                        'Elixir.Makeup.Lexers.ErlangLexer.Testing']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger,makeup]},
              {description,"Erlang lexer for the Makeup syntax highlighter.\n"},
              {registered,[]},
              {vsn,"1.0.2"},
              {mod,{'Elixir.Makeup.Lexers.ErlangLexer.Application',[]}}]}.

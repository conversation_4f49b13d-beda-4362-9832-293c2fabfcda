(()=>{var ui=Object.defineProperty,fi=Object.defineProperties;var pi=Object.getOwnPropertyDescriptors;var xe=Object.getOwnPropertySymbols;var wt=Object.prototype.hasOwnProperty,St=Object.prototype.propertyIsEnumerable;var yt=(e,t,i)=>t in e?ui(e,t,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[t]=i,U=(e,t)=>{for(var i in t||(t={}))wt.call(t,i)&&yt(e,i,t[i]);if(xe)for(var i of xe(t))St.call(t,i)&&yt(e,i,t[i]);return e},At=(e,t)=>fi(e,pi(t));var kt=(e,t)=>{var i={};for(var s in e)wt.call(e,s)&&t.indexOf(s)<0&&(i[s]=e[s]);if(e!=null&&xe)for(var s of xe(e))t.indexOf(s)<0&&St.call(e,s)&&(i[s]=e[s]);return i};(function(){var e=t();function t(){if(typeof window.CustomEvent=="function")return window.CustomEvent;function n(r,o){o=o||{bubbles:!1,cancelable:!1,detail:void 0};var a=document.createEvent("CustomEvent");return a.initCustomEvent(r,o.bubbles,o.cancelable,o.detail),a}return n.prototype=window.Event.prototype,n}function i(n,r){var o=document.createElement("input");return o.type="hidden",o.name=n,o.value=r,o}function s(n,r){var o=n.getAttribute("data-to"),a=i("_method",n.getAttribute("data-method")),l=i("_csrf_token",n.getAttribute("data-csrf")),h=document.createElement("form"),d=document.createElement("input"),p=n.getAttribute("target");h.method=n.getAttribute("data-method")==="get"?"get":"post",h.action=o,h.style.display="none",p?h.target=p:r&&(h.target="_blank"),h.appendChild(l),h.appendChild(a),document.body.appendChild(h),d.type="submit",h.appendChild(d),d.click()}window.addEventListener("click",function(n){var r=n.target;if(!n.defaultPrevented)for(;r&&r.getAttribute;){var o=new e("phoenix.link.click",{bubbles:!0,cancelable:!0});if(!r.dispatchEvent(o))return n.preventDefault(),n.stopImmediatePropagation(),!1;if(r.getAttribute("data-method"))return s(r,n.metaKey||n.shiftKey),n.preventDefault(),!1;r=r.parentNode}},!1),window.addEventListener("phoenix.link.click",function(n){var r=n.target.getAttribute("data-confirm");r&&!window.confirm(r)&&n.preventDefault()},!1)})();var be=e=>typeof e=="function"?e:function(){return e},gi=typeof self!="undefined"?self:null,ve=typeof window!="undefined"?window:null,oe=gi||ve||oe,mi="2.0.0",j={connecting:0,open:1,closing:2,closed:3},vi=1e4,bi=1e3,H={closed:"closed",errored:"errored",joined:"joined",joining:"joining",leaving:"leaving"},q={close:"phx_close",error:"phx_error",join:"phx_join",reply:"phx_reply",leave:"phx_leave"},Ze={longpoll:"longpoll",websocket:"websocket"},yi={complete:4},Le=class{constructor(e,t,i,s){this.channel=e,this.event=t,this.payload=i||function(){return{}},this.receivedResp=null,this.timeout=s,this.timeoutTimer=null,this.recHooks=[],this.sent=!1}resend(e){this.timeout=e,this.reset(),this.send()}send(){this.hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload(),ref:this.ref,join_ref:this.channel.joinRef()}))}receive(e,t){return this.hasReceived(e)&&t(this.receivedResp.response),this.recHooks.push({status:e,callback:t}),this}reset(){this.cancelRefEvent(),this.ref=null,this.refEvent=null,this.receivedResp=null,this.sent=!1}matchReceive({status:e,response:t,_ref:i}){this.recHooks.filter(s=>s.status===e).forEach(s=>s.callback(t))}cancelRefEvent(){this.refEvent&&this.channel.off(this.refEvent)}cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=null}startTimeout(){this.timeoutTimer&&this.cancelTimeout(),this.ref=this.channel.socket.makeRef(),this.refEvent=this.channel.replyEventName(this.ref),this.channel.on(this.refEvent,e=>{this.cancelRefEvent(),this.cancelTimeout(),this.receivedResp=e,this.matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}trigger(e,t){this.channel.trigger(this.refEvent,{status:e,response:t})}},Ct=class{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=null,this.tries=0}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}},wi=class{constructor(e,t,i){this.state=H.closed,this.topic=e,this.params=be(t||{}),this.socket=i,this.bindings=[],this.bindingRef=0,this.timeout=this.socket.timeout,this.joinedOnce=!1,this.joinPush=new Le(this,q.join,this.params,this.timeout),this.pushBuffer=[],this.stateChangeRefs=[],this.rejoinTimer=new Ct(()=>{this.socket.isConnected()&&this.rejoin()},this.socket.rejoinAfterMs),this.stateChangeRefs.push(this.socket.onError(()=>this.rejoinTimer.reset())),this.stateChangeRefs.push(this.socket.onOpen(()=>{this.rejoinTimer.reset(),this.isErrored()&&this.rejoin()})),this.joinPush.receive("ok",()=>{this.state=H.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(s=>s.send()),this.pushBuffer=[]}),this.joinPush.receive("error",()=>{this.state=H.errored,this.socket.isConnected()&&this.rejoinTimer.scheduleTimeout()}),this.onClose(()=>{this.rejoinTimer.reset(),this.socket.hasLogger()&&this.socket.log("channel",`close ${this.topic} ${this.joinRef()}`),this.state=H.closed,this.socket.remove(this)}),this.onError(s=>{this.socket.hasLogger()&&this.socket.log("channel",`error ${this.topic}`,s),this.isJoining()&&this.joinPush.reset(),this.state=H.errored,this.socket.isConnected()&&this.rejoinTimer.scheduleTimeout()}),this.joinPush.receive("timeout",()=>{this.socket.hasLogger()&&this.socket.log("channel",`timeout ${this.topic} (${this.joinRef()})`,this.joinPush.timeout),new Le(this,q.leave,be({}),this.timeout).send(),this.state=H.errored,this.joinPush.reset(),this.socket.isConnected()&&this.rejoinTimer.scheduleTimeout()}),this.on(q.reply,(s,n)=>{this.trigger(this.replyEventName(n),s)})}join(e=this.timeout){if(this.joinedOnce)throw new Error("tried to join multiple times. 'join' can only be called a single time per channel instance");return this.timeout=e,this.joinedOnce=!0,this.rejoin(),this.joinPush}onClose(e){this.on(q.close,e)}onError(e){return this.on(q.error,t=>e(t))}on(e,t){let i=this.bindingRef++;return this.bindings.push({event:e,ref:i,callback:t}),i}off(e,t){this.bindings=this.bindings.filter(i=>!(i.event===e&&(typeof t=="undefined"||t===i.ref)))}canPush(){return this.socket.isConnected()&&this.isJoined()}push(e,t,i=this.timeout){if(t=t||{},!this.joinedOnce)throw new Error(`tried to push '${e}' to '${this.topic}' before joining. Use channel.join() before pushing events`);let s=new Le(this,e,function(){return t},i);return this.canPush()?s.send():(s.startTimeout(),this.pushBuffer.push(s)),s}leave(e=this.timeout){this.rejoinTimer.reset(),this.joinPush.cancelTimeout(),this.state=H.leaving;let t=()=>{this.socket.hasLogger()&&this.socket.log("channel",`leave ${this.topic}`),this.trigger(q.close,"leave")},i=new Le(this,q.leave,be({}),e);return i.receive("ok",()=>t()).receive("timeout",()=>t()),i.send(),this.canPush()||i.trigger("ok",{}),i}onMessage(e,t,i){return t}isMember(e,t,i,s){return this.topic!==e?!1:s&&s!==this.joinRef()?(this.socket.hasLogger()&&this.socket.log("channel","dropping outdated message",{topic:e,event:t,payload:i,joinRef:s}),!1):!0}joinRef(){return this.joinPush.ref}rejoin(e=this.timeout){this.isLeaving()||(this.socket.leaveOpenTopic(this.topic),this.state=H.joining,this.joinPush.resend(e))}trigger(e,t,i,s){let n=this.onMessage(e,t,i,s);if(t&&!n)throw new Error("channel onMessage callbacks must return the payload, modified or unmodified");let r=this.bindings.filter(o=>o.event===e);for(let o=0;o<r.length;o++)r[o].callback(n,i,s||this.joinRef())}replyEventName(e){return`chan_reply_${e}`}isClosed(){return this.state===H.closed}isErrored(){return this.state===H.errored}isJoined(){return this.state===H.joined}isJoining(){return this.state===H.joining}isLeaving(){return this.state===H.leaving}},De=class{static request(e,t,i,s,n,r,o){if(oe.XDomainRequest){let a=new oe.XDomainRequest;return this.xdomainRequest(a,e,t,s,n,r,o)}else{let a=new oe.XMLHttpRequest;return this.xhrRequest(a,e,t,i,s,n,r,o)}}static xdomainRequest(e,t,i,s,n,r,o){return e.timeout=n,e.open(t,i),e.onload=()=>{let a=this.parseJSON(e.responseText);o&&o(a)},r&&(e.ontimeout=r),e.onprogress=()=>{},e.send(s),e}static xhrRequest(e,t,i,s,n,r,o,a){return e.open(t,i,!0),e.timeout=r,e.setRequestHeader("Content-Type",s),e.onerror=()=>a&&a(null),e.onreadystatechange=()=>{if(e.readyState===yi.complete&&a){let l=this.parseJSON(e.responseText);a(l)}},o&&(e.ontimeout=o),e.send(n),e}static parseJSON(e){if(!e||e==="")return null;try{return JSON.parse(e)}catch(t){return console&&console.log("failed to parse JSON response",e),null}}static serialize(e,t){let i=[];for(var s in e){if(!Object.prototype.hasOwnProperty.call(e,s))continue;let n=t?`${t}[${s}]`:s,r=e[s];typeof r=="object"?i.push(this.serialize(r,n)):i.push(encodeURIComponent(n)+"="+encodeURIComponent(r))}return i.join("&")}static appendParams(e,t){if(Object.keys(t).length===0)return e;let i=e.match(/\?/)?"&":"?";return`${e}${i}${this.serialize(t)}`}},Si=e=>{let t="",i=new Uint8Array(e),s=i.byteLength;for(let n=0;n<s;n++)t+=String.fromCharCode(i[n]);return btoa(t)},me=class{constructor(e){this.endPoint=null,this.token=null,this.skipHeartbeat=!0,this.reqs=new Set,this.awaitingBatchAck=!1,this.currentBatch=null,this.currentBatchTimer=null,this.batchBuffer=[],this.onopen=function(){},this.onerror=function(){},this.onmessage=function(){},this.onclose=function(){},this.pollEndpoint=this.normalizeEndpoint(e),this.readyState=j.connecting,setTimeout(()=>this.poll(),0)}normalizeEndpoint(e){return e.replace("ws://","http://").replace("wss://","https://").replace(new RegExp("(.*)/"+Ze.websocket),"$1/"+Ze.longpoll)}endpointURL(){return De.appendParams(this.pollEndpoint,{token:this.token})}closeAndRetry(e,t,i){this.close(e,t,i),this.readyState=j.connecting}ontimeout(){this.onerror("timeout"),this.closeAndRetry(1005,"timeout",!1)}isActive(){return this.readyState===j.open||this.readyState===j.connecting}poll(){this.ajax("GET","application/json",null,()=>this.ontimeout(),e=>{if(e){var{status:t,token:i,messages:s}=e;this.token=i}else t=0;switch(t){case 200:s.forEach(n=>{setTimeout(()=>this.onmessage({data:n}),0)}),this.poll();break;case 204:this.poll();break;case 410:this.readyState=j.open,this.onopen({}),this.poll();break;case 403:this.onerror(403),this.close(1008,"forbidden",!1);break;case 0:case 500:this.onerror(500),this.closeAndRetry(1011,"internal server error",500);break;default:throw new Error(`unhandled poll status ${t}`)}})}send(e){typeof e!="string"&&(e=Si(e)),this.currentBatch?this.currentBatch.push(e):this.awaitingBatchAck?this.batchBuffer.push(e):(this.currentBatch=[e],this.currentBatchTimer=setTimeout(()=>{this.batchSend(this.currentBatch),this.currentBatch=null},0))}batchSend(e){this.awaitingBatchAck=!0,this.ajax("POST","application/x-ndjson",e.join(`
`),()=>this.onerror("timeout"),t=>{this.awaitingBatchAck=!1,!t||t.status!==200?(this.onerror(t&&t.status),this.closeAndRetry(1011,"internal server error",!1)):this.batchBuffer.length>0&&(this.batchSend(this.batchBuffer),this.batchBuffer=[])})}close(e,t,i){for(let n of this.reqs)n.abort();this.readyState=j.closed;let s=Object.assign({code:1e3,reason:void 0,wasClean:!0},{code:e,reason:t,wasClean:i});this.batchBuffer=[],clearTimeout(this.currentBatchTimer),this.currentBatchTimer=null,typeof CloseEvent!="undefined"?this.onclose(new CloseEvent("close",s)):this.onclose(s)}ajax(e,t,i,s,n){let r,o=()=>{this.reqs.delete(r),s()};r=De.request(e,this.endpointURL(),t,i,this.timeout,o,a=>{this.reqs.delete(r),this.isActive()&&n(a)}),this.reqs.add(r)}};var Ie={HEADER_LENGTH:1,META_LENGTH:4,KINDS:{push:0,reply:1,broadcast:2},encode(e,t){if(e.payload.constructor===ArrayBuffer)return t(this.binaryEncode(e));{let i=[e.join_ref,e.ref,e.topic,e.event,e.payload];return t(JSON.stringify(i))}},decode(e,t){if(e.constructor===ArrayBuffer)return t(this.binaryDecode(e));{let[i,s,n,r,o]=JSON.parse(e);return t({join_ref:i,ref:s,topic:n,event:r,payload:o})}},binaryEncode(e){let{join_ref:t,ref:i,event:s,topic:n,payload:r}=e,o=this.META_LENGTH+t.length+i.length+n.length+s.length,a=new ArrayBuffer(this.HEADER_LENGTH+o),l=new DataView(a),h=0;l.setUint8(h++,this.KINDS.push),l.setUint8(h++,t.length),l.setUint8(h++,i.length),l.setUint8(h++,n.length),l.setUint8(h++,s.length),Array.from(t,p=>l.setUint8(h++,p.charCodeAt(0))),Array.from(i,p=>l.setUint8(h++,p.charCodeAt(0))),Array.from(n,p=>l.setUint8(h++,p.charCodeAt(0))),Array.from(s,p=>l.setUint8(h++,p.charCodeAt(0)));var d=new Uint8Array(a.byteLength+r.byteLength);return d.set(new Uint8Array(a),0),d.set(new Uint8Array(r),a.byteLength),d.buffer},binaryDecode(e){let t=new DataView(e),i=t.getUint8(0),s=new TextDecoder;switch(i){case this.KINDS.push:return this.decodePush(e,t,s);case this.KINDS.reply:return this.decodeReply(e,t,s);case this.KINDS.broadcast:return this.decodeBroadcast(e,t,s)}},decodePush(e,t,i){let s=t.getUint8(1),n=t.getUint8(2),r=t.getUint8(3),o=this.HEADER_LENGTH+this.META_LENGTH-1,a=i.decode(e.slice(o,o+s));o=o+s;let l=i.decode(e.slice(o,o+n));o=o+n;let h=i.decode(e.slice(o,o+r));o=o+r;let d=e.slice(o,e.byteLength);return{join_ref:a,ref:null,topic:l,event:h,payload:d}},decodeReply(e,t,i){let s=t.getUint8(1),n=t.getUint8(2),r=t.getUint8(3),o=t.getUint8(4),a=this.HEADER_LENGTH+this.META_LENGTH,l=i.decode(e.slice(a,a+s));a=a+s;let h=i.decode(e.slice(a,a+n));a=a+n;let d=i.decode(e.slice(a,a+r));a=a+r;let p=i.decode(e.slice(a,a+o));a=a+o;let f=e.slice(a,e.byteLength),g={status:p,response:f};return{join_ref:l,ref:h,topic:d,event:q.reply,payload:g}},decodeBroadcast(e,t,i){let s=t.getUint8(1),n=t.getUint8(2),r=this.HEADER_LENGTH+2,o=i.decode(e.slice(r,r+s));r=r+s;let a=i.decode(e.slice(r,r+n));r=r+n;let l=e.slice(r,e.byteLength);return{join_ref:null,ref:null,topic:o,event:a,payload:l}}},Et=class{constructor(e,t={}){this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.channels=[],this.sendBuffer=[],this.ref=0,this.timeout=t.timeout||vi,this.transport=t.transport||oe.WebSocket||me,this.primaryPassedHealthCheck=!1,this.longPollFallbackMs=t.longPollFallbackMs,this.fallbackTimer=null,this.sessionStore=t.sessionStorage||oe&&oe.sessionStorage,this.establishedConnections=0,this.defaultEncoder=Ie.encode.bind(Ie),this.defaultDecoder=Ie.decode.bind(Ie),this.closeWasClean=!1,this.disconnecting=!1,this.binaryType=t.binaryType||"arraybuffer",this.connectClock=1,this.transport!==me?(this.encode=t.encode||this.defaultEncoder,this.decode=t.decode||this.defaultDecoder):(this.encode=this.defaultEncoder,this.decode=this.defaultDecoder);let i=null;ve&&ve.addEventListener&&(ve.addEventListener("pagehide",s=>{this.conn&&(this.disconnect(),i=this.connectClock)}),ve.addEventListener("pageshow",s=>{i===this.connectClock&&(i=null,this.connect())})),this.heartbeatIntervalMs=t.heartbeatIntervalMs||3e4,this.rejoinAfterMs=s=>t.rejoinAfterMs?t.rejoinAfterMs(s):[1e3,2e3,5e3][s-1]||1e4,this.reconnectAfterMs=s=>t.reconnectAfterMs?t.reconnectAfterMs(s):[10,50,100,150,200,250,500,1e3,2e3][s-1]||5e3,this.logger=t.logger||null,!this.logger&&t.debug&&(this.logger=(s,n,r)=>{console.log(`${s}: ${n}`,r)}),this.longpollerTimeout=t.longpollerTimeout||2e4,this.params=be(t.params||{}),this.endPoint=`${e}/${Ze.websocket}`,this.vsn=t.vsn||mi,this.heartbeatTimeoutTimer=null,this.heartbeatTimer=null,this.pendingHeartbeatRef=null,this.reconnectTimer=new Ct(()=>{this.teardown(()=>this.connect())},this.reconnectAfterMs)}getLongPollTransport(){return me}replaceTransport(e){this.connectClock++,this.closeWasClean=!0,clearTimeout(this.fallbackTimer),this.reconnectTimer.reset(),this.conn&&(this.conn.close(),this.conn=null),this.transport=e}protocol(){return location.protocol.match(/^https/)?"wss":"ws"}endPointURL(){let e=De.appendParams(De.appendParams(this.endPoint,this.params()),{vsn:this.vsn});return e.charAt(0)!=="/"?e:e.charAt(1)==="/"?`${this.protocol()}:${e}`:`${this.protocol()}://${location.host}${e}`}disconnect(e,t,i){this.connectClock++,this.disconnecting=!0,this.closeWasClean=!0,clearTimeout(this.fallbackTimer),this.reconnectTimer.reset(),this.teardown(()=>{this.disconnecting=!1,e&&e()},t,i)}connect(e){e&&(console&&console.log("passing params to connect is deprecated. Instead pass :params to the Socket constructor"),this.params=be(e)),!(this.conn&&!this.disconnecting)&&(this.longPollFallbackMs&&this.transport!==me?this.connectWithFallback(me,this.longPollFallbackMs):this.transportConnect())}log(e,t,i){this.logger&&this.logger(e,t,i)}hasLogger(){return this.logger!==null}onOpen(e){let t=this.makeRef();return this.stateChangeCallbacks.open.push([t,e]),t}onClose(e){let t=this.makeRef();return this.stateChangeCallbacks.close.push([t,e]),t}onError(e){let t=this.makeRef();return this.stateChangeCallbacks.error.push([t,e]),t}onMessage(e){let t=this.makeRef();return this.stateChangeCallbacks.message.push([t,e]),t}ping(e){if(!this.isConnected())return!1;let t=this.makeRef(),i=Date.now();this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:t});let s=this.onMessage(n=>{n.ref===t&&(this.off([s]),e(Date.now()-i))});return!0}transportConnect(){this.connectClock++,this.closeWasClean=!1,this.conn=new this.transport(this.endPointURL()),this.conn.binaryType=this.binaryType,this.conn.timeout=this.longpollerTimeout,this.conn.onopen=()=>this.onConnOpen(),this.conn.onerror=e=>this.onConnError(e),this.conn.onmessage=e=>this.onConnMessage(e),this.conn.onclose=e=>this.onConnClose(e)}getSession(e){return this.sessionStore&&this.sessionStore.getItem(e)}storeSession(e,t){this.sessionStore&&this.sessionStore.setItem(e,t)}connectWithFallback(e,t=2500){clearTimeout(this.fallbackTimer);let i=!1,s=!0,n,r,o=a=>{this.log("transport",`falling back to ${e.name}...`,a),this.off([n,r]),s=!1,this.replaceTransport(e),this.transportConnect()};if(this.getSession(`phx:fallback:${e.name}`))return o("memorized");this.fallbackTimer=setTimeout(o,t),r=this.onError(a=>{this.log("transport","error",a),s&&!i&&(clearTimeout(this.fallbackTimer),o(a))}),this.onOpen(()=>{if(i=!0,!s)return this.primaryPassedHealthCheck||this.storeSession(`phx:fallback:${e.name}`,"true"),this.log("transport",`established ${e.name} fallback`);clearTimeout(this.fallbackTimer),this.fallbackTimer=setTimeout(o,t),this.ping(a=>{this.log("transport","connected to primary after",a),this.primaryPassedHealthCheck=!0,clearTimeout(this.fallbackTimer)})}),this.transportConnect()}clearHeartbeats(){clearTimeout(this.heartbeatTimer),clearTimeout(this.heartbeatTimeoutTimer)}onConnOpen(){this.hasLogger()&&this.log("transport",`${this.transport.name} connected to ${this.endPointURL()}`),this.closeWasClean=!1,this.disconnecting=!1,this.establishedConnections++,this.flushSendBuffer(),this.reconnectTimer.reset(),this.resetHeartbeat(),this.stateChangeCallbacks.open.forEach(([,e])=>e())}heartbeatTimeout(){this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null,this.hasLogger()&&this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.triggerChanError(),this.closeWasClean=!1,this.teardown(()=>this.reconnectTimer.scheduleTimeout(),bi,"heartbeat timeout"))}resetHeartbeat(){this.conn&&this.conn.skipHeartbeat||(this.pendingHeartbeatRef=null,this.clearHeartbeats(),this.heartbeatTimer=setTimeout(()=>this.sendHeartbeat(),this.heartbeatIntervalMs))}teardown(e,t,i){if(!this.conn)return e&&e();let s=this.connectClock;this.waitForBufferDone(()=>{s===this.connectClock&&(this.conn&&(t?this.conn.close(t,i||""):this.conn.close()),this.waitForSocketClosed(()=>{s===this.connectClock&&(this.conn&&(this.conn.onopen=function(){},this.conn.onerror=function(){},this.conn.onmessage=function(){},this.conn.onclose=function(){},this.conn=null),e&&e())}))})}waitForBufferDone(e,t=1){if(t===5||!this.conn||!this.conn.bufferedAmount){e();return}setTimeout(()=>{this.waitForBufferDone(e,t+1)},150*t)}waitForSocketClosed(e,t=1){if(t===5||!this.conn||this.conn.readyState===j.closed){e();return}setTimeout(()=>{this.waitForSocketClosed(e,t+1)},150*t)}onConnClose(e){let t=e&&e.code;this.hasLogger()&&this.log("transport","close",e),this.triggerChanError(),this.clearHeartbeats(),!this.closeWasClean&&t!==1e3&&this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(([,i])=>i(e))}onConnError(e){this.hasLogger()&&this.log("transport",e);let t=this.transport,i=this.establishedConnections;this.stateChangeCallbacks.error.forEach(([,s])=>{s(e,t,i)}),(t===this.transport||i>0)&&this.triggerChanError()}triggerChanError(){this.channels.forEach(e=>{e.isErrored()||e.isLeaving()||e.isClosed()||e.trigger(q.error)})}connectionState(){switch(this.conn&&this.conn.readyState){case j.connecting:return"connecting";case j.open:return"open";case j.closing:return"closing";default:return"closed"}}isConnected(){return this.connectionState()==="open"}remove(e){this.off(e.stateChangeRefs),this.channels=this.channels.filter(t=>t!==e)}off(e){for(let t in this.stateChangeCallbacks)this.stateChangeCallbacks[t]=this.stateChangeCallbacks[t].filter(([i])=>e.indexOf(i)===-1)}channel(e,t={}){let i=new wi(e,t,this);return this.channels.push(i),i}push(e){if(this.hasLogger()){let{topic:t,event:i,payload:s,ref:n,join_ref:r}=e;this.log("push",`${t} ${i} (${r}, ${n})`,s)}this.isConnected()?this.encode(e,t=>this.conn.send(t)):this.sendBuffer.push(()=>this.encode(e,t=>this.conn.send(t)))}makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}sendHeartbeat(){this.pendingHeartbeatRef&&!this.isConnected()||(this.pendingHeartbeatRef=this.makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatTimeoutTimer=setTimeout(()=>this.heartbeatTimeout(),this.heartbeatIntervalMs))}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}onConnMessage(e){this.decode(e.data,t=>{let{topic:i,event:s,payload:n,ref:r,join_ref:o}=t;r&&r===this.pendingHeartbeatRef&&(this.clearHeartbeats(),this.pendingHeartbeatRef=null,this.heartbeatTimer=setTimeout(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)),this.hasLogger()&&this.log("receive",`${n.status||""} ${i} ${s} ${r&&"("+r+")"||""}`,n);for(let a=0;a<this.channels.length;a++){let l=this.channels[a];l.isMember(i,s,n,o)&&l.trigger(s,n,r,o)}for(let a=0;a<this.stateChangeCallbacks.message.length;a++){let[,l]=this.stateChangeCallbacks.message[a];l(t)}})}leaveOpenTopic(e){let t=this.channels.find(i=>i.topic===e&&(i.isJoined()||i.isJoining()));t&&(this.hasLogger()&&this.log("transport",`leaving duplicate topic "${e}"`),t.leave())}};var Qt="consecutive-reloads",Ai=10,ki=5e3,Ci=1e4,Ei=3e4,Zt=["phx-click-loading","phx-change-loading","phx-submit-loading","phx-keydown-loading","phx-keyup-loading","phx-blur-loading","phx-focus-loading","phx-hook-loading"],K="data-phx-component",et="data-phx-link",Ti="track-static",Pi="data-phx-link-state",M="data-phx-ref",te="data-phx-ref-src",ei="track-uploads",G="data-phx-upload-ref",pt="data-phx-preflighted-refs",_i="data-phx-done-refs",Tt="drop-target",ht="data-phx-active-refs",Ve="phx:live-file:updated",ti="data-phx-skip",ii="data-phx-id",Pt="data-phx-prune",_t="page-loading",Rt="phx-connected",ye="phx-loading",Oe="phx-no-feedback",He="phx-error",xt="phx-client-error",tt="phx-server-error",ae="data-phx-parent-id",gt="data-phx-main",le="data-phx-root-id",dt="viewport-top",ct="viewport-bottom",Ri="trigger-action",Xe="feedback-for",We="feedback-group",ut="phx-has-focused",xi=["text","textarea","number","email","password","search","tel","url","date","time","datetime-local","color","range"],si=["checkbox","radio"],qe="phx-has-submitted",Y="data-phx-session",ge=`[${Y}]`,Lt="data-phx-sticky",fe="data-phx-static",it="data-phx-readonly",ce="data-phx-disabled",ft="disable-with",Me="data-phx-disable-with-restore",we="hook",Li="debounce",Ii="throttle",ze="update",st="stream",nt="data-phx-stream",Di="key",B="phxPrivate",It="auto-recover",Ne="phx:live-socket:debug",rt="phx:live-socket:profiling",ot="phx:live-socket:latency-sim",Oi="progress",Dt="mounted",Hi=1,Mi=200,Ni="phx-",Fi=3e4,Se="debounce-trigger",Ae="throttled",Ot="debounce-prev-key",$i={debounce:300,throttle:300},Fe="d",V="s",at="r",R="c",Ht="e",Mt="r",Nt="t",Ui="p",Ft="stream",ji=class{constructor(e,t,i){this.liveSocket=i,this.entry=e,this.offset=0,this.chunkSize=t,this.chunkTimer=null,this.errored=!1,this.uploadChannel=i.channel(`lvu:${e.ref}`,{token:e.metadata()})}error(e){this.errored||(this.uploadChannel.leave(),this.errored=!0,clearTimeout(this.chunkTimer),this.entry.error(e))}upload(){this.uploadChannel.onError(e=>this.error(e)),this.uploadChannel.join().receive("ok",e=>this.readNextChunk()).receive("error",e=>this.error(e))}isDone(){return this.offset>=this.entry.file.size}readNextChunk(){let e=new window.FileReader,t=this.entry.file.slice(this.offset,this.chunkSize+this.offset);e.onload=i=>{if(i.target.error===null)this.offset+=i.target.result.byteLength,this.pushChunk(i.target.result);else return N("Read error: "+i.target.error)},e.readAsArrayBuffer(t)}pushChunk(e){this.uploadChannel.isJoined()&&this.uploadChannel.push("chunk",e).receive("ok",()=>{this.entry.progress(this.offset/this.entry.file.size*100),this.isDone()||(this.chunkTimer=setTimeout(()=>this.readNextChunk(),this.liveSocket.getLatencySim()||0))}).receive("error",({reason:t})=>this.error(t))}},N=(e,t)=>console.error&&console.error(e,t),z=e=>{let t=typeof e;return t==="number"||t==="string"&&/^(0|[1-9]\d*)$/.test(e)};function Bi(){let e=new Set,t=document.querySelectorAll("*[id]");for(let i=0,s=t.length;i<s;i++)e.has(t[i].id)?console.error(`Multiple IDs detected: ${t[i].id}. Ensure unique element ids.`):e.add(t[i].id)}var Vi=(e,t,i,s)=>{e.liveSocket.isDebugEnabled()&&console.log(`${e.id} ${t}: ${i} - `,s)},ke=e=>typeof e=="function"?e:function(){return e},Je=e=>JSON.parse(JSON.stringify(e)),Pe=(e,t,i)=>{do{if(e.matches(`[${t}]`)&&!e.disabled)return e;e=e.parentElement||e.parentNode}while(e!==null&&e.nodeType===1&&!(i&&i.isSameNode(e)||e.matches(ge)));return null},ue=e=>e!==null&&typeof e=="object"&&!(e instanceof Array),Ji=(e,t)=>JSON.stringify(e)===JSON.stringify(t),$t=e=>{for(let t in e)return!1;return!0},ie=(e,t)=>e&&t(e),Xi=function(e,t,i,s){e.forEach(n=>{new ji(n,i.config.chunk_size,s).upload()})},ni={canPushState(){return typeof history.pushState!="undefined"},dropLocal(e,t,i){return e.removeItem(this.localKey(t,i))},updateLocal(e,t,i,s,n){let r=this.getLocal(e,t,i),o=this.localKey(t,i),a=r===null?s:n(r);return e.setItem(o,JSON.stringify(a)),a},getLocal(e,t,i){return JSON.parse(e.getItem(this.localKey(t,i)))},updateCurrentState(e){this.canPushState()&&history.replaceState(e(history.state||{}),"",window.location.href)},pushState(e,t,i){if(this.canPushState()){if(i!==window.location.href){if(t.type=="redirect"&&t.scroll){let n=history.state||{};n.scroll=t.scroll,history.replaceState(n,"",window.location.href)}delete t.scroll,history[e+"State"](t,"",i||null);let s=this.getHashTargetEl(window.location.hash);s?s.scrollIntoView():t.type==="redirect"&&window.scroll(0,0)}}else this.redirect(i)},setCookie(e,t){document.cookie=`${e}=${t}`},getCookie(e){return document.cookie.replace(new RegExp(`(?:(?:^|.*;s*)${e}s*=s*([^;]*).*$)|^.*$`),"$1")},redirect(e,t){t&&ni.setCookie("__phoenix_flash__",t+"; max-age=60000; path=/"),window.location=e},localKey(e,t){return`${e}-${t}`},getHashTargetEl(e){let t=e.toString().substring(1);if(t!=="")return document.getElementById(t)||document.querySelector(`a[name="${t}"]`)}},J=ni,Wi={focusMain(){let e=document.querySelector("main h1, main, h1");if(e){let t=e.tabIndex;e.tabIndex=-1,e.focus(),e.tabIndex=t}},anyOf(e,t){return t.find(i=>e instanceof i)},isFocusable(e,t){return e instanceof HTMLAnchorElement&&e.rel!=="ignore"||e instanceof HTMLAreaElement&&e.href!==void 0||!e.disabled&&this.anyOf(e,[HTMLInputElement,HTMLSelectElement,HTMLTextAreaElement,HTMLButtonElement])||e instanceof HTMLIFrameElement||e.tabIndex>0||!t&&e.getAttribute("tabindex")!==null&&e.getAttribute("aria-hidden")!=="true"},attemptFocus(e,t){if(this.isFocusable(e,t))try{e.focus()}catch(i){}return!!document.activeElement&&document.activeElement.isSameNode(e)},focusFirstInteractive(e){let t=e.firstElementChild;for(;t;){if(this.attemptFocus(t,!0)||this.focusFirstInteractive(t,!0))return!0;t=t.nextElementSibling}},focusFirst(e){let t=e.firstElementChild;for(;t;){if(this.attemptFocus(t)||this.focusFirst(t))return!0;t=t.nextElementSibling}},focusLast(e){let t=e.lastElementChild;for(;t;){if(this.attemptFocus(t)||this.focusLast(t))return!0;t=t.previousElementSibling}}},pe=Wi,Ut=[],jt=200,qi={exec(e,t,i,s,n){let[r,o]=n||[null,{callback:n&&n.callback}];(t.charAt(0)==="["?JSON.parse(t):[[r,o]]).forEach(([l,h])=>{l===r&&o.data&&(h.data=Object.assign(h.data||{},o.data),h.callback=h.callback||o.callback),this.filterToEls(s,h).forEach(d=>{this[`exec_${l}`](e,t,i,s,d,h)})})},isVisible(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length>0)},isInViewport(e){let t=e.getBoundingClientRect(),i=window.innerHeight||document.documentElement.clientHeight,s=window.innerWidth||document.documentElement.clientWidth;return t.right>0&&t.bottom>0&&t.left<s&&t.top<i},exec_exec(e,t,i,s,n,{attr:r,to:o}){(o?c.all(document,o):[s]).forEach(l=>{let h=l.getAttribute(r);if(!h)throw new Error(`expected ${r} to contain JS command on "${o}"`);i.liveSocket.execJS(l,h,e)})},exec_dispatch(e,t,i,s,n,{to:r,event:o,detail:a,bubbles:l}){a=a||{},a.dispatcher=s,c.dispatchEvent(n,o,{detail:a,bubbles:l})},exec_push(e,t,i,s,n,r){let{event:o,data:a,target:l,page_loading:h,loading:d,value:p,dispatcher:f,callback:g}=r,m={loading:d,value:p,target:l,page_loading:!!h},w=e==="change"&&f?f:s,P=l||w.getAttribute(i.binding("target"))||w;i.withinTargets(P,(_,L)=>{if(_.isConnected())if(e==="change"){let{newCid:F,_target:I}=r;I=I||(c.isFormInput(s)?s.name:void 0),I&&(m._target=I),_.pushInput(s,L,F,o||t,m,g)}else if(e==="submit"){let{submitter:F}=r;_.submitForm(s,L,o||t,F,m,g)}else _.pushEvent(e,s,L,o||t,a,m,g)})},exec_navigate(e,t,i,s,n,{href:r,replace:o}){i.liveSocket.historyRedirect(r,o?"replace":"push")},exec_patch(e,t,i,s,n,{href:r,replace:o}){i.liveSocket.pushHistoryPatch(r,o?"replace":"push",s)},exec_focus(e,t,i,s,n){window.requestAnimationFrame(()=>pe.attemptFocus(n))},exec_focus_first(e,t,i,s,n){window.requestAnimationFrame(()=>pe.focusFirstInteractive(n)||pe.focusFirst(n))},exec_push_focus(e,t,i,s,n){window.requestAnimationFrame(()=>Ut.push(n||s))},exec_pop_focus(e,t,i,s,n){window.requestAnimationFrame(()=>{let r=Ut.pop();r&&r.focus()})},exec_add_class(e,t,i,s,n,{names:r,transition:o,time:a}){this.addOrRemoveClasses(n,r,[],o,a,i)},exec_remove_class(e,t,i,s,n,{names:r,transition:o,time:a}){this.addOrRemoveClasses(n,[],r,o,a,i)},exec_toggle_class(e,t,i,s,n,{to:r,names:o,transition:a,time:l}){this.toggleClasses(n,o,a,l,i)},exec_toggle_attr(e,t,i,s,n,{attr:[r,o,a]}){n.hasAttribute(r)?a!==void 0?n.getAttribute(r)===o?this.setOrRemoveAttrs(n,[[r,a]],[]):this.setOrRemoveAttrs(n,[[r,o]],[]):this.setOrRemoveAttrs(n,[],[r]):this.setOrRemoveAttrs(n,[[r,o]],[])},exec_transition(e,t,i,s,n,{time:r,transition:o}){this.addOrRemoveClasses(n,[],[],o,r,i)},exec_toggle(e,t,i,s,n,{display:r,ins:o,outs:a,time:l}){this.toggle(e,i,n,r,o,a,l)},exec_show(e,t,i,s,n,{display:r,transition:o,time:a}){this.show(e,i,n,r,o,a)},exec_hide(e,t,i,s,n,{display:r,transition:o,time:a}){this.hide(e,i,n,r,o,a)},exec_set_attr(e,t,i,s,n,{attr:[r,o]}){this.setOrRemoveAttrs(n,[[r,o]],[])},exec_remove_attr(e,t,i,s,n,{attr:r}){this.setOrRemoveAttrs(n,[],[r])},show(e,t,i,s,n,r){this.isVisible(i)||this.toggle(e,t,i,s,n,null,r)},hide(e,t,i,s,n,r){this.isVisible(i)&&this.toggle(e,t,i,s,null,n,r)},toggle(e,t,i,s,n,r,o){o=o||jt;let[a,l,h]=n||[[],[],[]],[d,p,f]=r||[[],[],[]];if(a.length>0||d.length>0)if(this.isVisible(i)){let g=()=>{this.addOrRemoveClasses(i,p,a.concat(l).concat(h)),window.requestAnimationFrame(()=>{this.addOrRemoveClasses(i,d,[]),window.requestAnimationFrame(()=>this.addOrRemoveClasses(i,f,p))})};i.dispatchEvent(new Event("phx:hide-start")),t.transition(o,g,()=>{this.addOrRemoveClasses(i,[],d.concat(f)),c.putSticky(i,"toggle",m=>m.style.display="none"),i.dispatchEvent(new Event("phx:hide-end"))})}else{if(e==="remove")return;let g=()=>{this.addOrRemoveClasses(i,l,d.concat(p).concat(f));let m=s||this.defaultDisplay(i);c.putSticky(i,"toggle",w=>w.style.display=m),window.requestAnimationFrame(()=>{this.addOrRemoveClasses(i,a,[]),window.requestAnimationFrame(()=>this.addOrRemoveClasses(i,h,l))})};i.dispatchEvent(new Event("phx:show-start")),t.transition(o,g,()=>{this.addOrRemoveClasses(i,[],a.concat(h)),i.dispatchEvent(new Event("phx:show-end"))})}else this.isVisible(i)?window.requestAnimationFrame(()=>{i.dispatchEvent(new Event("phx:hide-start")),c.putSticky(i,"toggle",g=>g.style.display="none"),i.dispatchEvent(new Event("phx:hide-end"))}):window.requestAnimationFrame(()=>{i.dispatchEvent(new Event("phx:show-start"));let g=s||this.defaultDisplay(i);c.putSticky(i,"toggle",m=>m.style.display=g),i.dispatchEvent(new Event("phx:show-end"))})},toggleClasses(e,t,i,s,n){window.requestAnimationFrame(()=>{let[r,o]=c.getSticky(e,"classes",[[],[]]),a=t.filter(h=>r.indexOf(h)<0&&!e.classList.contains(h)),l=t.filter(h=>o.indexOf(h)<0&&e.classList.contains(h));this.addOrRemoveClasses(e,a,l,i,s,n)})},addOrRemoveClasses(e,t,i,s,n,r){n=n||jt;let[o,a,l]=s||[[],[],[]];if(o.length>0){let h=()=>{this.addOrRemoveClasses(e,a,[].concat(o).concat(l)),window.requestAnimationFrame(()=>{this.addOrRemoveClasses(e,o,[]),window.requestAnimationFrame(()=>this.addOrRemoveClasses(e,l,a))})},d=()=>this.addOrRemoveClasses(e,t.concat(l),i.concat(o).concat(a));return r.transition(n,h,d)}window.requestAnimationFrame(()=>{let[h,d]=c.getSticky(e,"classes",[[],[]]),p=t.filter(w=>h.indexOf(w)<0&&!e.classList.contains(w)),f=i.filter(w=>d.indexOf(w)<0&&e.classList.contains(w)),g=h.filter(w=>i.indexOf(w)<0).concat(p),m=d.filter(w=>t.indexOf(w)<0).concat(f);c.putSticky(e,"classes",w=>(w.classList.remove(...m),w.classList.add(...g),[g,m]))})},setOrRemoveAttrs(e,t,i){let[s,n]=c.getSticky(e,"attrs",[[],[]]),r=t.map(([l,h])=>l).concat(i),o=s.filter(([l,h])=>!r.includes(l)).concat(t),a=n.filter(l=>!r.includes(l)).concat(i);c.putSticky(e,"attrs",l=>(a.forEach(h=>l.removeAttribute(h)),o.forEach(([h,d])=>l.setAttribute(h,d)),[o,a]))},hasAllClasses(e,t){return t.every(i=>e.classList.contains(i))},isToggledOut(e,t){return!this.isVisible(e)||this.hasAllClasses(e,t)},filterToEls(e,{to:t}){return t?c.all(document,t):[e]},defaultDisplay(e){return{tr:"table-row",td:"table-cell"}[e.tagName.toLowerCase()]||"block"}},x=qi,$={byId(e){return document.getElementById(e)||N(`no id found for ${e}`)},removeClass(e,t){e.classList.remove(t),e.classList.length===0&&e.removeAttribute("class")},all(e,t,i){if(!e)return[];let s=Array.from(e.querySelectorAll(t));return i?s.forEach(i):s},childNodeLength(e){let t=document.createElement("template");return t.innerHTML=e,t.content.childElementCount},isUploadInput(e){return e.type==="file"&&e.getAttribute(G)!==null},isAutoUpload(e){return e.hasAttribute("data-phx-auto-upload")},findUploadInputs(e){let t=e.id,i=this.all(document,`input[type="file"][${G}][form="${t}"]`);return this.all(e,`input[type="file"][${G}]`).concat(i)},findComponentNodeList(e,t){return this.filterWithinSameLiveView(this.all(e,`[${K}="${t}"]`),e)},isPhxDestroyed(e){return!!(e.id&&$.private(e,"destroyed"))},wantsNewTab(e){let t=e.ctrlKey||e.shiftKey||e.metaKey||e.button&&e.button===1,i=e.target instanceof HTMLAnchorElement&&e.target.hasAttribute("download"),s=e.target.hasAttribute("target")&&e.target.getAttribute("target").toLowerCase()==="_blank",n=e.target.hasAttribute("target")&&!e.target.getAttribute("target").startsWith("_");return t||s||i||n},isUnloadableFormSubmit(e){return e.target&&e.target.getAttribute("method")==="dialog"||e.submitter&&e.submitter.getAttribute("formmethod")==="dialog"?!1:!e.defaultPrevented&&!this.wantsNewTab(e)},isNewPageClick(e,t){let i=e.target instanceof HTMLAnchorElement?e.target.getAttribute("href"):null,s;if(e.defaultPrevented||i===null||this.wantsNewTab(e)||i.startsWith("mailto:")||i.startsWith("tel:")||e.target.isContentEditable)return!1;try{s=new URL(i)}catch(n){try{s=new URL(i,t)}catch(r){return!0}}return s.host===t.host&&s.protocol===t.protocol&&s.pathname===t.pathname&&s.search===t.search?s.hash===""&&!s.href.endsWith("#"):s.protocol.startsWith("http")},markPhxChildDestroyed(e){this.isPhxChild(e)&&e.setAttribute(Y,""),this.putPrivate(e,"destroyed",!0)},findPhxChildrenInFragment(e,t){let i=document.createElement("template");return i.innerHTML=e,this.findPhxChildren(i.content,t)},isIgnored(e,t){return(e.getAttribute(t)||e.getAttribute("data-phx-update"))==="ignore"},isPhxUpdate(e,t,i){return e.getAttribute&&i.indexOf(e.getAttribute(t))>=0},findPhxSticky(e){return this.all(e,`[${Lt}]`)},findPhxChildren(e,t){return this.all(e,`${ge}[${ae}="${t}"]`)},findExistingParentCIDs(e,t){let i=new Set,s=new Set;return t.forEach(n=>{this.filterWithinSameLiveView(this.all(e,`[${K}="${n}"]`),e).forEach(r=>{i.add(n),this.all(r,`[${K}]`).map(o=>parseInt(o.getAttribute(K))).forEach(o=>s.add(o))})}),s.forEach(n=>i.delete(n)),i},filterWithinSameLiveView(e,t){return t.querySelector(ge)?e.filter(i=>this.withinSameLiveView(i,t)):e},withinSameLiveView(e,t){for(;e=e.parentNode;){if(e.isSameNode(t))return!0;if(e.getAttribute(Y)!==null)return!1}},private(e,t){return e[B]&&e[B][t]},deletePrivate(e,t){e[B]&&delete e[B][t]},putPrivate(e,t,i){e[B]||(e[B]={}),e[B][t]=i},updatePrivate(e,t,i,s){let n=this.private(e,t);n===void 0?this.putPrivate(e,t,s(i)):this.putPrivate(e,t,s(n))},copyPrivates(e,t){t[B]&&(e[B]=t[B])},putTitle(e){let t=document.querySelector("title");if(t){let{prefix:i,suffix:s}=t.dataset;document.title=`${i||""}${e}${s||""}`}else document.title=e},debounce(e,t,i,s,n,r,o,a){let l=e.getAttribute(i),h=e.getAttribute(n);l===""&&(l=s),h===""&&(h=r);let d=l||h;switch(d){case null:return a();case"blur":this.once(e,"debounce-blur")&&e.addEventListener("blur",()=>{o()&&a()});return;default:let p=parseInt(d),f=()=>h?this.deletePrivate(e,Ae):a(),g=this.incCycle(e,Se,f);if(isNaN(p))return N(`invalid throttle/debounce value: ${d}`);if(h){let w=!1;if(t.type==="keydown"){let P=this.private(e,Ot);this.putPrivate(e,Ot,t.key),w=P!==t.key}if(!w&&this.private(e,Ae))return!1;{a();let P=setTimeout(()=>{o()&&this.triggerCycle(e,Se)},p);this.putPrivate(e,Ae,P)}}else setTimeout(()=>{o()&&this.triggerCycle(e,Se,g)},p);let m=e.form;m&&this.once(m,"bind-debounce")&&m.addEventListener("submit",()=>{Array.from(new FormData(m).entries(),([w])=>{let P=m.querySelector(`[name="${w}"]`);this.incCycle(P,Se),this.deletePrivate(P,Ae)})}),this.once(e,"bind-debounce")&&e.addEventListener("blur",()=>{clearTimeout(this.private(e,Ae)),this.triggerCycle(e,Se)})}},triggerCycle(e,t,i){let[s,n]=this.private(e,t);i||(i=s),i===s&&(this.incCycle(e,t),n())},once(e,t){return this.private(e,t)===!0?!1:(this.putPrivate(e,t,!0),!0)},incCycle(e,t,i=function(){}){let[s]=this.private(e,t)||[0,i];return s++,this.putPrivate(e,t,[s,i]),s},maybeAddPrivateHooks(e,t,i){e.hasAttribute&&(e.hasAttribute(t)||e.hasAttribute(i))&&e.setAttribute("data-phx-hook","Phoenix.InfiniteScroll")},isFeedbackContainer(e,t){return e.hasAttribute&&e.hasAttribute(t)},maybeHideFeedback(e,t,i,s){let n={};t.forEach(r=>{if(!e.contains(r))return;let o=r.getAttribute(i);if(!o){x.addOrRemoveClasses(r,[],[Oe]);return}if(n[o]===!0){this.hideFeedback(r);return}n[o]=this.shouldHideFeedback(e,o,s),n[o]===!0&&this.hideFeedback(r)})},hideFeedback(e){x.addOrRemoveClasses(e,[Oe],[])},shouldHideFeedback(e,t,i){let s=`[name="${t}"],
                   [name="${t}[]"],
                   [${i}="${t}"]`,n=!1;return $.all(e,s,r=>{(this.private(r,ut)||this.private(r,qe))&&(n=!0)}),!n},feedbackSelector(e,t,i){let s=`[${t}="${e.name}"],
                 [${t}="${e.name.replace(/\[\]$/,"")}"]`;return e.getAttribute(i)&&(s+=`,[${t}="${e.getAttribute(i)}"]`),s},resetForm(e,t,i){Array.from(e.elements).forEach(s=>{let n=this.feedbackSelector(s,t,i);this.deletePrivate(s,ut),this.deletePrivate(s,qe),this.all(document,n,r=>{x.addOrRemoveClasses(r,[Oe],[])})})},showError(e,t,i){if(e.name){let s=this.feedbackSelector(e,t,i);this.all(document,s,n=>{x.addOrRemoveClasses(n,[],[Oe])})}},isPhxChild(e){return e.getAttribute&&e.getAttribute(ae)},isPhxSticky(e){return e.getAttribute&&e.getAttribute(Lt)!==null},isChildOfAny(e,t){return!!t.find(i=>i.contains(e))},firstPhxChild(e){return this.isPhxChild(e)?e:this.all(e,`[${ae}]`)[0]},dispatchEvent(e,t,i={}){let s=!0;e.nodeName==="INPUT"&&e.type==="file"&&t==="click"&&(s=!1);let o={bubbles:i.bubbles===void 0?s:!!i.bubbles,cancelable:!0,detail:i.detail||{}},a=t==="click"?new MouseEvent("click",o):new CustomEvent(t,o);e.dispatchEvent(a)},cloneNode(e,t){if(typeof t=="undefined")return e.cloneNode(!0);{let i=e.cloneNode(!1);return i.innerHTML=t,i}},mergeAttrs(e,t,i={}){let s=new Set(i.exclude||[]),n=i.isIgnored,r=t.attributes;for(let a=r.length-1;a>=0;a--){let l=r[a].name;if(s.has(l))l==="value"&&e.value===t.value&&e.setAttribute("value",t.getAttribute(l));else{let h=t.getAttribute(l);e.getAttribute(l)!==h&&(!n||n&&l.startsWith("data-"))&&e.setAttribute(l,h)}}let o=e.attributes;for(let a=o.length-1;a>=0;a--){let l=o[a].name;n?l.startsWith("data-")&&!t.hasAttribute(l)&&![M,te].includes(l)&&e.removeAttribute(l):t.hasAttribute(l)||e.removeAttribute(l)}},mergeFocusedInput(e,t){e instanceof HTMLSelectElement||$.mergeAttrs(e,t,{exclude:["value"]}),t.readOnly?e.setAttribute("readonly",!0):e.removeAttribute("readonly")},hasSelectionRange(e){return e.setSelectionRange&&(e.type==="text"||e.type==="textarea")},restoreFocus(e,t,i){if(e instanceof HTMLSelectElement&&e.focus(),!$.isTextualInput(e))return;e.matches(":focus")||e.focus(),this.hasSelectionRange(e)&&e.setSelectionRange(t,i)},isFormInput(e){return/^(?:input|select|textarea)$/i.test(e.tagName)&&e.type!=="button"},syncAttrsToProps(e){e instanceof HTMLInputElement&&si.indexOf(e.type.toLocaleLowerCase())>=0&&(e.checked=e.getAttribute("checked")!==null)},isTextualInput(e){return xi.indexOf(e.type)>=0},isNowTriggerFormExternal(e,t){return e.getAttribute&&e.getAttribute(t)!==null},syncPendingRef(e,t,i){let s=e.getAttribute(M);if(s===null)return!0;let n=e.getAttribute(te);return $.isFormInput(e)||e.getAttribute(i)!==null?($.isUploadInput(e)&&$.mergeAttrs(e,t,{isIgnored:!0}),$.putPrivate(e,M,t),!1):(Zt.forEach(r=>{e.classList.contains(r)&&t.classList.add(r)}),t.setAttribute(M,s),t.setAttribute(te,n),!0)},cleanChildNodes(e,t){if($.isPhxUpdate(e,t,["append","prepend"])){let i=[];e.childNodes.forEach(s=>{s.id||(!(s.nodeType===Node.TEXT_NODE&&s.nodeValue.trim()==="")&&s.nodeType!==Node.COMMENT_NODE&&N(`only HTML element tags with an id are allowed inside containers with phx-update.

removing illegal node: "${(s.outerHTML||s.nodeValue).trim()}"

`),i.push(s))}),i.forEach(s=>s.remove())}},replaceRootContainer(e,t,i){let s=new Set(["id",Y,fe,gt,le]);if(e.tagName.toLowerCase()===t.toLowerCase())return Array.from(e.attributes).filter(n=>!s.has(n.name.toLowerCase())).forEach(n=>e.removeAttribute(n.name)),Object.keys(i).filter(n=>!s.has(n.toLowerCase())).forEach(n=>e.setAttribute(n,i[n])),e;{let n=document.createElement(t);return Object.keys(i).forEach(r=>n.setAttribute(r,i[r])),s.forEach(r=>n.setAttribute(r,e.getAttribute(r))),n.innerHTML=e.innerHTML,e.replaceWith(n),n}},getSticky(e,t,i){let s=($.private(e,"sticky")||[]).find(([n])=>t===n);if(s){let[n,r,o]=s;return o}else return typeof i=="function"?i():i},deleteSticky(e,t){this.updatePrivate(e,"sticky",[],i=>i.filter(([s,n])=>s!==t))},putSticky(e,t,i){let s=i(e);this.updatePrivate(e,"sticky",[],n=>{let r=n.findIndex(([o])=>t===o);return r>=0?n[r]=[t,i,s]:n.push([t,i,s]),n})},applyStickyOperations(e){let t=$.private(e,"sticky");t&&t.forEach(([i,s,n])=>this.putSticky(e,i,s))}},c=$,Ce=class{static isActive(e,t){let i=t._phxRef===void 0,n=e.getAttribute(ht).split(",").indexOf(T.genFileRef(t))>=0;return t.size>0&&(i||n)}static isPreflighted(e,t){return e.getAttribute(pt).split(",").indexOf(T.genFileRef(t))>=0&&this.isActive(e,t)}static isPreflightInProgress(e){return e._preflightInProgress===!0}static markPreflightInProgress(e){e._preflightInProgress=!0}constructor(e,t,i,s){this.ref=T.genFileRef(t),this.fileEl=e,this.file=t,this.view=i,this.meta=null,this._isCancelled=!1,this._isDone=!1,this._progress=0,this._lastProgressSent=-1,this._onDone=function(){},this._onElUpdated=this.onElUpdated.bind(this),this.fileEl.addEventListener(Ve,this._onElUpdated),this.autoUpload=s}metadata(){return this.meta}progress(e){this._progress=Math.floor(e),this._progress>this._lastProgressSent&&(this._progress>=100?(this._progress=100,this._lastProgressSent=100,this._isDone=!0,this.view.pushFileProgress(this.fileEl,this.ref,100,()=>{T.untrackFile(this.fileEl,this.file),this._onDone()})):(this._lastProgressSent=this._progress,this.view.pushFileProgress(this.fileEl,this.ref,this._progress)))}isCancelled(){return this._isCancelled}cancel(){this.file._preflightInProgress=!1,this._isCancelled=!0,this._isDone=!0,this._onDone()}isDone(){return this._isDone}error(e="failed"){this.fileEl.removeEventListener(Ve,this._onElUpdated),this.view.pushFileProgress(this.fileEl,this.ref,{error:e}),this.isAutoUpload()||T.clearFiles(this.fileEl)}isAutoUpload(){return this.autoUpload}onDone(e){this._onDone=()=>{this.fileEl.removeEventListener(Ve,this._onElUpdated),e()}}onElUpdated(){this.fileEl.getAttribute(ht).split(",").indexOf(this.ref)===-1&&(T.untrackFile(this.fileEl,this.file),this.cancel())}toPreflightPayload(){return{last_modified:this.file.lastModified,name:this.file.name,relative_path:this.file.webkitRelativePath,size:this.file.size,type:this.file.type,ref:this.ref,meta:typeof this.file.meta=="function"?this.file.meta():void 0}}uploader(e){if(this.meta.uploader){let t=e[this.meta.uploader]||N(`no uploader configured for ${this.meta.uploader}`);return{name:this.meta.uploader,callback:t}}else return{name:"channel",callback:Xi}}zipPostFlight(e){this.meta=e.entries[this.ref],this.meta||N(`no preflight upload response returned with ref ${this.ref}`,{input:this.fileEl,response:e})}},zi=0,T=class{static genFileRef(e){let t=e._phxRef;return t!==void 0?t:(e._phxRef=(zi++).toString(),e._phxRef)}static getEntryDataURL(e,t,i){let s=this.activeFiles(e).find(n=>this.genFileRef(n)===t);i(URL.createObjectURL(s))}static hasUploadsInProgress(e){let t=0;return c.findUploadInputs(e).forEach(i=>{i.getAttribute(pt)!==i.getAttribute(_i)&&t++}),t>0}static serializeUploads(e){let t=this.activeFiles(e),i={};return t.forEach(s=>{let n={path:e.name},r=e.getAttribute(G);i[r]=i[r]||[],n.ref=this.genFileRef(s),n.last_modified=s.lastModified,n.name=s.name||n.ref,n.relative_path=s.webkitRelativePath,n.type=s.type,n.size=s.size,typeof s.meta=="function"&&(n.meta=s.meta()),i[r].push(n)}),i}static clearFiles(e){e.value=null,e.removeAttribute(G),c.putPrivate(e,"files",[])}static untrackFile(e,t){c.putPrivate(e,"files",c.private(e,"files").filter(i=>!Object.is(i,t)))}static trackFiles(e,t,i){if(e.getAttribute("multiple")!==null){let s=t.filter(n=>!this.activeFiles(e).find(r=>Object.is(r,n)));c.updatePrivate(e,"files",[],n=>n.concat(s)),e.value=null}else i&&i.files.length>0&&(e.files=i.files),c.putPrivate(e,"files",t)}static activeFileInputs(e){let t=c.findUploadInputs(e);return Array.from(t).filter(i=>i.files&&this.activeFiles(i).length>0)}static activeFiles(e){return(c.private(e,"files")||[]).filter(t=>Ce.isActive(e,t))}static inputsAwaitingPreflight(e){let t=c.findUploadInputs(e);return Array.from(t).filter(i=>this.filesAwaitingPreflight(i).length>0)}static filesAwaitingPreflight(e){return this.activeFiles(e).filter(t=>!Ce.isPreflighted(e,t)&&!Ce.isPreflightInProgress(t))}static markPreflightInProgress(e){e.forEach(t=>Ce.markPreflightInProgress(t.file))}constructor(e,t,i){this.autoUpload=c.isAutoUpload(e),this.view=t,this.onComplete=i,this._entries=Array.from(T.filesAwaitingPreflight(e)||[]).map(s=>new Ce(e,s,t,this.autoUpload)),T.markPreflightInProgress(this._entries),this.numEntriesInProgress=this._entries.length}isAutoUpload(){return this.autoUpload}entries(){return this._entries}initAdapterUpload(e,t,i){this._entries=this._entries.map(n=>(n.isCancelled()?(this.numEntriesInProgress--,this.numEntriesInProgress===0&&this.onComplete()):(n.zipPostFlight(e),n.onDone(()=>{this.numEntriesInProgress--,this.numEntriesInProgress===0&&this.onComplete()})),n));let s=this._entries.reduce((n,r)=>{if(!r.meta)return n;let{name:o,callback:a}=r.uploader(i.uploaders);return n[o]=n[o]||{callback:a,entries:[]},n[o].entries.push(r),n},{});for(let n in s){let{callback:r,entries:o}=s[n];r(o,t,e,i)}}},ri={LiveFileUpload:{activeRefs(){return this.el.getAttribute(ht)},preflightedRefs(){return this.el.getAttribute(pt)},mounted(){this.preflightedWas=this.preflightedRefs()},updated(){let e=this.preflightedRefs();this.preflightedWas!==e&&(this.preflightedWas=e,e===""&&this.__view.cancelSubmit(this.el.form)),this.activeRefs()===""&&(this.el.value=null),this.el.dispatchEvent(new CustomEvent(Ve))}},LiveImgPreview:{mounted(){this.ref=this.el.getAttribute("data-phx-entry-ref"),this.inputEl=document.getElementById(this.el.getAttribute(G)),T.getEntryDataURL(this.inputEl,this.ref,e=>{this.url=e,this.el.src=e})},destroyed(){URL.revokeObjectURL(this.url)}},FocusWrap:{mounted(){this.focusStart=this.el.firstElementChild,this.focusEnd=this.el.lastElementChild,this.focusStart.addEventListener("focus",()=>pe.focusLast(this.el)),this.focusEnd.addEventListener("focus",()=>pe.focusFirst(this.el)),this.el.addEventListener("phx:show-end",()=>this.el.focus()),window.getComputedStyle(this.el).display!=="none"&&pe.focusFirst(this.el)}}},oi=e=>["HTML","BODY"].indexOf(e.nodeName.toUpperCase())>=0?null:["scroll","auto"].indexOf(getComputedStyle(e).overflowY)>=0?e:oi(e.parentElement),Bt=e=>e?e.scrollTop:document.documentElement.scrollTop||document.body.scrollTop,mt=e=>e?e.getBoundingClientRect().bottom:window.innerHeight||document.documentElement.clientHeight,vt=e=>e?e.getBoundingClientRect().top:0,Ki=(e,t)=>{let i=e.getBoundingClientRect();return i.top>=vt(t)&&i.left>=0&&i.top<=mt(t)},Gi=(e,t)=>{let i=e.getBoundingClientRect();return i.right>=vt(t)&&i.left>=0&&i.bottom<=mt(t)},Vt=(e,t)=>{let i=e.getBoundingClientRect();return i.top>=vt(t)&&i.left>=0&&i.top<=mt(t)};ri.InfiniteScroll={mounted(){this.scrollContainer=oi(this.el);let e=Bt(this.scrollContainer),t=!1,i=500,s=null,n=this.throttle(i,(a,l)=>{s=()=>!0,this.liveSocket.execJSHookPush(this.el,a,{id:l.id,_overran:!0},()=>{s=null})}),r=this.throttle(i,(a,l)=>{s=()=>l.scrollIntoView({block:"start"}),this.liveSocket.execJSHookPush(this.el,a,{id:l.id},()=>{s=null,window.requestAnimationFrame(()=>{Vt(l,this.scrollContainer)||l.scrollIntoView({block:"start"})})})}),o=this.throttle(i,(a,l)=>{s=()=>l.scrollIntoView({block:"end"}),this.liveSocket.execJSHookPush(this.el,a,{id:l.id},()=>{s=null,window.requestAnimationFrame(()=>{Vt(l,this.scrollContainer)||l.scrollIntoView({block:"end"})})})});this.onScroll=a=>{let l=Bt(this.scrollContainer);if(s)return e=l,s();let h=this.el.getBoundingClientRect(),d=this.el.getAttribute(this.liveSocket.binding("viewport-top")),p=this.el.getAttribute(this.liveSocket.binding("viewport-bottom")),f=this.el.lastElementChild,g=this.el.firstElementChild,m=l<e,w=l>e;m&&d&&!t&&h.top>=0?(t=!0,n(d,g)):w&&t&&h.top<=0&&(t=!1),d&&m&&Ki(g,this.scrollContainer)?r(d,g):p&&w&&Gi(f,this.scrollContainer)&&o(p,f),e=l},this.scrollContainer?this.scrollContainer.addEventListener("scroll",this.onScroll):window.addEventListener("scroll",this.onScroll)},destroyed(){this.scrollContainer?this.scrollContainer.removeEventListener("scroll",this.onScroll):window.removeEventListener("scroll",this.onScroll)},throttle(e,t){let i=0,s;return(...n)=>{let r=Date.now(),o=e-(r-i);o<=0||o>e?(s&&(clearTimeout(s),s=null),i=r,t(...n)):s||(s=setTimeout(()=>{i=Date.now(),s=null,t(...n)},o))}}};var Yi=ri,Qi=class{constructor(e,t,i){let s=new Set,n=new Set([...t.children].map(o=>o.id)),r=[];Array.from(e.children).forEach(o=>{if(o.id&&(s.add(o.id),n.has(o.id))){let a=o.previousElementSibling&&o.previousElementSibling.id;r.push({elementId:o.id,previousElementId:a})}}),this.containerId=t.id,this.updateType=i,this.elementsToModify=r,this.elementIdsToAdd=[...n].filter(o=>!s.has(o))}perform(){let e=c.byId(this.containerId);this.elementsToModify.forEach(t=>{t.previousElementId?ie(document.getElementById(t.previousElementId),i=>{ie(document.getElementById(t.elementId),s=>{s.previousElementSibling&&s.previousElementSibling.id==i.id||i.insertAdjacentElement("afterend",s)})}):ie(document.getElementById(t.elementId),i=>{i.previousElementSibling==null||e.insertAdjacentElement("afterbegin",i)})}),this.updateType=="prepend"&&this.elementIdsToAdd.reverse().forEach(t=>{ie(document.getElementById(t),i=>e.insertAdjacentElement("afterbegin",i))})}},Jt=11;function Zi(e,t){var i=t.attributes,s,n,r,o,a;if(!(t.nodeType===Jt||e.nodeType===Jt)){for(var l=i.length-1;l>=0;l--)s=i[l],n=s.name,r=s.namespaceURI,o=s.value,r?(n=s.localName||n,a=e.getAttributeNS(r,n),a!==o&&(s.prefix==="xmlns"&&(n=s.name),e.setAttributeNS(r,n,o))):(a=e.getAttribute(n),a!==o&&e.setAttribute(n,o));for(var h=e.attributes,d=h.length-1;d>=0;d--)s=h[d],n=s.name,r=s.namespaceURI,r?(n=s.localName||n,t.hasAttributeNS(r,n)||e.removeAttributeNS(r,n)):t.hasAttribute(n)||e.removeAttribute(n)}}var $e,es="http://www.w3.org/1999/xhtml",O=typeof document=="undefined"?void 0:document,ts=!!O&&"content"in O.createElement("template"),is=!!O&&O.createRange&&"createContextualFragment"in O.createRange();function ss(e){var t=O.createElement("template");return t.innerHTML=e,t.content.childNodes[0]}function ns(e){$e||($e=O.createRange(),$e.selectNode(O.body));var t=$e.createContextualFragment(e);return t.childNodes[0]}function rs(e){var t=O.createElement("body");return t.innerHTML=e,t.childNodes[0]}function os(e){return e=e.trim(),ts?ss(e):is?ns(e):rs(e)}function Ue(e,t){var i=e.nodeName,s=t.nodeName,n,r;return i===s?!0:(n=i.charCodeAt(0),r=s.charCodeAt(0),n<=90&&r>=97?i===s.toUpperCase():r<=90&&n>=97?s===i.toUpperCase():!1)}function as(e,t){return!t||t===es?O.createElement(e):O.createElementNS(t,e)}function ls(e,t){for(var i=e.firstChild;i;){var s=i.nextSibling;t.appendChild(i),i=s}return t}function lt(e,t,i){e[i]!==t[i]&&(e[i]=t[i],e[i]?e.setAttribute(i,""):e.removeAttribute(i))}var Xt={OPTION:function(e,t){var i=e.parentNode;if(i){var s=i.nodeName.toUpperCase();s==="OPTGROUP"&&(i=i.parentNode,s=i&&i.nodeName.toUpperCase()),s==="SELECT"&&!i.hasAttribute("multiple")&&(e.hasAttribute("selected")&&!t.selected&&(e.setAttribute("selected","selected"),e.removeAttribute("selected")),i.selectedIndex=-1)}lt(e,t,"selected")},INPUT:function(e,t){lt(e,t,"checked"),lt(e,t,"disabled"),e.value!==t.value&&(e.value=t.value),t.hasAttribute("value")||e.removeAttribute("value")},TEXTAREA:function(e,t){var i=t.value;e.value!==i&&(e.value=i);var s=e.firstChild;if(s){var n=s.nodeValue;if(n==i||!i&&n==e.placeholder)return;s.nodeValue=i}},SELECT:function(e,t){if(!t.hasAttribute("multiple")){for(var i=-1,s=0,n=e.firstChild,r,o;n;)if(o=n.nodeName&&n.nodeName.toUpperCase(),o==="OPTGROUP")r=n,n=r.firstChild;else{if(o==="OPTION"){if(n.hasAttribute("selected")){i=s;break}s++}n=n.nextSibling,!n&&r&&(n=r.nextSibling,r=null)}e.selectedIndex=i}}},Ee=1,Wt=11,qt=3,zt=8;function ee(){}function hs(e){if(e)return e.getAttribute&&e.getAttribute("id")||e.id}function ds(e){return function(i,s,n){if(n||(n={}),typeof s=="string")if(i.nodeName==="#document"||i.nodeName==="HTML"||i.nodeName==="BODY"){var r=s;s=O.createElement("html"),s.innerHTML=r}else s=os(s);else s.nodeType===Wt&&(s=s.firstElementChild);var o=n.getNodeKey||hs,a=n.onBeforeNodeAdded||ee,l=n.onNodeAdded||ee,h=n.onBeforeElUpdated||ee,d=n.onElUpdated||ee,p=n.onBeforeNodeDiscarded||ee,f=n.onNodeDiscarded||ee,g=n.onBeforeElChildrenUpdated||ee,m=n.skipFromChildren||ee,w=n.addChild||function(y,v){return y.appendChild(v)},P=n.childrenOnly===!0,_=Object.create(null),L=[];function F(y){L.push(y)}function I(y,v){if(y.nodeType===Ee)for(var C=y.firstChild;C;){var S=void 0;v&&(S=o(C))?F(S):(f(C),C.firstChild&&I(C,v)),C=C.nextSibling}}function se(y,v,C){p(y)!==!1&&(v&&v.removeChild(y),f(y),I(y,C))}function E(y){if(y.nodeType===Ee||y.nodeType===Wt)for(var v=y.firstChild;v;){var C=o(v);C&&(_[C]=v),E(v),v=v.nextSibling}}E(i);function X(y){l(y);for(var v=y.firstChild;v;){var C=v.nextSibling,S=o(v);if(S){var k=_[S];k&&Ue(v,k)?(v.parentNode.replaceChild(k,v),u(k,v)):X(v)}else X(v);v=C}}function ne(y,v,C){for(;v;){var S=v.nextSibling;(C=o(v))?F(C):se(v,y,!0),v=S}}function u(y,v,C){var S=o(v);S&&delete _[S],!(!C&&(h(y,v)===!1||(e(y,v),d(y),g(y,v)===!1)))&&(y.nodeName!=="TEXTAREA"?b(y,v):Xt.TEXTAREA(y,v))}function b(y,v){var C=m(y,v),S=v.firstChild,k=y.firstChild,he,W,de,_e,Q;e:for(;S;){for(_e=S.nextSibling,he=o(S);!C&&k;){if(de=k.nextSibling,S.isSameNode&&S.isSameNode(k)){S=_e,k=de;continue e}W=o(k);var Re=k.nodeType,Z=void 0;if(Re===S.nodeType&&(Re===Ee?(he?he!==W&&((Q=_[he])?de===Q?Z=!1:(y.insertBefore(Q,k),W?F(W):se(k,y,!0),k=Q,W=o(k)):Z=!1):W&&(Z=!1),Z=Z!==!1&&Ue(k,S),Z&&u(k,S)):(Re===qt||Re==zt)&&(Z=!0,k.nodeValue!==S.nodeValue&&(k.nodeValue=S.nodeValue))),Z){S=_e,k=de;continue e}W?F(W):se(k,y,!0),k=de}if(he&&(Q=_[he])&&Ue(Q,S))C||w(y,Q),u(Q,S);else{var Qe=a(S);Qe!==!1&&(Qe&&(S=Qe),S.actualize&&(S=S.actualize(y.ownerDocument||O)),w(y,S),X(S))}S=_e,k=de}ne(y,k,W);var bt=Xt[y.nodeName];bt&&bt(y,v)}var A=i,D=A.nodeType,re=s.nodeType;if(!P){if(D===Ee)re===Ee?Ue(i,s)||(f(i),A=ls(i,as(s.nodeName,s.namespaceURI))):A=s;else if(D===qt||D===zt){if(re===D)return A.nodeValue!==s.nodeValue&&(A.nodeValue=s.nodeValue),A;A=s}}if(A===s)f(i);else{if(s.isSameNode&&s.isSameNode(A))return;if(u(A,s,P),L)for(var Ge=0,ci=L.length;Ge<ci;Ge++){var Ye=_[L[Ge]];Ye&&se(Ye,Ye.parentNode,!1)}}return!P&&A!==i&&i.parentNode&&(A.actualize&&(A=A.actualize(i.ownerDocument||O)),i.parentNode.replaceChild(A,i)),A}}var cs=ds(Zi),Kt=cs,je=class{static patchEl(e,t,i){Kt(e,t,{childrenOnly:!1,onBeforeElUpdated:(s,n)=>{if(i&&i.isSameNode(s)&&c.isFormInput(s))return c.mergeFocusedInput(s,n),!1}})}constructor(e,t,i,s,n,r){this.view=e,this.liveSocket=e.liveSocket,this.container=t,this.id=i,this.rootID=e.root.id,this.html=s,this.streams=n,this.streamInserts={},this.streamComponentRestore={},this.targetCID=r,this.cidPatch=z(this.targetCID),this.pendingRemoves=[],this.phxRemove=this.liveSocket.binding("remove"),this.callbacks={beforeadded:[],beforeupdated:[],beforephxChildAdded:[],afteradded:[],afterupdated:[],afterdiscarded:[],afterphxChildAdded:[],aftertransitionsDiscarded:[]}}before(e,t){this.callbacks[`before${e}`].push(t)}after(e,t){this.callbacks[`after${e}`].push(t)}trackBefore(e,...t){this.callbacks[`before${e}`].forEach(i=>i(...t))}trackAfter(e,...t){this.callbacks[`after${e}`].forEach(i=>i(...t))}markPrunableContentForRemoval(){let e=this.liveSocket.binding(ze);c.all(this.container,`[${e}=append] > *, [${e}=prepend] > *`,t=>{t.setAttribute(Pt,"")})}perform(e){let{view:t,liveSocket:i,container:s,html:n}=this,r=this.isCIDPatch()?this.targetCIDContainer(n):s;if(this.isCIDPatch()&&!r)return;let o=i.getActiveElement(),{selectionStart:a,selectionEnd:l}=o&&c.hasSelectionRange(o)?o:{},h=i.binding(ze),d=i.binding(Xe),p=i.binding(We),f=i.binding(ft),g=i.binding(dt),m=i.binding(ct),w=i.binding(Ri),P=[],_=[],L=[],F=[],I=null;function se(E,X,ne=!1){Kt(E,X,{childrenOnly:E.getAttribute(K)===null&&!ne,getNodeKey:u=>c.isPhxDestroyed(u)?null:e?u.id:u.id||u.getAttribute&&u.getAttribute(ii),skipFromChildren:u=>u.getAttribute(h)===st,addChild:(u,b)=>{let{ref:A,streamAt:D}=this.getStreamInsert(b);if(A===void 0)return u.appendChild(b);if(this.setStreamRef(b,A),D===0)u.insertAdjacentElement("afterbegin",b);else if(D===-1)u.appendChild(b);else if(D>0){let re=Array.from(u.children)[D];u.insertBefore(b,re)}},onBeforeNodeAdded:u=>{c.maybeAddPrivateHooks(u,g,m),this.trackBefore("added",u);let b=u;return!e&&this.streamComponentRestore[u.id]&&(b=this.streamComponentRestore[u.id],delete this.streamComponentRestore[u.id],se.call(this,b,u,!0)),b},onNodeAdded:u=>{u.getAttribute&&this.maybeReOrderStream(u,!0),c.isFeedbackContainer(u,d)&&_.push(u),u instanceof HTMLImageElement&&u.srcset?u.srcset=u.srcset:u instanceof HTMLVideoElement&&u.autoplay&&u.play(),c.isNowTriggerFormExternal(u,w)&&(I=u),(c.isPhxChild(u)&&t.ownsElement(u)||c.isPhxSticky(u)&&t.ownsElement(u.parentNode))&&this.trackAfter("phxChildAdded",u),P.push(u)},onNodeDiscarded:u=>this.onNodeDiscarded(u),onBeforeNodeDiscarded:u=>u.getAttribute&&u.getAttribute(Pt)!==null?!0:!(u.parentElement!==null&&u.id&&c.isPhxUpdate(u.parentElement,h,[st,"append","prepend"])||this.maybePendingRemove(u)||this.skipCIDSibling(u)),onElUpdated:u=>{c.isNowTriggerFormExternal(u,w)&&(I=u),L.push(u),this.maybeReOrderStream(u,!1)},onBeforeElUpdated:(u,b)=>{if(c.maybeAddPrivateHooks(b,g,m),(c.isFeedbackContainer(u,d)||c.isFeedbackContainer(b,d))&&(_.push(u),_.push(b)),c.cleanChildNodes(b,h),this.skipCIDSibling(b))return this.maybeReOrderStream(u),!1;if(c.isPhxSticky(u))return!1;if(c.isIgnored(u,h)||u.form&&u.form.isSameNode(I))return this.trackBefore("updated",u,b),c.mergeAttrs(u,b,{isIgnored:c.isIgnored(u,h)}),L.push(u),c.applyStickyOperations(u),!1;if(u.type==="number"&&u.validity&&u.validity.badInput)return!1;if(!c.syncPendingRef(u,b,f))return c.isUploadInput(u)&&(this.trackBefore("updated",u,b),L.push(u)),c.applyStickyOperations(u),!1;if(c.isPhxChild(b)){let re=u.getAttribute(Y);return c.mergeAttrs(u,b,{exclude:[fe]}),re!==""&&u.setAttribute(Y,re),u.setAttribute(le,this.rootID),c.applyStickyOperations(u),!1}c.copyPrivates(b,u);let A=o&&u.isSameNode(o)&&c.isFormInput(u),D=A&&this.isChangedSelect(u,b);return A&&u.type!=="hidden"&&!D?(this.trackBefore("updated",u,b),c.mergeFocusedInput(u,b),c.syncAttrsToProps(u),L.push(u),c.applyStickyOperations(u),!1):(D&&u.blur(),c.isPhxUpdate(b,h,["append","prepend"])&&F.push(new Qi(u,b,b.getAttribute(h))),c.syncAttrsToProps(b),c.applyStickyOperations(b),this.trackBefore("updated",u,b),!0)}})}return this.trackBefore("added",s),this.trackBefore("updated",s,s),i.time("morphdom",()=>{this.streams.forEach(([E,X,ne,u])=>{X.forEach(([b,A,D])=>{this.streamInserts[b]={ref:E,streamAt:A,limit:D,reset:u}}),u!==void 0&&c.all(s,`[${nt}="${E}"]`,b=>{this.removeStreamChildElement(b)}),ne.forEach(b=>{let A=s.querySelector(`[id="${b}"]`);A&&this.removeStreamChildElement(A)})}),e&&c.all(this.container,`[${h}=${st}]`,E=>{this.liveSocket.owner(E,X=>{X===this.view&&Array.from(E.children).forEach(ne=>{this.removeStreamChildElement(ne)})})}),se.call(this,r,n)}),i.isDebugEnabled()&&(Bi(),Array.from(document.querySelectorAll("input[name=id]")).forEach(E=>{E.form&&console.error(`Detected an input with name="id" inside a form! This will cause problems when patching the DOM.
`,E)})),F.length>0&&i.time("post-morph append/prepend restoration",()=>{F.forEach(E=>E.perform())}),c.maybeHideFeedback(r,_,d,p),i.silenceEvents(()=>c.restoreFocus(o,a,l)),c.dispatchEvent(document,"phx:update"),P.forEach(E=>this.trackAfter("added",E)),L.forEach(E=>this.trackAfter("updated",E)),this.transitionPendingRemoves(),I&&(i.unload(),Object.getPrototypeOf(I).submit.call(I)),!0}onNodeDiscarded(e){(c.isPhxChild(e)||c.isPhxSticky(e))&&this.liveSocket.destroyViewByEl(e),this.trackAfter("discarded",e)}maybePendingRemove(e){return e.getAttribute&&e.getAttribute(this.phxRemove)!==null?(this.pendingRemoves.push(e),!0):!1}removeStreamChildElement(e){this.streamInserts[e.id]?(this.streamComponentRestore[e.id]=e,e.remove()):this.maybePendingRemove(e)||(e.remove(),this.onNodeDiscarded(e))}getStreamInsert(e){return(e.id?this.streamInserts[e.id]:{})||{}}setStreamRef(e,t){c.putSticky(e,nt,i=>i.setAttribute(nt,t))}maybeReOrderStream(e,t){let{ref:i,streamAt:s,reset:n}=this.getStreamInsert(e);if(s!==void 0&&(this.setStreamRef(e,i),!(!n&&!t)&&e.parentElement)){if(s===0)e.parentElement.insertBefore(e,e.parentElement.firstElementChild);else if(s>0){let r=Array.from(e.parentElement.children),o=r.indexOf(e);if(s>=r.length-1)e.parentElement.appendChild(e);else{let a=r[s];o>s?e.parentElement.insertBefore(e,a):e.parentElement.insertBefore(e,a.nextElementSibling)}}this.maybeLimitStream(e)}}maybeLimitStream(e){let{limit:t}=this.getStreamInsert(e),i=t!==null&&Array.from(e.parentElement.children);t&&t<0&&i.length>t*-1?i.slice(0,i.length+t).forEach(s=>this.removeStreamChildElement(s)):t&&t>=0&&i.length>t&&i.slice(t).forEach(s=>this.removeStreamChildElement(s))}transitionPendingRemoves(){let{pendingRemoves:e,liveSocket:t}=this;e.length>0&&(t.transitionRemoves(e),t.requestDOMUpdate(()=>{e.forEach(i=>{let s=c.firstPhxChild(i);s&&t.destroyViewByEl(s),i.remove()}),this.trackAfter("transitionsDiscarded",e)}))}isChangedSelect(e,t){if(!(e instanceof HTMLSelectElement)||e.multiple)return!1;if(e.options.length!==t.options.length)return!0;let i=e.selectedOptions[0],s=t.selectedOptions[0];return i&&i.hasAttribute("selected")&&s.setAttribute("selected",i.getAttribute("selected")),!e.isEqualNode(t)}isCIDPatch(){return this.cidPatch}skipCIDSibling(e){return e.nodeType===Node.ELEMENT_NODE&&e.hasAttribute(ti)}targetCIDContainer(e){if(!this.isCIDPatch())return;let[t,...i]=c.findComponentNodeList(this.container,this.targetCID);return i.length===0&&c.childNodeLength(e)===1?t:t&&t.parentNode}indexOf(e,t){return Array.from(e.children).indexOf(t)}},us=new Set(["area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),fs=new Set(["'",'"']),Gt=(e,t,i)=>{let s=0,n=!1,r,o,a,l,h,d,p=e.match(/^(\s*(?:<!--.*?-->\s*)*)<([^\s\/>]+)/);if(p===null)throw new Error(`malformed html ${e}`);for(s=p[0].length,r=p[1],a=p[2],l=s,s;s<e.length&&e.charAt(s)!==">";s++)if(e.charAt(s)==="="){let m=e.slice(s-3,s)===" id";s++;let w=e.charAt(s);if(fs.has(w)){let P=s;for(s++,s;s<e.length&&e.charAt(s)!==w;s++);if(m){h=e.slice(P+1,s);break}}}let f=e.length-1;for(n=!1;f>=r.length+a.length;){let m=e.charAt(f);if(n)m==="-"&&e.slice(f-3,f)==="<!-"?(n=!1,f-=4):f-=1;else if(m===">"&&e.slice(f-2,f)==="--")n=!0,f-=3;else{if(m===">")break;f-=1}}o=e.slice(f+1,e.length);let g=Object.keys(t).map(m=>t[m]===!0?m:`${m}="${t[m]}"`).join(" ");if(i){let m=h?` id="${h}"`:"";us.has(a)?d=`<${a}${m}${g===""?"":" "}${g}/>`:d=`<${a}${m}${g===""?"":" "}${g}></${a}>`}else{let m=e.slice(l,f+1);d=`<${a}${g===""?"":" "}${g}${m}`}return[d,r,o]},Yt=class{static extract(e){let{[Mt]:t,[Ht]:i,[Nt]:s}=e;return delete e[Mt],delete e[Ht],delete e[Nt],{diff:e,title:s,reply:t||null,events:i||[]}}constructor(e,t){this.viewId=e,this.rendered={},this.magicId=0,this.mergeDiff(t)}parentViewId(){return this.viewId}toString(e){let[t,i]=this.recursiveToString(this.rendered,this.rendered[R],e,!0,{});return[t,i]}recursiveToString(e,t=e[R],i,s,n){i=i?new Set(i):null;let r={buffer:"",components:t,onlyCids:i,streams:new Set};return this.toOutputBuffer(e,null,r,s,n),[r.buffer,r.streams]}componentCIDs(e){return Object.keys(e[R]||{}).map(t=>parseInt(t))}isComponentOnlyDiff(e){return e[R]?Object.keys(e).length===1:!1}getComponent(e,t){return e[R][t]}resetRender(e){this.rendered[R][e]&&(this.rendered[R][e].reset=!0)}mergeDiff(e){let t=e[R],i={};if(delete e[R],this.rendered=this.mutableMerge(this.rendered,e),this.rendered[R]=this.rendered[R]||{},t){let s=this.rendered[R];for(let n in t)t[n]=this.cachedFindComponent(n,t[n],s,t,i);for(let n in t)s[n]=t[n];e[R]=t}}cachedFindComponent(e,t,i,s,n){if(n[e])return n[e];{let r,o,a=t[V];if(z(a)){let l;a>0?l=this.cachedFindComponent(a,s[a],i,s,n):l=i[-a],o=l[V],r=this.cloneMerge(l,t,!0),r[V]=o}else r=t[V]!==void 0||i[e]===void 0?t:this.cloneMerge(i[e],t,!1);return n[e]=r,r}}mutableMerge(e,t){return t[V]!==void 0?t:(this.doMutableMerge(e,t),e)}doMutableMerge(e,t){for(let i in t){let s=t[i],n=e[i];ue(s)&&s[V]===void 0&&ue(n)?this.doMutableMerge(n,s):e[i]=s}e[at]&&(e.newRender=!0)}cloneMerge(e,t,i){let s=U(U({},e),t);for(let n in s){let r=t[n],o=e[n];ue(r)&&r[V]===void 0&&ue(o)?s[n]=this.cloneMerge(o,r,i):r===void 0&&ue(o)&&(s[n]=this.cloneMerge(o,{},i))}return i?(delete s.magicId,delete s.newRender):e[at]&&(s.newRender=!0),s}componentToString(e){let[t,i]=this.recursiveCIDToString(this.rendered[R],e,null),[s,n,r]=Gt(t,{});return[s,i]}pruneCIDs(e){e.forEach(t=>delete this.rendered[R][t])}get(){return this.rendered}isNewFingerprint(e={}){return!!e[V]}templateStatic(e,t){return typeof e=="number"?t[e]:e}nextMagicID(){return this.magicId++,`m${this.magicId}-${this.parentViewId()}`}toOutputBuffer(e,t,i,s,n={}){if(e[Fe])return this.comprehensionToBuffer(e,t,i);let{[V]:r}=e;r=this.templateStatic(r,t);let o=e[at],a=i.buffer;o&&(i.buffer=""),s&&o&&!e.magicId&&(e.newRender=!0,e.magicId=this.nextMagicID()),i.buffer+=r[0];for(let l=1;l<r.length;l++)this.dynamicToBuffer(e[l-1],t,i,s),i.buffer+=r[l];if(o){let l=!1,h;s||e.magicId?(l=s&&!e.newRender,h=U({[ii]:e.magicId},n)):h=n,l&&(h[ti]=!0);let[d,p,f]=Gt(i.buffer,h,l);e.newRender=!1,i.buffer=a+p+d+f}}comprehensionToBuffer(e,t,i){let{[Fe]:s,[V]:n,[Ft]:r}=e,[o,a,l,h]=r||[null,{},[],null];n=this.templateStatic(n,t);let d=t||e[Ui];for(let p=0;p<s.length;p++){let f=s[p];i.buffer+=n[0];for(let g=1;g<n.length;g++){let m=!1;this.dynamicToBuffer(f[g-1],d,i,m),i.buffer+=n[g]}}r!==void 0&&(e[Fe].length>0||l.length>0||h)&&(delete e[Ft],e[Fe]=[],i.streams.add(r))}dynamicToBuffer(e,t,i,s){if(typeof e=="number"){let[n,r]=this.recursiveCIDToString(i.components,e,i.onlyCids);i.buffer+=n,i.streams=new Set([...i.streams,...r])}else ue(e)?this.toOutputBuffer(e,t,i,s,{}):i.buffer+=e}recursiveCIDToString(e,t,i){let s=e[t]||N(`no component for CID ${t}`,e),n={[K]:t},r=i&&!i.has(t);s.newRender=!r,s.magicId=`c${t}-${this.parentViewId()}`;let o=!s.reset,[a,l]=this.recursiveToString(s,e,i,o,n);return delete s.reset,[a,l]}},ps=1,Te=class{static makeID(){return ps++}static elementID(e){return e.phxHookId}constructor(e,t,i){this.__view=e,this.liveSocket=e.liveSocket,this.__callbacks=i,this.__listeners=new Set,this.__isDisconnected=!1,this.el=t,this.el.phxHookId=this.constructor.makeID();for(let s in this.__callbacks)this[s]=this.__callbacks[s]}__mounted(){this.mounted&&this.mounted()}__updated(){this.updated&&this.updated()}__beforeUpdate(){this.beforeUpdate&&this.beforeUpdate()}__destroyed(){this.destroyed&&this.destroyed()}__reconnected(){this.__isDisconnected&&(this.__isDisconnected=!1,this.reconnected&&this.reconnected())}__disconnected(){this.__isDisconnected=!0,this.disconnected&&this.disconnected()}pushEvent(e,t={},i=function(){}){return this.__view.pushHookEvent(this.el,null,e,t,i)}pushEventTo(e,t,i={},s=function(){}){return this.__view.withinTargets(e,(n,r)=>n.pushHookEvent(this.el,r,t,i,s))}handleEvent(e,t){let i=(s,n)=>n?e:t(s.detail);return window.addEventListener(`phx:${e}`,i),this.__listeners.add(i),i}removeHandleEvent(e){let t=e(null,!0);window.removeEventListener(`phx:${t}`,e),this.__listeners.delete(e)}upload(e,t){return this.__view.dispatchUploads(null,e,t)}uploadTo(e,t,i){return this.__view.withinTargets(e,(s,n)=>{s.dispatchUploads(n,t,i)})}__cleanup__(){this.__listeners.forEach(e=>this.removeHandleEvent(e))}},Be=(e,t,i=[])=>{let h=t,{submitter:s}=h,n=kt(h,["submitter"]),r;if(s&&s.name){let d=document.createElement("input");d.type="hidden";let p=s.getAttribute("form");p&&d.setAttribute("form",p),d.name=s.name,d.value=s.value,s.parentElement.insertBefore(d,s),r=d}let o=new FormData(e),a=[];o.forEach((d,p,f)=>{d instanceof File&&a.push(p)}),a.forEach(d=>o.delete(d));let l=new URLSearchParams;for(let[d,p]of o.entries())(i.length===0||i.indexOf(d)>=0)&&l.append(d,p);s&&r&&s.parentElement.removeChild(r);for(let d in n)l.append(d,n[d]);return l.toString()},ai=class{constructor(e,t,i,s,n){this.isDead=!1,this.liveSocket=t,this.flash=s,this.parent=i,this.root=i?i.root:this,this.el=e,this.id=this.el.id,this.ref=0,this.childJoins=0,this.loaderTimer=null,this.pendingDiffs=[],this.pendingForms=new Set,this.redirect=!1,this.href=null,this.joinCount=this.parent?this.parent.joinCount-1:0,this.joinPending=!0,this.destroyed=!1,this.joinCallback=function(r){r&&r()},this.stopCallback=function(){},this.pendingJoinOps=this.parent?null:[],this.viewHooks={},this.formSubmits=[],this.children=this.parent?null:{},this.root.children[this.id]={},this.formsForRecovery={},this.channel=this.liveSocket.channel(`lv:${this.id}`,()=>{let r=this.href&&this.expandURL(this.href);return{redirect:this.redirect?r:void 0,url:this.redirect?void 0:r||void 0,params:this.connectParams(n),session:this.getSession(),static:this.getStatic(),flash:this.flash}})}setHref(e){this.href=e}setRedirect(e){this.redirect=!0,this.href=e}isMain(){return this.el.hasAttribute(gt)}connectParams(e){let t=this.liveSocket.params(this.el),i=c.all(document,`[${this.binding(Ti)}]`).map(s=>s.src||s.href).filter(s=>typeof s=="string");return i.length>0&&(t._track_static=i),t._mounts=this.joinCount,t._live_referer=e,t}isConnected(){return this.channel.canPush()}getSession(){return this.el.getAttribute(Y)}getStatic(){let e=this.el.getAttribute(fe);return e===""?null:e}destroy(e=function(){}){this.destroyAllChildren(),this.destroyed=!0,delete this.root.children[this.id],this.parent&&delete this.root.children[this.parent.id][this.id],clearTimeout(this.loaderTimer);let t=()=>{e();for(let i in this.viewHooks)this.destroyHook(this.viewHooks[i])};c.markPhxChildDestroyed(this.el),this.log("destroyed",()=>["the child has been removed from the parent"]),this.channel.leave().receive("ok",t).receive("error",t).receive("timeout",t)}setContainerClasses(...e){this.el.classList.remove(Rt,ye,He,xt,tt),this.el.classList.add(...e)}showLoader(e){if(clearTimeout(this.loaderTimer),e)this.loaderTimer=setTimeout(()=>this.showLoader(),e);else{for(let t in this.viewHooks)this.viewHooks[t].__disconnected();this.setContainerClasses(ye)}}execAll(e){c.all(this.el,`[${e}]`,t=>this.liveSocket.execJS(t,t.getAttribute(e)))}hideLoader(){clearTimeout(this.loaderTimer),this.setContainerClasses(Rt),this.execAll(this.binding("connected"))}triggerReconnected(){for(let e in this.viewHooks)this.viewHooks[e].__reconnected()}log(e,t){this.liveSocket.log(this,e,t)}transition(e,t,i=function(){}){this.liveSocket.transition(e,t,i)}withinTargets(e,t,i=document,s){if(e instanceof HTMLElement||e instanceof SVGElement)return this.liveSocket.owner(e,n=>t(n,e));if(z(e))c.findComponentNodeList(s||this.el,e).length===0?N(`no component found matching phx-target of ${e}`):t(this,parseInt(e));else{let n=Array.from(i.querySelectorAll(e));n.length===0&&N(`nothing found matching the phx-target selector "${e}"`),n.forEach(r=>this.liveSocket.owner(r,o=>t(o,r)))}}applyDiff(e,t,i){this.log(e,()=>["",Je(t)]);let{diff:s,reply:n,events:r,title:o}=Yt.extract(t);i({diff:s,reply:n,events:r}),typeof o=="string"&&window.requestAnimationFrame(()=>c.putTitle(o))}onJoin(e){let{rendered:t,container:i,liveview_version:s}=e;if(i){let[n,r]=i;this.el=c.replaceRootContainer(this.el,n,r)}this.childJoins=0,this.joinPending=!0,this.flash=null,this.root===this&&(this.formsForRecovery=this.getFormsForRecovery()),s!==this.liveSocket.version()&&console.error(`LiveView asset version mismatch. JavaScript version ${this.liveSocket.version()} vs. server ${s}. To avoid issues, please ensure that your assets use the same version as the server.`),J.dropLocal(this.liveSocket.localStorage,window.location.pathname,Qt),this.applyDiff("mount",t,({diff:n,events:r})=>{this.rendered=new Yt(this.id,n);let[o,a]=this.renderContainer(null,"join");this.dropPendingRefs(),this.joinCount++,this.maybeRecoverForms(o,()=>{this.onJoinComplete(e,o,a,r)})})}dropPendingRefs(){c.all(document,`[${te}="${this.id}"][${M}]`,e=>{e.removeAttribute(M),e.removeAttribute(te)})}onJoinComplete({live_patch:e},t,i,s){if(this.joinCount>1||this.parent&&!this.parent.isJoinPending())return this.applyJoinPatch(e,t,i,s);c.findPhxChildrenInFragment(t,this.id).filter(r=>{let o=r.id&&this.el.querySelector(`[id="${r.id}"]`),a=o&&o.getAttribute(fe);return a&&r.setAttribute(fe,a),o&&o.setAttribute(le,this.root.id),this.joinChild(r)}).length===0?this.parent?(this.root.pendingJoinOps.push([this,()=>this.applyJoinPatch(e,t,i,s)]),this.parent.ackJoin(this)):(this.onAllChildJoinsComplete(),this.applyJoinPatch(e,t,i,s)):this.root.pendingJoinOps.push([this,()=>this.applyJoinPatch(e,t,i,s)])}attachTrueDocEl(){this.el=c.byId(this.id),this.el.setAttribute(le,this.root.id)}execNewMounted(){let e=this.binding(dt),t=this.binding(ct);c.all(this.el,`[${e}], [${t}]`,i=>{this.ownsElement(i)&&(c.maybeAddPrivateHooks(i,e,t),this.maybeAddNewHook(i))}),c.all(this.el,`[${this.binding(we)}], [data-phx-${we}]`,i=>{this.ownsElement(i)&&this.maybeAddNewHook(i)}),c.all(this.el,`[${this.binding(Dt)}]`,i=>{this.ownsElement(i)&&this.maybeMounted(i)})}applyJoinPatch(e,t,i,s){this.attachTrueDocEl();let n=new je(this,this.el,this.id,t,i,null);if(n.markPrunableContentForRemoval(),this.performPatch(n,!1,!0),this.joinNewChildren(),this.execNewMounted(),this.joinPending=!1,this.liveSocket.dispatchEvents(s),this.applyPendingUpdates(),e){let{kind:r,to:o}=e;this.liveSocket.historyPatch(o,r)}this.hideLoader(),this.joinCount>1&&this.triggerReconnected(),this.stopCallback()}triggerBeforeUpdateHook(e,t){this.liveSocket.triggerDOM("onBeforeElUpdated",[e,t]);let i=this.getHook(e),s=i&&c.isIgnored(e,this.binding(ze));if(i&&!e.isEqualNode(t)&&!(s&&Ji(e.dataset,t.dataset)))return i.__beforeUpdate(),i}maybeMounted(e){let t=e.getAttribute(this.binding(Dt)),i=t&&c.private(e,"mounted");t&&!i&&(this.liveSocket.execJS(e,t),c.putPrivate(e,"mounted",!0))}maybeAddNewHook(e,t){let i=this.addHook(e);i&&i.__mounted()}performPatch(e,t,i=!1){let s=[],n=!1,r=new Set;return e.after("added",o=>{this.liveSocket.triggerDOM("onNodeAdded",[o]);let a=this.binding(dt),l=this.binding(ct);c.maybeAddPrivateHooks(o,a,l),this.maybeAddNewHook(o),o.getAttribute&&this.maybeMounted(o)}),e.after("phxChildAdded",o=>{c.isPhxSticky(o)?this.liveSocket.joinRootViews():n=!0}),e.before("updated",(o,a)=>{this.triggerBeforeUpdateHook(o,a)&&r.add(o.id)}),e.after("updated",o=>{r.has(o.id)&&this.getHook(o).__updated()}),e.after("discarded",o=>{o.nodeType===Node.ELEMENT_NODE&&s.push(o)}),e.after("transitionsDiscarded",o=>this.afterElementsRemoved(o,t)),e.perform(i),this.afterElementsRemoved(s,t),n}afterElementsRemoved(e,t){let i=[];e.forEach(s=>{let n=c.all(s,`[${K}]`),r=c.all(s,`[${this.binding(we)}]`);n.concat(s).forEach(o=>{let a=this.componentID(o);z(a)&&i.indexOf(a)===-1&&i.push(a)}),r.concat(s).forEach(o=>{let a=this.getHook(o);a&&this.destroyHook(a)})}),t&&this.maybePushComponentsDestroyed(i)}joinNewChildren(){c.findPhxChildren(this.el,this.id).forEach(e=>this.joinChild(e))}maybeRecoverForms(e,t){let i=this.binding("change"),s=this.root.formsForRecovery,n=document.createElement("template");n.innerHTML=e;let r=n.content.firstElementChild;r.id=this.id,r.setAttribute(le,this.root.id),r.setAttribute(Y,this.getSession()),r.setAttribute(fe,this.getStatic()),r.setAttribute(ae,this.parent?this.parent.id:null);let o=c.all(n.content,"form").filter(a=>a.id&&s[a.id]).filter(a=>!this.pendingForms.has(a.id)).filter(a=>s[a.id].getAttribute(i)===a.getAttribute(i)).map(a=>[s[a.id],a]);if(o.length===0)return t();o.forEach(([a,l],h)=>{this.pendingForms.add(l.id),this.pushFormRecovery(a,l,n.content,()=>{this.pendingForms.delete(l.id),h===o.length-1&&t()})})}getChildById(e){return this.root.children[this.id][e]}getDescendentByEl(e){return e.id===this.id?this:this.children[e.getAttribute(ae)][e.id]}destroyDescendent(e){for(let t in this.root.children)for(let i in this.root.children[t])if(i===e)return this.root.children[t][i].destroy()}joinChild(e){if(!this.getChildById(e.id)){let i=new ai(e,this.liveSocket,this);return this.root.children[this.id][i.id]=i,i.join(),this.childJoins++,!0}}isJoinPending(){return this.joinPending}ackJoin(e){this.childJoins--,this.childJoins===0&&(this.parent?this.parent.ackJoin(this):this.onAllChildJoinsComplete())}onAllChildJoinsComplete(){this.pendingForms.clear(),this.formsForRecovery={},this.joinCallback(()=>{this.pendingJoinOps.forEach(([e,t])=>{e.isDestroyed()||t()}),this.pendingJoinOps=[]})}update(e,t){if(this.isJoinPending()||this.liveSocket.hasPendingLink()&&this.root.isMain())return this.pendingDiffs.push({diff:e,events:t});this.rendered.mergeDiff(e);let i=!1;this.rendered.isComponentOnlyDiff(e)?this.liveSocket.time("component patch complete",()=>{c.findExistingParentCIDs(this.el,this.rendered.componentCIDs(e)).forEach(n=>{this.componentPatch(this.rendered.getComponent(e,n),n)&&(i=!0)})}):$t(e)||this.liveSocket.time("full patch complete",()=>{let[s,n]=this.renderContainer(e,"update"),r=new je(this,this.el,this.id,s,n,null);i=this.performPatch(r,!0)}),this.liveSocket.dispatchEvents(t),i&&this.joinNewChildren()}renderContainer(e,t){return this.liveSocket.time(`toString diff (${t})`,()=>{let i=this.el.tagName,s=e?this.rendered.componentCIDs(e):null,[n,r]=this.rendered.toString(s);return[`<${i}>${n}</${i}>`,r]})}componentPatch(e,t){if($t(e))return!1;let[i,s]=this.rendered.componentToString(t),n=new je(this,this.el,this.id,i,s,t);return this.performPatch(n,!0)}getHook(e){return this.viewHooks[Te.elementID(e)]}addHook(e){if(Te.elementID(e)||!e.getAttribute)return;let t=e.getAttribute(`data-phx-${we}`)||e.getAttribute(this.binding(we));if(t&&!this.ownsElement(e))return;let i=this.liveSocket.getHookCallbacks(t);if(i){e.id||N(`no DOM ID for hook "${t}". Hooks require a unique ID on each element.`,e);let s=new Te(this,e,i);return this.viewHooks[Te.elementID(s.el)]=s,s}else t!==null&&N(`unknown hook found for "${t}"`,e)}destroyHook(e){e.__destroyed(),e.__cleanup__(),delete this.viewHooks[Te.elementID(e.el)]}applyPendingUpdates(){this.pendingDiffs.forEach(({diff:e,events:t})=>this.update(e,t)),this.pendingDiffs=[],this.eachChild(e=>e.applyPendingUpdates())}eachChild(e){let t=this.root.children[this.id]||{};for(let i in t)e(this.getChildById(i))}onChannel(e,t){this.liveSocket.onChannel(this.channel,e,i=>{this.isJoinPending()?this.root.pendingJoinOps.push([this,()=>t(i)]):this.liveSocket.requestDOMUpdate(()=>t(i))})}bindChannel(){this.liveSocket.onChannel(this.channel,"diff",e=>{this.liveSocket.requestDOMUpdate(()=>{this.applyDiff("update",e,({diff:t,events:i})=>this.update(t,i))})}),this.onChannel("redirect",({to:e,flash:t})=>this.onRedirect({to:e,flash:t})),this.onChannel("live_patch",e=>this.onLivePatch(e)),this.onChannel("live_redirect",e=>this.onLiveRedirect(e)),this.channel.onError(e=>this.onError(e)),this.channel.onClose(e=>this.onClose(e))}destroyAllChildren(){this.eachChild(e=>e.destroy())}onLiveRedirect(e){let{to:t,kind:i,flash:s}=e,n=this.expandURL(t);this.liveSocket.historyRedirect(n,i,s)}onLivePatch(e){let{to:t,kind:i}=e;this.href=this.expandURL(t),this.liveSocket.historyPatch(t,i)}expandURL(e){return e.startsWith("/")?`${window.location.protocol}//${window.location.host}${e}`:e}onRedirect({to:e,flash:t}){this.liveSocket.redirect(e,t)}isDestroyed(){return this.destroyed}joinDead(){this.isDead=!0}join(e){this.showLoader(this.liveSocket.loaderTimeout),this.bindChannel(),this.isMain()&&(this.stopCallback=this.liveSocket.withPageLoading({to:this.href,kind:"initial"})),this.joinCallback=t=>{t=t||function(){},e?e(this.joinCount,t):t()},this.liveSocket.wrapPush(this,{timeout:!1},()=>this.channel.join().receive("ok",t=>{this.isDestroyed()||this.liveSocket.requestDOMUpdate(()=>this.onJoin(t))}).receive("error",t=>!this.isDestroyed()&&this.onJoinError(t)).receive("timeout",()=>!this.isDestroyed()&&this.onJoinError({reason:"timeout"})))}onJoinError(e){if(e.reason==="reload"){this.log("error",()=>[`failed mount with ${e.status}. Falling back to page request`,e]),this.isMain()&&this.onRedirect({to:this.href});return}else if(e.reason==="unauthorized"||e.reason==="stale"){this.log("error",()=>["unauthorized live_redirect. Falling back to page request",e]),this.isMain()&&this.onRedirect({to:this.href});return}if((e.redirect||e.live_redirect)&&(this.joinPending=!1,this.channel.leave()),e.redirect)return this.onRedirect(e.redirect);if(e.live_redirect)return this.onLiveRedirect(e.live_redirect);this.displayError([ye,He,tt]),this.log("error",()=>["unable to join",e]),this.liveSocket.isConnected()&&this.liveSocket.reloadWithJitter(this)}onClose(e){if(!this.isDestroyed()){if(this.liveSocket.hasPendingLink()&&e!=="leave")return this.liveSocket.reloadWithJitter(this);this.destroyAllChildren(),this.liveSocket.dropActiveElement(this),document.activeElement&&document.activeElement.blur(),this.liveSocket.isUnloaded()&&this.showLoader(Mi)}}onError(e){this.onClose(e),this.liveSocket.isConnected()&&this.log("error",()=>["view crashed",e]),this.liveSocket.isUnloaded()||(this.liveSocket.isConnected()?this.displayError([ye,He,tt]):this.displayError([ye,He,xt]))}displayError(e){this.isMain()&&c.dispatchEvent(window,"phx:page-loading-start",{detail:{to:this.href,kind:"error"}}),this.showLoader(),this.setContainerClasses(...e),this.execAll(this.binding("disconnected"))}pushWithReply(e,t,i,s=function(){}){if(!this.isConnected())return;let[n,[r],o]=e?e():[null,[],{}],a=function(){};return(o.page_loading||r&&r.getAttribute(this.binding(_t))!==null)&&(a=this.liveSocket.withPageLoading({kind:"element",target:r})),typeof i.cid!="number"&&delete i.cid,this.liveSocket.wrapPush(this,{timeout:!0},()=>this.channel.push(t,i,Fi).receive("ok",l=>{let h=d=>{l.redirect&&this.onRedirect(l.redirect),l.live_patch&&this.onLivePatch(l.live_patch),l.live_redirect&&this.onLiveRedirect(l.live_redirect),a(),s(l,d)};l.diff?this.liveSocket.requestDOMUpdate(()=>{this.applyDiff("update",l.diff,({diff:d,reply:p,events:f})=>{n!==null&&this.undoRefs(n),this.update(d,f),h(p)})}):(n!==null&&this.undoRefs(n),h(null))}))}undoRefs(e,t){t=t?new Set(t):null,this.isConnected()&&c.all(document,`[${te}="${this.id}"][${M}="${e}"]`,i=>{if(t&&!t.has(i))return;i.dispatchEvent(new CustomEvent("phx:unlock",{bubbles:!0,cancelable:!1}));let s=i.getAttribute(ce),n=i.getAttribute(it);i.removeAttribute(M),i.removeAttribute(te),n!==null&&(i.readOnly=n==="true",i.removeAttribute(it)),s!==null&&(i.disabled=s==="true",i.removeAttribute(ce)),Zt.forEach(a=>c.removeClass(i,a));let r=i.getAttribute(Me);r!==null&&(i.innerText=r,i.removeAttribute(Me));let o=c.private(i,M);if(o){let a=this.triggerBeforeUpdateHook(i,o);je.patchEl(i,o,this.liveSocket.getActiveElement()),a&&a.__updated(),c.deletePrivate(i,M)}})}putRef(e,t,i={}){let s=this.ref++,n=this.binding(ft);i.loading&&(e=e.concat(c.all(document,i.loading)));for(let r of e){if(r.setAttribute(M,s),r.setAttribute(te,this.el.id),i.submitter&&!(r===i.submitter||r===i.form))continue;r.classList.add(`phx-${t}-loading`),r.dispatchEvent(new CustomEvent(`phx:${t}-loading`,{bubbles:!0,cancelable:!1}));let o=r.getAttribute(n);o!==null&&(r.getAttribute(Me)||r.setAttribute(Me,r.innerText),o!==""&&(r.innerText=o),r.setAttribute(ce,r.getAttribute(ce)||r.disabled),r.setAttribute("disabled",""))}return[s,e,i]}componentID(e){let t=e.getAttribute&&e.getAttribute(K);return t?parseInt(t):null}targetComponentID(e,t,i={}){if(z(t))return t;let s=i.target||e.getAttribute(this.binding("target"));return z(s)?parseInt(s):t&&(s!==null||i.target)?this.closestComponentID(t):null}closestComponentID(e){return z(e)?e:e?ie(e.closest(`[${K}]`),t=>this.ownsElement(t)&&this.componentID(t)):null}pushHookEvent(e,t,i,s,n){if(!this.isConnected())return this.log("hook",()=>["unable to push hook event. LiveView not connected",i,s]),!1;let[r,o,a]=this.putRef([e],"hook");return this.pushWithReply(()=>[r,o,a],"event",{type:"hook",event:i,value:s,cid:this.closestComponentID(t)},(l,h)=>n(h,r)),r}extractMeta(e,t,i){let s=this.binding("value-");for(let n=0;n<e.attributes.length;n++){t||(t={});let r=e.attributes[n].name;r.startsWith(s)&&(t[r.replace(s,"")]=e.getAttribute(r))}if(e.value!==void 0&&!(e instanceof HTMLFormElement)&&(t||(t={}),t.value=e.value,e.tagName==="INPUT"&&si.indexOf(e.type)>=0&&!e.checked&&delete t.value),i){t||(t={});for(let n in i)t[n]=i[n]}return t}pushEvent(e,t,i,s,n,r={},o){this.pushWithReply(()=>this.putRef([t],e,r),"event",{type:e,event:s,value:this.extractMeta(t,n,r.value),cid:this.targetComponentID(t,i,r)},(a,l)=>o&&o(l))}pushFileProgress(e,t,i,s=function(){}){this.liveSocket.withinOwners(e.form,(n,r)=>{n.pushWithReply(null,"progress",{event:e.getAttribute(n.binding(Oi)),ref:e.getAttribute(G),entry_ref:t,progress:i,cid:n.targetComponentID(e.form,r)},s)})}pushInput(e,t,i,s,n,r){let o,a=z(i)?i:this.targetComponentID(e.form,t,n),l=()=>this.putRef([e,e.form],"change",n),h,d=this.extractMeta(e.form);e instanceof HTMLButtonElement&&(d.submitter=e),e.getAttribute(this.binding("change"))?h=Be(e.form,U({_target:n._target},d),[e.name]):h=Be(e.form,U({_target:n._target},d)),c.isUploadInput(e)&&e.files&&e.files.length>0&&T.trackFiles(e,Array.from(e.files)),o=T.serializeUploads(e);let p={type:"form",event:s,value:h,uploads:o,cid:a};this.pushWithReply(l,"event",p,f=>{if(c.showError(e,this.liveSocket.binding(Xe),this.liveSocket.binding(We)),c.isUploadInput(e)&&c.isAutoUpload(e)){if(T.filesAwaitingPreflight(e).length>0){let[g,m]=l();this.undoRefs(g,[e.form]),this.uploadFiles(e.form,t,g,a,w=>{r&&r(f),this.triggerAwaitingSubmit(e.form),this.undoRefs(g)})}}else r&&r(f)})}triggerAwaitingSubmit(e){let t=this.getScheduledSubmit(e);if(t){let[i,s,n,r]=t;this.cancelSubmit(e),r()}}getScheduledSubmit(e){return this.formSubmits.find(([t,i,s,n])=>t.isSameNode(e))}scheduleSubmit(e,t,i,s){if(this.getScheduledSubmit(e))return!0;this.formSubmits.push([e,t,i,s])}cancelSubmit(e){this.formSubmits=this.formSubmits.filter(([t,i,s])=>t.isSameNode(e)?(this.undoRefs(i),!1):!0)}disableForm(e,t={}){let i=d=>!(Pe(d,`${this.binding(ze)}=ignore`,d.form)||Pe(d,"data-phx-update=ignore",d.form)),s=d=>d.hasAttribute(this.binding(ft)),n=d=>d.tagName=="BUTTON",r=d=>["INPUT","TEXTAREA","SELECT"].includes(d.tagName),o=Array.from(e.elements),a=o.filter(s),l=o.filter(n).filter(i),h=o.filter(r).filter(i);return l.forEach(d=>{d.setAttribute(ce,d.disabled),d.disabled=!0}),h.forEach(d=>{d.setAttribute(it,d.readOnly),d.readOnly=!0,d.files&&(d.setAttribute(ce,d.disabled),d.disabled=!0)}),e.setAttribute(this.binding(_t),""),this.putRef([e].concat(a).concat(l).concat(h),"submit",t)}pushFormSubmit(e,t,i,s,n,r){let o=()=>this.disableForm(e,At(U({},n),{form:e,submitter:s})),a=this.targetComponentID(e,t);if(T.hasUploadsInProgress(e)){let[l,h]=o(),d=()=>this.pushFormSubmit(e,t,i,s,n,r);return this.scheduleSubmit(e,l,n,d)}else if(T.inputsAwaitingPreflight(e).length>0){let[l,h]=o(),d=()=>[l,h,n];this.uploadFiles(e,t,l,a,p=>{if(T.inputsAwaitingPreflight(e).length>0)return this.undoRefs(l);let f=this.extractMeta(e),g=Be(e,U({submitter:s},f));this.pushWithReply(d,"event",{type:"form",event:i,value:g,cid:a},r)})}else if(!(e.hasAttribute(M)&&e.classList.contains("phx-submit-loading"))){let l=this.extractMeta(e),h=Be(e,U({submitter:s},l));this.pushWithReply(o,"event",{type:"form",event:i,value:h,cid:a},r)}}uploadFiles(e,t,i,s,n){let r=this.joinCount,o=T.activeFileInputs(e),a=o.length;o.forEach(l=>{let h=new T(l,this,()=>{a--,a===0&&n()}),d=h.entries().map(f=>f.toPreflightPayload());if(d.length===0){a--;return}let p={ref:l.getAttribute(G),entries:d,cid:this.targetComponentID(l.form,t)};this.log("upload",()=>["sending preflight request",p]),this.pushWithReply(null,"allow_upload",p,f=>{if(this.log("upload",()=>["got preflight response",f]),h.entries().forEach(g=>{f.entries&&!f.entries[g.ref]&&this.handleFailedEntryPreflight(g.ref,"failed preflight",h)}),f.error||Object.keys(f.entries).length===0)this.undoRefs(i),(f.error||[]).map(([m,w])=>{this.handleFailedEntryPreflight(m,w,h)});else{let g=m=>{this.channel.onError(()=>{this.joinCount===r&&m()})};h.initAdapterUpload(f,g,this.liveSocket)}})})}handleFailedEntryPreflight(e,t,i){if(i.isAutoUpload()){let s=i.entries().find(n=>n.ref===e.toString());s&&s.cancel()}else i.entries().map(s=>s.cancel());this.log("upload",()=>[`error for entry ${e}`,t])}dispatchUploads(e,t,i){let s=this.targetCtxElement(e)||this.el,n=c.findUploadInputs(s).filter(r=>r.name===t);n.length===0?N(`no live file inputs found matching the name "${t}"`):n.length>1?N(`duplicate live file inputs found matching the name "${t}"`):c.dispatchEvent(n[0],ei,{detail:{files:i}})}targetCtxElement(e){if(z(e)){let[t]=c.findComponentNodeList(this.el,e);return t}else return e||null}pushFormRecovery(e,t,i,s){let n=this.binding("change"),r=t.getAttribute(this.binding("target"))||t,o=t.getAttribute(this.binding(It))||t.getAttribute(this.binding("change")),a=Array.from(e.elements).filter(d=>c.isFormInput(d)&&d.name&&!d.hasAttribute(n));if(a.length===0)return;a.forEach(d=>d.hasAttribute(G)&&T.clearFiles(d));let l=a.find(d=>d.type!=="hidden")||a[0],h=0;this.withinTargets(r,(d,p)=>{let f=this.targetComponentID(t,p);h++,d.pushInput(l,p,f,o,{_target:l.name},()=>{h--,h===0&&s()})},i,i)}pushLinkPatch(e,t,i){let s=this.liveSocket.setPendingLink(e),n=t?()=>this.putRef([t],"click"):null,r=()=>this.liveSocket.redirect(window.location.href),o=e.startsWith("/")?`${location.protocol}//${location.host}${e}`:e,a=this.pushWithReply(n,"live_patch",{url:o},l=>{this.liveSocket.requestDOMUpdate(()=>{l.link_redirect?this.liveSocket.replaceMain(e,null,i,s):(this.liveSocket.commitPendingLink(s)&&(this.href=e),this.applyPendingUpdates(),i&&i(s))})});a?a.receive("timeout",r):r()}getFormsForRecovery(){if(this.joinCount===0)return{};let e=this.binding("change");return c.all(this.el,`form[${e}]`).filter(t=>t.id).filter(t=>t.elements.length>0).filter(t=>t.getAttribute(this.binding(It))!=="ignore").map(t=>t.cloneNode(!0)).reduce((t,i)=>(t[i.id]=i,t),{})}maybePushComponentsDestroyed(e){let t=e.filter(i=>c.findComponentNodeList(this.el,i).length===0);t.length>0&&(t.forEach(i=>this.rendered.resetRender(i)),this.pushWithReply(null,"cids_will_destroy",{cids:t},()=>{this.liveSocket.requestDOMUpdate(()=>{let i=t.filter(s=>c.findComponentNodeList(this.el,s).length===0);i.length>0&&this.pushWithReply(null,"cids_destroyed",{cids:i},s=>{this.rendered.pruneCIDs(s.cids)})})}))}ownsElement(e){let t=e.closest(ge);return e.getAttribute(ae)===this.id||t&&t.id===this.id||!t&&this.isDead}submitForm(e,t,i,s,n={}){c.putPrivate(e,qe,!0);let r=this.liveSocket.binding(Xe),o=this.liveSocket.binding(We),a=Array.from(e.elements);a.forEach(l=>c.putPrivate(l,qe,!0)),this.liveSocket.blurActiveElement(this),this.pushFormSubmit(e,t,i,s,n,()=>{a.forEach(l=>c.showError(l,r,o)),this.liveSocket.restorePreviouslyActiveFocus()})}binding(e){return this.liveSocket.binding(e)}},li=class{constructor(e,t,i={}){if(this.unloaded=!1,!t||t.constructor.name==="Object")throw new Error(`
      a phoenix Socket must be provided as the second argument to the LiveSocket constructor. For example:

          import {Socket} from "phoenix"
          import {LiveSocket} from "phoenix_live_view"
          let liveSocket = new LiveSocket("/live", Socket, {...})
      `);this.socket=new t(e,i),this.bindingPrefix=i.bindingPrefix||Ni,this.opts=i,this.params=ke(i.params||{}),this.viewLogger=i.viewLogger,this.metadataCallbacks=i.metadata||{},this.defaults=Object.assign(Je($i),i.defaults||{}),this.activeElement=null,this.prevActive=null,this.silenced=!1,this.main=null,this.outgoingMainEl=null,this.clickStartedAtTarget=null,this.linkRef=1,this.roots={},this.href=window.location.href,this.pendingLink=null,this.currentLocation=Je(window.location),this.hooks=i.hooks||{},this.uploaders=i.uploaders||{},this.loaderTimeout=i.loaderTimeout||Hi,this.reloadWithJitterTimer=null,this.maxReloads=i.maxReloads||Ai,this.reloadJitterMin=i.reloadJitterMin||ki,this.reloadJitterMax=i.reloadJitterMax||Ci,this.failsafeJitter=i.failsafeJitter||Ei,this.localStorage=i.localStorage||window.localStorage,this.sessionStorage=i.sessionStorage||window.sessionStorage,this.boundTopLevelEvents=!1,this.serverCloseRef=null,this.domCallbacks=Object.assign({onPatchStart:ke(),onPatchEnd:ke(),onNodeAdded:ke(),onBeforeElUpdated:ke()},i.dom||{}),this.transitions=new gs,window.addEventListener("pagehide",s=>{this.unloaded=!0}),this.socket.onOpen(()=>{this.isUnloaded()&&window.location.reload()})}version(){return"0.20.17"}isProfileEnabled(){return this.sessionStorage.getItem(rt)==="true"}isDebugEnabled(){return this.sessionStorage.getItem(Ne)==="true"}isDebugDisabled(){return this.sessionStorage.getItem(Ne)==="false"}enableDebug(){this.sessionStorage.setItem(Ne,"true")}enableProfiling(){this.sessionStorage.setItem(rt,"true")}disableDebug(){this.sessionStorage.setItem(Ne,"false")}disableProfiling(){this.sessionStorage.removeItem(rt)}enableLatencySim(e){this.enableDebug(),console.log("latency simulator enabled for the duration of this browser session. Call disableLatencySim() to disable"),this.sessionStorage.setItem(ot,e)}disableLatencySim(){this.sessionStorage.removeItem(ot)}getLatencySim(){let e=this.sessionStorage.getItem(ot);return e?parseInt(e):null}getSocket(){return this.socket}connect(){window.location.hostname==="localhost"&&!this.isDebugDisabled()&&this.enableDebug();let e=()=>{this.joinRootViews()?(this.bindTopLevelEvents(),this.socket.connect()):this.main?this.socket.connect():this.bindTopLevelEvents({dead:!0}),this.joinDeadView()};["complete","loaded","interactive"].indexOf(document.readyState)>=0?e():document.addEventListener("DOMContentLoaded",()=>e())}disconnect(e){clearTimeout(this.reloadWithJitterTimer),this.serverCloseRef&&(this.socket.off(this.serverCloseRef),this.serverCloseRef=null),this.socket.disconnect(e)}replaceTransport(e){clearTimeout(this.reloadWithJitterTimer),this.socket.replaceTransport(e),this.connect()}execJS(e,t,i=null){this.owner(e,s=>x.exec(i,t,s,e))}execJSHookPush(e,t,i,s){this.withinOwners(e,n=>{x.exec("hook",t,n,e,["push",{data:i,callback:s}])})}unload(){this.unloaded||(this.main&&this.isConnected()&&this.log(this.main,"socket",()=>["disconnect for page nav"]),this.unloaded=!0,this.destroyAllViews(),this.disconnect())}triggerDOM(e,t){this.domCallbacks[e](...t)}time(e,t){if(!this.isProfileEnabled()||!console.time)return t();console.time(e);let i=t();return console.timeEnd(e),i}log(e,t,i){if(this.viewLogger){let[s,n]=i();this.viewLogger(e,t,s,n)}else if(this.isDebugEnabled()){let[s,n]=i();Vi(e,t,s,n)}}requestDOMUpdate(e){this.transitions.after(e)}transition(e,t,i=function(){}){this.transitions.addTransition(e,t,i)}onChannel(e,t,i){e.on(t,s=>{let n=this.getLatencySim();n?setTimeout(()=>i(s),n):i(s)})}wrapPush(e,t,i){let s=this.getLatencySim(),n=e.joinCount;if(!s)return this.isConnected()&&t.timeout?i().receive("timeout",()=>{e.joinCount===n&&!e.isDestroyed()&&this.reloadWithJitter(e,()=>{this.log(e,"timeout",()=>["received timeout while communicating with server. Falling back to hard refresh for recovery"])})}):i();let r={receives:[],receive(o,a){this.receives.push([o,a])}};return setTimeout(()=>{e.isDestroyed()||r.receives.reduce((o,[a,l])=>o.receive(a,l),i())},s),r}reloadWithJitter(e,t){clearTimeout(this.reloadWithJitterTimer),this.disconnect();let i=this.reloadJitterMin,s=this.reloadJitterMax,n=Math.floor(Math.random()*(s-i+1))+i,r=J.updateLocal(this.localStorage,window.location.pathname,Qt,0,o=>o+1);r>this.maxReloads&&(n=this.failsafeJitter),this.reloadWithJitterTimer=setTimeout(()=>{e.isDestroyed()||e.isConnected()||(e.destroy(),t?t():this.log(e,"join",()=>[`encountered ${r} consecutive reloads`]),r>this.maxReloads&&this.log(e,"join",()=>[`exceeded ${this.maxReloads} consecutive reloads. Entering failsafe mode`]),this.hasPendingLink()?window.location=this.pendingLink:window.location.reload())},n)}getHookCallbacks(e){return e&&e.startsWith("Phoenix.")?Yi[e.split(".")[1]]:this.hooks[e]}isUnloaded(){return this.unloaded}isConnected(){return this.socket.isConnected()}getBindingPrefix(){return this.bindingPrefix}binding(e){return`${this.getBindingPrefix()}${e}`}channel(e,t){return this.socket.channel(e,t)}joinDeadView(){let e=document.body;if(e&&!this.isPhxView(e)&&!this.isPhxView(document.firstElementChild)){let t=this.newRootView(e);t.setHref(this.getHref()),t.joinDead(),this.main||(this.main=t),window.requestAnimationFrame(()=>t.execNewMounted())}}joinRootViews(){let e=!1;return c.all(document,`${ge}:not([${ae}])`,t=>{if(!this.getRootById(t.id)){let i=this.newRootView(t);i.setHref(this.getHref()),i.join(),t.hasAttribute(gt)&&(this.main=i)}e=!0}),e}redirect(e,t){this.unload(),J.redirect(e,t)}replaceMain(e,t,i=null,s=this.setPendingLink(e)){let n=this.currentLocation.href;this.outgoingMainEl=this.outgoingMainEl||this.main.el;let r=c.cloneNode(this.outgoingMainEl,"");this.main.showLoader(this.loaderTimeout),this.main.destroy(),this.main=this.newRootView(r,t,n),this.main.setRedirect(e),this.transitionRemoves(null,!0),this.main.join((o,a)=>{o===1&&this.commitPendingLink(s)&&this.requestDOMUpdate(()=>{c.findPhxSticky(document).forEach(l=>r.appendChild(l)),this.outgoingMainEl.replaceWith(r),this.outgoingMainEl=null,i&&i(s),a()})})}transitionRemoves(e,t){let i=this.binding("remove");if(e=e||c.all(document,`[${i}]`),t){let s=c.findPhxSticky(document)||[];e=e.filter(n=>!c.isChildOfAny(n,s))}e.forEach(s=>{this.execJS(s,s.getAttribute(i),"remove")})}isPhxView(e){return e.getAttribute&&e.getAttribute(Y)!==null}newRootView(e,t,i){let s=new ai(e,this,null,t,i);return this.roots[s.id]=s,s}owner(e,t){let i=ie(e.closest(ge),s=>this.getViewByEl(s))||this.main;i&&t(i)}withinOwners(e,t){this.owner(e,i=>t(i,e))}getViewByEl(e){let t=e.getAttribute(le);return ie(this.getRootById(t),i=>i.getDescendentByEl(e))}getRootById(e){return this.roots[e]}destroyAllViews(){for(let e in this.roots)this.roots[e].destroy(),delete this.roots[e];this.main=null}destroyViewByEl(e){let t=this.getRootById(e.getAttribute(le));t&&t.id===e.id?(t.destroy(),delete this.roots[t.id]):t&&t.destroyDescendent(e.id)}setActiveElement(e){if(this.activeElement===e)return;this.activeElement=e;let t=()=>{e===this.activeElement&&(this.activeElement=null),e.removeEventListener("mouseup",this),e.removeEventListener("touchend",this)};e.addEventListener("mouseup",t),e.addEventListener("touchend",t)}getActiveElement(){return document.activeElement===document.body?this.activeElement||document.activeElement:document.activeElement||document.body}dropActiveElement(e){this.prevActive&&e.ownsElement(this.prevActive)&&(this.prevActive=null)}restorePreviouslyActiveFocus(){this.prevActive&&this.prevActive!==document.body&&this.prevActive.focus()}blurActiveElement(){this.prevActive=this.getActiveElement(),this.prevActive!==document.body&&this.prevActive.blur()}bindTopLevelEvents({dead:e}={}){this.boundTopLevelEvents||(this.boundTopLevelEvents=!0,this.serverCloseRef=this.socket.onClose(t=>{if(t&&t.code===1e3&&this.main)return this.reloadWithJitter(this.main)}),document.body.addEventListener("click",function(){}),window.addEventListener("pageshow",t=>{t.persisted&&(this.getSocket().disconnect(),this.withPageLoading({to:window.location.href,kind:"redirect"}),window.location.reload())},!0),e||this.bindNav(),this.bindClicks(),e||this.bindForms(),this.bind({keyup:"keyup",keydown:"keydown"},(t,i,s,n,r,o)=>{let a=n.getAttribute(this.binding(Di)),l=t.key&&t.key.toLowerCase();if(a&&a.toLowerCase()!==l)return;let h=U({key:t.key},this.eventMeta(i,t,n));x.exec(i,r,s,n,["push",{data:h}])}),this.bind({blur:"focusout",focus:"focusin"},(t,i,s,n,r,o)=>{if(!o){let a=U({key:t.key},this.eventMeta(i,t,n));x.exec(i,r,s,n,["push",{data:a}])}}),this.bind({blur:"blur",focus:"focus"},(t,i,s,n,r,o)=>{if(o==="window"){let a=this.eventMeta(i,t,n);x.exec(i,r,s,n,["push",{data:a}])}}),window.addEventListener("dragover",t=>t.preventDefault()),window.addEventListener("drop",t=>{t.preventDefault();let i=ie(Pe(t.target,this.binding(Tt)),r=>r.getAttribute(this.binding(Tt))),s=i&&document.getElementById(i),n=Array.from(t.dataTransfer.files||[]);!s||s.disabled||n.length===0||!(s.files instanceof FileList)||(T.trackFiles(s,n,t.dataTransfer),s.dispatchEvent(new Event("input",{bubbles:!0})))}),this.on(ei,t=>{let i=t.target;if(!c.isUploadInput(i))return;let s=Array.from(t.detail.files||[]).filter(n=>n instanceof File||n instanceof Blob);T.trackFiles(i,s),i.dispatchEvent(new Event("input",{bubbles:!0}))}))}eventMeta(e,t,i){let s=this.metadataCallbacks[e];return s?s(t,i):{}}setPendingLink(e){return this.linkRef++,this.pendingLink=e,this.linkRef}commitPendingLink(e){return this.linkRef!==e?!1:(this.href=this.pendingLink,this.pendingLink=null,!0)}getHref(){return this.href}hasPendingLink(){return!!this.pendingLink}bind(e,t){for(let i in e){let s=e[i];this.on(s,n=>{let r=this.binding(i),o=this.binding(`window-${i}`),a=n.target.getAttribute&&n.target.getAttribute(r);a?this.debounce(n.target,n,s,()=>{this.withinOwners(n.target,l=>{t(n,i,l,n.target,a,null)})}):c.all(document,`[${o}]`,l=>{let h=l.getAttribute(o);this.debounce(l,n,s,()=>{this.withinOwners(l,d=>{t(n,i,d,l,h,"window")})})})})}}bindClicks(){window.addEventListener("mousedown",e=>this.clickStartedAtTarget=e.target),this.bindClick("click","click")}bindClick(e,t){let i=this.binding(t);window.addEventListener(e,s=>{let n=null;s.detail===0&&(this.clickStartedAtTarget=s.target);let r=this.clickStartedAtTarget||s.target;n=Pe(r,i),this.dispatchClickAway(s,r),this.clickStartedAtTarget=null;let o=n&&n.getAttribute(i);if(!o){c.isNewPageClick(s,window.location)&&this.unload();return}n.getAttribute("href")==="#"&&s.preventDefault(),!n.hasAttribute(M)&&this.debounce(n,s,"click",()=>{this.withinOwners(n,a=>{x.exec("click",o,a,n,["push",{data:this.eventMeta("click",s,n)}])})})},!1)}dispatchClickAway(e,t){let i=this.binding("click-away");c.all(document,`[${i}]`,s=>{s.isSameNode(t)||s.contains(t)||this.withinOwners(s,n=>{let r=s.getAttribute(i);x.isVisible(s)&&x.isInViewport(s)&&x.exec("click",r,n,s,["push",{data:this.eventMeta("click",e,e.target)}])})})}bindNav(){if(!J.canPushState())return;history.scrollRestoration&&(history.scrollRestoration="manual");let e=null;window.addEventListener("scroll",t=>{clearTimeout(e),e=setTimeout(()=>{J.updateCurrentState(i=>Object.assign(i,{scroll:window.scrollY}))},100)}),window.addEventListener("popstate",t=>{if(!this.registerNewLocation(window.location))return;let{type:i,id:s,root:n,scroll:r}=t.state||{},o=window.location.href;c.dispatchEvent(window,"phx:navigate",{detail:{href:o,patch:i==="patch",pop:!0}}),this.requestDOMUpdate(()=>{this.main.isConnected()&&i==="patch"&&s===this.main.id?this.main.pushLinkPatch(o,null,()=>{this.maybeScroll(r)}):this.replaceMain(o,null,()=>{n&&this.replaceRootHistory(),this.maybeScroll(r)})})},!1),window.addEventListener("click",t=>{let i=Pe(t.target,et),s=i&&i.getAttribute(et);if(!s||!this.isConnected()||!this.main||c.wantsNewTab(t))return;let n=i.href instanceof SVGAnimatedString?i.href.baseVal:i.href,r=i.getAttribute(Pi);t.preventDefault(),t.stopImmediatePropagation(),this.pendingLink!==n&&this.requestDOMUpdate(()=>{if(s==="patch")this.pushHistoryPatch(n,r,i);else if(s==="redirect")this.historyRedirect(n,r);else throw new Error(`expected ${et} to be "patch" or "redirect", got: ${s}`);let o=i.getAttribute(this.binding("click"));o&&this.requestDOMUpdate(()=>this.execJS(i,o,"click"))})},!1)}maybeScroll(e){typeof e=="number"&&requestAnimationFrame(()=>{window.scrollTo(0,e)})}dispatchEvent(e,t={}){c.dispatchEvent(window,`phx:${e}`,{detail:t})}dispatchEvents(e){e.forEach(([t,i])=>this.dispatchEvent(t,i))}withPageLoading(e,t){c.dispatchEvent(window,"phx:page-loading-start",{detail:e});let i=()=>c.dispatchEvent(window,"phx:page-loading-stop",{detail:e});return t?t(i):i}pushHistoryPatch(e,t,i){if(!this.isConnected()||!this.main.isMain())return J.redirect(e);this.withPageLoading({to:e,kind:"patch"},s=>{this.main.pushLinkPatch(e,i,n=>{this.historyPatch(e,t,n),s()})})}historyPatch(e,t,i=this.setPendingLink(e)){this.commitPendingLink(i)&&(J.pushState(t,{type:"patch",id:this.main.id},e),c.dispatchEvent(window,"phx:navigate",{detail:{patch:!0,href:e,pop:!1}}),this.registerNewLocation(window.location))}historyRedirect(e,t,i){if(!this.isConnected()||!this.main.isMain())return J.redirect(e,i);if(/^\/$|^\/[^\/]+.*$/.test(e)){let{protocol:n,host:r}=window.location;e=`${n}//${r}${e}`}let s=window.scrollY;this.withPageLoading({to:e,kind:"redirect"},n=>{this.replaceMain(e,i,r=>{r===this.linkRef&&(J.pushState(t,{type:"redirect",id:this.main.id,scroll:s},e),c.dispatchEvent(window,"phx:navigate",{detail:{href:e,patch:!1,pop:!1}}),this.registerNewLocation(window.location)),n()})})}replaceRootHistory(){J.pushState("replace",{root:!0,type:"patch",id:this.main.id})}registerNewLocation(e){let{pathname:t,search:i}=this.currentLocation;return t+i===e.pathname+e.search?!1:(this.currentLocation=Je(e),!0)}bindForms(){let e=0,t=!1;this.on("submit",i=>{let s=i.target.getAttribute(this.binding("submit")),n=i.target.getAttribute(this.binding("change"));!t&&n&&!s&&(t=!0,i.preventDefault(),this.withinOwners(i.target,r=>{r.disableForm(i.target),window.requestAnimationFrame(()=>{c.isUnloadableFormSubmit(i)&&this.unload(),i.target.submit()})}))},!0),this.on("submit",i=>{let s=i.target.getAttribute(this.binding("submit"));if(!s){c.isUnloadableFormSubmit(i)&&this.unload();return}i.preventDefault(),i.target.disabled=!0,this.withinOwners(i.target,n=>{x.exec("submit",s,n,i.target,["push",{submitter:i.submitter}])})},!1);for(let i of["change","input"])this.on(i,s=>{let n=this.binding("change"),r=s.target,o=r.getAttribute(n),a=r.form&&r.form.getAttribute(n),l=o||a;if(!l||r.type==="number"&&r.validity&&r.validity.badInput)return;let h=o?r:r.form,d=e;e++;let{at:p,type:f}=c.private(r,"prev-iteration")||{};p===d-1&&i==="change"&&f==="input"||(c.putPrivate(r,"prev-iteration",{at:d,type:i}),this.debounce(r,s,i,()=>{this.withinOwners(h,g=>{c.putPrivate(r,ut,!0),c.isTextualInput(r)||this.setActiveElement(r),x.exec("change",l,g,r,["push",{_target:s.target.name,dispatcher:h}])})}))},!1);this.on("reset",i=>{let s=i.target;c.resetForm(s,this.binding(Xe),this.binding(We));let n=Array.from(s.elements).find(r=>r.type==="reset");n&&window.requestAnimationFrame(()=>{n.dispatchEvent(new Event("input",{bubbles:!0,cancelable:!1}))})})}debounce(e,t,i,s){if(i==="blur"||i==="focusout")return s();let n=this.binding(Li),r=this.binding(Ii),o=this.defaults.debounce.toString(),a=this.defaults.throttle.toString();this.withinOwners(e,l=>{let h=()=>!l.isDestroyed()&&document.body.contains(e);c.debounce(e,t,n,o,r,a,h,()=>{s()})})}silenceEvents(e){this.silenced=!0,e(),this.silenced=!1}on(e,t){window.addEventListener(e,i=>{this.silenced||t(i)})}},gs=class{constructor(){this.transitions=new Set,this.pendingOps=[]}reset(){this.transitions.forEach(e=>{clearTimeout(e),this.transitions.delete(e)}),this.flushPendingOps()}after(e){this.size()===0?e():this.pushPendingOp(e)}addTransition(e,t,i){t();let s=setTimeout(()=>{this.transitions.delete(s),i(),this.flushPendingOps()},e);this.transitions.add(s)}pushPendingOp(e){this.pendingOps.push(e)}size(){return this.transitions.size}flushPendingOps(){if(this.size()>0)return;let e=this.pendingOps.shift();e&&(e(),this.flushPendingOps())}};(function(e,t){"use strict";function i(){s.width=e.innerWidth,s.height=5*h.barThickness;var f=s.getContext("2d");f.shadowBlur=h.shadowBlur,f.shadowColor=h.shadowColor;var g,m=f.createLinearGradient(0,0,s.width,0);for(g in h.barColors)m.addColorStop(g,h.barColors[g]);f.lineWidth=h.barThickness,f.beginPath(),f.moveTo(0,h.barThickness/2),f.lineTo(Math.ceil(n*s.width),h.barThickness/2),f.strokeStyle=m,f.stroke()}var s,n,r,o=null,a=null,l=null,h={autoRun:!0,barThickness:3,barColors:{0:"rgba(26, 188, 156, .9)",".25":"rgba(52, 152, 219, .9)",".50":"rgba(241, 196, 15, .9)",".75":"rgba(230, 126, 34, .9)","1.0":"rgba(211, 84, 0, .9)"},shadowBlur:10,shadowColor:"rgba(0, 0, 0, .6)",className:null},d={config:function(f){for(var g in f)h.hasOwnProperty(g)&&(h[g]=f[g])},show:function(f){var g,m;r||(f?l=l||setTimeout(()=>d.show(),f):(r=!0,a!==null&&e.cancelAnimationFrame(a),s||((m=(s=t.createElement("canvas")).style).position="fixed",m.top=m.left=m.right=m.margin=m.padding=0,m.zIndex=100001,m.display="none",h.className&&s.classList.add(h.className),t.body.appendChild(s),g="resize",f=i,(m=e).addEventListener?m.addEventListener(g,f,!1):m.attachEvent?m.attachEvent("on"+g,f):m["on"+g]=f),s.style.opacity=1,s.style.display="block",d.progress(0),h.autoRun&&function w(){o=e.requestAnimationFrame(w),d.progress("+"+.05*Math.pow(1-Math.sqrt(n),2))}()))},progress:function(f){return f===void 0||(typeof f=="string"&&(f=(0<=f.indexOf("+")||0<=f.indexOf("-")?n:0)+parseFloat(f)),n=1<f?1:f,i()),n},hide:function(){r&&(r=!1,o!=null&&(e.cancelAnimationFrame(o),o=null),l!=null&&(clearTimeout(l),l=null),function f(){return a=e.requestAnimationFrame(f),s.style.opacity-=.05,s.style.opacity<=.05?(s.style.opacity=0,s.style.display="none",void(a=null)):void 0}())}};if(typeof module=="object"&&typeof module.exports=="object")module.exports=d;else if(typeof define=="function"&&define.amd)define(function(){return d});else{var p=e.topbar;d.noConflict=function(){return e.topbar=p,d},e.topbar=d}})(window,document);var Ke=window.topbar;var ms={mounted(){this.initCanvas(),this.setupEventListeners()},initCanvas(){this.canvas=this.el,this.isDragging=!1,this.isConnecting=!1,this.draggedNode=null,this.connectionStart=null,this.connectionLine=null,this.lastMousePos={x:0,y:0},this.createTempConnectionLine()},setupEventListeners(){this.canvas.addEventListener("mousedown",this.handleMouseDown.bind(this)),document.addEventListener("mousemove",this.handleMouseMove.bind(this)),document.addEventListener("mouseup",this.handleMouseUp.bind(this)),this.canvas.addEventListener("wheel",this.handleWheel.bind(this)),this.setupNodeDragging(),this.setupConnectionPoints()},handleMouseDown(e){this.lastMousePos={x:e.clientX,y:e.clientY},(e.target===this.canvas||e.target.classList.contains("canvas"))&&(this.isDragging=!0,this.canvas.style.cursor="grabbing")},handleMouseMove(e){let t=e.clientX-this.lastMousePos.x,i=e.clientY-this.lastMousePos.y;if(this.isDragging)this.pushEvent("canvas_pan",{dx:t,dy:i});else if(this.isConnecting&&this.connectionLine){let s=this.canvas.getBoundingClientRect(),n=e.clientX-s.left,r=e.clientY-s.top;this.updateConnectionLine(n,r)}this.lastMousePos={x:e.clientX,y:e.clientY}},handleMouseUp(e){this.isDragging&&(this.isDragging=!1,this.canvas.style.cursor="default"),this.isConnecting&&this.finishConnection(e)},handleWheel(e){e.preventDefault();let t=e.deltaY>0?-1:1;this.pushEvent("canvas_zoom",{delta:t})},setupNodeDragging(){new MutationObserver(()=>{this.attachNodeDragListeners()}).observe(this.canvas,{childList:!0,subtree:!0}),this.attachNodeDragListeners()},attachNodeDragListeners(){this.canvas.querySelectorAll(".workflow-node").forEach(t=>{if(t.dataset.dragListenerAttached)return;t.dataset.dragListenerAttached="true";let i=!1,s={x:0,y:0},n={x:0,y:0},r=l=>{if(l.target.closest(".connection-point")||l.target.closest(".node-menu"))return;l.stopPropagation(),i=!0,s={x:l.clientX,y:l.clientY};let h=window.getComputedStyle(t);n={x:parseInt(h.left)||0,y:parseInt(h.top)||0},t.style.cursor="grabbing",t.style.zIndex="1000"},o=l=>{if(!i)return;let h=l.clientX-s.x,d=l.clientY-s.y,p=n.x+h,f=n.y+d;t.style.left=`${p}px`,t.style.top=`${f}px`},a=l=>{if(!i)return;i=!1,t.style.cursor="move",t.style.zIndex="";let h=l.clientX-s.x,d=l.clientY-s.y,p=n.x+h,f=n.y+d,g=t.dataset.nodeId;this.pushEvent("update_node_position",{node_id:g,x:p,y:f})};t.addEventListener("mousedown",r),document.addEventListener("mousemove",o),document.addEventListener("mouseup",a)})},setupConnectionPoints(){new MutationObserver(()=>{this.attachConnectionListeners()}).observe(this.canvas,{childList:!0,subtree:!0}),this.attachConnectionListeners()},attachConnectionListeners(){this.canvas.querySelectorAll(".connection-point").forEach(t=>{t.dataset.connectionListenerAttached||(t.dataset.connectionListenerAttached="true",t.addEventListener("mousedown",i=>{i.stopPropagation(),this.startConnection(t,i)}),t.addEventListener("mouseup",i=>{i.stopPropagation(),this.endConnection(t,i)}))})},startConnection(e,t){if(!e.classList.contains("output"))return;this.isConnecting=!0,this.connectionStart={point:e,node:e.closest(".workflow-node"),index:parseInt(e.dataset.outputIndex)||0};let i=e.getBoundingClientRect(),s=this.canvas.getBoundingClientRect();this.connectionStart.pos={x:i.left+i.width/2-s.left,y:i.top+i.height/2-s.top},this.showConnectionLine()},endConnection(e,t){if(!this.isConnecting||!e.classList.contains("input")){this.cancelConnection();return}let i=e.closest(".workflow-node"),s=this.connectionStart.node;if(i===s){this.cancelConnection();return}let n=s.dataset.nodeId,r=i.dataset.nodeId,o=this.connectionStart.index,a=parseInt(e.dataset.inputIndex)||0;this.pushEvent("connect_nodes",{source:n,target:r,output:o,input:a}),this.cancelConnection()},finishConnection(e){let t=document.elementFromPoint(e.clientX,e.clientY),i=t==null?void 0:t.closest(".connection-point.input");i?this.endConnection(i,e):this.cancelConnection()},cancelConnection(){this.isConnecting=!1,this.connectionStart=null,this.hideConnectionLine()},createTempConnectionLine(){let e=this.canvas.querySelector(".connections-layer");e&&(this.connectionLine=document.createElementNS("http://www.w3.org/2000/svg","path"),this.connectionLine.setAttribute("stroke","#007bff"),this.connectionLine.setAttribute("stroke-width","2"),this.connectionLine.setAttribute("fill","none"),this.connectionLine.setAttribute("stroke-dasharray","5,5"),this.connectionLine.style.display="none",e.appendChild(this.connectionLine))},showConnectionLine(){this.connectionLine&&(this.connectionLine.style.display="block")},hideConnectionLine(){this.connectionLine&&(this.connectionLine.style.display="none")},updateConnectionLine(e,t){if(!this.connectionLine||!this.connectionStart)return;let i=this.connectionStart.pos.x,s=this.connectionStart.pos.y,n=Math.abs(e-i)*.5,r=i+n,o=s,a=e-n,h=`M ${i} ${s} C ${r} ${o}, ${a} ${t}, ${e} ${t}`;this.connectionLine.setAttribute("d",h)},updated(){this.attachNodeDragListeners(),this.attachConnectionListeners()}},hi=ms;var vs=document.querySelector("meta[name='csrf-token']").getAttribute("content"),bs={WorkflowCanvas:hi},di=new li("/live",Et,{longPollFallbackMs:2500,params:{_csrf_token:vs},hooks:bs});Ke.config({barColors:{0:"#29d"},shadowColor:"rgba(0, 0, 0, .3)"});window.addEventListener("phx:page-loading-start",e=>Ke.show(300));window.addEventListener("phx:page-loading-stop",e=>Ke.hide());di.connect();window.liveSocket=di;})();
/**
 * @license MIT
 * topbar 2.0.0, 2023-02-04
 * http://buunguyen.github.io/topbar
 * Copyright (c) 2021 Buu Nguyen
 */

{application,arc,
             [{modules,['Elixir.Arc','Elixir.Arc.Actions.Delete',
                        'Elixir.Arc.Actions.Store','Elixir.Arc.Actions.Url',
                        'Elixir.Arc.Definition',
                        'Elixir.Arc.Definition.Storage',
                        'Elixir.Arc.Definition.Versioning','Elixir.Arc.File',
                        'Elixir.Arc.MissingExecutableError',
                        'Elixir.Arc.Processor','Elixir.Arc.Storage.Local',
                        'Elixir.Arc.Storage.S3',
                        'Elixir.Arc.Transformations.Convert',
                        'Elixir.Mix.Tasks.Arc','Elixir.Mix.Tasks.Arc.G']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger,hackney]},
              {description,"Flexible file upload and attachment library for Elixir.\n"},
              {registered,[]},
              {vsn,"0.11.0"}]}.

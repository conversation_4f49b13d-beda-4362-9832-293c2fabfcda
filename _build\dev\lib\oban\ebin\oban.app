{application,oban,
             [{modules,['Elixir.Inspect.Oban.Cron.Expression',
                        'Elixir.Mix.Tasks.Oban.Install',
                        'Elixir.Mix.Tasks.Oban.Install.Docs','Elixir.Oban',
                        'Elixir.Oban.Application','Elixir.Oban.Backoff',
                        'Elixir.Oban.Config','Elixir.Oban.CrashError',
                        'Elixir.Oban.Cron','Elixir.Oban.Cron.Expression',
                        'Elixir.Oban.Engine','Elixir.Oban.Engines.Basic',
                        'Elixir.Oban.Engines.Dolphin',
                        'Elixir.Oban.Engines.Inline',
                        'Elixir.Oban.Engines.Lite','Elixir.Oban.JSON',
                        'Elixir.Oban.Job','Elixir.Oban.Midwife',
                        'Elixir.Oban.Migration','Elixir.Oban.Migrations',
                        'Elixir.Oban.Migrations.MyXQL',
                        'Elixir.Oban.Migrations.Postgres',
                        'Elixir.Oban.Migrations.Postgres.V01',
                        'Elixir.Oban.Migrations.Postgres.V02',
                        'Elixir.Oban.Migrations.Postgres.V03',
                        'Elixir.Oban.Migrations.Postgres.V04',
                        'Elixir.Oban.Migrations.Postgres.V05',
                        'Elixir.Oban.Migrations.Postgres.V06',
                        'Elixir.Oban.Migrations.Postgres.V07',
                        'Elixir.Oban.Migrations.Postgres.V08',
                        'Elixir.Oban.Migrations.Postgres.V09',
                        'Elixir.Oban.Migrations.Postgres.V10',
                        'Elixir.Oban.Migrations.Postgres.V11',
                        'Elixir.Oban.Migrations.Postgres.V12',
                        'Elixir.Oban.Migrations.SQLite',
                        'Elixir.Oban.Notifier',
                        'Elixir.Oban.Notifiers.Isolated',
                        'Elixir.Oban.Notifiers.PG',
                        'Elixir.Oban.Notifiers.Postgres',
                        'Elixir.Oban.Nursery','Elixir.Oban.Peer',
                        'Elixir.Oban.Peers.Database',
                        'Elixir.Oban.Peers.Global',
                        'Elixir.Oban.Peers.Isolated',
                        'Elixir.Oban.PerformError','Elixir.Oban.Plugin',
                        'Elixir.Oban.Plugins.Cron',
                        'Elixir.Oban.Plugins.Gossip',
                        'Elixir.Oban.Plugins.Lifeline',
                        'Elixir.Oban.Plugins.Pruner',
                        'Elixir.Oban.Plugins.Reindexer',
                        'Elixir.Oban.Plugins.Repeater',
                        'Elixir.Oban.Queue.Drainer',
                        'Elixir.Oban.Queue.Executor',
                        'Elixir.Oban.Queue.Producer',
                        'Elixir.Oban.Queue.Supervisor',
                        'Elixir.Oban.Queue.Watchman','Elixir.Oban.Registry',
                        'Elixir.Oban.Repo','Elixir.Oban.Sonar',
                        'Elixir.Oban.Stager','Elixir.Oban.Telemetry',
                        'Elixir.Oban.Testing','Elixir.Oban.TimeoutError',
                        'Elixir.Oban.Validation','Elixir.Oban.Worker']},
              {compile_env,[{oban,['Elixir.Oban.Backoff',retry_mult],error}]},
              {optional_applications,[ecto_sqlite3,jason,igniter,myxql,
                                      postgrex]},
              {applications,[kernel,stdlib,elixir,logger,ecto_sql,
                             ecto_sqlite3,jason,igniter,myxql,postgrex,
                             telemetry]},
              {description,"Robust job processing, backed by modern PostgreSQL, SQLite3, and MySQL.\n"},
              {registered,[]},
              {vsn,"2.19.4"},
              {mod,{'Elixir.Oban.Application',[]}}]}.

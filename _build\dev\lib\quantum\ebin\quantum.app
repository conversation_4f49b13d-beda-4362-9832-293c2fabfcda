{application,quantum,
             [{modules,['Elixir.Quantum','Elixir.Quantum.ClockBroadcaster',
                        'Elixir.Quantum.ClockBroadcaster.Event',
                        'Elixir.Quantum.ClockBroadcaster.InitOpts',
                        'Elixir.Quantum.ClockBroadcaster.StartOpts',
                        'Elixir.Quantum.ClockBroadcaster.State',
                        'Elixir.Quantum.DateLibrary',
                        'Elixir.Quantum.DateLibrary.InvalidDateTimeForTimezoneError',
                        'Elixir.Quantum.DateLibrary.InvalidTimezoneError',
                        'Elixir.Quantum.ExecutionBroadcaster',
                        'Elixir.Quantum.ExecutionBroadcaster.Event',
                        'Elixir.Quantum.ExecutionBroadcaster.InitOpts',
                        'Elixir.Quantum.ExecutionBroadcaster.JobInPastError',
                        'Elixir.Quantum.ExecutionBroadcaster.StartOpts',
                        'Elixir.Quantum.ExecutionBroadcaster.State',
                        'Elixir.Quantum.Executor',
                        'Elixir.Quantum.Executor.StartOpts',
                        'Elixir.Quantum.ExecutorSupervisor',
                        'Elixir.Quantum.ExecutorSupervisor.InitOpts',
                        'Elixir.Quantum.ExecutorSupervisor.StartOpts',
                        'Elixir.Quantum.Job','Elixir.Quantum.JobBroadcaster',
                        'Elixir.Quantum.JobBroadcaster.InitOpts',
                        'Elixir.Quantum.JobBroadcaster.StartOpts',
                        'Elixir.Quantum.JobBroadcaster.State',
                        'Elixir.Quantum.NodeSelectorBroadcaster',
                        'Elixir.Quantum.NodeSelectorBroadcaster.Event',
                        'Elixir.Quantum.NodeSelectorBroadcaster.InitOpts',
                        'Elixir.Quantum.NodeSelectorBroadcaster.StartOpts',
                        'Elixir.Quantum.NodeSelectorBroadcaster.State',
                        'Elixir.Quantum.Normalizer',
                        'Elixir.Quantum.RunStrategy',
                        'Elixir.Quantum.RunStrategy.All',
                        'Elixir.Quantum.RunStrategy.Local',
                        'Elixir.Quantum.RunStrategy.NodeList',
                        'Elixir.Quantum.RunStrategy.NodeList.Quantum.RunStrategy.All',
                        'Elixir.Quantum.RunStrategy.NodeList.Quantum.RunStrategy.Local',
                        'Elixir.Quantum.RunStrategy.NodeList.Quantum.RunStrategy.Random',
                        'Elixir.Quantum.RunStrategy.Random',
                        'Elixir.Quantum.Storage',
                        'Elixir.Quantum.Storage.Noop',
                        'Elixir.Quantum.Supervisor',
                        'Elixir.Quantum.TaskRegistry',
                        'Elixir.Quantum.TaskRegistry.InitOpts',
                        'Elixir.Quantum.TaskRegistry.StartOpts',
                        'Elixir.Quantum.TaskRegistry.State']},
              {optional_applications,[]},
              {applications,[kernel,stdlib,elixir,logger,crontab,gen_stage,
                             telemetry,telemetry_registry]},
              {description,"Cron-like job scheduler for Elixir."},
              {registered,[]},
              {vsn,"3.5.3"}]}.

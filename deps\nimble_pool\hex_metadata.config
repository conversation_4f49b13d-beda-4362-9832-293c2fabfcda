{<<"links">>,[{<<"GitHub">>,<<"https://github.com/dashbitco/nimble_pool">>}]}.
{<<"name">>,<<"nimble_pool">>}.
{<<"version">>,<<"1.1.0">>}.
{<<"description">>,<<"A tiny resource-pool implementation">>}.
{<<"elixir">>,<<"~> 1.7">>}.
{<<"app">>,<<"nimble_pool">>}.
{<<"licenses">>,[<<"Apache-2.0">>]}.
{<<"requirements">>,[]}.
{<<"files">>,
 [<<"lib">>,<<"lib/nimble_pool">>,<<"lib/nimble_pool/application.ex">>,
  <<"lib/nimble_pool.ex">>,<<".formatter.exs">>,<<"mix.exs">>,<<"README.md">>,
  <<"CHANGELOG.md">>]}.
{<<"build_tools">>,[<<"mix">>]}.

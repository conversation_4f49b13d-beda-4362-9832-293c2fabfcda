# N8n <PERSON><PERSON><PERSON> Rewriter - Dockerfile
FROM elixir:1.15-alpine

# Install system dependencies
RUN apk add --no-cache \
    build-base \
    npm \
    nodejs \
    git \
    postgresql-client \
    python3 \
    make \
    gcc \
    inotify-tools

# Set working directory
WORKDIR /app

# Install hex and rebar
RUN mix local.hex --force && \
    mix local.rebar --force

# Copy mix files
COPY mix.exs mix.lock ./

# Install dependencies
RUN mix deps.get

# Copy application code
COPY . .

# Install node modules for assets
RUN npm install --prefix assets

# Compile application
RUN mix compile

# Expose Phoenix port
EXPOSE 4000

# Start the Phoenix server
CMD ["mix", "phx.server"]
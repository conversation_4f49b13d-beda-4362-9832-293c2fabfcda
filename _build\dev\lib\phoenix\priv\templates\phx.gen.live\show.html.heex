<.header>
  <%= schema.human_singular %> {@<%= schema.singular %>.id}
  <:subtitle>This is a <%= schema.singular %> record from your database.</:subtitle>
  <:actions>
    <.link patch={~p"<%= schema.route_prefix %>/#{@<%= schema.singular %>}/show/edit"} phx-click={JS.push_focus()}>
      <.button>Edit <%= schema.singular %></.button>
    </.link>
  </:actions>
</.header>

<.list><%= for {k, _} <- schema.attrs do %>
  <:item title="<%= Phoenix.Naming.humanize(Atom.to_string(k)) %>">{@<%= schema.singular %>.<%= k %>}</:item><% end %>
</.list>

<.back navigate={~p"<%= schema.route_prefix %>"}>Back to <%= schema.plural %></.back>

<.modal :if={@live_action == :edit} id="<%= schema.singular %>-modal" show on_cancel={JS.patch(~p"<%= schema.route_prefix %>/#{@<%= schema.singular %>}")}>
  <.live_component
    module={<%= inspect context.web_module %>.<%= inspect Module.concat(schema.web_namespace, schema.alias) %>Live.FormComponent}
    id={@<%= schema.singular %>.id}
    title={@page_title}
    action={@live_action}
    <%= schema.singular %>={@<%= schema.singular %>}
    patch={~p"<%= schema.route_prefix %>/#{@<%= schema.singular %>}"}
  />
</.modal>

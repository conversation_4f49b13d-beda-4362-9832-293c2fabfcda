import Config

# 数据库配置
config :n8n_elixir, N8nElixir.Repo,
  username: "postgres",
  password: "postgres",
  hostname: "localhost",
  database: "n8n_elixir_test#{System.get_env("MIX_TEST_PARTITION")}",
  pool: Ecto.Adapters.SQL.Sandbox,
  pool_size: 10

# Phoenix 端点配置
config :n8n_elixir, N8nElixirWeb.Endpoint,
  http: [ip: {127, 0, 0, 1}, port: 4002],
  secret_key_base: "your-test-secret-key-base",
  server: false

# 日志配置
config :logger, level: :warn

# Phoenix 配置
config :phoenix, :plug_init_mode, :runtime
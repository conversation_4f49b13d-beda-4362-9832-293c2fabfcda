defmodule N8nElixir.ExecutionTracker do
  @moduledoc """
  执行追踪器

  追踪和监控工作流执行状态
  """

  use GenServer
  require Logger

  @registry_name __MODULE__

  def start_link(init_arg) do
    GenServer.start_link(__MODULE__, init_arg, name: @registry_name)
  end

  @impl true
  def init(_init_arg) do
    Logger.info("Starting ExecutionTracker")
    {:ok, %{}}
  end

  @impl true
  def handle_call(_msg, _from, state) do
    {:reply, :ok, state}
  end

  @impl true
  def handle_cast(_msg, state) do
    {:noreply, state}
  end
end
defmodule N8nElixirWeb.WorkflowLive do
  @moduledoc """
  工作流编辑器主页面

  参考n8n的工作流编辑器界面，提供可视化的节点编辑和连接功能
  使用Phoenix LiveView实现实时交互
  """
  use N8nElixirWeb, :live_view

  @impl true
  def mount(%{"id" => workflow_id}, _session, socket) do
    # 临时简化版本
    socket = 
      socket
      |> assign(:workflow_id, workflow_id)
      |> assign(:page_title, "Edit Workflow")
      |> assign(:mode, "design")
    
    {:ok, socket}
  end

  @impl true
  def mount(_params, _session, socket) do
    # 创建新工作流
    socket = 
      socket
      |> assign(:workflow_id, nil)
      |> assign(:page_title, "New Workflow")
      |> assign(:mode, "design")
    
    {:ok, socket}
  end

  @impl true
  def handle_params(params, _url, socket) do
    {:noreply, apply_action(socket, socket.assigns.live_action, params)}
  end

  defp apply_action(socket, :edit, %{"id" => workflow_id}) do
    socket
    |> assign(:page_title, "Edit Workflow")
    |> assign(:workflow_id, workflow_id)
  end

  defp apply_action(socket, :new, _params) do
    socket
    |> assign(:page_title, "New Workflow")
  end

  @impl true
  def handle_event("save_workflow", _params, socket) do
    {:noreply, put_flash(socket, :info, "Save functionality coming soon")}
  end
end
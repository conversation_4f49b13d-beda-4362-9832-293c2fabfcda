{application,hackney,
             [{description,"simple HTTP client"},
              {vsn,"1.24.1"},
              {registered,[hackney_pool]},
              {applications,[kernel,stdlib,crypto,asn1,public_key,ssl,idna,
                             mimerl,certifi,parse_trans,ssl_verify_fun,
                             metrics,unicode_util_compat]},
              {included_applications,[]},
              {mod,{hackney_app,[]}},
              {env,[{timeout,150000},
                    {max_connections,50},
                    {restart,permanent},
                    {shutdown,10000},
                    {maxr,10},
                    {maxt,1}]},
              {licenses,["Apache-2.0"]},
              {links,[{"Github","https://github.com/benoitc/hackney"}]},
              {files,["src","include","rebar.config","rebar.lock","README.md",
                      "NEWS.md","LICENSE","NOTICE","MAINTAINERS"]}]}.

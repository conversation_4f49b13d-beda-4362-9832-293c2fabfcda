{<<"links">>,[{<<"GitHub">>,<<"https://github.com/elixir-lang/gen_stage">>}]}.
{<<"name">>,<<"gen_stage">>}.
{<<"version">>,<<"1.3.1">>}.
{<<"description">>,
 <<"Producer and consumer actors with back-pressure for Elixir">>}.
{<<"elixir">>,<<"~> 1.11">>}.
{<<"files">>,
 [<<"lib">>,<<"lib/gen_stage">>,<<"lib/gen_stage/stream.ex">>,
  <<"lib/gen_stage/streamer.ex">>,<<"lib/gen_stage/dispatcher.ex">>,
  <<"lib/gen_stage/dispatchers">>,
  <<"lib/gen_stage/dispatchers/broadcast_dispatcher.ex">>,
  <<"lib/gen_stage/dispatchers/demand_dispatcher.ex">>,
  <<"lib/gen_stage/dispatchers/partition_dispatcher.ex">>,
  <<"lib/gen_stage/utils.ex">>,<<"lib/gen_stage/buffer.ex">>,
  <<"lib/gen_stage.ex">>,<<"lib/consumer_supervisor.ex">>,
  <<".formatter.exs">>,<<"mix.exs">>,<<"README.md">>,<<"CHANGELOG.md">>]}.
{<<"app">>,<<"gen_stage">>}.
{<<"licenses">>,[<<"Apache-2.0">>]}.
{<<"requirements">>,[]}.
{<<"build_tools">>,[<<"mix">>]}.

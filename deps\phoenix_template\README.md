# Phoenix.Template

Templates are markup languages that are compiled to Elixir code.

This packages provides functions for loading and compiling templates
from disk as well as the behaviour for compiling markup languages.

See the [`Phoenix.Template`](https://hexdocs.pm/phoenix_template/Phoenix.Template.html)
module documentation for more information.

## Installation

You can install `phoenix_template` by adding it to your list of dependencies in `mix.exs`:

```elixir
def deps do
  [
    {:phoenix_template, "~> 1.0"}
  ]
end
```

{<<"name">>,<<"poolboy">>}.
{<<"version">>,<<"1.5.2">>}.
{<<"requirements">>,#{}}.
{<<"app">>,<<"poolboy">>}.
{<<"maintainers">>,
 [<<"<PERSON>">>,<<"<PERSON>">>,<<"<PERSON>">>]}.
{<<"precompiled">>,false}.
{<<"description">>,<<"A hunky Erlang worker pool factory">>}.
{<<"files">>,
 [<<"src/poolboy.app.src">>,<<"LICENSE">>,<<"README.md">>,<<"rebar.config">>,
  <<"rebar.lock">>,<<"src/poolboy.erl">>,<<"src/poolboy_sup.erl">>,
  <<"src/poolboy_worker.erl">>]}.
{<<"licenses">>,[<<"Unlicense">>,<<"Apache 2.0">>]}.
{<<"links">>,[{<<"GitHub">>,<<"https://github.com/devinus/poolboy">>}]}.
{<<"build_tools">>,[<<"rebar3">>]}.
